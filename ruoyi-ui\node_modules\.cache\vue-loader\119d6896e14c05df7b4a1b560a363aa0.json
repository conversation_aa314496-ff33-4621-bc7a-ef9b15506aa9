{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\networkSecurity.vue?vue&type=template&id=9071903a&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\networkSecurity.vue", "mtime": 1754010111790}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}