{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\mytask\\index.vue?vue&type=template&id=6db8f8f2&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\mytask\\index.vue", "mtime": 1754010084484}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}