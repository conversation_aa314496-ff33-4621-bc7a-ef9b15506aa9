<template>
  <div v-if="funEsSeach">
    <splitpanes class="default-theme">
      <pane
        class="leftLink"
        ref="leftLink"
        min-size="20"
        max-size="50"
        size="26"
      >
        <div class="treeMain" style="width: 100%; margin: 0">
          <!-- 搜索框 - 直接从Wechat.vue复制 -->
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              gap: 10px;
            "
          >
            <el-input
              placeholder="输入关键字进行过滤"
              v-model="filterText"
              clearable
              style="margin-bottom: 10px"
              class="input_Fixed"
            >
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
          <!-- 控制区域 -->
          <div
            style="
              margin-top: -10px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 10px;
            "
          >
            <el-tooltip
              class="item"
              effect="dark"
              content="重置"
              placement="top"
            >
              <el-button
                icon="el-icon-refresh"
                @click="treeClear"
                type="text"
                style="color: #666"
                >重置</el-button
              >
            </el-tooltip>
          </div>
          <div class="treeBox">
            <el-tree
              :data="treeData"
              ref="tree"
              show-checkbox
              node-key="id"
              :default-expanded-keys="[1000]"
              @check="handleTreeCheck"
              @node-click="treeNodeClick"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
            >
              <template slot-scope="scoped">
                <div style="display: flex; align-items: center">
                  <div>{{ scoped.data.label }}</div>
                  <div
                    v-if="scoped.data.country && scoped.data.country !== '0'"
                    style="display: flex; align-items: center"
                  >
                    <div>[</div>
                    <dict-tag
                      :options="dict.type.country"
                      :value="scoped.data.country"
                    />
                    <div>]</div>
                  </div>
                  <div style="font-weight: 600">
                    {{ `${scoped.data.count ? `(${scoped.data.count})` : ""}` }}
                  </div>
                  <div v-if="scoped.data.label !== '数据源' && scoped.data.url">
                    <el-tooltip
                      content="打开数据源链接"
                      placement="top-start"
                      effect="light"
                    >
                      <i
                        class="el-icon-connection"
                        style="
                          font-size: 20px;
                          color: #409eff;
                          margin-left: 5px;
                          margin-top: 4px;
                        "
                        @click="openUrl(scoped.data.url)"
                      ></i>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
          <!-- 分页组件 -->
          <div class="tree-pagination">
            <el-pagination
              @current-change="handleTreeCurrentChange"
              @size-change="handleTreePageSizeChange"
              :current-page="treeCurrentPage"
              :pager-count="5"
              :page-size="treePageSize"
              :page-sizes="[50, 100, 150]"
              layout="total, sizes, prev, pager, next"
              :total="treeTotal"
              :small="true"
            >
            </el-pagination>
          </div>
        </div>
      </pane>
      <pane min-size="50" max-size="80" size="74">
        <div
          class="rightMain"
          style="margin-left: 0; overflow-y: auto"
          ref="rightMain"
        >
          <div class="toolBox">
            <!-- <div class="title" :style="{ height: ActiveData.title ? '' : '50px' }">
              <p v-if="ActiveData.title">{{ ActiveData.title }}</p>
              <p v-else></p>
            </div> -->
            <div class="mainTool">
              <p>
                发布日期:
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == '' ? 'primary' : ''"
                  @click="SeachData.timeRange = ''"
                  >24小时</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 1 ? 'primary' : ''"
                  @click="SeachData.timeRange = 1"
                  >今天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 2 ? 'primary' : ''"
                  @click="SeachData.timeRange = 2"
                  >近2天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 4 ? 'primary' : ''"
                  @click="SeachData.timeRange = 4"
                  >近7天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 5 ? 'primary' : ''"
                  @click="SeachData.timeRange = 5"
                  >近30天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 6 ? 'primary' : ''"
                  @click="SeachData.timeRange = 6"
                  >自定义</el-button
                >
                <el-date-picker
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="SeachData.customDay"
                  v-if="SeachData.timeRange == 6"
                  style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  unlink-panels
                  clearable
                ></el-date-picker>
              </p>
              <p>
                采集日期:
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 0 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 0"
                  >24小时</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 1 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 1"
                  >今天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 2 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 2"
                  >近2天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 4 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 4"
                  >近7天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 5 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 5"
                  >近30天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 6 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 6"
                  >自定义</el-button
                >
                <el-date-picker
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="SeachData.collectionTime"
                  v-if="SeachData.collectionDateType == 6"
                  style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  unlink-panels
                  clearable
                ></el-date-picker>
              </p>
              <p>
                <span
                  style="
                    width: 60px;
                    display: inline-block;
                    text-align: right;
                    margin-right: 5px;
                  "
                  >是否与科技有关:</span
                >
                <el-radio-group v-model="SeachData.isTechnology" size="small">
                  <el-radio-button
                    v-for="dict in dict.type.is_technology"
                    :label="dict.value"
                    :key="'is_technology' + dict.value"
                    >{{ dict.label }}</el-radio-button
                  >
                  <el-radio-button :label="null" :key="'is_technology3'"
                    >全部</el-radio-button
                  >
                </el-radio-group>
              </p>
              <div class="keyword">
                <span
                  style="
                    width: 60px;
                    display: inline-block;
                    text-align: right;
                    margin-right: 5px;
                  "
                  >关键词:</span
                >
                <el-input
                  ref="keywordRef"
                  placeholder="请输入关键词,使用逗号分割(英文)"
                  style="width: 430px"
                  v-model="SeachData.keyword"
                  @focus="showHistoryList()"
                  @blur="hideHistoryList()"
                >
                </el-input>
                <el-button
                  type="primary"
                  size="mini"
                  @click="funEsSeach('filter')"
                  :loading="buttonDisabled"
                  style="margin-left: 10px; height: 36px"
                  >搜索</el-button
                >
                <div class="history" v-show="showHistory">
                  <div
                    class="historyItem"
                    v-for="(history, index) in historyList"
                    :key="index"
                    v-loading="historyLoading"
                  >
                    <div @click="keywordsChange(history)" class="historyText">
                      {{ history.keyword }}
                    </div>
                    <el-button
                      type="text"
                      @click="removeHistory(history, 1)"
                      style="color: #999; font-size: 12px"
                      >删除</el-button
                    >
                  </div>
                  <div class="historyItem">
                    <el-button type="text" @click="moreHistory()"
                      >更多</el-button
                    >
                    <el-button
                      type="text"
                      @click="clearHistory()"
                      style="color: #999; font-size: 12px"
                      >清空</el-button
                    >
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="btn">
              <el-button size="mini" @click="resetting">重置</el-button>
              <el-button
                type="primary"
                size="mini"
                @click="funEsSeach('filter')"
                :loading="buttonDisabled"
                >搜索</el-button
              >
            </div> -->
          </div>
          <MainArticle
            v-loading="tableLoading"
            :flag="'artificialIntelligence'"
            :currentPage="currentPage"
            :pageSize="pageSize"
            :total="total"
            :ArticleList="ArticleList"
            :keywords="SeachData.keyword"
            @handleCurrentChange="handleCurrentChange"
            @handleSizeChange="handleSizeChange"
            @Refresh="funEsSeach('filter')"
            :SeachData="SeachData"
            ref="mainArticle"
          ></MainArticle>
        </div>
      </pane>
    </splitpanes>

    <el-dialog
      title="关键词历史"
      :visible.sync="dialogVisible1"
      width="570px"
      :close-on-click-modal="false"
    >
      <div class="history" v-loading="historyLoading">
        <div
          class="historyItem"
          v-for="(history, index) in historyList1"
          :key="index"
        >
          <div @click="keywordsChange(history)" class="historyText">
            {{ history.keyword }}
          </div>
          <el-button type="text" @click="removeHistory(history, 2)"
            >删除</el-button
          >
        </div>
      </div>
      <pagination
        v-show="total1 > 0"
        :total="total1"
        :page.sync="queryParams1.pageNum"
        :limit.sync="queryParams1.pageSize"
        :background="false"
        @pagination="getArticleHistory1"
        :layout="'total, prev, pager, next'"
      />
    </el-dialog>
  </div>
</template>

<script>
import api from "@/api/ScienceApi/index.js";
import topSeach from "@/views/components/topSeach.vue";
import MainArticle from "../components/MainArticle.vue";
import {
  listArticleHistory,
  delArticleHistory,
  addArticleHistory,
  cleanArticleHistory,
} from "@/api/article/articleHistory";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";

export default {
  components: { topSeach, MainArticle, Splitpanes, Pane },
  dicts: ["is_technology", "country"],
  data() {
    return {
      width: "258",
      isReSize: false,
      /* 文章主体组件数据 */
      currentPage: 1,
      pageSize: 50,
      total: 0,
      ArticleList: [],
      /* 左侧tree数据 */
      filterText: "",
      treeData: [],
      treeDataTransfer: [],
      checkList: [],
      /* 树形分页数据 */
      treeCurrentPage: 1,
      treePageSize: 100,
      treeTotal: 0,
      /* 搜索防抖 */
      searchDebounceTimer: null,
      /* 搜索组件数据 */
      SeachData: {
        metaMode: "" /* 匹配模式 */,
        keyword: "" /* 关键词 */,
        timeRange: "4" /* 时间范围 */,
        customDay: [] /* 自定义天 */,
        collectionDateType: null /* 时间范围 */,
        collectionTime: [] /* 自定义天 */,
        isTechnology: "1",
        sortMode: "0",
      } /* 搜索条件 */,
      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */
      buttonDisabled: false /* 按钮防抖 */,
      ActiveData: {},
      seniorSerchFlag: false /* 普通检索或高级检索 */,
      areaList: [] /* 国内地区 */,
      countryList: [] /* 国家或地区 */,
      KeList: [],
      funEsSeach: null,
      treeQuery: {
        filterwords: "", // 添加树搜索关键字
      },
      domainList: [],
      industryList: [],
      showHistory: false,
      historyList: [],
      historyTimeout: null,
      dialogVisible1: false,
      historyLoading: false,
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
      },
      total1: 0,
      historyList1: [],
      nodeCheckList: [],
      isTreeSlotProcessing: false,
      initializationCompleted: false, // 标记初始化是否完成
      // 从Wechat.vue同步的属性
      tableLoading: false, // 表格loading状态
      treeReady: false, // 树组件是否已准备好
      isQuerying: false, // 查询防抖
      queryDebounceTimer: null, // 查询防抖定时器
    };
  },
  async created() {
    try {
      // 初始化funEsSeach方法
      this.funEsSeach = this.EsSeach;

      // 先加载基础数据
      await this.getArticleHistory();

      // 加载树数据和内容数据
      await this.initializeData();

      // 标记初始化完成，这样watch监听器才会开始工作
      this.initializationCompleted = true;
    } catch (error) {
      console.error("组件初始化失败:", error);
      this.$message.error("初始化失败，请刷新页面重试");
    }
  },

  mounted() {
    // 在mounted中再次检查树组件状态
    this.$nextTick(() => {
      this.checkTreeReadyStatus();
    });
  },
  watch: {
    // 监听过滤文本变化，调用后端接口搜索 - 直接从Wechat.vue复制
    filterText(val) {
      // 清除之前的防抖定时器
      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer);
      }

      // 设置防抖，500ms后执行搜索
      this.searchDebounceTimer = setTimeout(() => {
        this.handleTreeSearch(val);
      }, 500);
    },

    // 监听筛选条件变化
    "SeachData.timeRange": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
    "SeachData.collectionDateType": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
    "SeachData.isTechnology": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
  },
  methods: {
    // 初始化数据
    async initializeData() {
      try {
        // 先加载树数据
        await this.queryTreeData();

        // 等待树组件完全渲染
        await this.$nextTick();

        // 再加载文章列表（内部已经处理了 tableLoading）
        await this.queryArticleList();
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.$message.error("初始化失败，请刷新页面重试");
      }
    },

    // 处理筛选条件变化 - 直接从Wechat.vue复制
    handleFilterChange() {
      // 清空左侧树选中状态
      this.clearTreeSelections();

      // 重置分页到第一页
      this.currentPage = 1;
      this.treeCurrentPage = 1;

      // 滚动到顶部
      this.scrollToTopImmediately();
      this.scrollTreeToTop(); // 右侧筛选条件变化时也要滚动左侧树到顶部

      // 同时查询树和列表
      this.queryTreeAndList();
    },

    // 清空树选中状态
    clearTreeSelections() {
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([]);
      }
      this.checkList = [];
    },

    // 同时查询树和列表 - 直接从Wechat.vue复制
    async queryTreeAndList() {
      try {
        await Promise.all([
          this.queryTreeData(),
          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading
        ]);
      } catch (error) {
        console.error("同时查询树和列表失败:", error);
        this.$message.error("查询失败，请重试");
      }
    },

    // 分页处理
    handleCurrentChange(current) {
      this.currentPage = current;
      this.scrollToTopImmediately();
      this.queryArticleList();
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.scrollToTopImmediately();
      this.queryArticleList();
    },

    // 检查树组件准备状态
    checkTreeReadyStatus() {
      let attempts = 0;
      const maxAttempts = 50; // 最多检查2.5秒

      const checkInterval = setInterval(() => {
        attempts++;

        if (
          this.$refs.tree &&
          this.treeData &&
          this.treeData[0] &&
          this.treeData[0].children &&
          this.treeData[0].children.length > 0
        ) {
          this.treeReady = true;
          clearInterval(checkInterval);
        } else if (attempts >= maxAttempts) {
          clearInterval(checkInterval);
        }
      }, 50);
    },

    // 确保树组件准备就绪
    async ensureTreeReady() {
      if (
        this.treeReady &&
        this.$refs.tree &&
        this.treeData[0].children.length > 0
      ) {
        return Promise.resolve();
      }

      return new Promise((resolve) => {
        let attempts = 0;
        const maxAttempts = 100; // 最多等待5秒

        const checkTree = () => {
          attempts++;

          if (
            this.$refs.tree &&
            this.treeData &&
            this.treeData[0] &&
            this.treeData[0].children &&
            this.treeData[0].children.length > 0
          ) {
            this.treeReady = true;

            resolve();
          } else if (attempts >= maxAttempts) {
            resolve(); // 超时也要resolve，避免卡死
          } else {
            setTimeout(checkTree, 50);
          }
        };
        checkTree();
      });
    },
    // 查询树数据
    async queryTreeData() {
      try {
        const params = {
          pageNum: this.treeCurrentPage,
          pageSize: this.treePageSize,
          platformType: 1,
          id: this.$route.query.id || 1,
          m: 1,
          dateType:
            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : "",
          startTime: this.SeachData.customDay
            ? this.SeachData.customDay[0]
            : "",
          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : "",
          collectionDateType:
            this.SeachData.collectionDateType != 6
              ? this.SeachData.collectionDateType
              : "",
          collectionStartTime: this.SeachData.collectionTime
            ? this.SeachData.collectionTime[0]
            : "",
          collectionEndTime: this.SeachData.collectionTime
            ? this.SeachData.collectionTime[1]
            : "",
          keywords: this.SeachData.keyword,
          isTechnology: this.SeachData.isTechnology,
          // 添加关键字过滤参数
          filterwords: this.treeQuery.filterwords || "",
          // 添加 artificialIntelligence 特有的参数
          menuType: this.$route.query.menuType,
        };

        const res = await api.monitoringMedium(params);

        if (res.code === 200) {
          const dataList = res.rows || [];
          const total = res.total || 0;

          const mapData = (data) =>
            data.map((item, index) => ({
              id: item.sourceSn || `node_${index}`, // 使用sourceSn作为ID，确保唯一性
              label: item.cnName,
              count: item.articleCount || 0,
              orderNum: item.orderNum,
              country: item.countryOfOrigin,
              sourceSn: item.sourceSn,
              url: item.url || null,
            }));

          this.treeData = [
            {
              id: 1000,
              label: "数据源",
              children: mapData(dataList),
            },
          ];
          this.treeDataTransfer = mapData(dataList);
          this.treeTotal = total;

          // 等待DOM更新后标记树组件为准备就绪
          this.$nextTick(() => {
            // 简单延迟，让mounted中的检查来处理准备状态
            setTimeout(() => {
              // 不在这里设置treeReady，让mounted中的检查来处理
            }, 100);
          });
        }
      } catch (error) {
        console.error("查询树数据失败:", error);
        this.$message.error("获取数据源失败");
      }
    },
    // 查询文章列表（带防抖）
    async queryArticleList() {
      // 防止重复查询
      if (this.isQuerying) {
        return;
      }

      // 立即显示loading状态
      this.tableLoading = true;

      // 清除之前的防抖定时器
      if (this.queryDebounceTimer) {
        clearTimeout(this.queryDebounceTimer);
      }

      // 设置防抖，300ms后执行查询
      this.queryDebounceTimer = setTimeout(async () => {
        try {
          this.isQuerying = true;

          const params = {
            m: 1,
            pageNum: this.currentPage,
            pageSize: this.pageSize,
            id: this.$route.query.id || 1,
            isSort: this.SeachData.sortMode,
            dateType:
              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : "",
            startTime: this.SeachData.customDay
              ? this.SeachData.customDay[0]
              : "",
            endTime: this.SeachData.customDay
              ? this.SeachData.customDay[1]
              : "",
            collectionDateType:
              this.SeachData.collectionDateType != 6
                ? this.SeachData.collectionDateType
                : "",
            collectionStartTime: this.SeachData.collectionTime
              ? this.SeachData.collectionTime[0]
              : "",
            collectionEndTime: this.SeachData.collectionTime
              ? this.SeachData.collectionTime[1]
              : "",
            keywords: this.SeachData.keyword,
            isTechnology: this.SeachData.isTechnology,
            platformType: 1,
            // 添加 artificialIntelligence 特有的参数
            menuType: this.$route.query.menuType,
          };

          // 如果有选中的数据源，添加数据源参数 - 直接从Wechat.vue复制
          if (this.checkList && this.checkList.length > 0) {
            const data = this.checkList.map((item) => item.label);
            const sourceSn = data
              .map((item) => {
                const foundItem = this.treeDataTransfer.find(
                  (row) => row.label === item
                );
                return foundItem ? foundItem.sourceSn : null;
              })
              .filter((sn) => sn !== null);

            params.weChatName = String(data);
            params.sourceSn = String(sourceSn);
          }

          // 记录关键词历史
          if (params.keywords) {
            addArticleHistory({ keyword: params.keywords, type: 2 }).then(
              () => {
                this.getArticleHistory();
              }
            );
          }

          const res = await api.KeIntegration({ ...params, ...this.treeQuery });

          if (res.code == 200) {
            this.ArticleList = res.data.list || [];
            this.total = res.data.total || 0;

            // 处理分页为空的情况
            if (
              this.ArticleList.length == 0 &&
              this.pageSize * (this.currentPage - 1) >= this.total &&
              this.total != 0
            ) {
              this.currentPage = Math.max(
                1,
                Math.ceil(this.total / this.pageSize)
              );
              // 重新查询
              await this.queryArticleList();
              return; // 重新查询时不要关闭loading
            }
          } else {
            this.$message.error(res.msg || "获取数据失败");
          }
        } catch (error) {
          console.error("查询文章列表失败:", error);
          this.$message.error("查询失败，请重试");
        } finally {
          this.isQuerying = false;
          this.tableLoading = false; // 查询完成后关闭loading
          this.buttonDisabled = false;
        }
      }, 300);
    },

    // 滚动到顶部
    scrollToTopImmediately() {
      this.$nextTick(() => {
        // 尝试多种滚动方式确保滚动成功
        const scrollBoxElement = document.querySelector(".scollBox");
        if (scrollBoxElement) {
          scrollBoxElement.scrollTop = 0;
        }

        // 如果MainArticle组件有scroll引用，也尝试滚动它
        if (
          this.$refs.mainArticle &&
          this.$refs.mainArticle.$refs &&
          this.$refs.mainArticle.$refs.scroll
        ) {
          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;
        }

        // 滚动整个右侧区域
        const rightMain = document.querySelector(".rightMain");
        if (rightMain) {
          rightMain.scrollTop = 0;
        }
      });
    },

    // 滚动左侧树到顶部
    scrollTreeToTop() {
      this.$nextTick(() => {
        const treeBox = document.querySelector(".treeBox");
        if (treeBox) {
          treeBox.scrollTop = 0;
        }
      });
    },
    // 树节点勾选处理（使用check事件）- 直接从Wechat.vue复制
    handleTreeCheck(data, checkedInfo) {
      // 如果树组件还没准备好，直接返回
      if (!this.treeReady || !this.$refs.tree) {
        return;
      }

      // check事件的参数：
      // data: 当前点击的节点数据
      // checkedInfo: { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }

      // 获取所有选中的节点（排除根节点）
      const allCheckedNodes = checkedInfo.checkedNodes || [];
      const selectedNodes = allCheckedNodes.filter(
        (node) => node.id !== 1000 && node.label !== "数据源"
      );

      // 更新选中列表
      this.checkList = selectedNodes.map((node) => ({ ...node }));

      // 重置页码并查询内容 - 修改为KeMonitor的分页参数
      this.currentPage = 1;
      this.scrollToTopImmediately();
      this.queryArticleList();
    },

    // checkChange方法 - 直接从Wechat.vue复制
    async checkChange(item, isCheck) {
      // 如果树组件还没准备好，直接返回，不处理勾选
      if (!this.treeReady || !this.$refs.tree) {
        return;
      }

      // 延迟执行，确保Element UI树组件内部状态已经更新
      setTimeout(() => {
        // 如果勾选的是数据源根节点，处理全选/取消全选
        if (item.id === 1000 || item.label === "数据源") {
          if (isCheck) {
            // 全选：选择所有可见的子节点
            this.checkList = [...this.treeData[0].children];
            const allIds = this.treeData[0].children.map((node) => node.id);

            this.$refs.tree.setCheckedKeys(allIds);
          } else {
            // 取消全选：清空所有选择
            this.checkList = [];
            this.$refs.tree.setCheckedKeys([]);
          }
        } else {
          // 处理单个节点的勾选/取消勾选

          if (isCheck) {
            // 添加到选中列表（多选）
            const existingIndex = this.checkList.findIndex(
              (row) => row.label === item.label
            );
            if (existingIndex === -1) {
              this.checkList.push({ ...item });
            }
          } else {
            // 从选中列表中移除
            this.checkList = this.checkList.filter(
              (row) => row.label !== item.label
            );
          }
        }

        // 重置页码并查询内容 - 修改为KeMonitor的分页参数
        this.currentPage = 1;
        this.scrollToTopImmediately();
        this.queryArticleList();
      }, 50); // 延迟50ms执行
    },

    // 树节点点击处理（混合选择模式）- 直接从Wechat.vue复制
    async treeNodeClick(item) {
      // 如果点击的是数据源根节点，不进行操作
      if (item.id === 1000 || item.label === "数据源") {
        return;
      }

      // 如果树组件还没准备好，直接返回，不处理点击
      if (!this.treeReady || !this.$refs.tree) {
        return;
      }

      // 先滚动到顶部
      this.scrollToTopImmediately();

      // 延迟执行，确保Element UI树组件内部状态已经更新
      setTimeout(() => {
        // 检查是否已选中该节点
        const existingIndex = this.checkList.findIndex(
          (row) => row.label === item.label
        );

        if (existingIndex !== -1) {
          // 如果当前项已被选中，则只取消该项的选择（保持其他选择）
          this.checkList = this.checkList.filter(
            (row) => row.label !== item.label
          );

          // 更新树组件的选中状态
          const remainingIds = this.checkList.map((node) => node.id);
          this.$refs.tree.setCheckedKeys(remainingIds);
        } else {
          // 如果当前项未被选中，清空所有选择，设置为唯一选中项（单选）
          this.checkList = [{ ...item }];

          // 更新树组件的选中状态
          this.$refs.tree.setCheckedKeys([item.id]);
        }

        // 重置页码并查询内容 - 修改为KeMonitor的分页参数
        this.currentPage = 1;
        this.queryArticleList();
      }, 50); // 延迟50ms执行
    },
    // 重置树选择 - 直接从Wechat.vue复制
    treeClear() {
      // 清空选中状态
      this.clearTreeSelections();

      // 重置页码并查询列表数据 - 修改为KeMonitor的分页参数
      this.currentPage = 1;
      this.scrollToTopImmediately();
      this.scrollTreeToTop();
      this.queryArticleList();
    },

    // 处理树搜索 - 直接从Wechat.vue复制并适配KeMonitor
    async handleTreeSearch(keyword) {
      try {
        // 树搜索不应该影响右侧列表的loading状态
        // this.tableLoading = true; // 移除这行

        // 更新搜索关键字
        this.treeQuery.filterwords = keyword || "";

        if (keyword) {
          // 使用filterwords参数调用树接口进行搜索
          this.scrollTreeToTop(); // 关键字过滤时滚动到顶部
          await this.queryTreeData();
        } else {
          // 如果搜索关键字为空，重新获取原始数据
          this.treeCurrentPage = 1;
          this.scrollTreeToTop(); // 清空关键字时滚动到顶部
          await this.queryTreeData();
        }
      } catch (error) {
        console.error("搜索树节点失败:", error);
        this.$message.error("搜索失败，请重试");
      }
      // 不需要finally块来关闭tableLoading
    },

    // 保留原有的必要方法
    SwitchInfo(data) {
      this.isInfo = data;
    },

    seniorSerch() {
      this.seniorSerchFlag = !this.seniorSerchFlag;
    },

    async getArea() {
      try {
        const Response = await api.getAreaList();
        if (Response && Response.code == 200 && Response.data) {
          this.areaList = Response.data[0] || [];
          this.countryList = Response.data[1] || [];
        } else {
          console.warn("获取地区数据失败或数据为空");
        }
      } catch (err) {
        console.error("获取区域数据失败:", err);
        this.$message.error("地区数据获取失败");
      }
    },

    async getArticleHistory() {
      try {
        const res = await listArticleHistory({
          pageNum: 1,
          pageSize: 5,
          type: 2,
        });
        if (res && res.code === 200) {
          this.historyList = res.rows || [];
        }
      } catch (error) {
        console.error("获取历史记录失败:", error);
      }
    },

    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 添加EsSeach方法以兼容原有调用
    EsSeach(flag) {
      if (flag === "filter") {
        // 筛选变化时同时查询树和列表
        this.queryTreeAndList();
      } else {
        // 其他情况只查询列表
        this.queryArticleList();
      }
    },

    // 重置搜索条件 - 简化版本
    resetting() {
      try {
        this.SeachData = {
          metaMode: "" /* 匹配模式 */,
          keyword: "" /* 关键词 */,
          timeRange: "" /* 时间范围 */,
          customDay: [] /* 自定义天 */,
          collectionDateType: null /* 时间范围 */,
          collectionTime: [] /* 自定义天 */,
          isTechnology: "1",
          sortMode: "0",
        };

        this.currentPage = 1;
        this.scrollToTopImmediately();
        this.queryTreeAndList();
      } catch (error) {
        console.error("重置搜索条件时出错:", error);
        this.$message.error("重置搜索条件失败");
      }
    },
    async removeHistory(item, type) {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        if (item && item.id) {
          await delArticleHistory([item.id]);

          if (type == 1) {
            if (this.$refs["keywordRef"]) {
              this.$refs["keywordRef"].focus();
            }
            await this.getArticleHistory();
          } else {
            await this.getArticleHistory();
            await this.getArticleHistory1();
          }
        }
      } catch (error) {
        console.error("删除历史记录时出错:", error);
        this.$message.error("删除历史记录失败");
      }
    },

    showHistoryList() {
      try {
        this.showHistory = true;
      } catch (error) {
        console.error("显示历史列表时出错:", error);
      }
    },

    hideHistoryList() {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        this.historyTimeout = setTimeout(() => {
          this.showHistory = false;
        }, 500);
      } catch (error) {
        console.error("隐藏历史列表时出错:", error);
        this.showHistory = false; // 确保在出错时也能隐藏列表
      }
    },

    // 关键词历史选择 - 直接从Wechat.vue复制
    keywordsChange(item) {
      this.SeachData.keyword = item.keyword;
      this.dialogVisible1 = false;
      this.scrollToTopImmediately();
      this.currentPage = 1;
      this.queryTreeAndList();
    },

    async clearHistory() {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        if (this.$refs["keywordRef"]) {
          this.$refs["keywordRef"].focus();
        }

        await cleanArticleHistory(2);
        await this.getArticleHistory();
      } catch (error) {
        console.error("清除历史记录时出错:", error);
        this.$message.error("清除历史记录失败");
      }
    },

    moreHistory() {
      try {
        this.historyLoading = true;
        this.getArticleHistory1();
        this.dialogVisible1 = true;
      } catch (error) {
        console.error("加载更多历史记录时出错:", error);
        this.historyLoading = false;
      }
    },

    async getArticleHistory1() {
      try {
        this.historyLoading = true;
        const response = await listArticleHistory({
          ...this.queryParams1,
          type: 2,
        });

        if (response) {
          this.historyList1 = response.rows || [];
          this.total1 = response.total || 0;
        }

        this.historyLoading = false;
      } catch (error) {
        console.error("获取文章历史记录时出错:", error);
        this.historyLoading = false;
        this.$message.error("获取搜索历史失败");
      }
    },

    openUrl(url) {
      window.open(url, "_blank");
    },

    // 处理树形分页页码变化
    handleTreeCurrentChange(page) {
      this.treeCurrentPage = page;
      this.clearTreeSelections();
      this.scrollTreeToTop(); // 左侧树翻页时滚动到顶部
      this.queryTreeData();
    },

    // 处理树形分页每页大小变化
    handleTreePageSizeChange(size) {
      this.treePageSize = size;
      this.treeCurrentPage = 1;
      this.clearTreeSelections();
      this.scrollTreeToTop(); // 左侧树页面大小变化时滚动到顶部
      this.queryTreeData();
    },

    // 添加缺失的openNewView方法
    openNewView(item) {
      window.open(
        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,
        "_blank"
      );
    },

    // 添加缺失的handleHistoryPagination方法
    handleHistoryPagination() {
      this.getArticleHistory1();
    },
  },
};
</script>

<style lang="scss" scoped>
.treeBox {
  width: 100%;
  height: calc(100vh - 180px);
  overflow-y: auto;
}

.tree-pagination {
  padding: 10px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  text-align: center;

  ::v-deep .el-pagination {
    .el-pagination__sizes {
      margin-top: -2px;
    }
  }
}

.treeMain {
  position: relative;
}

.treeQuery {
  ::v-deep .el-input--mini .el-input__inner {
    height: 24px;
    line-height: 24px;
    padding: 0 4px;
  }

  ::v-deep .el-input__suffix {
    // height: 20px;
    right: -2px;
    // top: 5px;
  }
}

.toolBox {
  min-height: 130px;
  height: auto;
  padding-bottom: 15px;
  background-color: rgb(255, 255, 255);
  // box-shadow: -1px 2px 15px #cecdcd;
  border-left: solid 1px rgb(221, 219, 219);

  .title {
    display: flex;
    justify-content: space-between;
    height: 70px;
    padding: 0 30px;
    font-size: 19px;
  }

  .mainTool {
    padding: 0 28px;
    font-size: 14px;
    color: rgb(58, 58, 58);
  }

  .mainToolOne {
    margin-top: 15px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    // align-items: center;
  }

  .mainToolTwo {
    display: flex;
    align-items: center;
    height: 40px;

    p {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .btn {
    margin: 15px 0 0 25px;
  }
}

.keyword {
  width: 100%;
  position: relative;

  .history {
    width: 430px;
    position: absolute;
    background: #fff;
    z-index: 9999;
    left: 65px;
    border: 1px solid rgb(221, 219, 219);

    .historyItem {
      padding-left: 20px;

      .historyText {
        width: 450px;
        height: 34px;
        line-height: 34px;
      }

      &:nth-last-of-type(1) {
        padding-left: 0;

        ::v-deep .el-button--text {
          padding: 10px 20px;
        }
      }
    }
  }
}

.history {
  width: 530px;

  .historyItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 0 10px;
    overflow: hidden;

    .historyText {
      width: 350px;
      height: 34px;
      line-height: 34px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
}
</style>
