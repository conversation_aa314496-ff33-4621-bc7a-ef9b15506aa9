<template>
  <div v-loading="globalLoading" element-loading-text="数据加载中">
    <splitpanes class="default-theme">
      <pane
        class="leftLink"
        ref="leftLink"
        min-size="20"
        max-size="50"
        size="26"
      >
        <TreeTable
          ref="treeTable"
          :data="treeDataTransfer"
          :total="treeTotal"
          :current-page="treeCurrentPage"
          :page-size="treePageSize"
          :loading="loading"
          :selected-sources="savedCheckboxData"
          row-key="id"
          @selection-change="handleSelectionChange"
          @reset="handleReset"
          @size-change="handleTreePageSizeChange"
          @current-change="handleTreeCurrentChange"
          @filter-search="handleFilterSearch"
          @classify-change="handleClassifyChange"
        />
      </pane>
      <pane min-size="50" max-size="80" size="74">
        <div
          class="rightMain"
          style="margin-left: 0; overflow-y: auto"
          ref="rightMain"
          v-loading="tableLoading"
          element-loading-text="数据加载中"
        >
          <el-form
            :model="queryParams"
            ref="Form"
            label-width="90px"
            @submit.native.prevent
          >
            <el-form-item label="发布日期:" prop="dateType">
              <el-radio-group v-model="queryParams.dateType" size="small">
                <el-radio-button :label="1">今天</el-radio-button>
                <el-radio-button :label="2">近2天</el-radio-button>
                <el-radio-button :label="4">近7天</el-radio-button>
                <el-radio-button :label="5">近30天</el-radio-button>
                <el-radio-button :label="10">全部</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <div style="display: flex">
              <el-form-item
                label="小信优选:"
                prop="isTechnology"
                style="margin-right: 20px"
              >
                <el-radio-group v-model="queryParams.isTechnology" size="small">
                  <el-radio-button
                    v-for="dict in dict.type.is_technology"
                    :label="dict.value"
                    :key="'is_technology' + dict.value"
                    >{{ dict.label }}</el-radio-button
                  >
                  <el-radio-button :label="null" :key="'is_technologyAll'"
                    >全部</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
              <el-form-item label="小信精选:" prop="emotion">
                <el-radio-group v-model="queryParams.emotion" size="small">
                  <el-radio-button :label="'1'" :key="'is_emotion1'"
                    >选中</el-radio-button
                  >
                  <el-radio-button :label="'0'" :key="'is_emotion0'"
                    >全部</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
            </div>
            <el-form-item label="检索词库:" prop="tags">
              <el-radio-group v-model="queryParams.tags" size="small">
                <el-radio :label="''">全部</el-radio>
                <el-radio
                  v-for="item in tagsList1"
                  :key="item.id"
                  :label="item.id"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              style="width: 100%; overflow: auto"
              label=""
              prop="tagsSubset"
              v-if="queryParams.tags != ''"
            >
              <el-checkbox
                style="float: left; margin-right: 30px"
                :indeterminate="isIndeterminate"
                v-model="checkAll"
                @change="handleCheckAllTagsSubset"
                >全选</el-checkbox
              >
              <el-checkbox-group v-model="queryParams.tagsSubset">
                <el-checkbox
                  v-for="item in tagsList"
                  :key="item.name"
                  :label="item.name"
                ></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item class="keyword" label="关键词:" prop="keywords">
              <el-input
                ref="keywordRef"
                placeholder="请输入关键词,使用逗号分割(英文)"
                style="width: 430px"
                v-model="queryParams.keywords"
                @focus="showHistoryList()"
                @blur="hideHistoryList()"
                @keyup.enter.native="handleSearch()"
              >
              </el-input>
              <div class="history" v-show="showHistory">
                <div
                  class="historyItem"
                  v-for="(history, index) in historyList"
                  :key="index"
                  v-loading="historyLoading"
                >
                  <div @click="keywordsChange(history)" class="historyText">
                    {{ history.keyword }}
                  </div>
                  <el-button
                    type="text"
                    @click="removeHistory(history, 1)"
                    style="color: #999; font-size: 12px"
                    >删除</el-button
                  >
                </div>
                <div class="historyItem">
                  <el-button type="text" @click="moreHistory()">更多</el-button>
                  <el-button
                    type="text"
                    @click="clearHistory()"
                    style="color: #999; font-size: 12px"
                    >清空</el-button
                  >
                </div>
              </div>
              <el-button
                type="primary"
                size="mini"
                style="margin-left: 10px; height: 36px"
                @click="handleSearch"
                >搜索</el-button
              >
            </el-form-item>
            <div class="keyword-tip">
              *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割
            </div>
          </el-form>
          <div class="TopBtnGroup">
            <div class="TopBtnGroup_left">
              <el-checkbox v-model="checked" @change="handleCheckAllChange"
                >全选</el-checkbox
              >
              <p class="toolTitle">
                <i
                  title="批量删除文章"
                  class="icon-shanchu"
                  @click="batchDelete"
                ></i>
              </p>
              <p class="toolTitle">
                <i
                  class="icon-shuaxin-copy"
                  title="刷新"
                  @click="handleSearch('refresh')"
                ></i>
              </p>
              <!-- <p class="toolTitle">
              <i class="icon--_tianjiadaoku" title="添加到报告" @click="openReport" v-hasPermi="['result:report:add']"></i>
            </p> -->
              <!-- <p class="toolTitle">
              <i title="批量生成快照" class="icon-pingmukuaizhao" style="color:green"
                v-hasPermi="['article:collection:snapshot']" @click="resultEvent()"></i>
            </p> -->
              <p class="toolTitle">
                <i
                  class="el-icon-document"
                  style="font-size: 24px"
                  title="添加到工作台账"
                  @click="openTaizhang"
                  v-hasPermi="['article:work:add']"
                ></i>
              </p>
              <p class="toolTitle">
                <i
                  class="el-icon-document-add"
                  style="font-size: 24px"
                  title="发布到每日最新热点"
                  @click="publishHot"
                ></i>
              </p>
              <p class="toolTitle">
                <i
                  class="el-icon-chat-dot-round"
                  style="font-size: 24px"
                  title="Deepseek深度解读"
                  @click="articleAiChat"
                ></i>
                <span class="deepseek-text" @click="articleAiChat"
                  >Deepseek深度解读</span
                >
              </p>
              <p class="toolTitle">
                <i
                  class="el-icon-pie-chart"
                  style="font-size: 24px"
                  title="生成Deepseek图表看板"
                  @click="chartAiChat"
                ></i>
                <span class="deepseek-text" @click="chartAiChat"
                  >生成Deepseek图表看板</span
                >
              </p>
            </div>
            <div>
              <el-checkbox
                v-model="showSummary"
                @change="(e) => (showSummary = e)"
                style="margin-right: 10px"
                >是否显示摘要</el-checkbox
              >
              <span style="font-size: 14px">排序方式:</span>&nbsp;
              <el-select v-model="queryParams.sortMode" size="mini">
                <el-option label="按发布时间倒序排序" :value="'0'"></el-option>
                <el-option label="按发布时间正序排序" :value="'1'"></el-option>
                <el-option label="按采集时间倒序排序" :value="'2'"></el-option>
                <el-option label="按采集时间正序排序" :value="'3'"></el-option>
                <el-option label="按系统推荐排序" :value="'4'"></el-option>
              </el-select>
            </div>
          </div>
          <!-- <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="multiple"
                @click="handleUpdate0"
                v-hasPermi="['wechat:user:remove']"
                >设置为科技无关</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="multiple"
                @click="handleUpdate1"
                v-hasPermi="['wechat:user:remove']"
                >设置为科技有关</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="multiple"
                @click="handleUpdate2"
                v-hasPermi="['wechat:user:remove']"
                >设置为其他</el-button
              >
            </el-col>
          </el-row> -->
          <el-table
            :data="ArticleList"
            style="width: 100%; user-select: text"
            :show-header="false"
            ref="table"
            :height="
              'calc(100vh - ' +
              (374 + (queryParams.tags != '' ? 51 : 0)) +
              'px)'
            "
            @selection-change="handleTableSelectionChange"
          >
            <el-table-column type="selection" width="35" align="center" />
            <el-table-column width="50" align="center">
              <template slot-scope="scope">
                <span style="color: #080808; font-size: 15px">
                  {{
                    (queryParams.pageNum - 1) * queryParams.pageSize +
                    scope.$index +
                    1
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              label="编号"
              align="center"
              key="id"
              prop="id"
              width="100"
              v-if="false"
            />
            <el-table-column prop="title" label="日期" min-width="180">
              <template slot-scope="scope">
                <span class="article_title" @click="openNewView(scope.row)">
                  <span
                    style="color: #080808"
                    v-html="scope.row.title || scope.row.cnTitle"
                  ></span>
                  <span style="color: #5589f5">
                    {{ "(" + scope.row.publishTime + ")" }}
                  </span>
                  <span>&nbsp;||&nbsp;</span>
                  <span style="color: #5589f5">
                    {{ scope.row.sourceName }}
                  </span>
                  <span>&nbsp;||&nbsp;</span>
                  <span style="color: #5589f5">
                    大模型筛选:{{ getTechnologyLabel(scope.row.isTechnology) }}
                  </span>
                </span>
                <div
                  class="ArticlMain"
                  style="
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    word-break: break-all;
                  "
                  v-if="
                    showSummary &&
                    hasActualContent(scope.row.cnSummary || scope.row.summary)
                  "
                >
                  <span style="color: #9b9b9b">摘要：</span>
                  <span
                    style="color: #4b4b4b"
                    v-html="
                      changeColor(
                        scope.row.cnSummary.replace(
                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,
                          'span'
                        ) ||
                          scope.row.summary.replace(
                            /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,
                            'span'
                          )
                      )
                    "
                    @click="openNewView(scope.row)"
                  ></span>
                </div>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="publishTime" label="发布时间" width="180">
            </el-table-column> -->
            <!-- <el-table-column label="操作" fixed="right">
              <template slot-scope="scope">
                <i class="icon--_tianjiadaoku" title="添加到报告" @click="separateAdd(item)"
                  v-hasPermi="['result:report:add']"></i>
              </template>
            </el-table-column> -->
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handlePagination"
            :autoScroll="true"
          />
        </div>
      </pane>
    </splitpanes>
    <el-dialog
      title="添加到报告"
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="closeReport"
      :close-on-click-modal="false"
    >
      <el-row style="line-height: 50px">
        <el-col :span="18">
          <el-select
            v-model="reportId"
            placeholder="请选择报告"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="(item, key) in reportOptions"
              :key="key"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeReport">取 消</el-button>
        <el-button type="primary" @click="reportSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="关键词历史"
      :visible.sync="dialogVisible1"
      width="570px"
      :close-on-click-modal="false"
    >
      <div class="history" v-loading="historyLoading">
        <div
          class="historyItem"
          v-for="(history, index) in historyList1"
          :key="index"
        >
          <div @click="keywordsChange(history)" class="historyText">
            {{ history.keyword }}
          </div>
          <el-button type="text" @click="removeHistory(history, 2)"
            >删除</el-button
          >
        </div>
      </div>
      <pagination
        v-show="total1 > 0"
        :total="total1"
        :page.sync="queryParams1.pageNum"
        :limit.sync="queryParams1.pageSize"
        :background="false"
        @pagination="handleHistoryPagination"
        :layout="'total, prev, pager, next'"
        :autoScroll="true"
      />
    </el-dialog>

    <el-dialog
      title="Deepseek深度解读"
      :visible.sync="aiDialogVisible"
      width="1000px"
      :before-close="closeAiDialog"
      custom-class="ai-dialog"
      :close-on-click-modal="false"
    >
      <div class="ai-chat-container">
        <div class="chat-messages" ref="chatMessages">
          <div
            class="message"
            v-for="(message, index) in chatMessages"
            :key="index"
            :class="[
              'message',
              message.role === 'user' ? 'user-message' : 'ai-message',
            ]"
          >
            <div class="avatar">
              <img
                style="width: 30px; height: 30px"
                v-if="message.role === 'user'"
                :src="userAvatar || require('@/assets/images/home/<USER>')"
                alt="用户头像"
              />
              <img v-else src="@/assets/images/logo2.png" alt="AI头像" />
            </div>
            <div class="message-wrapper">
              <div
                v-if="
                  message.role === 'assistant' && isThinking && !message.content
                "
                class="thinking-animation"
              >
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div
                v-else
                class="message-content"
                v-html="
                  message.role === 'assistant'
                    ? message.content
                    : message.content
                "
              ></div>
            </div>
          </div>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="closeAiDialog">取 消</el-button>
      </template>
    </el-dialog>

    <el-dialog
      title="Deepseek图表看板"
      :visible.sync="chartDialogVisible"
      width="1200px"
      :before-close="closeChartDialog"
      custom-class="chart-dialog"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div
        v-if="chartDialogVisible"
        class="chart-container"
        v-loading="chartLoading"
        element-loading-text="正在生成图表看板..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <div class="chart-content" ref="chartContent"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeChartDialog">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import api from "@/api/ScienceApi/index.js";
import {
  listWork,
  getWork,
  delWork,
  addWork,
  updateWork,
} from "@/api/article/work";
import { listKeywords } from "@/api/article/keywords";
import API from "@/api/ScienceApi/index.js";
import {
  listArticleHistory,
  delArticleHistory,
  addArticleHistory,
  cleanArticleHistory,
  getListByIds,
} from "@/api/article/articleHistory";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import { mapGetters } from "vuex";
import TreeTable from "@/components/TreeTable/index.vue";
import { deepseekAiQa, difyAiQa, ollamaAiQa } from "@/api/infoEscalation/ai";
import { marked } from "marked";
import { getConfigKey } from "@/api/system/config";

export default {
  components: { Splitpanes, Pane, TreeTable },
  dicts: ["is_technology"],
  data() {
    return {
      loading: false,
      tableLoading: false, // 表格loading状态
      queryParams: {
        id: 100,
        pageNum: 1,
        pageSize: 50,
        dateType: 4,
        tags: "",
        tagsSubset: [],
        keywords: "",
        isTechnology: "1",
        sortMode: "4",
        emotion: "0",
        hasCache: "0",
      },
      total: 0,
      treeDataTransfer: [], // 原始树形数据
      filterText: "", // 左侧树搜索栏
      checkList: [], // 左侧勾选数据
      ArticleList: [], // 列表数据
      checked: false, // 全选
      ids: [], // 选中的数据
      // 非多个禁用
      multiple: true,
      dialogVisible: false, // 添加到报告弹框
      reportOptions: [], // 报告列表
      reportId: "", // 已选择的报告
      tagsList: [], // 检索词库二级列表
      tagsList1: [], // 检索词库一级列表
      checkAll: false, // 检索词库全选
      isIndeterminate: true, // 检索词库选了值
      showHistory: false,
      historyList: [],
      historyTimeout: null,
      dialogVisible1: false,
      historyLoading: false,
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
      },
      total1: 0,
      historyList1: [],
      showSummary: true,
      /* 树形分页数据 */
      treeCurrentPage: 1,
      treePageSize: 100,
      treeTotal: 0,
      /* 初始化完成标记 */
      initializationCompleted: false,
      /* 搜索防抖 */
      searchDebounceTimer: null,
      /* 查询防抖 */
      queryDebounceTimer: null,
      /* 防止重复查询 */
      isQuerying: false,
      /* 标记右侧筛选条件是否发生变化 */
      isRightFilter: false,
      /* 标记左侧树是否重置 */
      isLeftReset: false,
      /* 选中的数据源分类 */
      selectedClassify: "5",
      /* 保存的勾选数据（永久保存，只有特定操作才更新） */
      savedCheckboxData: [],
      // ai相关
      aiDialogVisible: false,
      chatMessages: [],
      isThinking: false,
      userAvatar: "", // 用户头像
      streamingMessage: "", // 添加用于存储正在流式输出的消息
      markdownOptions: {
        gfm: true,
        breaks: true,
        headerIds: true,
        mangle: false,
        headerPrefix: "",
        pedantic: false,
        sanitize: false,
        smartLists: true,
        smartypants: true,
        xhtml: true,
      },
      isRequesting: false, // 标记是否正在请求中
      isAborted: false, // 标记是否已中断
      currentReader: null, // 当前的 reader
      aiPlatform: "",
      articleAiPrompt: "",
      nodeCheckList: [],
      chartDialogVisible: false,
      chartHtml: "",
      chartLoading: true,
      currentChartIframe: null, // 添加变量跟踪当前iframe
      difyApikey: {
        article: "",
        chart: "",
      },
      chartPrompt: "",
      globalLoading: false,
    };
  },
  watch: {
    // 监听筛选条件变化
    "queryParams.dateType": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleRightFilterChange();
      },
    },
    "queryParams.isTechnology": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleRightFilterChange();
      },
    },
    "queryParams.emotion": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleRightFilterChange();
      },
    },
    "queryParams.tags": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;

        // this.queryParams.tagsSubset = [];
        this.checkAll = true;
        this.isIndeterminate = false;

        if (newVal != "") {
          // 不在这里设置tableLoading，让后续的queryArticleList来处理
          listKeywords({ parentId: newVal, pageNum: 1, pageSize: 10 })
            .then((res) => {
              this.tagsList = res.data;
              this.handleCheckAllTagsSubset(true);
              // this.handleRightFilterChange();
            })
            .catch((error) => {
              console.error("获取检索词库失败:", error);
              this.$message.error("获取检索词库失败");
            });
        } else {
          this.handleRightFilterChange();
        }
      },
    },
    "queryParams.tagsSubset": {
      handler(newVal, oldVal) {
        if (
          !this.initializationCompleted ||
          JSON.stringify(newVal) === JSON.stringify(oldVal)
        )
          return;
        this.handleRightFilterChange();
      },
    },
    "queryParams.sortMode": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.scrollToTopImmediately();
        this.queryArticleList();
      },
    },
    dialogVisible(val) {
      if (val) {
        api.getNewBuilt({ sourceType: "1" }).then((data) => {
          if (data.code == 200) {
            this.reportOptions = data.data;
          } else {
            this.$message({ message: "报告列表获取失败了", type: "error" });
            this.closeReport();
          }
        });
      }
    },
  },
  computed: {
    ...mapGetters(["roles"]),
  },
  async created() {
    getConfigKey("sys.ai.platform").then((res) => {
      if (res.code == 200) {
        this.aiPlatform = res.msg;
      }
    });
    getConfigKey("wechat.ai.articlePrompt").then((res) => {
      if (res.code == 200) {
        this.articleAiPrompt = res.msg;
      }
    });
    getConfigKey("wechat.ai.chartPrompt").then((res) => {
      if (res.code == 200) {
        this.chartPrompt = res.msg;
      }
    });
    // 获取用户头像
    this.userAvatar = this.$store.getters.avatar;
    try {
      // 先加载基础数据
      Promise.all([
        this.getArticleHistory(),
        listKeywords({ parentId: 0, pageNum: 1, pageSize: 10 }).then((res) => {
          this.tagsList1 = res.data.filter((item) => item.parentId == 0);
        }),
      ]);

      // 加载树数据和内容数据
      await this.initializeData();

      if (this.roles.includes("information")) {
        this.showSummary = false;
      }

      // 标记初始化完成，这样watch监听器才会开始工作
      this.initializationCompleted = true;
    } catch (error) {
      console.error("组件初始化失败:", error);
      this.$message.error("初始化失败，请刷新页面重试");
    }
  },

  mounted() {},
  methods: {
    // 初始化数据
    async initializeData() {
      try {
        this.globalLoading = true;
        // 先加载树数据
        await this.queryTreeData();
        // 等待树组件完全渲染
        await this.$nextTick();

        // 默认全选第一页数据源
        if (this.treeDataTransfer && this.treeDataTransfer.length > 0) {
          // 全选第一页的所有数据源
          const firstPageData = [...this.treeDataTransfer];
          this.checkList = firstPageData;
          this.savedCheckboxData = firstPageData;

          // 通知 TreeTable 组件设置选中状态
          this.$nextTick(() => {
            if (this.$refs.treeTable) {
              this.$refs.treeTable.restoreSelectionSilently(firstPageData);
            }
          });

          // 延迟一下再查询文章列表，确保选中状态已设置
          setTimeout(() => {
            this.queryArticleList();
          }, 100);
        } else {
          // 如果没有数据源，直接查询文章列表
          this.queryArticleList();
        }
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.$message.error("初始化失败，请刷新页面重试");
        this.globalLoading = false;
      }
    },

    // 处理右侧筛选条件变化
    handleRightFilterChange() {
      this.isRightFilter = true; // 标记右侧筛选条件发生变化

      // 不再保存当前选中状态，使用永久保存的勾选数据
      // 永久保存的勾选数据会在查询后自动恢复

      // 重置分页到第一页
      this.queryParams.pageNum = 1;
      this.treeCurrentPage = 1;
      this.queryParams.hasCache = "0";

      // 滚动到顶部
      this.scrollToTopImmediately();

      // 同时查询树和列表
      this.queryTreeAndList();
    },

    // 同时查询树和列表
    async queryTreeAndList() {
      try {
        // 保存当前的永久勾选数据，避免在查询过程中丢失
        const savedData = [...this.savedCheckboxData];

        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数
        if (savedData && savedData.length > 0) {
          this.checkList = [...savedData];
        } else {
          // 如果没有永久保存的勾选数据，清空选中状态
          this.checkList = [];
        }

        // 同时查询树数据和右侧列表（保持性能优势）
        await Promise.all([
          this.queryTreeData(),
          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading
        ]);

        // 确保永久保存的勾选数据不会丢失
        this.savedCheckboxData = savedData;

        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态
        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
          this.restoreFromSavedCheckboxData();
        }

        // 查询完成后重置右侧筛选标记
        this.isRightFilter = false;
        setTimeout(() => {
          this.isLeftReset = false;
        }, 300);
      } catch (error) {
        console.error("同时查询树和列表失败:", error);
        this.$message.error("查询失败，请重试");
        // 即使出错也要重置标记
        this.isRightFilter = false;
        setTimeout(() => {
          this.isLeftReset = false;
        }, 300);
      }
    },

    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据

    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）
    restoreFromSavedCheckboxData() {
      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {
        return;
      }

      // 在当前树数据中查找匹配的项
      const matchedItems = [];
      this.savedCheckboxData.forEach((savedItem) => {
        const foundItem = this.treeDataTransfer.find(
          (treeItem) => treeItem.sourceSn === savedItem.sourceSn
        );
        if (foundItem) {
          matchedItems.push(foundItem);
        }
      });

      if (matchedItems.length > 0) {
        // 更新选中列表（此时 checkList 已经在查询前恢复过了）
        this.checkList = matchedItems;
        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）
        this.$nextTick(() => {
          if (this.$refs.treeTable) {
            this.$refs.treeTable.restoreSelectionSilently(matchedItems);
          }
        });
      } else {
        // 如果没有匹配项，清空选中状态
        this.checkList = [];
      }
    },

    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据

    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）
    async queryTreeDataWithRestoreFromSaved() {
      try {
        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数
        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
          this.checkList = [...this.savedCheckboxData];
        } else {
          this.checkList = [];
        }

        // 查询树数据
        await this.queryTreeData();

        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态
        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
          this.restoreFromSavedCheckboxData();
        }
      } catch (error) {
        console.error(
          "查询树数据并从永久保存的勾选数据恢复选中状态失败:",
          error
        );
      }
    },

    // 分页处理
    handlePagination() {
      this.scrollToTopImmediately();
      this.queryArticleList();
    },

    // 历史记录分页处理
    handleHistoryPagination() {
      this.getArticleHistory1();
      this.$nextTick(() => {
        const dialogContent = document.querySelector(".el-dialog__body");
        if (dialogContent) {
          dialogContent.scrollTop = 0;
        }
      });
    },

    // 查询树数据
    async queryTreeData() {
      this.loading = true;
      try {
        const params = {
          platformType: 0,
          id: this.queryParams.id,
          pageNum: this.treeCurrentPage,
          pageSize: this.treePageSize,
          m: 1,
          dateType:
            this.queryParams.dateType != 6 ? this.queryParams.dateType : "",
          tags: this.queryParams.tags,
          tagsSubset: this.queryParams.tagsSubset,
          keywords: this.queryParams.keywords,
          isTechnology: this.queryParams.isTechnology,
          emotion: this.queryParams.emotion,
          label: this.queryParams.tagsSubset.join(","),
          // 添加关键字过滤参数
          filterwords: this.filterText || "",
          // 添加数据源分类参数
          thinkTankClassification: this.selectedClassify,
          hasCache: this.queryParams.hasCache,
          // 小信精选附加参数
          isSummary: this.queryParams.emotion,
          isSwdt01: this.queryParams.emotion,
          isSwdt02: this.queryParams.emotion,
          isContentTranslated: this.queryParams.emotion,
          isTranslated: this.queryParams.emotion,
        };

        if (!this.queryParams.tags) {
          params.tagsSubset = [];
          params.label = "";
        }

        const res = await api.monitoringMedium(params);

        if (res.code === 200) {
          const dataList = res.rows || [];
          const total = res.total || 0;

          const mapData = (data) =>
            data.map((item, index) => ({
              id: `${
                item.sourceSn || "unknown"
              }_${index}_${Date.now()}_${Math.random()
                .toString(36)
                .substring(2, 11)}`, // 确保绝对唯一性
              label: item.cnName,
              count: item.articleCount || 0,
              orderNum: item.orderNum,
              country: item.countryOfOrigin || null,
              sourceSn: item.sourceSn,
              url: item.url || null,
            }));

          this.treeDataTransfer = mapData(dataList);
          this.treeTotal = total;
        }
      } catch (error) {
        console.error("查询树数据失败:", error);
        this.$message.error("获取数据源失败");
      } finally {
        this.loading = false;
      }
    },

    // 查询文章列表（带防抖）
    async queryArticleList(flag) {
      // 防止重复查询
      if (this.isQuerying) {
        return;
      }

      if (!flag) {
        this.tableLoading = true;
      }

      // 清除之前的防抖定时器
      if (this.queryDebounceTimer) {
        clearTimeout(this.queryDebounceTimer);
      }

      // 设置防抖，300ms后执行查询
      this.queryDebounceTimer = setTimeout(async () => {
        try {
          if (flag === "sourceItemChanged") {
            this.globalLoading = true;
          }

          this.isQuerying = true;

          const params = {
            m: 1,
            pageNum: this.queryParams.pageNum,
            pageSize: this.queryParams.pageSize,
            id: this.queryParams.id,
            isSort: this.queryParams.sortMode,
            dateType:
              this.queryParams.dateType != 6 ? this.queryParams.dateType : "",
            tags: this.queryParams.tags,
            tagsSubset: this.queryParams.tagsSubset,
            keywords: this.queryParams.keywords,
            isTechnology: this.queryParams.isTechnology,
            emotion: this.queryParams.emotion,
            label: this.queryParams.tagsSubset.join(","),
            platformType: 0,
            // 小信精选附加参数
            isSummary: this.queryParams.emotion,
            isSwdt01: this.queryParams.emotion,
            isSwdt02: this.queryParams.emotion,
            isContentTranslated: this.queryParams.emotion,
            isTranslated: this.queryParams.emotion,
          };

          if (!this.queryParams.tags) {
            params.tagsSubset = [];
            params.label = "";
          }

          // 使用永久保存的勾选数据构建查询参数
          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
            const data = this.savedCheckboxData.map((item) => item.label);
            const sourceSn = this.savedCheckboxData.map(
              (item) => item.sourceSn
            );

            params.weChatName = String(data);
            params.sourceSn = String(sourceSn);
          }

          // 记录关键词历史
          if (params.keywords) {
            addArticleHistory({ keyword: params.keywords, type: 1 }).then(
              () => {
                this.getArticleHistory();
              }
            );
          }

          const res = await api.esRetrieval(params);

          if (res.code == 200) {
            let articleList = res.data.list
              ? res.data.list.map((item) => {
                  item.cnTitle = item.cnTitle
                    ? this.changeColor(item.cnTitle)
                    : null;
                  item.title = this.changeColor(item.title);
                  return item;
                })
              : [];

            // 去重逻辑：只有在没有关键词搜索时才进行去重
            if (
              !this.queryParams.keywords ||
              this.queryParams.keywords.trim() === ""
            ) {
              articleList = this.deduplicateArticles(articleList);
            }

            this.ArticleList = articleList;
            this.total = res.data.total || 0;

            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）
            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
              this.restoreFromSavedCheckboxData();
            }

            // 处理分页为空的情况
            if (
              this.ArticleList.length == 0 &&
              this.queryParams.pageSize * (this.queryParams.pageNum - 1) >=
                this.total &&
              this.total != 0
            ) {
              this.queryParams.pageNum = Math.max(
                1,
                Math.ceil(this.total / this.queryParams.pageSize)
              );
              // 重新查询
              await this.queryArticleList();
              return; // 重新查询时不要关闭loading
            }
          } else {
            this.$message.error(res.msg || "获取数据失败");
          }
        } catch (error) {
          console.error("查询文章列表失败:", error);
          this.$message.error("查询失败，请重试");
        } finally {
          this.isQuerying = false;
          this.globalLoading = false;
          this.tableLoading = false; // 查询完成后关闭loading
        }
      }, 1000);
    },

    // TreeTable 组件事件处理方法

    // 处理选择变化
    handleSelectionChange(selectedData, operationType) {
      if (operationType === "row-click" || operationType === "clear-all") {
        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重
        this.checkList = [...selectedData];
        this.savedCheckboxData = [...selectedData];
      } else if (
        operationType === "checkbox-change" ||
        operationType === "select-all"
      ) {
        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中
        // 先从保存的数据中移除当前页面的所有数据
        const currentPageIds = this.treeDataTransfer.map(
          (item) => item.sourceSn
        );
        const filteredCheckList = this.checkList.filter(
          (item) => !currentPageIds.includes(item.sourceSn)
        );
        const filteredSavedData = this.savedCheckboxData.filter(
          (item) => !currentPageIds.includes(item.sourceSn)
        );

        // 然后添加当前页面新选中的数据
        const combinedCheckList = [...filteredCheckList, ...selectedData];
        const combinedSavedData = [...filteredSavedData, ...selectedData];

        // 对合并后的数据进行去重处理
        this.checkList = this.deduplicateBySourceSn(combinedCheckList);
        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);
      } else {
        // 默认情况：直接替换（兼容性处理）
        this.checkList = [...selectedData];
        this.savedCheckboxData = [...selectedData];
      }

      // 重置页码并查询内容
      this.queryParams.pageNum = 1;
      this.scrollToTopImmediately();
      if (!this.isRightFilter) {
        this.queryArticleList("sourceItemChanged");
      }
    },

    // 根据sourceSn去重的辅助方法
    deduplicateBySourceSn(dataArray) {
      const seen = new Set();
      return dataArray.filter((item) => {
        if (seen.has(item.sourceSn)) {
          return false;
        }
        seen.add(item.sourceSn);
        return true;
      });
    },

    // 处理重置
    handleReset() {
      // 先清空过滤文本，避免触发 handleFilterSearch
      this.filterText = "";
      this.selectedClassify = null;

      // 然后设置重置标记
      this.isLeftReset = true;

      // 清空选中状态
      this.checkList = [];

      // 清空保存的勾选数据（永久保存）
      this.savedCheckboxData = [];

      // 重置页码并查询列表数据
      this.queryParams.pageNum = 1;
      this.treeCurrentPage = 1;
      this.queryParams.hasCache = "1";
      this.scrollToTopImmediately();

      // 重新查询树和列表
      this.queryTreeAndList();
    },

    // 重置树选择（保留原方法名以兼容）
    treeClear() {
      this.handleReset();
    },

    // 处理树分页
    handleTreeCurrentChange(page) {
      this.treeCurrentPage = page;
      this.queryParams.hasCache = "1";
      this.queryTreeDataWithRestoreFromSaved();
    },

    handleTreePageSizeChange(size) {
      this.treePageSize = size;
      this.treeCurrentPage = 1;
      this.queryParams.hasCache = "1";
      this.queryTreeDataWithRestoreFromSaved();
    },

    // 检索词库全选处理
    handleCheckAllTagsSubset(val) {
      this.queryParams.tagsSubset = val
        ? this.tagsList.map((item) => item.name)
        : [];
      this.isIndeterminate = false;
      this.queryParams.pageNum = 1;

      if (!this.initializationCompleted) return;

      this.scrollToTopImmediately();
    },

    // 处理过滤搜索（来自 TreeTable 组件）
    handleFilterSearch(keyword) {
      if (this.isLeftReset) {
        return;
      }

      // 更新过滤文本
      this.filterText = keyword || "";

      // 重置到第一页
      this.treeCurrentPage = 1;
      this.queryParams.hasCache = "1";

      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）
      this.queryTreeDataWithRestoreFromSaved();
    },

    // 处理数据源分类变化（来自 TreeTable 组件）
    handleClassifyChange(classifyValue) {
      // 更新选中的分类
      this.selectedClassify = classifyValue;

      // 重置到第一页
      this.treeCurrentPage = 1;
      this.queryParams.hasCache = "1";

      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据
      this.queryTreeDataWithRestoreFromSaved();
    },

    // 检索词库多选处理
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.tagsList.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.tagsList.length;
      this.queryParams.pageNum = 1;

      if (!this.initializationCompleted) return;

      this.scrollToTopImmediately();
      this.handleRightFilterChange();
    },

    // 搜索处理
    handleSearch(flag) {
      this.scrollToTopImmediately();
      if (!flag) {
        this.queryParams.pageNum = 1;
      }
      this.queryArticleList();
    },

    // 关键词历史选择
    keywordsChange(item) {
      this.queryParams.keywords = item.keyword;
      this.dialogVisible1 = false;
      this.scrollToTopImmediately();
      this.queryParams.pageNum = 1;
      // this.queryTreeAndList();
      this.queryArticleList();
    },

    // 右侧表格多选框选中数据
    handleTableSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      if (selection.length == this.ArticleList.length) {
        this.checked = true;
      } else {
        this.checked = false;
      }
      this.multiple = !selection.length;
    },
    // 全选
    handleCheckAllChange(val) {
      if (val) {
        this.$refs["table"].toggleAllSelection();
      } else {
        this.$refs["table"].clearSelection();
      }
    },
    // 打开添加到报告
    openReport() {
      if (this.ids.length == 0) {
        return this.$message({
          message: "请勾选要添加的数据",
          type: "warning",
        });
      }
      this.dialogVisible = true;
    },
    // 确定添加到报告
    async reportSubmit() {
      if (!this.reportId)
        return this.$message({
          message: "请选择要添加到的报告",
          type: "warning",
        });
      let keyWordList = this.ids.map((item) => {
        return { reportId: this.reportId, listId: item };
      });
      let res = await api.AddReport(keyWordList);
      if (res.code == 200) {
        this.$message({ message: "已添加到报告", type: "success" });
        this.queryArticleList();
      } else {
        this.$message({
          message: "添加到报告失败,请联系管理员",
          type: "error",
        });
      }
      this.$refs["table"].clearSelection();
      this.checked = false;
      this.closeReport();
    },
    // 关闭添加到报告
    closeReport() {
      this.reportId = "";
      this.dialogVisible = false;
    },
    // 批量删除
    batchDelete() {
      if (this.ids.length == 0) {
        return this.$message({
          message: "请勾选要删除的数据",
          type: "warning",
        });
      }
      this.$confirm("是否确认删除已勾选的数据项?")
        .then(() => {
          API.batchRemove(this.ids.join(",")).then((response) => {
            this.$message({ message: "删除成功", type: "success" });
            this.queryArticleList();
          });
        })
        .catch(() => {});
    },
    // 添加到台账
    openTaizhang() {
      if (this.ids.length == 0) {
        return this.$message({
          message: "请勾选要添加的数据",
          type: "warning",
        });
      }
      this.$confirm("是否确认添加已勾选的数据项到台账统计?")
        .then(() => {
          addWork(this.ids).then(() => {
            this.$message({ type: "success", message: "添加成功!" });
            this.queryArticleList();
          });
        })
        .catch(() => {});
    },
    // 发布到每日最新热点
    publishHot() {
      if (this.ids.length == 0) {
        return this.$message({
          message: "请勾选要发布到每日最新热点的数据",
          type: "warning",
        });
      }
      this.$confirm("是否确认发布已勾选的数据项到每日最新热点?")
        .then(() => {
          API.publishEverydayHot(this.ids.join(",")).then(() => {
            this.$message({ type: "success", message: "发布成功!" });
            this.queryArticleList();
          });
        })
        .catch(() => {});
    },
    // 历史记录相关方法
    async removeHistory(item, type) {
      clearTimeout(this.historyTimeout);
      await delArticleHistory([item.id]);
      if (type == 1) {
        this.$refs["keywordRef"].focus();
        this.getArticleHistory();
      } else {
        this.getArticleHistory();
        this.getArticleHistory1();
      }
    },

    showHistoryList() {
      this.showHistory = true;
    },

    hideHistoryList() {
      this.historyTimeout = setTimeout(() => {
        this.showHistory = false;
      }, 500);
    },

    getArticleHistory() {
      this.historyLoading = true;
      listArticleHistory({ pageNum: 1, pageSize: 5, type: 1 }).then(
        (response) => {
          this.historyList = response.rows;
          this.historyLoading = false;
        }
      );
    },

    async clearHistory() {
      clearTimeout(this.historyTimeout);
      this.$refs["keywordRef"].focus();
      await cleanArticleHistory(1);
      this.getArticleHistory();
    },

    moreHistory() {
      clearTimeout(this.historyTimeout);
      this.showHistory = false;
      this.historyLoading = true;
      this.getArticleHistory1();
      this.dialogVisible1 = true;
    },

    getArticleHistory1() {
      this.historyLoading = true;
      listArticleHistory({ ...this.queryParams1, type: 1 }).then((response) => {
        this.historyList1 = response.rows;
        this.total1 = response.total;
        this.historyLoading = false;
      });
    },

    // 文章详情
    openNewView(item) {
      window.open(
        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,
        "_blank"
      );
    },

    // 处理科技相关字段的显示映射
    getTechnologyLabel(value) {
      const mapping = {
        0: "否",
        1: "是",
        2: "其他",
        3: "待定",
      };
      return mapping[value];
    },

    // 检查文本是否有实际内容
    hasActualContent(text) {
      if (!text) return false;
      const contentWithoutTags = text.replace(/<[^>]*>/g, "");
      return /[\u4e00-\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);
    },

    // 滚动到顶部
    scrollToTopImmediately() {
      if (this.$refs.rightMain) {
        this.$refs.rightMain.scrollTop = 0;
      }

      if (this.$refs.table) {
        const bodyWrapper = this.$refs.table.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (bodyWrapper) {
          bodyWrapper.scrollTop = 0;
        }
      }
    },

    // 关键字高亮
    changeColor(str) {
      const regex = /<img\b[^>]*>/gi;
      let Str = str && str.replace(regex, "");
      if (
        Str &&
        ((this.queryParams.tags &&
          this.queryParams.tagsSubset &&
          this.queryParams.tagsSubset.length) ||
          this.queryParams.keywords)
      ) {
        let keywords = [
          ...this.queryParams.tagsSubset,
          ...(this.queryParams.keywords
            ? this.queryParams.keywords.split(",")
            : []),
        ];
        keywords.forEach((keyitem) => {
          if (keyitem && keyitem.length > 0) {
            let replaceReg = new RegExp(keyitem, "g");
            let replaceString =
              '<span class="highlight" style="color: red;">' +
              keyitem +
              "</span>";
            Str = Str.replace(replaceReg, replaceString);
          }
        });
      }
      return Str;
    },

    // 快照生成
    resultEvent() {
      if (this.ids.length == 0) {
        return this.$message.warning("请先选择文章");
      }
      let ids = this.ids;
      let zhuangtai = "生成";
      let url = "";
      if (ids.length == 1) {
        let row = this.ArticleList.filter((item) => item.id == ids[0]);
        if (row && row.snapshotUrl) zhuangtai = "查看";
        url = row.snapshotUrl;
      }
      if (zhuangtai == "生成") {
        this.$msgbox({
          title: "提示",
          message: "快照正在生成中，请稍后查看",
          showCancelButton: false,
          confirmButtonText: "关闭",
          beforeClose: (_, __, done) => {
            done();
          },
        });
        API.downLoadExportKe(ids)
          .then((response) => {
            if (response.code != 200) {
              this.$message({
                message: "申请失败，请联系管理员，确认采集器是否正常",
                type: "error",
              });
            }
          })
          .catch(() => {});
      } else {
        url = url.replace(new RegExp("/home/<USER>/dpx/server-api/", "g"), "/");
        url = url.replace(new RegExp("/home/<USER>/dpx/", "g"), "/");
        window.open(window.location.origin + url, "_blank");
      }
    },

    openUrl(url) {
      window.open(url, "_blank");
    },

    // ai相关
    // dify
    async difyAiChat() {
      if (this.ids.length === 0) {
        return this.$message({
          message: "请先选择要解读的文章",
          type: "warning",
        });
      }

      // 如果有正在进行的请求，中断它
      if (this.isRequesting) {
        this.isAborted = true;
        if (this.currentReader) {
          try {
            await this.currentReader.cancel();
          } catch (e) {
            console.log("中断之前的请求失败", e);
          }
        }
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      this.isRequesting = true;
      this.isAborted = false;
      this.aiDialogVisible = true;
      this.chatMessages = [];
      this.isThinking = true;

      try {
        // 获取选中的文章
        const selectedArticles = this.ArticleList.filter((article) =>
          this.ids.includes(article.id)
        );
        const titles = selectedArticles
          .map((article) => `《${article.cnTitle || article.title}》`)
          .join("\n");

        // 获取文章内容
        const articlesResponse = await getListByIds(this.ids.join(","));
        if (!articlesResponse.data?.length) {
          throw new Error("获取文章内容失败");
        }

        // 格式化文章内容
        const articlesContent = articlesResponse.data
          .map((article, index) => {
            const title =
              selectedArticles[index]?.cnTitle ||
              selectedArticles[index]?.title ||
              "";
            const content = article.content || "";
            return `【第 ${index + 1} 篇文章】《${title}》\n\n${content}`;
          })
          .join("\n\n-------------------------------------------\n\n");

        // 添加用户消息
        this.chatMessages.push({
          role: "user",
          content: `帮我深度解读以下${this.ids.length}篇文章：\n${titles}`,
        });

        // 创建AI消息
        const aiMessage = {
          role: "assistant",
          content: "",
        };
        this.chatMessages.push(aiMessage);

        // 构建提示词
        const prompt =
          this.articleAiPrompt
            .replace("articleLength", this.ids.length)
            .replace(/\&gt;/g, ">") +
          `**以下是待处理的文章：**\n\n${articlesContent}`;

        // 调用AI接口
        const response = await difyAiQa(
          articlesContent,
          "streaming",
          "dify.article.apikey"
        );
        if (!response.ok) {
          throw new Error("AI接口调用失败");
        }

        // 处理流式响应
        const reader = response.body.getReader();
        this.currentReader = reader;
        const decoder = new TextDecoder();
        let buffer = "";
        let pendingBuffer = ""; // 用于存储待处理的不完整数据
        let isInThinkTag = false; // 新增：标记是否在think标签内

        // 将Unicode转义字符(\uXXXX)转换为实际字符
        const decodeUnicode = (str) => {
          return str.replace(/\\u[\dA-Fa-f]{4}/g, (match) => {
            return String.fromCharCode(parseInt(match.replace(/\\u/g, ""), 16));
          });
        };

        // 更新内容的函数
        const updateContent = (newContent) => {
          try {
            const renderedContent = marked(newContent, this.markdownOptions);
            aiMessage.content = renderedContent;

            // 确保消息容器滚动到底部
            this.$nextTick(() => {
              const chatMessages = this.$refs.chatMessages;
              if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
              }
            });
          } catch (error) {
            console.error("渲染内容时出错:", error);
          }
        };

        // 处理流式响应
        while (true) {
          // 检查是否已中断
          if (this.isAborted) {
            throw new Error("AbortError");
          }

          const { done, value } = await reader.read();

          if (done) {
            // 处理最后可能剩余的数据
            if (pendingBuffer) {
              try {
                const lastData = JSON.parse(pendingBuffer);
                if (lastData.answer) {
                  // 解码Unicode转义字符
                  const decodedAnswer = decodeUnicode(lastData.answer);
                  buffer += decodedAnswer;
                  updateContent(buffer);
                }
              } catch (e) {
                console.warn("处理最后的数据时出错:", e);
              }
            }
            break;
          }

          const chunk = decoder.decode(value);
          pendingBuffer += chunk;

          // 处理完整的数据行
          while (pendingBuffer.includes("\n")) {
            const newlineIndex = pendingBuffer.indexOf("\n");
            const line = pendingBuffer.slice(0, newlineIndex).trim();
            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);

            if (!line || line === "data:" || !line.startsWith("data:")) {
              continue;
            }

            try {
              const data = line.slice(5).trim();
              if (data === "[DONE]") {
                continue;
              }

              const jsonData = JSON.parse(data);
              if (!jsonData.answer) {
                continue;
              }

              // 跳过特殊字符
              if (jsonData.answer === "```" || jsonData.answer === "markdown") {
                continue;
              }

              // 解码Unicode转义字符
              let answer = decodeUnicode(jsonData.answer);

              // 检查是否包含<think>开始标签
              if (answer.includes("<think>")) {
                isInThinkTag = true;
                continue; // 跳过包含<think>的部分
              }

              // 检查是否包含</think>结束标签
              if (answer.includes("</think>")) {
                isInThinkTag = false;
                continue; // 跳过包含</think>的部分
              }

              // 只有不在think标签内的内容才会被添加到buffer中
              if (!isInThinkTag && answer) {
                buffer += answer;
                updateContent(buffer);
              }
            } catch (parseError) {
              console.warn("解析数据行时出错:", {
                line,
                error: parseError.message,
                pendingBuffer,
              });
              continue;
            }
          }
        }
      } catch (error) {
        console.error("AI解读出错:", error);
        this.$message.error(error.message || "AI解读失败，请稍后重试");
        if (this.chatMessages[1]) {
          this.chatMessages[1].content = "抱歉，服务器繁忙，请稍后再试";
        }
      } finally {
        this.currentReader = null;
        if (this.aiDialogVisible) {
          this.isThinking = false;
          this.isRequesting = false;
        }
      }
    },
    // Ollama
    async ollamaAiChat() {
      if (this.ids.length === 0) {
        return this.$message({
          message: "请先选择要解读的文章",
          type: "warning",
        });
      }

      // 如果有正在进行的请求，中断它
      if (this.isRequesting) {
        this.isAborted = true;
        if (this.currentReader) {
          try {
            await this.currentReader.cancel();
          } catch (e) {
            console.log("中断之前的请求失败", e);
          }
        }
        // 等待之前的请求状态清理完成
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      this.isRequesting = true;
      this.isAborted = false;
      this.aiDialogVisible = true;
      this.chatMessages = [];
      this.isThinking = true;

      try {
        // 获取选中的文章
        const selectedArticles = this.ArticleList.filter((article) =>
          this.ids.includes(article.id)
        );
        const titles = selectedArticles
          .map((article) => `《${article.cnTitle || article.title}》`)
          .join("\n");

        // 获取文章内容
        const articlesResponse = await getListByIds(this.ids.join(","));
        if (!articlesResponse.data?.length) {
          throw new Error("获取文章内容失败");
        }

        // 格式化文章内容
        const articlesContent = articlesResponse.data
          .map((article, index) => {
            const title =
              selectedArticles[index]?.cnTitle ||
              selectedArticles[index]?.title ||
              "";
            const content = article.content || "";
            return `【第 ${index + 1} 篇文章】《${title}》\n\n${content}`;
          })
          .join("\n\n-------------------------------------------\n\n");

        // 添加用户消息
        this.chatMessages.push({
          role: "user",
          content: `帮我深度解读以下${this.ids.length}篇文章：\n${titles}`,
        });

        // 创建AI消息
        const aiMessage = {
          role: "assistant",
          content: "",
        };
        this.chatMessages.push(aiMessage);

        // 构建提示词
        const prompt =
          this.articleAiPrompt
            .replace("articleLength", this.ids.length)
            .replace(/\&gt;/g, ">") +
          `**以下是待处理的文章：**\n\n${articlesContent}`;

        // 调用AI接口
        const response = await ollamaAiQa(prompt, true);
        if (!response.ok) {
          throw new Error("AI接口调用失败");
        }

        // 处理流式响应
        const reader = response.body.getReader();
        this.currentReader = reader; // 保存当前的 reader
        const decoder = new TextDecoder();
        let buffer = "";
        let lastUpdateTime = Date.now();
        let isThinkContent = false;
        let tempBuffer = "";

        // 更新内容的函数
        const updateContent = (newContent) => {
          const currentTime = Date.now();
          // 控制更新频率，避免过于频繁的DOM更新
          if (currentTime - lastUpdateTime >= 50) {
            aiMessage.content = newContent;
            lastUpdateTime = currentTime;
            // 确保消息容器滚动到底部
            this.$nextTick(() => {
              const chatMessages = this.$refs.chatMessages;
              if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
              }
            });
          }
        };

        // 处理流式响应
        const processStream = async () => {
          try {
            while (true) {
              // 检查是否已中断
              if (this.isAborted) {
                throw new Error("AbortError");
              }

              const { done, value } = await reader.read();
              if (done) {
                if (buffer.length > 0) {
                  updateContent(buffer);
                }
                break;
              }

              const chunk = decoder.decode(value);
              const lines = chunk.split("\n").filter((line) => line.trim());

              for (const line of lines) {
                try {
                  const jsonData = JSON.parse(line);
                  if (!jsonData.response) continue;

                  const response = jsonData.response;

                  // 跳过特殊字符
                  if (response === "```" || response === "markdown") {
                    continue;
                  }

                  tempBuffer += response;

                  // 检查是否包含完整的think标签对
                  while (true) {
                    const thinkStartIndex = tempBuffer.indexOf("<think>");
                    const thinkEndIndex = tempBuffer.indexOf("</think>");

                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {
                      // 没有think标签，直接添加到buffer
                      if (!isThinkContent) {
                        buffer += tempBuffer;
                        // 使用marked渲染markdown内容
                        updateContent(marked(buffer, this.markdownOptions));
                      }
                      tempBuffer = "";
                      break;
                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {
                      // 只有开始标签，等待结束标签
                      isThinkContent = true;
                      if (thinkStartIndex > 0) {
                        buffer += tempBuffer.substring(0, thinkStartIndex);
                        // 使用marked渲染markdown内容
                        updateContent(marked(buffer, this.markdownOptions));
                      }
                      tempBuffer = tempBuffer.substring(thinkStartIndex);
                      break;
                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {
                      // 只有结束标签，移除之前的内容
                      isThinkContent = false;
                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);
                      continue;
                    } else {
                      // 有完整的think标签对
                      if (thinkStartIndex > 0) {
                        buffer += tempBuffer.substring(0, thinkStartIndex);
                        // 使用marked渲染markdown内容
                        updateContent(marked(buffer, this.markdownOptions));
                      }
                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);
                      isThinkContent = false;
                      continue;
                    }
                  }
                } catch (parseError) {
                  console.warn("无效的JSON行，已跳过", {
                    line,
                    error: parseError.message,
                  });
                }
              }
            }
          } catch (streamError) {
            if (streamError.message === "AbortError") {
              throw new Error("AbortError");
            }
            console.error("处理流式响应时出错:", streamError);
            throw streamError;
          }
        };

        await processStream();
      } catch (error) {
        // 判断是否是中断导致的错误
        if (error.message === "AbortError") {
          console.log("请求已被中断");
          return;
        }
        console.error("AI解读出错:", error);
        this.$message.error(error.message || "AI解读失败，请稍后重试");
        if (this.chatMessages[1]) {
          this.chatMessages[1].content = "抱歉，服务器繁忙，请稍后再试";
        }
      } finally {
        this.currentReader = null; // 清理当前的 reader
        // 只有在没有被中断的情况下才重置状态
        if (this.aiDialogVisible) {
          this.isThinking = false;
          this.isRequesting = false;
        }
      }
    },
    // deepseek
    async deepseekAiChat() {
      if (this.ids.length === 0) {
        return this.$message({
          message: "请先选择要解读的文章",
          type: "warning",
        });
      }

      // 如果有正在进行的请求，中断它
      if (this.isRequesting) {
        this.isAborted = true;
        if (this.currentReader) {
          try {
            await this.currentReader.cancel();
          } catch (e) {
            console.log("中断之前的请求失败", e);
          }
        }
        // 等待之前的请求状态清理完成
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      this.isRequesting = true;
      this.isAborted = false;
      this.aiDialogVisible = true;
      this.chatMessages = [];
      this.isThinking = true;

      const selectedArticles = this.ArticleList.filter((article) =>
        this.ids.includes(article.id)
      );
      const titles = selectedArticles
        .map((article) => `《${article.cnTitle || article.title}》`)
        .join("\n");

      try {
        const articlesResponse = await getListByIds(this.ids.join(","));
        if (!articlesResponse.data || !articlesResponse.data.length) {
          throw new Error("Failed to get article contents");
        }

        const articlesContent = articlesResponse.data
          .map((article, index) => {
            const title =
              selectedArticles[index]?.cnTitle ||
              selectedArticles[index]?.title ||
              "";
            const content = article.content || "";
            return `【第 ${index + 1} 篇文章】《${title}》\n\n${content}`;
          })
          .join("\n\n-------------------------------------------\n\n");

        // 添加用户消息
        this.chatMessages.push({
          role: "user",
          content: `帮我深度解读以下${this.ids.length}篇文章：\n${titles}`,
        });

        // 创建AI消息并添加到对话中
        const aiMessage = {
          role: "assistant",
          content: "",
        };
        this.chatMessages.push(aiMessage);
        this.isThinking = true;

        const prompt =
          this.articleAiPrompt
            .replace("articleLength", this.ids.length)
            .replace(/\&gt;/g, ">") +
          `\n\n**以下是待处理的文章：**\n\n${articlesContent}`;

        const response = await deepseekAiQa(prompt, true);

        if (response.ok) {
          const reader = response.body.getReader();
          this.currentReader = reader; // 保存当前的 reader
          const decoder = new TextDecoder();
          let buffer = "";
          let lastUpdateTime = Date.now();

          const updateContent = (newContent) => {
            const currentTime = Date.now();
            if (currentTime - lastUpdateTime >= 50) {
              aiMessage.content = newContent;
              lastUpdateTime = currentTime;
              this.$nextTick(() => {
                const chatMessages = this.$refs.chatMessages;
                if (chatMessages) {
                  chatMessages.scrollTop = chatMessages.scrollHeight;
                }
              });
            }
          };

          while (true) {
            // 检查是否已中断
            if (this.isAborted) {
              throw new Error("AbortError");
            }

            const { done, value } = await reader.read();
            if (done) {
              if (buffer.length > 0) {
                updateContent(buffer);
              }
              break;
            }

            const chunk = decoder.decode(value);
            try {
              const lines = chunk.split("\n");

              for (const line of lines) {
                if (!line.trim() || !line.startsWith("data: ")) continue;

                const data = line.slice(5);
                if (data === "[DONE]") break;

                try {
                  const jsonData = JSON.parse(data);
                  if (jsonData.choices?.[0]?.delta?.content) {
                    let content = jsonData.choices[0].delta.content;

                    // 跳过特殊字符
                    if (content === "```" || content === "markdown") {
                      continue;
                    }

                    buffer += content;
                    updateContent(buffer);
                  }
                } catch (parseError) {
                  console.error("Error parsing JSON:", parseError);
                }
              }
            } catch (e) {
              console.error("Error processing chunk:", e);
            }
          }
        } else {
          throw new Error("Request failed");
        }
      } catch (error) {
        // 判断是否是中断导致的错误
        if (error.message === "AbortError") {
          console.log("请求已被中断");
          return;
        }
        console.error("AI Chat Error:", error);
        this.$message.error("AI解读失败，请稍后重试");
        if (this.chatMessages[1]) {
          this.chatMessages[1].content = "抱歉，服务器繁忙，请稍后再试";
        }
      } finally {
        this.currentReader = null; // 清理当前的 reader
        // 只有在没有被中断的情况下才重置状态
        if (this.aiDialogVisible) {
          this.isThinking = false;
          this.isRequesting = false;
        }
      }
    },
    // 关闭AI对话
    closeAiDialog() {
      this.isAborted = true; // 设置中断标志
      if (this.currentReader) {
        this.currentReader.cancel(); // 中断当前的读取
      }
      this.aiDialogVisible = false;
      this.chatMessages = [];
      this.isThinking = false;
      this.isRequesting = false;
      this.currentReader = null;
    },
    articleAiChat() {
      if (this.aiPlatform === "dify") {
        this.difyAiChat();
      } else if (this.aiPlatform === "ollama") {
        this.ollamaAiChat();
      } else if (this.aiPlatform === "deepseek") {
        this.deepseekAiChat();
      }
    },
    chartAiChat() {
      if (this.aiPlatform === "dify") {
        this.difyChartAiChat();
      } else if (this.aiPlatform === "deepseek") {
        this.deepseekChartAiChat();
      }
    },
    // dify图表看板
    async difyChartAiChat() {
      // 参数检查
      if (this.ids.length === 0) {
        this.$message.warning("请先选择要解读的文章");
        return;
      }

      if (this.ids.length > 1) {
        this.$message.warning("生成Deepseek图表看板只能选择一篇内容");
        return;
      }

      // 显示对话框与加载状态
      this.chartDialogVisible = true;
      this.chartLoading = true;

      // 确保清空上次的内容
      if (this.$refs.chartContent) {
        this.$refs.chartContent.innerHTML = "";
      }

      try {
        // 1. 获取文章内容
        const articleResult = await API.AreaInfo(this.ids[0]);
        if (!articleResult.data || !articleResult.data.content) {
          throw new Error("获取文章内容失败");
        }

        // 2. 调用AI接口
        const aiResult = await difyAiQa(
          articleResult.data.content,
          "blocking",
          "dify.chart.apikey"
        );

        if (!aiResult.ok) {
          throw new Error("AI接口调用失败");
        }

        const aiData = await aiResult.json();
        if (!aiData || !aiData.answer) {
          throw new Error("AI返回数据格式错误");
        }

        // 3. 处理HTML内容
        let content2 = "";

        try {
          // 尝试解析JSON格式（有些返回可能是JSON字符串）
          const parsedData = JSON.parse(aiData.answer);
          content2 =
            parsedData.answer ||
            parsedData.html ||
            parsedData.content ||
            aiData.answer;
        } catch (e) {
          // 如果不是JSON格式，直接使用原始内容
          content2 = aiData.answer;
        }

        // 处理思考标记
        const thinkStartIndex = content2.indexOf("<think>");
        const thinkEndIndex = content2.indexOf("</think>");

        // 提取有效内容
        if (thinkStartIndex !== -1 && thinkEndIndex !== -1) {
          // 如果存在思考标记，只取</think>后面的内容
          content2 = content2.substring(thinkEndIndex + 8).trim();
        }

        // 清理html标记和其他特殊字符
        content2 = content2
          // 移除html代码块标记
          .replace(/```html\s*|```\s*/g, "")
          // 移除可能存在的其他html语言标记，如```json等
          .replace(/```[a-zA-Z]*\s*/g, "")
          // 移除多余的空行
          .replace(/\n\s*\n\s*\n/g, "\n\n")
          // 移除行首行尾空白字符
          .trim();

        // 确保内容非空
        if (!content2 || content2.length < 10) {
          throw new Error("返回的图表内容无效");
        }

        // 检查HTML结构的完整性，如果不完整则添加必要的标签
        // 这是为了兼容可能不返回完整HTML的情况
        let finalHtml = content2;

        // 将各种形式的外部CDN引用替换为本地文件
        // 替换双引号版本
        finalHtml = finalHtml.replace(
          /https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js/g,
          "/chart.js"
        );
        // 替换单引号版本
        finalHtml = finalHtml.replace(
          /\'https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js\'/g,
          "'/chart.js'"
        );
        finalHtml = finalHtml.replace(
          /\"https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js\"/g,
          '"/chart.js"'
        );
        // 替换可能带有版本号的引用
        finalHtml = finalHtml.replace(
          /https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js@\d+\.\d+\.\d+/g,
          "/chart.js"
        );
        // 替换其他可能的CDN
        finalHtml = finalHtml.replace(
          /https:\/\/cdnjs\.cloudflare\.com\/ajax\/libs\/Chart\.js\/\d+\.\d+\.\d+\/chart(\.min)?\.js/g,
          "/chart.js"
        );

        if (!finalHtml.includes("<!DOCTYPE") && !finalHtml.includes("<html")) {
          // 内容只是HTML片段，需要添加完整结构
          finalHtml =
            "<!DOCTYPE html>" +
            "<html>" +
            "<head>" +
            '  <meta charset="UTF-8">' +
            '  <meta name="viewport" content="width=device-width, initial-scale=1.0">' +
            '  <script src="/chart.js"><\/script>' +
            "</head>" +
            "<body>" +
            "  " +
            content2 +
            "</body>" +
            "</html>";
        } else if (
          !finalHtml.includes("<script") &&
          finalHtml.includes("<canvas")
        ) {
          // 有canvas但没有script标签，可能缺少Chart.js引用
          finalHtml = finalHtml.replace(
            "<head>",
            "<head>" + '  <script src="/chart.js"><\/script>'
          );
        }

        // 4. 创建iframe并渲染
        this.$nextTick(() => {
          if (this.$refs.chartContent) {
            // 清理之前的iframe
            if (this.currentChartIframe) {
              try {
                this.currentChartIframe.onload = null;
                this.currentChartIframe.onerror = null;
              } catch (e) {
                console.error("清理之前的iframe失败:", e);
              }
            }

            // 创建iframe
            const iframe = document.createElement("iframe");
            iframe.style.width = "100%";
            iframe.style.height = "600px";
            iframe.style.border = "none";
            iframe.style.display = "block";
            iframe.style.overflow = "auto";

            // 保存iframe引用
            this.currentChartIframe = iframe;

            // 清空容器并添加iframe
            this.$refs.chartContent.innerHTML = "";
            this.$refs.chartContent.appendChild(iframe);

            // 在iframe加载完成后重新执行脚本并隐藏加载状态
            iframe.onload = () => {
              try {
                // 检查是否需要加载本地Chart.js
                if (
                  !iframe.contentWindow.Chart &&
                  !iframe.contentWindow.document.querySelector(
                    'script[src*="chart.js" i]'
                  )
                ) {
                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js
                  const chartScript =
                    iframe.contentWindow.document.createElement("script");
                  chartScript.src = "/chart.js";
                  iframe.contentWindow.document.head.appendChild(chartScript);

                  // 等待Chart.js加载完成后再执行后续脚本
                  chartScript.onload = () => {
                    this.executeIframeScripts(iframe);
                  };

                  // 如果脚本加载失败，也需要隐藏加载动画
                  chartScript.onerror = () => {
                    console.error("加载本地Chart.js失败");
                    this.chartLoading = false;
                  };
                } else {
                  // 直接执行内联脚本
                  this.executeIframeScripts(iframe);
                }
              } catch (e) {
                console.error("脚本执行出错:", e);
                this.chartLoading = false;
              }
            };

            // 添加错误处理
            iframe.onerror = () => {
              console.error("iframe加载失败");
              this.chartLoading = false;
            };

            // 写入内容
            const doc = iframe.contentWindow.document;
            doc.open();
            doc.write(finalHtml);
            doc.close();
          }
        });
      } catch (error) {
        console.error("生成图表失败:", error);
        this.$message.error(error.message || "生成图表失败，请稍后重试");
        this.closeChartDialog();
      }
    },
    // deepseek图表看板
    async deepseekChartAiChat() {
      // 参数检查
      if (this.ids.length === 0) {
        this.$message.warning("请先选择要解读的文章");
        return;
      }

      if (this.ids.length > 1) {
        this.$message.warning("生成Deepseek图表看板只能选择一篇内容");
        return;
      }

      // 显示对话框与加载状态
      this.chartDialogVisible = true;
      this.chartLoading = true;

      // 确保清空上次的内容
      if (this.$refs.chartContent) {
        this.$refs.chartContent.innerHTML = "";
      }

      try {
        // 1. 获取文章内容
        const articleResult = await API.AreaInfo(this.ids[0]);
        if (!articleResult.data || !articleResult.data.content) {
          throw new Error("获取文章内容失败");
        }

        const prompt = this.chartPrompt + `\n\n${articleResult.data.content}`;

        // 2. 调用AI接口
        const aiResult = await deepseekAiQa(prompt, false);

        if (!aiResult.ok) {
          throw new Error("AI接口调用失败");
        }

        const aiData = await aiResult.json();
        if (!aiData || !aiData.choices) {
          throw new Error("AI返回数据格式错误");
        }

        // 3. 处理HTML内容
        let content2 = aiData.choices[0].message.content;

        // 清理html标记和其他特殊字符
        content2 = content2
          // 移除html代码块标记
          .replace(/```html\s*|```\s*/g, "")
          // 移除可能存在的其他html语言标记，如```json等
          .replace(/```[a-zA-Z]*\s*/g, "")
          // 移除多余的空行
          .replace(/\n\s*\n\s*\n/g, "\n\n")
          // 移除行首行尾空白字符
          .trim();

        // 确保内容非空
        if (!content2 || content2.length < 10) {
          throw new Error("返回的图表内容无效");
        }

        // 检查HTML结构的完整性，如果不完整则添加必要的标签
        // 这是为了兼容可能不返回完整HTML的情况
        let finalHtml = content2;

        // 将各种形式的外部CDN引用替换为本地文件
        // 替换双引号版本
        finalHtml = finalHtml.replace(
          /https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js/g,
          "/chart.js"
        );
        // 替换单引号版本
        finalHtml = finalHtml.replace(
          /\'https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js\'/g,
          "'/chart.js'"
        );
        finalHtml = finalHtml.replace(
          /\"https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js\"/g,
          '"/chart.js"'
        );
        // 替换可能带有版本号的引用
        finalHtml = finalHtml.replace(
          /https:\/\/cdn\.jsdelivr\.net\/npm\/chart\.js@\d+\.\d+\.\d+/g,
          "/chart.js"
        );
        // 替换其他可能的CDN
        finalHtml = finalHtml.replace(
          /https:\/\/cdnjs\.cloudflare\.com\/ajax\/libs\/Chart\.js\/\d+\.\d+\.\d+\/chart(\.min)?\.js/g,
          "/chart.js"
        );

        if (!finalHtml.includes("<!DOCTYPE") && !finalHtml.includes("<html")) {
          // 内容只是HTML片段，需要添加完整结构
          finalHtml =
            "<!DOCTYPE html>" +
            "<html>" +
            "<head>" +
            '  <meta charset="UTF-8">' +
            '  <meta name="viewport" content="width=device-width, initial-scale=1.0">' +
            '  <script src="/chart.js"><\/script>' +
            "</head>" +
            "<body>" +
            "  " +
            content2 +
            "</body>" +
            "</html>";
        } else if (
          !finalHtml.includes("<script") &&
          finalHtml.includes("<canvas")
        ) {
          // 有canvas但没有script标签，可能缺少Chart.js引用
          finalHtml = finalHtml.replace(
            "<head>",
            "<head>" + '  <script src="/chart.js"><\/script>'
          );
        }

        // 4. 创建iframe并渲染
        this.$nextTick(() => {
          if (this.$refs.chartContent) {
            // 清理之前的iframe
            if (this.currentChartIframe) {
              try {
                this.currentChartIframe.onload = null;
                this.currentChartIframe.onerror = null;
              } catch (e) {
                console.error("清理之前的iframe失败:", e);
              }
            }

            // 创建iframe
            const iframe = document.createElement("iframe");
            iframe.style.width = "100%";
            iframe.style.height = "600px";
            iframe.style.border = "none";
            iframe.style.display = "block";
            iframe.style.overflow = "auto";

            // 保存iframe引用
            this.currentChartIframe = iframe;

            // 清空容器并添加iframe
            this.$refs.chartContent.innerHTML = "";
            this.$refs.chartContent.appendChild(iframe);

            // 在iframe加载完成后重新执行脚本并隐藏加载状态
            iframe.onload = () => {
              try {
                // 检查是否需要加载本地Chart.js
                if (
                  !iframe.contentWindow.Chart &&
                  !iframe.contentWindow.document.querySelector(
                    'script[src*="chart.js" i]'
                  )
                ) {
                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js
                  const chartScript =
                    iframe.contentWindow.document.createElement("script");
                  chartScript.src = "/chart.js";
                  iframe.contentWindow.document.head.appendChild(chartScript);

                  // 等待Chart.js加载完成后再执行后续脚本
                  chartScript.onload = () => {
                    this.executeIframeScripts(iframe);
                  };

                  // 如果脚本加载失败，也需要隐藏加载动画
                  chartScript.onerror = () => {
                    console.error("加载本地Chart.js失败");
                    this.chartLoading = false;
                  };
                } else {
                  // 直接执行内联脚本
                  this.executeIframeScripts(iframe);
                }
              } catch (e) {
                console.error("脚本执行出错:", e);
                this.chartLoading = false;
              }
            };

            // 添加错误处理
            iframe.onerror = () => {
              console.error("iframe加载失败");
              this.chartLoading = false;
            };

            // 写入内容
            const doc = iframe.contentWindow.document;
            doc.open();
            doc.write(finalHtml);
            doc.close();
          }
        });
      } catch (error) {
        console.error("生成图表失败:", error);
        this.$message.error(error.message || "生成图表失败，请稍后重试");
        this.closeChartDialog();
      }
    },
    // 关闭图表对话框
    closeChartDialog() {
      this.isAborted = true;
      this.chartDialogVisible = false;
      this.chartHtml = "";
      this.chartLoading = false;
      this.isRequesting = false;

      // 清理Chart实例
      if (this.currentChartIframe && this.currentChartIframe.contentWindow) {
        try {
          // 尝试销毁所有Chart实例
          if (this.currentChartIframe.contentWindow.Chart) {
            const instances =
              this.currentChartIframe.contentWindow.Chart.instances;
            if (instances) {
              Object.values(instances).forEach((instance) => {
                if (instance && typeof instance.destroy === "function") {
                  instance.destroy();
                }
              });
            }
          }
        } catch (e) {
          console.error("清理Chart实例失败:", e);
        }
      }

      // 清空图表容器内容
      if (this.$refs.chartContent) {
        this.$refs.chartContent.innerHTML = "";
      }

      // 清理iframe引用
      if (this.currentChartIframe) {
        try {
          this.currentChartIframe.onload = null;
          this.currentChartIframe.onerror = null;
          this.currentChartIframe = null;
        } catch (e) {
          console.error("清理iframe失败:", e);
        }
      }
    },
    // 执行iframe内的所有内联脚本
    executeIframeScripts(iframe) {
      // 简化后的方法，不再尝试手动执行脚本
      console.log("图表iframe已加载，等待自然渲染...");

      // 确保所有图表都有机会渲染后再隐藏loading
      setTimeout(() => {
        this.chartLoading = false;
      }, 800);
    },

    // 文章去重方法
    deduplicateArticles(articles) {
      if (!articles || articles.length === 0) {
        return articles;
      }

      const titleMap = new Map();
      const result = [];

      // 统计相同标题的文章数量
      articles.forEach((article) => {
        // 去除HTML标签和所有空格来比较标题
        const cleanTitle = article.title
          ? article.title.replace(/<[^>]*>/g, "").replace(/\s+/g, "")
          : "";

        if (titleMap.has(cleanTitle)) {
          titleMap.get(cleanTitle).count++;
        } else {
          titleMap.set(cleanTitle, {
            article: { ...article },
            count: 1,
            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）
          });
        }
      });

      // 生成去重后的文章列表
      titleMap.forEach(({ article, count, originalTitle }) => {
        if (count > 1) {
          // 如果有重复，在标题后面加上数量标记
          // 使用原始标题（保持HTML格式）
          article.title = `${originalTitle || ""}（${count}）`;
        }
        result.push(article);
      });

      return result;
    },
  },
};
</script>

<style lang="scss" scoped>
.input_Fixed {
  width: 100%;
}

.treeBox {
  // margin-top:70px;
  width: 100%;
  height: calc(100vh - 178px);
  overflow-y: auto;
}

.tree-pagination {
  padding: 10px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  text-align: center;

  ::v-deep .el-pagination {
    .el-pagination__sizes {
      margin-top: -2px;
    }
  }
}

.rightMain {
  height: calc(100vh - 60px);
  overflow: hidden;

  .TopBtnGroup {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    // background: #dbdbd8;
    // margin-bottom: 20px;
    height: 60px;
    box-shadow: 0 0px 10px 0px #cecdcd;
    border-bottom: solid 1px #e2e2e2;

    .TopBtnGroup_left {
      display: flex;
      align-items: center;
    }

    .toolTitle {
      margin: 0 10px;
      display: flex;
      align-items: center;
      cursor: pointer;

      .deepseek-text {
        color: #5589f5; // 使用与图标相同的颜色
        margin-left: 4px;
        font-size: 14px;
        line-height: 24px;
      }

      &:nth-of-type(1) {
        margin-left: 30px;
      }
    }
  }

  .ArticlMain {
    padding: 0 0 0 30px;
    color: #3f3f3f;
    font-size: 14px;
    line-height: 24px;
  }

  .ArticlMain > span:hover {
    color: #1889f3;
    border-bottom: solid 1px #0798f8;
    cursor: pointer;
  }
}

::v-deep .drawer_Title {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

::v-deep .drawer_Style {
  z-index: 2;
  margin: 0 15px 0 15px;
  width: 661px;
  height: 80vh;

  .title {
    font-size: 16px;
    font-weight: 500px;
    text-align: center;
  }

  .source {
    color: #0798f8;
    text-align: center;
    font-size: 14px;
  }

  .time {
    font-size: 14px;
    text-align: center;
    margin-left: 10px;
    color: #9b9b9b;
  }
}

::v-deep .el-icon-document:before {
  color: #5589f5;
}

::v-deep .el-icon-document-add:before {
  color: #5589f5;
}

::v-deep .el-icon-chat-dot-round:before {
  color: #5589f5;
}

::v-deep .el-icon-pie-chart:before {
  color: #5589f5;
}

::v-deep .el-table td.el-table__cell div {
  padding-left: 10px;
}

::v-deep .el-table-column--selection .cell {
  padding-right: 0px;
  padding-left: 14px;
  margin-left: 5px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

.treeMain {
  position: relative;
}

.treeQuery {
  ::v-deep .el-input--mini .el-input__inner {
    height: 24px;
    line-height: 24px;
    padding: 0 4px;
  }

  ::v-deep .el-input__suffix {
    // height: 20px;
    right: -2px;
    // top: 5px;
  }
}

.keyword {
  width: 100%;
  position: relative;
  margin-bottom: 10px;

  .history {
    width: 430px;
    position: absolute;
    background: #fff;
    z-index: 9999;
    left: 0;
    border: 1px solid rgb(221, 219, 219);

    .historyItem {
      padding-left: 20px;

      .historyText {
        width: 450px;
        height: 34px;
        line-height: 34px;
      }

      &:nth-last-of-type(1) {
        padding-left: 0;

        ::v-deep .el-button--text {
          padding: 10px 20px;
        }
      }
    }
  }
}

.keyword-tip {
  font-size: 14px;
  color: #999;
  margin-left: 90px;
  line-height: 1;
  margin-bottom: 10px;
}

.history {
  width: 530px;

  .historyItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 0 10px;
    overflow: hidden;

    .historyText {
      width: 350px;
      height: 34px;
      line-height: 34px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
}

::v-deep .el-table--medium .el-table__cell {
  padding: 10px 0;
}

.article_title {
  margin-left: 10px;
  font-size: 15px;
}

.article_title:hover {
  color: #1889f3;
  border-bottom: solid 1px #0798f8;
  cursor: pointer;
}

// ai相关
.ai-chat-container {
  height: 550px;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;

    .message {
      margin-bottom: 28px;
      display: flex;
      align-items: flex-start;

      .avatar {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          background-color: #fff;
        }
      }

      .message-wrapper {
        margin: 0 16px;
        max-width: calc(100% - 100px);
      }

      .message-content {
        padding: 12px 16px;
        border-radius: 12px;
        font-size: 16px;
        line-height: 1;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 14px;
          width: 0;
          height: 0;
          border: 6px solid transparent;
        }
      }
    }

    .user-message {
      flex-direction: row-reverse;

      .message-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }

      .message-content {
        background-color: #e6f3ff;
        color: #2d2d2d;
        line-height: 1.8em;
        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,
          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,
          Arial, sans-serif;

        &::before {
          right: -12px;
          border-left-color: #e6f3ff;
        }
      }
    }

    .ai-message {
      .message-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .message-content {
        background-color: #fff;
        color: #2d2d2d;

        &::before {
          left: -12px;
          border-right-color: #fff;
        }
      }
    }
  }

  .thinking-animation {
    display: inline-flex;
    align-items: center;
    padding: 12px 16px;
    min-height: 45px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 14px;
      left: -12px;
      width: 0;
      height: 0;
      border: 6px solid transparent;
      border-right-color: #fff;
    }

    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin: 0 3px;
      background-color: #409eff;
      border-radius: 50%;
      opacity: 0.7;
      animation: thinking 1.4s infinite ease-in-out both;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }

      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }

  .message-content {
    min-height: 45px;
    white-space: pre-wrap;
    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,
      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,
      Arial, sans-serif;

    ::v-deep {
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 0.05em 0 0.02em 0;
        font-weight: 600;
        line-height: 1.8em;
        color: #2d3748;
      }

      h1 {
        font-size: 1.6em;
        margin-top: 0;
        padding-bottom: 0.05em;
        margin-bottom: 0.02em;
      }

      h2 {
        font-size: 1.4em;
        padding-bottom: 0.05em;
        margin-bottom: 0.02em;
      }

      h3 {
        font-size: 1.2em;
      }

      p {
        margin: 0;
        line-height: 1.8em;
        color: #2d3748;
      }

      strong {
        font-weight: 600;
        color: #1a1a1a;
      }

      em {
        font-style: italic;
        color: #2c5282;
      }

      ul,
      ol {
        margin: 0;
        padding-left: 1em;
        display: flex !important;
        flex-direction: column !important;
        // row-gap: 20px !important;

        li {
          margin: 0;
          line-height: 1.8em;
          color: #2d3748;

          // 如果li中包含p标签，则设置行高为1
          &:has(p) {
            line-height: 1;
          }

          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）

          p {
            margin: 0;
            line-height: 1.8em;
          }
        }
      }

      blockquote {
        margin: 0.05em 0;
        padding: 0.05em 0.4em;
        color: #2c5282;
        background: #ebf8ff;
        border-left: 4px solid #4299e1;

        p {
          margin: 0.02em 0;
          line-height: 1.8em;
        }

        > :first-child {
          margin-top: 0;
        }

        > :last-child {
          margin-bottom: 0;
        }
      }

      code {
        padding: 0.05em 0.1em;
        margin: 0;
        font-size: 0.9em;
        background: #edf2f7;
        border-radius: 3px;
        color: #2d3748;
      }

      hr {
        height: 1px;
        margin: 0.1em 0;
        border: none;
        background-color: #e2e8f0;
      }
    }
  }
}

.chat-messages {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(192, 196, 204, 0.5);
    border-radius: 3px;

    &:hover {
      background-color: rgba(192, 196, 204, 0.8);
    }
  }
}

@keyframes thinking {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

// 修改弹窗样式
::v-deep .ai-dialog {
  .el-dialog__body {
    padding: 0;
    background-color: #f5f7fa;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    background: #fff;
    border-top: 1px solid #e4e7ed;

    .el-button {
      padding: 9px 20px;
      font-size: 14px;
    }
  }
}

.chart-container {
  min-height: 600px;
  width: 100%;
  overflow: auto;
  padding: 0;
  position: relative;

  .chart-content {
    width: 100%;
    height: 600px;
    overflow: auto;
    display: block;
  }
}

::v-deep .chart-dialog {
  .el-dialog__body {
    padding: 0;
    overflow: hidden;
  }

  .el-dialog__header {
    // padding: 15px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__footer {
    padding: 10px 15px;
    border-top: 1px solid #e4e7ed;
  }
}

.pagination-container {
  padding: 0 !important;
}
</style>
