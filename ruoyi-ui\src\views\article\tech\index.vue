<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
      class="queryForm"
    >
      <el-row type="flex" justify="space-between">
        <el-col :span="6">
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="queryParams.title"
              placeholder="请输入标题"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="网址" prop="originalUrl">
            <el-input
              v-model="queryParams.originalUrl"
              placeholder="请输入网址"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="主键" prop="id">
            <el-input
              v-model="queryParams.id"
              placeholder="请输入主键"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属领域" prop="sourceArea">
            <el-select
              v-model="queryParams.sourceArea"
              placeholder="请选择所属领域"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.source_area"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="space-between">
        <el-col :span="6">
          <el-form-item label="是否与科技有关" prop="isTechnology">
            <el-select
              v-model="queryParams.isTechnology"
              placeholder="请选择是否与科技有关"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.is_technology"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="平台类型" prop="sourceType">
            <el-select
              v-model="queryParams.sourceType"
              placeholder="请选择平台类型"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.source_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否审核" prop="isReviewed">
            <el-select
              v-model="queryParams.isReviewed"
              placeholder="审核状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.is_reviewed"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否翻译" prop="isTranslated">
            <el-select
              v-model="queryParams.isTranslated"
              placeholder="正文翻译状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.is_translated"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否推送" prop="isPulled">
            <el-select
              v-model="queryParams.isPulled"
              placeholder="推送状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.is_pulled"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item style="width: 240px">
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          plain-->
      <!--          icon="el-icon-edit"-->
      <!--          size="mini"-->
      <!--          :disabled="single"-->
      <!--          @click="handleUpdate"-->
      <!--          v-hasPermi="['system:list:edit']"-->
      <!--        >修改</el-button>-->
      <!--      </el-col&ndash;&gt;-->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-if="false"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleUpdate0"
          >设置为小信优选未选中</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleUpdate1"
          >设置为小信优选选中</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleUpdate2"
          >设置为小信优选待定</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handlePass"
          >审核通过</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleCancelPass"
          >撤回审核通过</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleNoPass"
          >审核不通过</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!--    <el-table v-loading="loading" :data="listList" @selection-change="handleSelectionChange" @cell-click="openArticle">-->
    <el-table
      v-loading="loading"
      :data="listList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 330px)"
      ref="tableRef"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="isReviewed" label="审核" align="center" width="90">
        <template slot-scope="scope">
          <el-radio-group
            v-model="scope.row.isReviewed"
            class="radio-group"
            @input="(e) => handleRadioChange(e, scope.row.id)"
          >
            <el-radio :label="'1'" style="color: #67c23a">通过</el-radio>
            <el-radio :label="'2'" style="color: #f56c6c">不通过</el-radio>
            <el-radio :label="'4'" style="color: #e6a23c">待撤回</el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column label="主键" align="center" prop="id" width="70" />
      <!--      <el-table-column label="文章唯一标识" align="center" prop="sn" />-->
      <!--      <el-table-column label="标题" align="center" width="150" prop="title" />-->
      <el-table-column prop="title" label="标题" align="center">
        <template slot-scope="scope">
          <el-link
            :href="`/expressDetails?id=${scope.row.id}&docId=${scope.row.docId}`"
            :underline="false"
            target="_blank"
          >
            <span
              class="el-icon-document"
              style="word-break: normal; word-wrap: break-word"
            >
              {{ scope.row.title }}
            </span>
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="中文标题" align="center" prop="cnTitle">
        <template slot-scope="scope">
          <span
            class="el-icon-document"
            style="word-break: normal; word-wrap: break-word"
          >
            {{ scope.row.cnTitle }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sourceType"
        label="平台类型"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          <!-- <span>{{ processSourceType(scope.row.sourceType) }}</span> -->
          <dict-tag
            v-if="scope.row.sourceType"
            :options="dict.type.source_type"
            :value="scope.row.sourceType"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="sourceArea"
        label="所属领域"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          <!-- <span>{{ processSourceArea(scope.row.sourceArea) }}</span> -->
          <dict-tag
            v-if="scope.row.sourceArea"
            :options="dict.type.source_area"
            :value="scope.row.sourceArea"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column>
      <el-table-column
        label="来源名称"
        align="center"
        prop="sourceName"
        width="120"
      />
      <!--      <el-table-column label="是否科技" align="center" prop="isTechnology" />-->
      <el-table-column
        prop="isTechnology"
        label="科技相关"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          <!-- <span>{{ processTechnologyState(scope.row.isTechnology) }}</span> -->
          <dict-tag
            v-if="scope.row.isTechnology"
            :options="dict.type.is_technology"
            :value="scope.row.isTechnology"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="isTranslated"
        label="翻译"
        align="center"
        width="65"
      >
        <template slot-scope="scope">
          <!-- <span>{{ processTranslateState(scope.row.isTranslated) }}</span> -->
          <dict-tag
            v-if="scope.row.isTranslated"
            :options="dict.type.is_translated"
            :value="scope.row.isTranslated"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="contentLength"
        label="长度"
        align="center"
        width="60"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.contentLength }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isPulled" label="推送" width="65" align="center">
        <template slot-scope="scope">
          <!-- <span>{{ processPulledState(scope.row.isPulled) }}</span> -->
          <dict-tag
            v-if="scope.row.isPulled"
            :options="dict.type.is_pulled"
            :value="scope.row.isPulled"
          />
          <span v-else>未定义</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="publishTime"
        width="95"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="95"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="60"
      >
        <template slot-scope="scope">
          <div
            style="
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            "
          >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              style="margin-left: 0"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改文章列对话框 -->
    <!--    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>-->
    <!--      <el-form ref="form" :model="form" :rules="rules" label-width="120px">-->
    <!--        <el-form-item label="文章唯一标识" prop="sn">-->
    <!--          <el-input v-model="form.sn" placeholder="请输入文章唯一标识" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="标题" prop="title">-->
    <!--          <el-input v-model="form.title" type="textarea" placeholder="请输入内容" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="中文标题" prop="cnTitle">-->
    <!--          <el-input v-model="form.cnTitle" type="textarea" placeholder="请输入内容" />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="发布时间" prop="publishTime">-->
    <!--          <el-date-picker clearable-->
    <!--            v-model="form.publishTime"-->
    <!--            type="date"-->
    <!--            value-format="yyyy-MM-dd"-->
    <!--            placeholder="请选择发布时间">-->
    <!--          </el-date-picker>-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="是否与科技有关" prop="isTechnology">-->
    <!--          <el-input v-model="form.isTechnology" placeholder="是否与科技有关(0.无；1有；2其他)" />-->
    <!--        </el-form-item>-->
    <!--     </el-form>-->
    <!--      <div slot="footer" class="dialog-footer">-->
    <!--        <el-button type="primary" @click="submitForm">确 定</el-button>-->
    <!--        <el-button @click="cancel">取 消</el-button>-->
    <!--      </div>-->
    <!--    </el-dialog>-->

    <el-dialog
      :title="'修改文章'"
      :visible.sync="open"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="dialog_Box">
        <el-form
          :model="form"
          class="form_Style"
          label-position="top"
          ref="form"
          :rules="rules"
          size="mini"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="文章标题" prop="title">
                <el-input v-model="form.title"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="中文标题" prop="cnTitle">
                <el-input v-model="form.cnTitle"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="平台类型" prop="sourceType">
                <el-select
                  v-model="form.sourceType"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.source_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="媒体来源" prop="sourceName">
                <!-- <el-select
                  v-model="form.sourceName"
                  style="width: 100%"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="(item, index) in sourceTypeLists"
                    :key="index"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select> -->
                <el-input
                  v-model="form.sourceName"
                  style="width: 100%"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="发布时间" prop="publishTime">
                <el-date-picker
                  v-model="form.publishTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  style="width: 100%"
                  clearable
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文章作者" prop="author">
                <el-input
                  v-model="form.author"
                  style="width: 100%"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="原文链接" prop="originalUrl">
            <el-input v-model="form.originalUrl"></el-input>
          </el-form-item>
          <el-form-item label="摘要" prop="summary">
            <el-input
              v-model="form.summary"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="中文摘要" prop="cnSummary">
            <el-input
              v-model="form.cnSummary"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
            ></el-input>
          </el-form-item>
          <el-form-item label="中文内容" prop="cnContent">
            <editor v-model="form.cnContent" :minHeight="150"></editor>
          </el-form-item>
          <el-form-item label="文章内容" prop="content">
            <editor v-model="form.content" :minHeight="150"></editor>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from "@/api/ScienceApi/index.js";
import {
  listList,
  getList,
  delList,
  addList,
  updateList,
} from "@/api/article/articleList";
import {
  updateArticleTech0,
  updateArticleTech1,
  updateArticleTech2,
  articlePass,
  articleNoPass,
  cancelArticlePass,
  getListByIds,
  deleteByIds,
} from "@/api/article/articleHistory";
import { articleListEdit, uploadCover } from "@/api/articleCrawler/list";

export default {
  name: "tech",
  dicts: [
    "is_technology",
    "is_reviewed",
    "is_pulled",
    "source_type",
    "source_area",
    "is_translated",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 文章列表格数据
      listList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        sn: null,
        title: null,
        cnTitle: null,
        sourceType: null,
        sourceArea: null,
        sourceName: null,
        sourceSn: null,
        originalUrl: null,
        shortUrl: null,
        author: null,
        keywords: null,
        description: null,
        summary: null,
        cnSummary: null,
        cover: null,
        publishType: null,
        publishCode: null,
        publishArea: null,
        publishTime: null,
        numberLikes: null,
        numberReads: null,
        numberCollects: null,
        numberShares: null,
        numberComments: null,
        emotion: null,
        status: null,
        userId: null,
        deptId: null,
        fileUrl: null,
        firstPublishTime: null,
        firstCreateTime: null,
        firstWebstePublishTime: null,
        webstePublishTime: null,
        isChanged: null,
        isTranslated: null,
        isTechnology: "1",
        isReviewed: "0",
        isPulled: "0",
        isFile: null,
        deleteBy: null,
        deleteTime: null,
        id: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // 表单校验
        title: [{ required: true, message: "文章标题为必填项" }],
        content: [{ required: true, message: "文章详情为必填项" }],
        publishTime: [{ required: true, message: "发布时间为必填项" }],
        cnTitle: [{ required: true, message: "中文名称为必填项" }],
        sourceType: [{ required: true, message: "平台类型为必填项" }],
        originalUrl: [{ required: true, message: "原文网址为必填项" }],
        summary: [{ required: true, message: "请填写摘要" }],
        // cnSummary: [{ required: true, message: '请填写中文摘要' }],
        sn: [{ required: true, message: "请填写文章地址唯一识别号" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询文章列列表 */
    getList() {
      this.loading = true;
      listList(this.queryParams).then((response) => {
        this.listList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.$nextTick(() => {
          this.scrollToTop();
        });
      });
    },
    // 表格滚动到顶部
    scrollToTop() {
      if (this.$refs.tableRef) {
        const tableEl = this.$refs.tableRef.$el.querySelector(
          ".el-table__body-wrapper"
        );
        if (tableEl) {
          tableEl.scrollTop = 0;
        }
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sn: null,
        title: null,
        cnTitle: null,
        sourceType: null,
        sourceName: null,
        sourceArea: null,
        sourceSn: null,
        originalUrl: null,
        shortUrl: null,
        author: null,
        keywords: null,
        description: null,
        summary: null,
        cnSummary: null,
        cover: null,
        publishType: null,
        publishCode: null,
        publishArea: null,
        publishTime: null,
        numberLikes: null,
        numberReads: null,
        numberCollects: null,
        numberShares: null,
        numberComments: null,
        emotion: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        fileUrl: null,
        firstPublishTime: null,
        firstCreateTime: null,
        firstWebstePublishTime: null,
        webstePublishTime: null,
        isChanged: null,
        isTranslated: null,
        isTechnology: 1,
        isFile: null,
        delFlag: null,
        deleteBy: null,
        deleteTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加文章列";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      // const id = row.id || this.ids
      // getList(id).then(response => {
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "修改文章列";
      // });
      API.AreaInfo(row.id).then((response) => {
        this.form = response.data;
        // this.form.sourceType = Number(this.form.sourceType)
        this.form.docId = row.id.toString();
        this.open = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            // updateList(this.form).then(response => {
            //   this.$modal.msgSuccess("修改成功");
            //   this.open = false;
            //   this.getList();
            // });
            let queryForm = JSON.parse(JSON.stringify(this.form));
            articleListEdit(queryForm).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addList(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除文章列编号为"' + ids + '"的数据项？')
        .then(function () {
          // return delList(ids);
          return deleteByIds(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
      // this.$modal.confirm('是否确认删除该条文章？"').then(() => {
      //   return API.monitoringEsRemove({ id: row.id, docId: row.id.toString() });
      // }).then(() => {
      //   this.Refresh();
      //   this.$modal.msgSuccess("删除成功");
      // }).catch(() => { });
    },
    // 文章详情
    openArticle(item, row) {
      window.open(
        `/expressDetails?id=${item.id}&docId=${item.docId}`,
        "_blank"
      );
    },
    handleUpdate0(row) {
      const articleIds = row.id || this.ids;
      var str = articleIds.join(",");
      this.$modal
        .confirm('是否确认修改编号为"' + str + '"的数据为科技无关项？')
        .then(function () {
          return updateArticleTech0(str);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("修改成功");
        })
        .catch(() => {});
    },
    handleUpdate1(row) {
      const articleIds = row.id || this.ids;
      this.$modal
        .confirm('是否确认修改编号为"' + articleIds + '"的数据为科技有关项？')
        .then(function () {
          return updateArticleTech1(articleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("修改成功");
        })
        .catch(() => {});
    },
    handleUpdate2(row) {
      const articleIds = row.id || this.ids;
      this.$modal
        .confirm('是否确认修改编号为"' + articleIds + '"的数据为其他项？')
        .then(function () {
          return updateArticleTech2(articleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("修改成功");
        })
        .catch(() => {});
    },
    processTechnologyState(state) {
      if (state == 0) return "未选中";
      if (state == 1) return "选中";
      if (state == 2) return "待定";
      if (state == 3) return "未检测";
      return "未定义";
    },
    processTranslateState(state) {
      if (state == 0) return "否";
      if (state == 1) return "是";
      if (state == 2) return "其他";
      return "未定义";
    },
    processSourceType(state) {
      if (state == 1) return "国内";
      if (state == 2) return "国外";
      if (state == 3) return "境内科技";
      if (state == 4) return "境外科技";
      return "未定义";
    },
    processSourceArea(state) {
      if (state == 1) return "安全";
      if (state == 2) return "数字化转型";
      if (state == 3) return "软件";
      if (state == 4) return "产业链";
      if (state == 5) return "集成电路";
      if (state == 6) return "人工智能";
      return "未定义";
    },
    processReviewedState(state) {
      if (state == 0) return "未审核";
      if (state == 1) return "通过";
      if (state == 2) return "不通过";
      if (state == 3) return "待修改";
      if (state == 4) return "待撤回";
      return "未定义";
    },
    processPulledState(state) {
      if (state == 0) return "否";
      if (state == 1) return "是";
      return "未定义";
    },
    handlePass(row) {
      const articleIds = row.id || this.ids;
      this.$modal
        .confirm("是否确认审核通过编号为" + articleIds + "的数据？")
        .then(function () {
          return articlePass(articleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("审核通过");
        })
        .catch(() => {});
    },
    handleNoPass(row) {
      const articleIds = row.id || this.ids;
      this.$modal
        .confirm("是否确认审核不通过编号为" + articleIds + "的数据？")
        .then(function () {
          return articleNoPass(articleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("审核不通过");
        })
        .catch(() => {});
    },
    handleCancelPass(row) {
      const articleIds = row.id || this.ids;
      this.$modal
        .confirm("是否确认撤回审核通过编号为" + articleIds + "的数据？")
        .then(function () {
          // getListByIds(articleIds);
          return cancelArticlePass(articleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("撤回审核通过");
        })
        .catch(() => {});
    },
    handleRadioChange(e, id) {
      if (e == "1") {
        console.log("通过");
        return articlePass(id);
      } else if (e == "2") {
        console.log("不通过");
        return articleNoPass(id);
      } else if (e == "4") {
        console.log("待撤回");
        return cancelArticlePass(id);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .queryForm {
  .el-form-item {
    width: 100%;
    margin-right: 0;
  }
  .el-form-item__content {
    width: calc(100% - 80px);
  }
}

::v-deep .radio-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .el-radio {
    margin-right: 0;
    margin-bottom: 2px;
  }
}
</style>
