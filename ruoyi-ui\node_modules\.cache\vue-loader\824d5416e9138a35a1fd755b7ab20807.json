{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue?vue&type=template&id=794a9327&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue", "mtime": 1754027680200}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}