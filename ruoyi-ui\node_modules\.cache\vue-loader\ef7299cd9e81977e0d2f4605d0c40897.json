{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue", "mtime": 1754010042354}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQVBJIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHJlcXVlc3QgZnJvbSAiQC91dGlscy9yZXF1ZXN0IjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCmltcG9ydCB7IE1lc3NhZ2VCb3ggfSBmcm9tICJlbGVtZW50LXVpIjsNCmltcG9ydCBheGlvcyBmcm9tICJheGlvcyI7DQppbXBvcnQgeyBnZXRMaXN0Q2xhc3NpZnkgfSBmcm9tICJAL2FwaS9hcnRpY2xlL2NsYXNzaWZ5IjsNCmltcG9ydCB7IHNhdmVBcyB9IGZyb20gImZpbGUtc2F2ZXIiOw0KaW1wb3J0IHsgYmxvYlZhbGlkYXRlLCB0YW5zUGFyYW1zIH0gZnJvbSAiQC91dGlscy9ydW95aSI7DQppbXBvcnQgeyBhcnRpY2xlTGlzdEVkaXQsIHVwbG9hZENvdmVyIH0gZnJvbSAiQC9hcGkvYXJ0aWNsZUNyYXdsZXIvbGlzdCI7DQppbXBvcnQgRGVlcHNlZWtSZXBvcnREaWFsb2cgZnJvbSAiLi9EZWVwc2Vla1JlcG9ydERpYWxvZy52dWUiOw0KaW1wb3J0IHsgZGVlcHNlZWtBaVFhLCBkaWZ5QWlRYSwgb2xsYW1hQWlRYSB9IGZyb20gIkAvYXBpL2luZm9Fc2NhbGF0aW9uL2FpIjsNCmltcG9ydCB7IG1hcmtlZCB9IGZyb20gIm1hcmtlZCI7DQppbXBvcnQgeyBnZXRDb25maWdLZXkgfSBmcm9tICJAL2FwaS9zeXN0ZW0vY29uZmlnIjsNCmltcG9ydCB7IGdldExpc3RCeUlkcyB9IGZyb20gIkAvYXBpL2FydGljbGUvYXJ0aWNsZUhpc3RvcnkiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgZG93bkxvYWRTaG93OiB7DQogICAgICAvKiDkuIvovb3mjInpkq4gKi8gcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUsDQogICAgfSwNCiAgICBlZGl0U2hvdzogew0KICAgICAgLyog57yW6L6R5oyJ6ZKuICovIHJlcXVpcmVkOiBmYWxzZSwNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlLA0KICAgIH0sDQogICAgY29weVNob3c6IHsNCiAgICAgIC8qIOWkjeWItuaMiemSriAqLyByZXVxaXJlZDogZmFsc2UsDQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZSwNCiAgICB9LA0KICAgIGhlaWdodDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogNjU1LA0KICAgIH0sDQogICAgY3VycmVudFBhZ2U6IHsNCiAgICAgIHJldXFpcmVkOiB0cnVlLA0KICAgICAgZGVmYXVsdDogMSwNCiAgICB9LA0KICAgIHBhZ2VTaXplOiB7DQogICAgICByZXVxaXJlZDogdHJ1ZSwNCiAgICAgIGRlZmF1bHQ6IDUwLA0KICAgIH0sDQogICAgdG90YWw6IHsNCiAgICAgIHJldXFpcmVkOiB0cnVlLA0KICAgICAgZGVmYXVsdDogMCwNCiAgICB9LA0KICAgIEFydGljbGVMaXN0OiB7DQogICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgIGRlZmF1bHQ6IFtdLA0KICAgIH0sDQogICAgZmxhZzogew0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgfSwNCiAgICBTZWFjaERhdGE6IHsNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgIH0sDQogICAga2V5d29yZHM6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICIiLA0KICAgIH0sDQogICAgLy8g5oql5ZGK57G75Z6L5a2X5q61DQogICAgc291cmNlVHlwZTogew0KICAgICAgZGVmYXVsdDogIiIsDQogICAgfSwNCiAgfSwNCiAgY29tcG9uZW50czogew0KICAgIERlZXBzZWVrUmVwb3J0RGlhbG9nLA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHJlZ0V4cEltZzogL15cbiQvLA0KICAgICAgcmVwb3J0SWQ6ICIiLA0KICAgICAgcmVwb3J0T3B0aW9uczogW10sDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNoZWNrZWRDaXRpZXM6IFtdIC8qIOWkmumAiSAqLywNCiAgICAgIGNoZWNrZWQ6IGZhbHNlIC8qIOWFqOmAiSAqLywNCiAgICAgIGh0bWw6ICIiLA0KICAgICAgdGV4dDogIiIsDQogICAgICB0aGF0OiB0aGlzLA0KICAgICAgdGFnU2hvdzogZmFsc2UsDQogICAgICBpc0luZGV0ZXJtaW5hdGU6IHRydWUsDQogICAgICBjb3VudDogMCwNCiAgICAgIHNlcGFyYXRlOiB7fSwNCiAgICAgIC8qIOagh+etvuWKn+iDvSAqLw0KICAgICAgdGFnRGlhbG9nOiBmYWxzZSwNCiAgICAgIGZvcm1MYWJlbEFsaWduOiB7DQogICAgICAgIHRhZzogIiIsDQogICAgICAgIGluZHVzdHJ5OiAiIiwNCiAgICAgICAgZG9tYWluOiAiIiwNCiAgICAgIH0sDQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIG9wdGlvbnMxOiBbXSwNCiAgICAgIHRhZ0l0ZW06IHt9IC8qIOagh+etvuWvueixoSAqLywNCiAgICAgIGFyZWFMaXN0OiBbXSAvKiDpoobln58gKi8sDQogICAgICBpbmR1c3RyeTogW10gLyog6KGM5LiaICovLA0KICAgICAgbnVtOiAwLA0KICAgICAgdGltZXI6IG51bGwsDQogICAgICBkcmF3ZXI6IGZhbHNlLA0KICAgICAgZHJhd2VySW5mbzoge30sDQogICAgICBBcmVhSWQ6IG51bGwsDQogICAgICB0cmFuc2xhdGlvbkJ0blNob3c6IG51bGwsDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIHNvdXJjZVR5cGVMaXN0OiBbXSwgLy8g5pWw5o2u5rqQ5YiG57G7DQogICAgICBzb3VyY2VMaXN0czogW10sIC8vIOaVsOaNrua6kOWIl+ihqA0KICAgICAgc291cmNlVHlwZUxpc3RzOiBbXSwNCiAgICAgIGZvcm06IHt9LCAvLyDooajljZXlj4LmlbANCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgICB0aXRsZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlofnq6DmoIfpopjkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBjb250ZW50OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaWh+eroOivpuaDheS4uuW/heWhq+mhuSIgfV0sDQogICAgICAgIHB1Ymxpc2hUaW1lOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWPkeW4g+aXtumXtOS4uuW/heWhq+mhuSIgfV0sDQogICAgICAgIGNuVGl0bGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lit5paH5ZCN56ew5Li65b+F5aGr6aG5IiB9XSwNCiAgICAgICAgc291cmNlVHlwZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlubPlj7DnsbvlnovkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBvcmlnaW5hbFVybDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLljp/mlofkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBzdW1tYXJ5OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+Whq+WGmeaRmOimgSIgfV0sDQogICAgICAgIC8vIGNuU3VtbWFyeTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnkuK3mlofmkZjopoEnIH1dLA0KICAgICAgICBzbjogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7floavlhpnmlofnq6DlnLDlnYDllK/kuIDor4bliKvlj7ciIH1dLA0KICAgICAgfSwNCiAgICAgIHZlcnRpZnlVcGxvYWQ6IHsNCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gNCiAgICAgICAgaGVhZGVyczogew0KICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCksDQogICAgICAgICAgQ29udGVudFR5cGU6ICJhcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTgiLA0KICAgICAgICB9LA0KICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYANCiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9hcnRpY2xlL2FydGljbGVMaXN0L2NvdmVyIiwNCiAgICAgIH0sDQogICAgICBmaWxlTGlzdDogW10sDQogICAgICBmaWxlVXJsTGlzdDogW10sDQogICAgICBmaWxlVXJsdXJsOg0KICAgICAgICBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9hcnRpY2xlL2FydGljbGVMaXN0L3VwbG9hZC9maWxlIiwNCiAgICAgIHNob3dTdW1tYXJ5OiB0cnVlLA0KICAgICAgLy8g5om56YeP5a+85YWl55u45YWz5pWw5o2uDQogICAgICBiYXRjaEltcG9ydFZpc2libGU6IGZhbHNlLA0KICAgICAgYmF0Y2hJbXBvcnRGaWxlczogW10sDQogICAgICAvLyBEZWVwc2Vla+aKpeWRiuino+ivu+W8ueeqlw0KICAgICAgc2hvd0RlZXBzZWVrRGlhbG9nOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRBcnRpY2xlOiB7fSwNCiAgICAgIC8vIGFp55u45YWzDQogICAgICBhaURpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgY2hhdE1lc3NhZ2VzOiBbXSwNCiAgICAgIGlzVGhpbmtpbmc6IGZhbHNlLA0KICAgICAgdXNlckF2YXRhcjogIiIsIC8vIOeUqOaIt+WktOWDjw0KICAgICAgc3RyZWFtaW5nTWVzc2FnZTogIiIsIC8vIOa3u+WKoOeUqOS6juWtmOWCqOato+WcqOa1geW8j+i+k+WHuueahOa2iOaBrw0KICAgICAgbWFya2Rvd25PcHRpb25zOiB7DQogICAgICAgIGdmbTogdHJ1ZSwNCiAgICAgICAgYnJlYWtzOiB0cnVlLA0KICAgICAgICBoZWFkZXJJZHM6IHRydWUsDQogICAgICAgIG1hbmdsZTogZmFsc2UsDQogICAgICAgIGhlYWRlclByZWZpeDogIiIsDQogICAgICAgIHBlZGFudGljOiBmYWxzZSwNCiAgICAgICAgc2FuaXRpemU6IGZhbHNlLA0KICAgICAgICBzbWFydExpc3RzOiB0cnVlLA0KICAgICAgICBzbWFydHlwYW50czogdHJ1ZSwNCiAgICAgICAgeGh0bWw6IHRydWUsDQogICAgICB9LA0KICAgICAgaXNSZXF1ZXN0aW5nOiBmYWxzZSwgLy8g5qCH6K6w5piv5ZCm5q2j5Zyo6K+35rGC5LitDQogICAgICBpc0Fib3J0ZWQ6IGZhbHNlLCAvLyDmoIforrDmmK/lkKblt7LkuK3mlq0NCiAgICAgIGN1cnJlbnRSZWFkZXI6IG51bGwsIC8vIOW9k+WJjeeahCByZWFkZXINCiAgICAgIGFpUGxhdGZvcm06ICIiLA0KICAgICAgYXJ0aWNsZUFpUHJvbXB0OiAiIiwNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDoge30sDQogIHdhdGNoOiB7DQogICAgZGlhbG9nVmlzaWJsZTogZnVuY3Rpb24gKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICBpZiAobmV3VmFsKSB7DQogICAgICAgIEFQSS5nZXROZXdCdWlsdCh7IHNvdXJjZVR5cGU6IHRoaXMuc291cmNlVHlwZSB9KS50aGVuKChkYXRhKSA9PiB7DQogICAgICAgICAgaWYgKGRhdGEuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMucmVwb3J0T3B0aW9ucyA9IGRhdGEuZGF0YTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLmiqXlkYrliJfooajojrflj5blpLHotKXkuoYiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyAnZm9ybUxhYmVsQWxpZ24uaW5kdXN0cnknOiB7DQogICAgLy8gICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgIC8vICAgICBpZiAobmV3VmFsID09ICcnKSB7DQogICAgLy8gICAgICAgdGhpcy5vcHRpb25zMSA9IHRoaXMuaW5kdXN0cnkNCiAgICAvLyAgICAgfQ0KICAgIC8vICAgfSwNCiAgICAvLyAgIGRlZXA6IHRydWUNCiAgICAvLyB9LA0KICAgIC8vICdmb3JtTGFiZWxBbGlnbi5kb21haW4nOiB7DQogICAgLy8gICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgIC8vICAgICBpZiAobmV3VmFsID09ICcnKSB7DQogICAgLy8gICAgICAgdGhpcy5vcHRpb25zID0gdGhpcy5hcmVhTGlzdA0KICAgIC8vICAgICB9DQogICAgLy8gICB9LA0KICAgIC8vICAgZGVlcDogdHJ1ZQ0KICAgIC8vIH0sDQogICAgIlNlYWNoRGF0YS5zb3J0TW9kZSI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgdGhpcy5SZWZyZXNoKCk7DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICB9LA0KICAgICJmb3JtLnNvdXJjZVR5cGUiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIHRoaXMuc291cmNlVHlwZUxpc3RzID0gdGhpcy5zb3VyY2VMaXN0cy5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS50eXBlID09IG5ld1ZhbDsNCiAgICAgICAgfSk7DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICB9LA0KICB9LA0KICBtb3VudGVkKCkge30sDQogIGNyZWF0ZWQoKSB7DQogICAgaWYgKA0KICAgICAgdGhpcy5mbGFnICE9PSAiTW9uaXRvclVzZSIgJiYNCiAgICAgIHRoaXMuZmxhZyAhPT0gInNwZWNpYWxTdWJqZWN0VXNlIiAmJg0KICAgICAgdGhpcy5mbGFnICE9PSAiV2VjaGF0Ig0KICAgICkgew0KICAgICAgdGhpcy5vcGVuRGlhbG9nKCk7DQogICAgfQ0KICAgIGlmICh0aGlzLmZsYWcgIT09ICJXZWNoYXQiKSB7DQogICAgICBnZXRMaXN0Q2xhc3NpZnkoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5zb3VyY2VUeXBlTGlzdCA9IHJlcy5kYXRhOw0KICAgICAgfSk7DQogICAgICBBUEkuZ2V0U291cmNlTGlzdCgpLnRoZW4oKGRhdGEpID0+IHsNCiAgICAgICAgaWYgKGRhdGEuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnNvdXJjZUxpc3RzID0gZGF0YS5kYXRhOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9DQogICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgew0KICAgICAgZ2V0Q29uZmlnS2V5KCJzeXMuYWkucGxhdGZvcm0iKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuYWlQbGF0Zm9ybSA9IHJlcy5tc2c7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgZ2V0Q29uZmlnS2V5KCJ3ZWNoYXQuYWkuYXJ0aWNsZVByb21wdCIpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5hcnRpY2xlQWlQcm9tcHQgPSByZXMubXNnOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIC8vIOiOt+WPlueUqOaIt+WktOWDjw0KICAgICAgdGhpcy51c2VyQXZhdGFyID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5hdmF0YXI7DQogICAgfQ0KDQogICAgdGhpcy5zaG93U3VtbWFyeSA9IHRydWU7DQogIH0sDQogIHVwZGF0ZWQoKSB7fSwNCiAgZmlsdGVyczoge30sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDlpITnkIbnp5HmioDnm7jlhbPlrZfmrrXnmoTmmL7npLrmmKDlsIQNCiAgICBnZXRUZWNobm9sb2d5TGFiZWwodmFsdWUpIHsNCiAgICAgIGNvbnN0IG1hcHBpbmcgPSB7DQogICAgICAgIDA6ICLml6AiLA0KICAgICAgICAxOiAi5pyJIiwNCiAgICAgICAgMjogIuWFtuS7liIsDQogICAgICAgIDM6ICLlvoXlrpoiDQogICAgICB9Ow0KICAgICAgcmV0dXJuIG1hcHBpbmdbdmFsdWVdIHx8ICLmnKrlrprkuYkiOw0KICAgIH0sDQogICAgLy8g5aSE55CG5Y+R5biD5pe26Ze055qE5pi+56S6DQogICAgZm9ybWF0UHVibGlzaFRpbWUocHVibGlzaFRpbWUsIHdlYnN0ZVB1Ymxpc2hUaW1lKSB7DQogICAgICAvLyDmoLzlvI/ljJZwdWJsaXNoVGltZeS4uuW5tOaciOaXpQ0KICAgICAgY29uc3QgZm9ybWF0dGVkUHVibGlzaFRpbWUgPSB0aGlzLnBhcnNlVGltZShwdWJsaXNoVGltZSwgInt5fS17bX0te2R9Iik7DQoNCiAgICAgIC8vIOWmguaenHdlYnN0ZVB1Ymxpc2hUaW1l5LiN5a2Y5Zyo77yM55u05o6l6L+U5ZuecHVibGlzaFRpbWUNCiAgICAgIGlmICghd2Vic3RlUHVibGlzaFRpbWUpIHsNCiAgICAgICAgcmV0dXJuIGZvcm1hdHRlZFB1Ymxpc2hUaW1lOw0KICAgICAgfQ0KDQogICAgICBsZXQgZm9ybWF0dGVkV2Vic3RlVGltZSA9ICIiOw0KICAgICAgLy8g5aSE55CG5LiN5ZCM5qC85byP55qEd2Vic3RlUHVibGlzaFRpbWUNCiAgICAgIGlmICh3ZWJzdGVQdWJsaXNoVGltZSkgew0KICAgICAgICAvLyDlpITnkIYyMDI1LTA0LTEyIDEwOjA5OjIxLjk3MTE5MeagvOW8j++8iOWMheWQq+i/nuWtl+espueahOagh+WHhuagvOW8j++8iQ0KICAgICAgICBpZiAod2Vic3RlUHVibGlzaFRpbWUuaW5jbHVkZXMoIi0iKSkgew0KICAgICAgICAgIGNvbnN0IGRhdGVNYXRjaCA9IHdlYnN0ZVB1Ymxpc2hUaW1lLm1hdGNoKC8oXGR7NH0pLShcZHsyfSktKFxkezJ9KS8pOw0KICAgICAgICAgIGlmIChkYXRlTWF0Y2gpIHsNCiAgICAgICAgICAgIGNvbnN0IHllYXIgPSBkYXRlTWF0Y2hbMV07DQogICAgICAgICAgICBjb25zdCBtb250aCA9IGRhdGVNYXRjaFsyXTsNCiAgICAgICAgICAgIGNvbnN0IGRheSA9IGRhdGVNYXRjaFszXTsNCiAgICAgICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX1gOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gd2Vic3RlUHVibGlzaFRpbWU7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC8vIOWkhOeQhjIwMjXlubQwNOaciDE05pelIDExOjI5OjIy5qC85byP77yI5Lit5paH5bm05pyI5pel5qC85byP77yM5bimIuaXpSLlrZfvvIkNCiAgICAgICAgZWxzZSBpZiAoDQogICAgICAgICAgd2Vic3RlUHVibGlzaFRpbWUuaW5jbHVkZXMoIuW5tCIpICYmDQogICAgICAgICAgd2Vic3RlUHVibGlzaFRpbWUuaW5jbHVkZXMoIuaciCIpICYmDQogICAgICAgICAgd2Vic3RlUHVibGlzaFRpbWUuaW5jbHVkZXMoIuaXpSIpDQogICAgICAgICkgew0KICAgICAgICAgIGNvbnN0IGRhdGVNYXRjaCA9IHdlYnN0ZVB1Ymxpc2hUaW1lLm1hdGNoKA0KICAgICAgICAgICAgLyhcZHs0fSnlubQoXGR7MSwyfSnmnIgoXGR7MSwyfSnml6UvDQogICAgICAgICAgKTsNCiAgICAgICAgICBpZiAoZGF0ZU1hdGNoKSB7DQogICAgICAgICAgICBjb25zdCB5ZWFyID0gZGF0ZU1hdGNoWzFdOw0KICAgICAgICAgICAgY29uc3QgbW9udGggPSBkYXRlTWF0Y2hbMl0ucGFkU3RhcnQoMiwgIjAiKTsNCiAgICAgICAgICAgIGNvbnN0IGRheSA9IGRhdGVNYXRjaFszXS5wYWRTdGFydCgyLCAiMCIpOw0KICAgICAgICAgICAgZm9ybWF0dGVkV2Vic3RlVGltZSA9IGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWA7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSB3ZWJzdGVQdWJsaXNoVGltZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8g5aSE55CGMjAyNeW5tDTmnIgxNeagvOW8j++8iOS4reaWh+W5tOaciOagvOW8j++8jOS4jeW4piLml6Ui5a2X77yJDQogICAgICAgIGVsc2UgaWYgKA0KICAgICAgICAgIHdlYnN0ZVB1Ymxpc2hUaW1lLmluY2x1ZGVzKCLlubQiKSAmJg0KICAgICAgICAgIHdlYnN0ZVB1Ymxpc2hUaW1lLmluY2x1ZGVzKCLmnIgiKQ0KICAgICAgICApIHsNCiAgICAgICAgICBjb25zdCBkYXRlTWF0Y2ggPSB3ZWJzdGVQdWJsaXNoVGltZS5tYXRjaCgNCiAgICAgICAgICAgIC8oXGR7NH0p5bm0KFxkezEsMn0p5pyIKFxkezEsMn0pLw0KICAgICAgICAgICk7DQogICAgICAgICAgaWYgKGRhdGVNYXRjaCkgew0KICAgICAgICAgICAgY29uc3QgeWVhciA9IGRhdGVNYXRjaFsxXTsNCiAgICAgICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZU1hdGNoWzJdLnBhZFN0YXJ0KDIsICIwIik7DQogICAgICAgICAgICBjb25zdCBkYXkgPSBkYXRlTWF0Y2hbM10ucGFkU3RhcnQoMiwgIjAiKTsNCiAgICAgICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX1gOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gd2Vic3RlUHVibGlzaFRpbWU7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC8vIOWkhOeQhjIwMjUvMDQvMTQgMTE6Mjk6MjLmoLzlvI/vvIjmlpzmnaDliIbpmpTnmoTmoLzlvI/vvIkNCiAgICAgICAgZWxzZSBpZiAod2Vic3RlUHVibGlzaFRpbWUuaW5jbHVkZXMoIi8iKSkgew0KICAgICAgICAgIGNvbnN0IGRhdGVNYXRjaCA9IHdlYnN0ZVB1Ymxpc2hUaW1lLm1hdGNoKA0KICAgICAgICAgICAgLyhcZHs0fSlcLyhcZHsxLDJ9KVwvKFxkezEsMn0pLw0KICAgICAgICAgICk7DQogICAgICAgICAgaWYgKGRhdGVNYXRjaCkgew0KICAgICAgICAgICAgY29uc3QgeWVhciA9IGRhdGVNYXRjaFsxXTsNCiAgICAgICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZU1hdGNoWzJdLnBhZFN0YXJ0KDIsICIwIik7DQogICAgICAgICAgICBjb25zdCBkYXkgPSBkYXRlTWF0Y2hbM10ucGFkU3RhcnQoMiwgIjAiKTsNCiAgICAgICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX1gOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gd2Vic3RlUHVibGlzaFRpbWU7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWFtuS7luagvOW8j+ebtOaOpeS9v+eUqOWOn+WAvA0KICAgICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSB3ZWJzdGVQdWJsaXNoVGltZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDmr5TovoPlubTmnIjml6XmmK/lkKbnm7jlkIwNCiAgICAgIGlmIChmb3JtYXR0ZWRQdWJsaXNoVGltZSA9PT0gZm9ybWF0dGVkV2Vic3RlVGltZSkgew0KICAgICAgICByZXR1cm4gZm9ybWF0dGVkUHVibGlzaFRpbWU7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gYCR7Zm9ybWF0dGVkUHVibGlzaFRpbWV9IC8gJHt3ZWJzdGVQdWJsaXNoVGltZX1gOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmo4Dmn6XmlofmnKzmmK/lkKbmnInlrp7pmYXlhoXlrrnvvIjljrvpmaRIVE1M5qCH562+5ZCO77yJDQogICAgaGFzQWN0dWFsQ29udGVudCh0ZXh0KSB7DQogICAgICBpZiAoIXRleHQpIHsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgICAgLy8g5Y676ZmkSFRNTOagh+etvg0KICAgICAgY29uc3QgY29udGVudFdpdGhvdXRUYWdzID0gdGV4dC5yZXBsYWNlKC88W14+XSo+L2csICIiKTsNCiAgICAgIC8vIOajgOafpeaYr+WQpuacieS4reaWh+OAgeiLseaWh+OAgeaVsOWtl+etieWunumZheWGheWuuQ0KICAgICAgcmV0dXJuIC9bXHU0ZTAwLVx1OWZhNWEtekEtWjAtOV0vLnRlc3QoY29udGVudFdpdGhvdXRUYWdzKTsNCiAgICB9LA0KICAgIC8vIOWFs+mUruWtl+abv+aNog0KICAgIGNoYW5nZUNvbG9yKHN0cikgew0KICAgICAgbGV0IFN0ciA9IHN0cjsNCiAgICAgIGlmIChTdHIpIHsNCiAgICAgICAgbGV0IGtleXdvcmRzID0gdGhpcy5rZXl3b3Jkcy5zcGxpdCgiLCIpOw0KICAgICAgICBrZXl3b3Jkcy5tYXAoKGtleWl0ZW0sIGtleWluZGV4KSA9PiB7DQogICAgICAgICAgaWYgKGtleWl0ZW0gJiYga2V5aXRlbS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAvLyDljLnphY3lhbPplK7lrZfmraPliJkNCiAgICAgICAgICAgIGxldCByZXBsYWNlUmVnID0gbmV3IFJlZ0V4cChrZXlpdGVtLCAiZyIpOw0KICAgICAgICAgICAgLy8g6auY5Lqu5pu/5o2idi1odG1s5YC8DQogICAgICAgICAgICBsZXQgcmVwbGFjZVN0cmluZyA9DQogICAgICAgICAgICAgICc8c3BhbiBjbGFzcz0iaGlnaGxpZ2h0IicgKw0KICAgICAgICAgICAgICAnIHN0eWxlPSJjb2xvcjogcmVkOyI+JyArDQogICAgICAgICAgICAgIGtleWl0ZW0gKw0KICAgICAgICAgICAgICAiPC9zcGFuPiI7DQogICAgICAgICAgICBTdHIgPSBTdHIucmVwbGFjZShyZXBsYWNlUmVnLCByZXBsYWNlU3RyaW5nKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIFN0cjsNCiAgICB9LA0KICAgIC8qIOS4i+i9vUV4Y2VsICovDQogICAgYXN5bmMgZG93bkxvYWRFeGNlbCgpIHsNCiAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLor7fpgInmi6nopoHlr7zlh7rnmoTmlbDmja4iLCB0eXBlOiAid2FybmluZyIgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgaWYgKHRoaXMuZmxhZyA9PSAic3BlY2lhbFN1YmplY3RVc2UiKSB7DQogICAgICAgIEFQSS5kb3duTG9hZEV4Y2VsKHRoaXMuY2hlY2tlZENpdGllcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICBsZXQgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKTsNCiAgICAgICAgICBhLmhyZWYgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChyZXNwb25zZSk7DQogICAgICAgICAgYS5kb3dubG9hZCA9IGBzb3VyY2VfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGA7DQogICAgICAgICAgYS5jbGljaygpOw0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGF3YWl0IEFQSS5kb3duTG9hZEV4cG9ydEV4Y2VsKHRoaXMuY2hlY2tlZENpdGllcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICBsZXQgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKTsNCiAgICAgICAgICBhLmhyZWYgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChyZXNwb25zZSk7DQogICAgICAgICAgYS5kb3dubG9hZCA9IGBzb3VyY2VfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGA7DQogICAgICAgICAgYS5jbGljaygpOw0KDQogICAgICAgICAgLy8gc2F2ZUFzKGJsb2IsIGBzb3VyY2VfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgYmF0Y2hEZWxldGUoKSB7DQogICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi6K+36YCJ5oup6KaB5Yig6Zmk55qE5pWw5o2uIiwgdHlwZTogIndhcm5pbmciIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTliKDpmaTlt7Lli77pgInnmoTmlbDmja7pobk/IikNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIEFQSS5iYXRjaFJlbW92ZSh0aGlzLmNoZWNrZWRDaXRpZXMuam9pbigiLCIpKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLliKDpmaTmiJDlip8iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgICAgICB0aGlzLiRlbWl0KCJSZWZyZXNoIik7DQogICAgICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMgPSBbXTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qIOWPkeW4g+WIsOavj+aXpeacgOaWsOeDreeCuSAqLw0KICAgIHB1Ymxpc2hIb3QoKSB7DQogICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nopoHlj5HluIPliLDmr4/ml6XmnIDmlrDng63ngrnnmoTmlbDmja4iLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruiupOWPkeW4g+W3suWLvumAieeahOaVsOaNrumhueWIsOavj+aXpeacgOaWsOeDreeCuT8iKQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgQVBJLnB1Ymxpc2hFdmVyeWRheUhvdCh0aGlzLmNoZWNrZWRDaXRpZXMuam9pbigiLCIpKS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyB0eXBlOiAic3VjY2VzcyIsIG1lc3NhZ2U6ICLlj5HluIPmiJDlip8hIiB9KTsNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoIlJlZnJlc2giKTsNCiAgICAgICAgICAgIHRoaXMuY2hlY2tlZENpdGllcyA9IFtdOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyog6L+U5Zue6aG26YOo5Yqo55S7ICovDQogICAgbWFpblNjb3JsbCgpIHsNCiAgICAgIHZhciBzY3JvbGxTdGVwID0gLXRoaXMuJHJlZnMuc2Nyb2xsLnNjcm9sbFRvcCAvICg4MDAgLyAxNSk7IC8vIOiuoeeul+avj+S4gOatpea7muWKqOeahOi3neemuw0KICAgICAgdmFyIHNjcm9sbEludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICBpZiAodGhpcy4kcmVmcy5zY3JvbGwuc2Nyb2xsVG9wICE9PSAwKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5zY3JvbGwuc2Nyb2xsQnkoMCwgc2Nyb2xsU3RlcCk7IC8vIOaMieeFp+e7meWumuatpemVv+a7muWKqOeql+WPow0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNsZWFySW50ZXJ2YWwoc2Nyb2xsSW50ZXJ2YWwpOyAvLyDliLDovr7pobbpg6jml7bmuIXpmaTlrprml7blmagNCiAgICAgICAgfQ0KICAgICAgfSwgMTUpOw0KICAgIH0sDQogICAgc2Nyb2xsQ2hhbmdlKCkgew0KICAgICAgY2xlYXJUaW1lb3V0KHRoaXMudGltZXIpOw0KICAgICAgdGhpcy50aW1lciA9IHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLnN0b3BTY3JvbGwoKTsNCiAgICAgIH0sIDUwMCk7DQogICAgICAvLyB0aGlzLiRyZWZzLnBhZ2luYXRpb24uc3R5bGUub3BhY2l0eSA9IDANCiAgICAgIC8vIHRoaXMuJHJlZnMucGFnaW5hdGlvbi5zdHlsZS50cmFuc2l0aW9uID0gJzAnDQogICAgfSAvKiDmu5rliqjkuovku7YgKi8sDQogICAgc3RvcFNjcm9sbCgpIHsNCiAgICAgIC8vIHRoaXMuJHJlZnMucGFnaW5hdGlvbi5zdHlsZS50cmFuc2l0aW9uID0gJzFzJw0KICAgICAgLy8gdGhpcy4kcmVmcy5wYWdpbmF0aW9uLnN0eWxlLm9wYWNpdHkgPSAxDQogICAgfSwNCiAgICAvKiDkuIvovb0gKi8NCiAgICBkb3duTG9hZCgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvKiDmr4/pobXmnaHmlbDlj5jljJYgKi8NCiAgICBoYW5kbGVTaXplQ2hhbmdlKG51bSkgew0KICAgICAgdGhpcy4kZW1pdCgiaGFuZGxlU2l6ZUNoYW5nZSIsIG51bSk7DQogICAgICB0aGlzLm1haW5TY29ybGwoKTsNCiAgICAgIHRoaXMuY2hlY2tlZCA9IGZhbHNlOw0KICAgIH0sDQogICAgLyog6aG156CB5Y+Y5YyWICovDQogICAgaGFuZGxlQ3VycmVudENoYW5nZShjdXJyZW50KSB7DQogICAgICB0aGlzLiRlbWl0KCJoYW5kbGVDdXJyZW50Q2hhbmdlIiwgY3VycmVudCk7DQogICAgICB0aGlzLm1haW5TY29ybGwoKTsNCiAgICAgIHRoaXMuY2hlY2tlZCA9IGZhbHNlOw0KICAgIH0sDQogICAgLyog5pS26JePICovDQogICAgYXN5bmMgY29sbGVjdChpdGVtKSB7DQogICAgICAvKiDngrnlh7vliJfooajmlLbol48gKi8NCiAgICAgIGlmIChpdGVtLmlkKSB7DQogICAgICAgIHRoaXMuY2hlY2tlZENpdGllcyA9IFtpdGVtLmlkXTsNCiAgICAgIH0NCiAgICAgIC8qIOacqumAieaLqeaPkOekuiAqLw0KICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuivt+mAieaLqeimgeaUtuiXj+eahOaWh+eroCIsIHR5cGU6ICJpbmZvIiB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgLyog5pS26JePICovDQogICAgICBpZiAoIWl0ZW0uZmF2b3JpdGVzKSB7DQogICAgICAgIGxldCByZXMgPSBhd2FpdCBBUEkuY29sbGVjdEFwaShbaXRlbS5pZF0pOw0KICAgICAgICBpZiAocmVzLmNvZGUpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLmlLbol4/miJDlip8s6K+35YmN5b6A5Liq5Lq65Lit5b+D5p+l55yLIiwNCiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLiRlbWl0KCJSZWZyZXNoIik7DQogICAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzID0gW107DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5pS26JeP5aSx6LSlIiwgdHlwZTogImluZm8iIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IHJlcyA9IGF3YWl0IEFQSS5jb2NlbENvbGxlY3QoW2l0ZW0uaWRdKTsNCiAgICAgICAgaWYgKHJlcy5jb2RlKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLlt7Llj5bmtojmlLbol48iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgICAgdGhpcy4kZW1pdCgiUmVmcmVzaCIpOw0KICAgICAgICAgIHRoaXMuY2hlY2tlZENpdGllcyA9IFtdOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuWPlua2iOaUtuiXj+Wksei0pSIsIHR5cGU6ICJpbmZvIiB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qIOS4gOmUruWkjeWItiAqLw0KICAgIGNvcHlUZXh0KGl0ZW0pIHsNCiAgICAgIG5hdmlnYXRvci5jbGlwYm9hcmQNCiAgICAgICAgLndyaXRlVGV4dChpdGVtLmNuVGl0bGUpDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuW3suaIkOWKn+WkjeWItuWIsOWJqui0tOadvyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICBhbGVydCgi5aSN5Yi25aSx6LSlIik7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgdHlwZUhhbmRsZShkYXRhKSB7DQogICAgICBpZiAoZGF0YSA9PSAxKSB7DQogICAgICAgIHJldHVybiAi5b6u5L+h5YWs5LyX5Y+3IjsNCiAgICAgIH0gZWxzZSBpZiAoZGF0YSA9PSAyKSB7DQogICAgICAgIHJldHVybiAi572R56uZIjsNCiAgICAgIH0gZWxzZSBpZiAoZGF0YSA9PSAzKSB7DQogICAgICAgIHJldHVybiAi5omL5Yqo5b2V5YWlIjsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qIOmAieaLqeS6i+S7tiAqLw0KICAgIGhhbmRsZUNoZWNrZWRDaXRpZXNDaGFuZ2UodmFsdWUpIHsNCiAgICAgIHRoaXMuY2hlY2tlZENpdGllcyA9IHZhbHVlOw0KICAgIH0sDQogICAgLyog5YWo6YCJICovDQogICAgaGFuZGxlQ2hlY2tBbGxDaGFuZ2UodmFsKSB7DQogICAgICB0aGlzLmNoZWNrZWRDaXRpZXMgPSB2YWwgPyB0aGlzLkFydGljbGVMaXN0Lm1hcCgoaXRlbSkgPT4gaXRlbS5pZCkgOiBbXTsNCiAgICAgIHRoaXMuaXNJbmRldGVybWluYXRlID0gZmFsc2U7DQogICAgfSwNCiAgICAvKiDliLfmlrAgKi8NCiAgICBSZWZyZXNoKCkgew0KICAgICAgdGhpcy4kZW1pdCgiUmVmcmVzaCIpOw0KICAgIH0sDQogICAgLyrnoa7lrprmt7vliqDliLDmiqXlkYogKi8NCiAgICBhc3luYyByZXBvcnRTdWJtaXQoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgIGxldCBrZXlXb3JkTGlzdCA9IFtdOw0KICAgICAgaWYgKCF0aGlzLnJlcG9ydElkKQ0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeimgea3u+WKoOWIsOeahOaKpeWRiiIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIC8qIOWNleeLrOa3u+WKoCAqLw0KICAgICAgaWYgKHRoaXMuc2VwYXJhdGUuaWQpIHsNCiAgICAgICAgLy8gbGV0IGtleXdvcmQgPSBPYmplY3Qua2V5cyh0aGlzLnNlcGFyYXRlLmtleXdvcmRDb3VudCkNCiAgICAgICAga2V5V29yZExpc3QucHVzaCh7DQogICAgICAgICAgcmVwb3J0SWQ6IHRoaXMucmVwb3J0SWQsDQogICAgICAgICAgbGlzdElkOiB0aGlzLnNlcGFyYXRlLmlkLA0KICAgICAgICAgIGxpc3RTbjogdGhpcy5zZXBhcmF0ZS5zbiwNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvKiDmibnph4/mt7vliqAgKi8NCiAgICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcyA9PSAiIikNCiAgICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6KaB5re75Yqg55qE5pWw5o2uIiwNCiAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBsZXQgYXJ0aWNsZSA9IHRoaXMuQXJ0aWNsZUxpc3QuZmlsdGVyKCh2YWx1ZSkgPT4gdmFsdWUuaWQgPT0gaXRlbSk7DQogICAgICAgICAga2V5V29yZExpc3QucHVzaCh7DQogICAgICAgICAgICByZXBvcnRJZDogdGhpcy5yZXBvcnRJZCwNCiAgICAgICAgICAgIGxpc3RJZDogaXRlbSwNCiAgICAgICAgICAgIGxpc3RTbjogYXJ0aWNsZVswXS5zbiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBsZXQgcmVzID0gYXdhaXQgQVBJLkFkZFJlcG9ydChrZXlXb3JkTGlzdCk7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5bey5re75Yqg5Yiw5oql5ZGKIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICB0aGlzLiRlbWl0KCJSZWZyZXNoIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi5re75Yqg5Yiw5oql5ZGK5aSx6LSlLOivt+iBlOezu+euoeeQhuWRmCIsDQogICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICB0aGlzLnNlcGFyYXRlID0ge307DQogICAgICB0aGlzLnJlcG9ydElkID0gIiI7DQogICAgICB0aGlzLmNoZWNrZWRDaXRpZXMgPSBbXTsNCiAgICAgIHRoaXMuY2hlY2tlZCA9IGZhbHNlOw0KICAgIH0sDQogICAgLyog5Y2V54us5re75Yqg5oql5ZGKICovDQogICAgc2VwYXJhdGVBZGQoaXRlbSkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuc2VwYXJhdGUgPSBpdGVtOw0KICAgIH0sDQogICAgLyog6Lez6L2s5paw6aG16Z2iICovDQogICAgb3Blbk5ld1ZpZXcoaXRlbSwgaXNMaW5rKSB7DQogICAgICBpZiAoaXNMaW5rKSB7DQogICAgICAgIGlmIChpdGVtLm9yaWdpbmFsVXJsKSB7DQogICAgICAgICAgd2luZG93Lm9wZW4oaXRlbS5vcmlnaW5hbFVybCk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi6K+l5paH56ug5rKh5pyJ5Y6f5paH6ZO+5o6lIiB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgd2luZG93Lm9wZW4oDQogICAgICAgIGAvZXhwcmVzc0RldGFpbHM/aWQ9JHtpdGVtLmlkfSZkb2NJZD0ke2l0ZW0uZG9jSWR9JnNvdXJjZVR5cGU9JHtpdGVtLnNvdXJjZVR5cGV9YCwNCiAgICAgICAgIl9ibGFuayINCiAgICAgICk7DQogICAgICAvLyB0aGlzLmRyYXdlckluZm8gPSBpdGVtDQogICAgICAvLyB0aGlzLmRyYXdlciA9IHRydWUNCiAgICB9LA0KICAgIC8qIOaWh+eroOaJk+agh+etviAqLw0KICAgIHRhZ0hhbmRsZXIoaXRlbSkgew0KICAgICAgdGhpcy50YWdEaWFsb2cgPSB0cnVlOw0KICAgICAgdGhpcy50YWdJdGVtID0gaXRlbTsNCiAgICAgIGlmIChpdGVtLmluZHVzdHJ5KSB7DQogICAgICAgIHRoaXMuZm9ybUxhYmVsQWxpZ24uaW5kdXN0cnkgPSBpdGVtLmluZHVzdHJ5DQogICAgICAgICAgLnNwbGl0KCIsIikNCiAgICAgICAgICAubWFwKChkYXRhKSA9PiBOdW1iZXIoZGF0YSkpOw0KICAgICAgfQ0KICAgICAgaWYgKGl0ZW0uZG9tYWluKSB7DQogICAgICAgIHRoaXMuZm9ybUxhYmVsQWxpZ24uZG9tYWluID0gaXRlbS5kb21haW4NCiAgICAgICAgICAuc3BsaXQoIiwiKQ0KICAgICAgICAgIC5tYXAoKGRhdGEpID0+IE51bWJlcihkYXRhKSk7DQogICAgICB9DQogICAgICB0aGlzLmZvcm1MYWJlbEFsaWduLnRhZyA9IGl0ZW0udGFncyA/IGl0ZW0udGFncy5zcGxpdCgiLCIpIDogIiI7DQogICAgfSwNCiAgICAvKiDojrflj5bpoobln5/lkozliIbnsbsgKi8NCiAgICBhc3luYyBvcGVuRGlhbG9nKCkgew0KICAgICAgYXdhaXQgQVBJLmFyZWFMaXN0KCkudGhlbigoZGF0YSkgPT4gew0KICAgICAgICBpZiAoZGF0YS5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuYXJlYUxpc3QgPSBkYXRhLmRhdGE7DQogICAgICAgICAgdGhpcy5vcHRpb25zID0gZGF0YS5kYXRhOw0KICAgICAgICAgIEFQSS5pbmR1c3RyeSgpLnRoZW4oKHZhbHVlKSA9PiB7DQogICAgICAgICAgICB0aGlzLmluZHVzdHJ5ID0gdmFsdWUuZGF0YTsNCiAgICAgICAgICAgIHRoaXMub3B0aW9uczEgPSB2YWx1ZS5kYXRhOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qIOetm+mAiemihuWfnyAqLw0KICAgIHJlbW90ZUV2ZW50KHF1ZXJ5KSB7DQogICAgICB0aGlzLm9wdGlvbnMgPSB0aGlzLmFyZWFMaXN0LmZpbHRlcigoaXRlbSkgPT4gaXRlbS5maWVsZE5hbWUgPT0gcXVlcnkpOw0KICAgIH0sDQogICAgLyog562b6YCJ6KGM5LiaICovDQogICAgcmVtb3RlSW5kdXN0cnkocXVlcnkpIHsNCiAgICAgIHRoaXMub3B0aW9uczEgPSB0aGlzLmluZHVzdHJ5LmZpbHRlcigNCiAgICAgICAgKGl0ZW0pID0+IGl0ZW0uaW5kdXN0cnlOYW1lID09IHF1ZXJ5DQogICAgICApOw0KICAgIH0sDQogICAgYXN5bmMgU3VibWl0VGFnKCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgZG9tYWluOiBTdHJpbmcodGhpcy5mb3JtTGFiZWxBbGlnbi5kb21haW4pLA0KICAgICAgICBpbmR1c3RyeTogU3RyaW5nKHRoaXMuZm9ybUxhYmVsQWxpZ24uaW5kdXN0cnkpLA0KICAgICAgICB0YWdzOiBTdHJpbmcodGhpcy5mb3JtTGFiZWxBbGlnbi50YWcpLA0KICAgICAgICBhcnRpY2xlSWQ6IFN0cmluZyh0aGlzLnRhZ0l0ZW0uaWQpLA0KICAgICAgICBkb2NJZDogdGhpcy50YWdJdGVtLmRvY0lkID8gU3RyaW5nKHRoaXMudGFnSXRlbS5kb2NJZCkgOiAiIiwNCiAgICAgIH07DQogICAgICBsZXQgcmVzID0gYXdhaXQgQVBJLnRhZ0FkZChwYXJhbXMpOw0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuS/neWtmOaIkOWKnyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5SZWZyZXNoKCk7DQogICAgICAgIH0sIDEwMDApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLkv53lrZjlpLHotKUiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgfQ0KICAgICAgdGhpcy5jbG9zZVRhZygpOw0KICAgIH0sDQogICAgY2xvc2VUYWcoKSB7DQogICAgICB0aGlzLiRyZWZzWyJydWxlRm9ybSJdLnJlc2V0RmllbGRzKCk7DQogICAgICB0aGlzLmZvcm1MYWJlbEFsaWduID0gew0KICAgICAgICB0YWc6ICIiLA0KICAgICAgICBpbmR1c3RyeTogIiIsDQogICAgICAgIGRvbWFpbjogIiIsDQogICAgICB9Ow0KICAgICAgdGhpcy50YWdEaWFsb2cgPSBmYWxzZTsNCiAgICB9LA0KICAgIGFzeW5jIGhvdEluY3JlYXNlKGl0ZW0pIHsNCiAgICAgIGxldCBpc1doZXRoZXIgPSBKU09OLnBhcnNlKGl0ZW0uaXNXaGV0aGVyKTsNCiAgICAgIGxldCByZXMgPSBhd2FpdCBBUEkudGFnQWRkKHsNCiAgICAgICAgYXJ0aWNsZUlkOiBpdGVtLmlkLA0KICAgICAgICBpc1doZXRoZXI6ICshQm9vbGVhbihpc1doZXRoZXIpLA0KICAgICAgfSk7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICB0aGlzLlJlZnJlc2goKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5pON5L2c5aSx6LSlIiwgdHlwZTogImVycm9yIiB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIG9wZW5EcmF3ZXIoKSB7DQogICAgICBsZXQgZG9jSWQgPSB0aGlzLmRyYXdlckluZm8uZG9jSWQ7DQogICAgICBhd2FpdCBBUEkuQXJlYUluZm8odGhpcy5kcmF3ZXJJbmZvLmlkKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZHJhd2VySW5mbyA9IHJlcy5kYXRhOw0KICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5kb2NJZCA9IGRvY0lkOw0KICAgICAgICAgIC8qIOWwhuWtl+espuS4suS4reeahFxu5pu/5o2i5Li6PGJyPiAqLw0KICAgICAgICAgIHRoaXMudHJhbnNsYXRpb25CdG5TaG93ID0gIXRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQ7DQogICAgICAgICAgaWYgKHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgfHwgdGhpcy5kcmF3ZXJJbmZvLmNvbnRlbnQpIHsNCiAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgPSAoDQogICAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgfHwgdGhpcy5kcmF3ZXJJbmZvLmNvbnRlbnQNCiAgICAgICAgICAgICkucmVwbGFjZSgvXFxuL2csIChhLCBiLCBjKSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiAiPGJyPiI7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgPSAoDQogICAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgfHwgdGhpcy5kcmF3ZXJJbmZvLmNvbnRlbnQNCiAgICAgICAgICAgICkucmVwbGFjZSgvXCR7W159XSt9L2csICI8YnI+Iik7DQogICAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50ID0gKA0KICAgICAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50IHx8IHRoaXMuZHJhd2VySW5mby5jb250ZW50DQogICAgICAgICAgICApLnJlcGxhY2UoInx4YTAiLCAiIik7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qIOaJgOWxnuihjOS4muWkhOeQhiAqLw0KICAgIGluZHVzdHJ5SGFuZGxlKGl0ZW0pIHsNCiAgICAgIGxldCBpZHMgPSBbXSwNCiAgICAgICAgc3RyID0gIiI7DQogICAgICBpZiAoaXRlbS5pbmR1c3RyeSkgew0KICAgICAgICBpZHMgPSBpdGVtLmluZHVzdHJ5LnNwbGl0KCIsIik7DQogICAgICB9DQogICAgICBpZHMuZm9yRWFjaCgoZGF0YSkgPT4gew0KICAgICAgICB0aGlzLmluZHVzdHJ5Lm1hcCgoZWxlKSA9PiB7DQogICAgICAgICAgaWYgKGVsZS5pZCA9PSBkYXRhKSB7DQogICAgICAgICAgICBpZiAoc3RyID09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICBzdHIgPSAiIjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHN0ciArPSBlbGUuaW5kdXN0cnlOYW1lICsgIiAiOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICAgIHJldHVybiBzdHI7DQogICAgfSwNCiAgICBkb21haW5IYW5kbGUoaXRlbSkgew0KICAgICAgbGV0IGlkcyA9IFtdLA0KICAgICAgICBzdHIgPSAiIjsNCiAgICAgIGlmIChpdGVtLmRvbWFpbikgew0KICAgICAgICBpZHMgPSBpdGVtLmRvbWFpbi5zcGxpdCgiLCIpOw0KICAgICAgfQ0KICAgICAgaWRzLmZvckVhY2goKGRhdGEpID0+IHsNCiAgICAgICAgdGhpcy5hcmVhTGlzdC5tYXAoKGVsZSkgPT4gew0KICAgICAgICAgIGlmIChlbGUuaWQgPT0gZGF0YSkgew0KICAgICAgICAgICAgaWYgKHN0ciA9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgc3RyID0gIiI7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBzdHIgKz0gZWxlLmZpZWxkTmFtZSArICIgIjsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgICByZXR1cm4gc3RyOw0KICAgIH0sDQogICAgLyog5b+r54Wn55Sf5oiQICovDQogICAgcmVzdWx0RXZlbnQoaXRlbSkgew0KICAgICAgaWYgKGl0ZW0gPT0gIkJhdGNoR2VuZXJhdGlvbiIgJiYgdGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI6YCJ5oup5paH56ugIik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGxldCBpZHMgPSBudWxsOw0KICAgICAgbGV0IHpodWFuZ3RhaSA9ICLnlJ/miJAiOw0KICAgICAgbGV0IHVybCA9ICIiOw0KICAgICAgaWYgKGl0ZW0gPT0gImRyYXdlciIpIHsNCiAgICAgICAgaWRzID0gW3RoaXMuZHJhd2VySW5mby5pZF07DQogICAgICAgIGlmICh0aGlzLmRyYXdlckluZm8uc25hcHNob3RVcmwpIHpodWFuZ3RhaSA9ICLmn6XnnIsiOw0KICAgICAgICB1cmwgPSB0aGlzLmRyYXdlckluZm8uc25hcHNob3RVcmw7DQogICAgICB9IGVsc2UgaWYgKGl0ZW0gPT0gIkJhdGNoR2VuZXJhdGlvbiIpIHsNCiAgICAgICAgaWRzID0gdGhpcy5jaGVja2VkQ2l0aWVzOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWRzID0gW2l0ZW0uaWRdOw0KICAgICAgICBpZiAoaXRlbS5zbmFwc2hvdFVybCkgemh1YW5ndGFpID0gIuafpeeciyI7DQogICAgICAgIHVybCA9IGl0ZW0uc25hcHNob3RVcmw7DQogICAgICB9DQogICAgICBpZiAoemh1YW5ndGFpID09ICLnlJ/miJAiKSB7DQogICAgICAgIGlmICh0aGlzLmZsYWcgPT0gIk1vbml0b3JVc2UiKSB7DQogICAgICAgICAgQVBJLmRvd25Mb2FkRXhwb3J0S2UoaWRzKQ0KICAgICAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJG1zZ2JveCh7DQogICAgICAgICAgICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5b+r54Wn5q2j5Zyo55Sf5oiQ5Lit77yM6K+356iN5ZCO5p+l55yLIiwNCiAgICAgICAgICAgICAgICAgIHNob3dDYW5jZWxCdXR0b246IHRydWUsDQogICAgICAgICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuWFs+mXrSIsDQogICAgICAgICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICAgICAgICAgIHNob3dDYW5jZWxCdXR0b246IGZhbHNlLA0KICAgICAgICAgICAgICAgICAgYmVmb3JlQ2xvc2U6IChhY3Rpb24sIGluc3RhbmNlLCBkb25lKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGRvbmUoKTsNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi55Sz6K+35aSx6LSl77yM6K+36IGU57O7566h55CG5ZGY77yM56Gu6K6k6YeH6ZuG5Zmo5piv5ZCm5q2j5bi4IiwNCiAgICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICAuY2F0Y2goKGVycikgPT4ge30pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIEFQSS5kb3duTG9hZEV4cG9ydFpodWFuKGlkcykNCiAgICAgICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtc2dib3goew0KICAgICAgICAgICAgICAgICAgdGl0bGU6ICLmj5DnpLoiLA0KICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuW/q+eFp+ato+WcqOeUn+aIkOS4re+8jOivt+eojeWQjuafpeeciyIsDQogICAgICAgICAgICAgICAgICBzaG93Q2FuY2VsQnV0dG9uOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLlhbPpl60iLA0KICAgICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgICAgICAgICAgICBzaG93Q2FuY2VsQnV0dG9uOiBmYWxzZSwNCiAgICAgICAgICAgICAgICAgIGJlZm9yZUNsb3NlOiAoYWN0aW9uLCBpbnN0YW5jZSwgZG9uZSkgPT4gew0KICAgICAgICAgICAgICAgICAgICBkb25lKCk7DQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIueUs+ivt+Wksei0pe+8jOivt+iBlOezu+euoeeQhuWRmO+8jOehruiupOmHh+mbhuWZqOaYr+WQpuato+W4uCIsDQogICAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgLmNhdGNoKChlcnIpID0+IHt9KTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdXJsID0gdXJsLnJlcGxhY2UobmV3IFJlZ0V4cCgiL2hvbWUvbG9jYWwvZHB4L3NlcnZlci1hcGkvIiwgImciKSwgIi8iKTsNCiAgICAgICAgdXJsID0gdXJsLnJlcGxhY2UobmV3IFJlZ0V4cCgiL2hvbWUvbG9jYWwvZHB4LyIsICJnIiksICIvIik7DQogICAgICAgIHdpbmRvdy5vcGVuKHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4gKyB1cmwsICJfYmxhbmsiKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qIOmZhOS7tuS4i+i9vSAqLw0KICAgIGFzeW5jIGRvY3VtZW50RG93bmxvYWQoaXRlbSkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGlmIChpdGVtLmZpbGVVcmwpIHsNCiAgICAgICAgY29uc3QgdXJscyA9IGl0ZW0uZmlsZVVybC5zcGxpdCgiLCIpOw0KICAgICAgICBmb3IgKGNvbnN0IFtpbmRleCwgdXJsXSBvZiB1cmxzLmVudHJpZXMoKSkgew0KICAgICAgICAgIGlmICh1cmwuaW5kZXhPZigiaHR0cHM6Ly8iKSA9PT0gLTEpIHsNCiAgICAgICAgICAgIHNldFRpbWVvdXQoYXN5bmMgKCkgPT4gew0KICAgICAgICAgICAgICBhd2FpdCB0aGlzLmRvd25Mb2FkRnVuKHVybCwgaW5kZXgsIGl0ZW0uY25UaXRsZSB8fCBpdGVtLnRpdGxlKTsNCiAgICAgICAgICAgIH0sIGluZGV4ICogNTAwKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6ZmE5Lu26L+Y5rKh5ZCM5q2l5Yiw5b2T5YmN57O757uf77yM5pqC5pe25peg5rOV5LiL6L29Iik7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICB9LA0KDQogICAgYXN5bmMgZG93bkxvYWRGdW4odXJsLCBpbmRleCwgdGl0bGUpIHsNCiAgICAgIGxldCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpOw0KICAgICAgZm9ybURhdGEuYXBwZW5kKCJmaWxlVXJsIiwgdXJsKTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBUEkuZG93bmxvYWRGaWxlKGZvcm1EYXRhKTsNCiAgICAgICAgY29uc3QgaXNCbG9iID0gYmxvYlZhbGlkYXRlKHJlc3BvbnNlKTsNCg0KICAgICAgICBpZiAoaXNCbG9iKSB7DQogICAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtyZXNwb25zZV0pOw0KICAgICAgICAgIGxldCBsaXN0ID0gdXJsLnNwbGl0KCIvIik7DQogICAgICAgICAgbGV0IGZpbGVOYW1lID0gbGlzdFtsaXN0Lmxlbmd0aCAtIDFdOw0KICAgICAgICAgIHNhdmVBcyhibG9iLCBmaWxlTmFtZSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc3QgcmVzVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTsNCiAgICAgICAgICBjb25zdCByc3BPYmogPSBKU09OLnBhcnNlKHJlc1RleHQpOw0KICAgICAgICAgIGNvbnN0IGVyck1zZyA9DQogICAgICAgICAgICBlcnJvckNvZGVbcnNwT2JqLmNvZGVdIHx8IHJzcE9iai5tc2cgfHwgZXJyb3JDb2RlWyJkZWZhdWx0Il07DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihlcnJNc2cpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgLy8gdGhpcy4kbWVzc2FnZS5lcnJvcihgRXJyb3IgZG93bmxvYWRpbmcgZmlsZTogJHtlcnJ9YCk7DQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICAvLyDnoa7kv50gbG9hZGluZyDlnKjmr4/mrKHkuIvovb3lkI7pg73orr7nva7kuLogZmFsc2UNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQoNCiAgICAgIC8vIOS5i+WJjeeahOmZhOS7tuS4i+i9vQ0KICAgICAgLy8gaWYgKGl0ZW0uYW5uZXhVcmwpIHsNCiAgICAgIC8vICAgLyog5pyJ5paH5Lu25Zyw5Z2AIOebtOaOpeS4i+i9vSAqLw0KICAgICAgLy8gICBsZXQgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKQ0KICAgICAgLy8gICBmb3JtRGF0YS5hcHBlbmQoJ2lkJywgaXRlbS5pZCkNCiAgICAgIC8vICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgLy8gICBBUEkuZG9jdW1lbnREb3dubG9hZEtlKGZvcm1EYXRhKS50aGVuKHJlcyA9PiB7DQogICAgICAvLyAgICAgbGV0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJykNCiAgICAgIC8vICAgICBhLmhyZWYgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKHJlcy5kYXRhKQ0KICAgICAgLy8gICAgIGEuZG93bmxvYWQgPSByZXMuaGVhZGVyc1snY29udGVudC1kaXNwb3NpdGlvbiddLnNwbGl0KCdmaWxlbmFtZT0nKVsxXQ0KICAgICAgLy8gICAgIGEuY2xpY2soKQ0KICAgICAgLy8gICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICAvLyAgIH0pDQogICAgICAvLyB9IGVsc2Ugew0KICAgICAgLy8gICAvKiDmsqHmnInmlofku7bmoLzlvI8g55Sz6K+35LiL6L29ICovDQogICAgICAvLyAgIEFQSS5kb2N1bWVudERvd25sb2FkKGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIC8vICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgIC8vICAgICAgIHRoaXMuJG1zZ2JveCh7DQogICAgICAvLyAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywNCiAgICAgIC8vICAgICAgICAgbWVzc2FnZTogJ+mZhOS7tuato+WcqOWQjOatpeS4re+8jOivt+eojeWQjuS4i+i9vScsDQogICAgICAvLyAgICAgICAgIHNob3dDYW5jZWxCdXR0b246IHRydWUsDQogICAgICAvLyAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn5YWz6ZetJywNCiAgICAgIC8vICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAvLyAgICAgICAgIHNob3dDYW5jZWxCdXR0b246IGZhbHNlLA0KICAgICAgLy8gICAgICAgICBiZWZvcmVDbG9zZTogKGFjdGlvbiwgaW5zdGFuY2UsIGRvbmUpID0+IHsNCiAgICAgIC8vICAgICAgICAgICBkb25lKCkNCiAgICAgIC8vICAgICAgICAgfQ0KICAgICAgLy8gICAgICAgfSkNCiAgICAgIC8vICAgICB9IGVsc2Ugew0KICAgICAgLy8gICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICfpmYTku7bkuIvovb3lpLHotKUnLCB0eXBlOiAnZXJyb3InIH0pDQogICAgICAvLyAgICAgfQ0KICAgICAgLy8gICB9KS5jYXRjaChlcnIgPT4geyB9KQ0KICAgICAgLy8gfQ0KICAgIH0sDQogICAgLy8g57+76K+R5qCH6aKYDQogICAgdHJhbnNsYXRlVGl0bGUocm93KSB7DQogICAgICBjb25zdCBsb2FkaW5nID0gdGhpcy4kbG9hZGluZyh7DQogICAgICAgIGxvY2s6IHRydWUsDQogICAgICAgIHRleHQ6ICJMb2FkaW5nIiwNCiAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsDQogICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLA0KICAgICAgfSk7DQogICAgICBBUEkudHJhbnNsYXRpb25UaXRsZSh7DQogICAgICAgIG9yaWdpbmFsVGV4dDogcm93LnRpdGxlLA0KICAgICAgICBkb2NJZDogcm93LmRvY0lkLA0KICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICB0cmFuc2xhdGlvbkZpZWxkOiAidGl0bGUiLA0KICAgICAgICB0cmFuc2xhdGlvblR5cGU6IDEsDQogICAgICB9KQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuVGl0bGUgPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLkFydGljbGVMaXN0Ww0KICAgICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdC5maW5kSW5kZXgoKHZhbHVlKSA9PiB2YWx1ZS5pZCA9PSByb3cuaWQpDQogICAgICAgICAgXS5jblRpdGxlID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdFsNCiAgICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3QuZmluZEluZGV4KCh2YWx1ZSkgPT4gdmFsdWUuaWQgPT0gcm93LmlkKQ0KICAgICAgICAgIF0uaXNUcmFuc2xhdGVkID0gMTsNCiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7DQogICAgICAgICAgbG9hZGluZy5jbG9zZSgpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOe/u+ivkeaWh+eroA0KICAgIHRyYW5zbGF0ZUV2ZW50KHJvdykgew0KICAgICAgY29uc3QgbG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoew0KICAgICAgICBsb2NrOiB0cnVlLA0KICAgICAgICB0ZXh0OiAiTG9hZGluZyIsDQogICAgICAgIHNwaW5uZXI6ICJlbC1pY29uLWxvYWRpbmciLA0KICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwNCiAgICAgIH0pOw0KICAgICAgQVBJLnRyYW5zbGF0aW9uVGl0bGUoew0KICAgICAgICBvcmlnaW5hbFRleHQ6IHJvdy5jb250ZW50LA0KICAgICAgICBkb2NJZDogcm93LmRvY0lkLA0KICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICB0cmFuc2xhdGlvbkZpZWxkOiAiY29udGVudCIsDQogICAgICAgIHRyYW5zbGF0aW9uVHlwZTogMSwNCiAgICAgIH0pDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50ID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdFsNCiAgICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3QuZmluZEluZGV4KCh2YWx1ZSkgPT4gdmFsdWUuaWQgPT0gcm93LmlkKQ0KICAgICAgICAgIF0uY25Db250ZW50ID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdFsNCiAgICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3QuZmluZEluZGV4KCh2YWx1ZSkgPT4gdmFsdWUuaWQgPT0gcm93LmlkKQ0KICAgICAgICAgIF0uaXNUcmFuc2xhdGVkID0gMTsNCiAgICAgICAgICB0aGlzLnRyYW5zbGF0aW9uQnRuU2hvdyA9IGZhbHNlOw0KICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIEFQSS5BcmVhSW5mbyhyb3cuaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMuZm9ybS5zb3VyY2VUeXBlID0gTnVtYmVyKHRoaXMuZm9ybS5zb3VyY2VUeXBlKTsNCiAgICAgICAgdGhpcy5mb3JtLmRvY0lkID0gcm93LmRvY0lkOw0KICAgICAgICAvLyB0aGlzLmZpbGVVcmxMaXN0ID0gdGhpcy5mb3JtLmZpbGVVcmwgPyB0aGlzLmZvcm0uZmlsZVVybC5zcGxpdCgiLCIpLm1hcChpdGVtID0+IHsNCiAgICAgICAgLy8gICByZXR1cm4gew0KICAgICAgICAvLyAgICAgbmFtZTogaXRlbSwNCiAgICAgICAgLy8gICAgIHVybDogaXRlbQ0KICAgICAgICAvLyAgIH0NCiAgICAgICAgLy8gfSkgOiBbXQ0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOivpeadoeaWh+eroO+8nyInKQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgcmV0dXJuIEFQSS5tb25pdG9yaW5nRXNSZW1vdmUoeyBpZDogcm93LmlkLCBkb2NJZDogcm93LmRvY0lkIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5SZWZyZXNoKCk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgbGV0IHF1ZXJ5Rm9ybSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5mb3JtKSk7DQogICAgICAgICAgLy8gbGV0IGNvdmVyID0gU3RyaW5nKHRoaXMuZmlsZUxpc3QubWFwKGl0ZW0gPT4gaXRlbS5wYXRoKSkNCiAgICAgICAgICAvLyBxdWVyeUZvcm0uY292ZXIgPSBjb3Zlcg0KICAgICAgICAgIGFydGljbGVMaXN0RWRpdChxdWVyeUZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5SZWZyZXNoKCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyog6Ieq5a6a5LmJ5LiK5LygICovDQogICAgYXN5bmMgcmVxdWVzdExvYWQoZmlsZSkgew0KICAgICAgbGV0IGRhdGEgPSBuZXcgRm9ybURhdGEoKTsNCiAgICAgIGRhdGEuYXBwZW5kKCJjb3ZlciIsIGZpbGUuZmlsZSk7DQogICAgICBhd2FpdCB1cGxvYWRDb3ZlcihkYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmZpbGVMaXN0Lm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgaWYgKGl0ZW0udWlkID09IGZpbGUuZmlsZS51aWQpIHsNCiAgICAgICAgICAgICAgaXRlbS5wYXRoID0gcmVzcG9uc2UuaW1nVXJsOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5LiK5Lyg5oiQ5YqfIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5LiK5Lyg5aSx6LSlLOivt+eojeWAmemHjeivlSIsIHR5cGU6ICJlcnJvciIgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyog5paH5Lu26LaF5Ye66ZmQ5Yi2ICovDQogICAgZXhjZWVkKCkgew0KICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgIG1lc3NhZ2U6ICLmlofku7bkuIrkvKDotoXlh7rpmZDliLYs5pyA5aSa5Y+v5Lul5LiK5Lyg5LiJ5Liq5paH5Lu2IiwNCiAgICAgICAgdHlwZTogImluZm8iLA0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiDnp7vpmaTmlofku7YgKi8NCiAgICBoYW5kbGVSZW1vdmUoZmlsZSkgew0KICAgICAgdGhpcy5maWxlTGlzdCA9IHRoaXMuZmlsZUxpc3QuZmlsdGVyKChpdGVtKSA9PiBpdGVtICE9PSBmaWxlKTsNCiAgICB9LA0KICAgIC8vIOaWh+S7tuabtOaUuQ0KICAgIGhhbmRsZUNoYW5nZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5maWxlTGlzdCA9IGZpbGVMaXN0Ow0KICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7DQogICAgfSwNCiAgICAvLyDkuIrkvKDpmYTku7bmoKHpqowNCiAgICBiZWZvcmVVcGxvYWRVcmwoZmlsZSkgew0KICAgICAgLy8g5Yik5pat5paH5Lu25piv5ZCm5Li6ZXhjZWwNCiAgICAgIGxldCBmaWxlTmFtZSA9IGZpbGUubmFtZQ0KICAgICAgICAgIC5zdWJzdHJpbmcoZmlsZS5uYW1lLmxhc3RJbmRleE9mKCIuIikgKyAxKQ0KICAgICAgICAgIC50b0xvd2VyQ2FzZSgpLA0KICAgICAgICBjb25kaXRpb24gPQ0KICAgICAgICAgIGZpbGVOYW1lID09ICJwZGYiIHx8DQogICAgICAgICAgZmlsZU5hbWUgPT0gImRvYyIgfHwNCiAgICAgICAgICBmaWxlTmFtZSA9PSAieGxzIiB8fA0KICAgICAgICAgIGZpbGVOYW1lID09ICJwcHQiIHx8DQogICAgICAgICAgZmlsZU5hbWUgPT0gInhsc3giIHx8DQogICAgICAgICAgZmlsZU5hbWUgPT0gInBwdHgiIHx8DQogICAgICAgICAgZmlsZU5hbWUgPT0gImRvY3giOw0KICAgICAgbGV0IGZpbGVTaXplID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAxMDsNCiAgICAgIGlmICghY29uZGl0aW9uKSB7DQogICAgICAgIHRoaXMuJG5vdGlmeSh7DQogICAgICAgICAgdGl0bGU6ICLorablkYoiLA0KICAgICAgICAgIG1lc3NhZ2U6ICLkuIrkvKDmlofku7blv4XpobvmmK9wZGYsZG9jLHhscyxwcHQseGxzeCxwcHR4LGRvY3jmoLzlvI8iLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICAvKiDmlofku7blpKflsI/pmZDliLYgKi8NCiAgICAgIGlmICghZmlsZVNpemUpIHsNCiAgICAgICAgdGhpcy4kbm90aWZ5KHsNCiAgICAgICAgICB0aXRsZTogIuitpuWRiiIsDQogICAgICAgICAgbWVzc2FnZTogIuS4iuS8oOaWh+S7tueahOWkp+Wwj+S4jeiDvei2hei/hyAxME1CISIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBjb25kaXRpb24gJiYgZmlsZVNpemU7DQogICAgfSwNCiAgICAvLyDmlofku7bkuIrkvKDmiJDlip8NCiAgICB1cGxvYWRVcmxTdWNjZXNzKHJlcywgZmlsZSkgew0KICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLkuIrkvKDmiJDlip8iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgfSwNCiAgICAvLyDmlofku7bkuIrkvKDotoXlh7rpmZDliLYNCiAgICB1cGxvYWRVcmxFeGNlZWQoKSB7DQogICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgbWVzc2FnZTogIuaWh+S7tuS4iuS8oOi2heWHuumZkOWItizmnIDlpJrlj6/ku6XkuIrkvKAx5Liq5paH5Lu2IiwNCiAgICAgICAgdHlwZTogImluZm8iLA0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDmlofku7bkuIrkvKDmlrnms5UNCiAgICB1cGxvYWRVcmxSZXF1ZXN0KGZpbGUpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ub3JpZ2luYWxVcmwgIT0gbnVsbCAmJiB0aGlzLmZvcm0ub3JpZ2luYWxVcmwgIT0gIiIpIHsNCiAgICAgICAgaWYgKA0KICAgICAgICAgIHRoaXMuZm9ybS5vcmlnaW5hbFVybC5tYXRjaCgNCiAgICAgICAgICAgIC8oaHR0cHxodHRwcyk6XC9cL1tcd1wtX10rKFwuW1x3XC1fXSspKyhbXHdcLVwuLEA/Xj0lJjovflwrI10qW1x3XC1cQD9ePSUmL35cKyNdKT8vDQogICAgICAgICAgKQ0KICAgICAgICApIHsNCiAgICAgICAgICBsZXQgZGF0YSA9IG5ldyBGb3JtRGF0YSgpOw0KICAgICAgICAgIGRhdGEuYXBwZW5kKCJmaWxlIiwgZmlsZS5maWxlKTsNCiAgICAgICAgICBkYXRhLmFwcGVuZCgib3JpZ2luYWxVcmwiLCB0aGlzLmZvcm0ub3JpZ2luYWxVcmwpOw0KDQogICAgICAgICAgQVBJLnVwbG9hZEZpbGUoZGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuS4iuS8oOaIkOWKnyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLmZpbGVVcmwgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLkuIrkvKDlpLHotKUs6K+356iN5YCZ6YeN6K+VIiwgdHlwZTogImVycm9yIiB9KTsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLmZpbGVVcmwgPSAiIjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuivt+Whq+WGmeato+ehrueahOWOn+aWh+mTvuaOpSIsIHR5cGU6ICJ3YXJuaW5nIiB9KTsNCiAgICAgICAgICB0aGlzLmZpbGVVcmxMaXN0ID0gW107DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi6K+35aGr5YaZ5Y6f5paH6ZO+5o6lIiwgdHlwZTogIndhcm5pbmciIH0pOw0KICAgICAgICB0aGlzLmZpbGVVcmxMaXN0ID0gW107DQogICAgICB9DQogICAgfSwNCiAgICAvLyDliKDpmaTpmYTku7YNCiAgICB1cGxvYWRVcmxSZW1vdmUoKSB7DQogICAgICBBUEkucmVtb3ZlRmlsZSh7IGZpbGVQYXRoOiB0aGlzLmZvcm0uZmlsZVVybCB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgICB0aGlzLmZpbGVVcmxMaXN0ID0gW107DQogICAgICAgICAgdGhpcy5mb3JtLmZpbGVVcmwgPSAiIjsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuWIoOmZpOWksei0pSzor7fnqI3lgJnph43or5UiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBhcnRpY2xlU246IG51bGwsDQogICAgICAgIHRpdGxlOiBudWxsLA0KICAgICAgICBjblRpdGxlOiBudWxsLA0KICAgICAgICBzb3VyY2VUeXBlOiBudWxsLA0KICAgICAgICBzb3VyY2VOYW1lOiBudWxsLA0KICAgICAgICBzb3VyY2VTbjogbnVsbCwNCiAgICAgICAgb3JpZ2luYWxVcmw6IG51bGwsDQogICAgICAgIHNob3J0VXJsOiBudWxsLA0KICAgICAgICBhdXRob3I6IG51bGwsDQogICAgICAgIGRlc2NyaXB0aW9uOiBudWxsLA0KICAgICAgICBzdW1tYXJ5OiBudWxsLA0KICAgICAgICBjblN1bW1hcnk6IG51bGwsDQogICAgICAgIGNvdmVyOiBudWxsLA0KICAgICAgICBwdWJsaXNoVHlwZTogbnVsbCwNCiAgICAgICAgcHVibGlzaENvZGU6IG51bGwsDQogICAgICAgIHB1Ymxpc2hBcmVhOiBudWxsLA0KICAgICAgICBwdWJsaXNoVGltZTogbnVsbCwNCiAgICAgICAgbnVtYmVyTGlrZXM6IG51bGwsDQogICAgICAgIG51bWJlclJlYWRzOiBudWxsLA0KICAgICAgICBudW1iZXJDb2xsZWN0czogbnVsbCwNCiAgICAgICAgbnVtYmVyU2hhcmVzOiBudWxsLA0KICAgICAgICBudW1iZXJDb21tZW50czogbnVsbCwNCiAgICAgICAgZW1vdGlvbjogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXNlcklkOiBudWxsLA0KICAgICAgICBkZXB0SWQ6IG51bGwsDQogICAgICAgIGNvbnRlbnQ6IG51bGwsDQogICAgICAgIGNuQ29udGVudDogbnVsbCwNCiAgICAgICAgZmlsZVVybDogbnVsbCwNCiAgICAgICAgaW5kdXN0cnk6IG51bGwsDQogICAgICAgIGRvbWFpbjogbnVsbCwNCiAgICAgICAgdG1wVXJsOiBudWxsLA0KICAgICAgICBpc0ZpbmlzaDogbnVsbCwNCiAgICAgICAgZ3JvdXBJZDogbnVsbCwNCiAgICAgICAgYXBwSWQ6IG51bGwsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8vIOaJuemHj+WvvOWFpeebuOWFs+aWueazlQ0KICAgIC8vIOaJk+W8gOaJuemHj+WvvOWFpeW8ueahhg0KICAgIG9wZW5CYXRjaEltcG9ydERpYWxvZygpIHsNCiAgICAgIHRoaXMuYmF0Y2hJbXBvcnRWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuYmF0Y2hJbXBvcnRGaWxlcyA9IFtdOw0KICAgIH0sDQogICAgLy8g5paH5Lu26YCJ5oup5aSE55CGDQogICAgaGFuZGxlRmlsZVNlbGVjdChmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgLy8g5bCG5paw6YCJ5oup55qE5paH5Lu25re75Yqg5Yiw5YiX6KGo5LitDQogICAgICBjb25zdCBuZXdGaWxlcyA9IGZpbGVMaXN0Lm1hcCgoaXRlbSkgPT4gKHsNCiAgICAgICAgZmlsZU5hbWU6IGl0ZW0ubmFtZSwNCiAgICAgICAgZmlsZTogaXRlbS5yYXcsDQogICAgICAgIHNvdXJjZU5hbWU6ICIiLA0KICAgICAgfSkpOw0KICAgICAgdGhpcy5iYXRjaEltcG9ydEZpbGVzID0gbmV3RmlsZXM7DQogICAgfSwNCiAgICAvLyDliKDpmaTmlofku7YNCiAgICByZW1vdmVGaWxlKGluZGV4KSB7DQogICAgICB0aGlzLmJhdGNoSW1wb3J0RmlsZXMuc3BsaWNlKGluZGV4LCAxKTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaJuemHj+WvvOWFpQ0KICAgIGNhbmNlbEJhdGNoSW1wb3J0KCkgew0KICAgICAgdGhpcy5iYXRjaEltcG9ydFZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuYmF0Y2hJbXBvcnRGaWxlcyA9IFtdOw0KICAgICAgLy8g5riF56m65paH5Lu26YCJ5oup5ZmoDQogICAgICB0aGlzLiRyZWZzLmJhdGNoVXBsb2FkLmNsZWFyRmlsZXMoKTsNCiAgICB9LA0KICAgIC8vIOehruiupOaJuemHj+WvvOWFpQ0KICAgIGFzeW5jIGNvbmZpcm1CYXRjaEltcG9ydCgpIHsNCiAgICAgIC8vIOmqjOivgeaVsOaNrua6kOWQjeensOaYr+WQpumDveW3suWhq+WGmQ0KICAgICAgY29uc3QgZW1wdHlTb3VyY2VOYW1lcyA9IHRoaXMuYmF0Y2hJbXBvcnRGaWxlcy5maWx0ZXIoDQogICAgICAgIChpdGVtKSA9PiAhaXRlbS5zb3VyY2VOYW1lLnRyaW0oKQ0KICAgICAgKTsNCiAgICAgIGlmIChlbXB0eVNvdXJjZU5hbWVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+S4uuaJgOacieaWh+S7tuWhq+WGmeaVsOaNrua6kOWQjeensCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0cnkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KDQogICAgICAgIC8vIOWIm+W7ukZvcm1EYXRh5a+56LGhDQogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7DQoNCiAgICAgICAgLy8g5re75Yqg5paH5Lu25YiwRm9ybURhdGENCiAgICAgICAgdGhpcy5iYXRjaEltcG9ydEZpbGVzLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoImZpbGVzIiwgaXRlbS5maWxlKTsNCiAgICAgICAgfSk7DQoNCiAgICAgICAgLy8g6I635Y+W5pWw5o2u5rqQ5ZCN56ew5pWw57uEDQogICAgICAgIGNvbnN0IHNvdXJjZU5hbWVzID0gdGhpcy5iYXRjaEltcG9ydEZpbGVzDQogICAgICAgICAgLm1hcCgoaXRlbSkgPT4gaXRlbS5zb3VyY2VOYW1lKQ0KICAgICAgICAgIC5qb2luKCIsIik7DQoNCiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCJzb3VyY2VOYW1lcyIsIHNvdXJjZU5hbWVzKTsNCg0KICAgICAgICAvLyDosIPnlKjmibnph4/lr7zlhaVBUEnvvIzkvKDpgJJGb3JtRGF0YeWSjHNvdXJjZU5hbWVz5Y+C5pWwDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgQVBJLmJhdGNoSW1wb3J0UmVwb3J0cyhmb3JtRGF0YSk7DQoNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogIuaJuemHj+WvvOWFpeaIkOWKnyIsDQogICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5iYXRjaEltcG9ydFZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmJhdGNoSW1wb3J0RmlsZXMgPSBbXTsNCiAgICAgICAgICB0aGlzLiRyZWZzLmJhdGNoVXBsb2FkLmNsZWFyRmlsZXMoKTsNCiAgICAgICAgICAvLyDliLfmlrDliJfooagNCiAgICAgICAgICB0aGlzLlJlZnJlc2goKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IHJlc3BvbnNlLm1zZyB8fCAi5om56YeP5a+85YWl5aSx6LSlIiwNCiAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuaJuemHj+WvvOWFpemUmeivrzoiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLmibnph4/lr7zlhaXlpLHotKXvvIzor7fnqI3lkI7ph43or5UiLA0KICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgIH0pOw0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICByZXBvcnRBaUNoYXQoKSB7DQogICAgICB0aGlzLnNob3dEZWVwc2Vla0RpYWxvZyA9IHRydWU7DQogICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuivt+mAieaLqeimgeino+ivu+eahOaWh+eroCIsIHR5cGU6ICJ3YXJuaW5nIiB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA+IDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLor7flj6rpgInmi6nkuIDnr4fmlofnq6Dov5vooYzop6Por7siLCB0eXBlOiAid2FybmluZyIgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g6I635Y+W6YCJ5Lit55qE5paH56ugDQogICAgICBjb25zdCBzZWxlY3RlZEFydGljbGVJZCA9IHRoaXMuY2hlY2tlZENpdGllc1swXTsNCiAgICAgIGNvbnN0IHNlbGVjdGVkQXJ0aWNsZSA9IHRoaXMuQXJ0aWNsZUxpc3QuZmluZCgNCiAgICAgICAgKGl0ZW0pID0+IGl0ZW0uaWQgPT09IHNlbGVjdGVkQXJ0aWNsZUlkDQogICAgICApOw0KDQogICAgICBpZiAoc2VsZWN0ZWRBcnRpY2xlKSB7DQogICAgICAgIHRoaXMuY3VycmVudEFydGljbGUgPSBzZWxlY3RlZEFydGljbGU7DQogICAgICAgIHRoaXMuc2hvd0RlZXBzZWVrRGlhbG9nID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5pyq5om+5Yiw6YCJ5Lit55qE5paH56ugIiwgdHlwZTogImVycm9yIiB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIGFp55u45YWzDQogICAgLy8gZGlmeQ0KICAgIGFzeW5jIGRpZnlBaUNoYXQoKSB7DQogICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+WFiOmAieaLqeimgeino+ivu+eahOaWh+eroCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5pyJ5q2j5Zyo6L+b6KGM55qE6K+35rGC77yM5Lit5pat5a6DDQogICAgICBpZiAodGhpcy5pc1JlcXVlc3RpbmcpIHsNCiAgICAgICAgdGhpcy5pc0Fib3J0ZWQgPSB0cnVlOw0KICAgICAgICBpZiAodGhpcy5jdXJyZW50UmVhZGVyKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuY3VycmVudFJlYWRlci5jYW5jZWwoKTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBjb25zb2xlLmxvZygi5Lit5pat5LmL5YmN55qE6K+35rGC5aSx6LSlIiwgZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IHRydWU7DQogICAgICB0aGlzLmlzQWJvcnRlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5haURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5jaGF0TWVzc2FnZXMgPSBbXTsNCiAgICAgIHRoaXMuaXNUaGlua2luZyA9IHRydWU7DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiOt+WPlumAieS4reeahOaWh+eroA0KICAgICAgICBjb25zdCBzZWxlY3RlZEFydGljbGVzID0gdGhpcy5BcnRpY2xlTGlzdC5maWx0ZXIoKGFydGljbGUpID0+DQogICAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzLmluY2x1ZGVzKGFydGljbGUuaWQpDQogICAgICAgICk7DQogICAgICAgIGNvbnN0IHRpdGxlcyA9IHNlbGVjdGVkQXJ0aWNsZXMNCiAgICAgICAgICAubWFwKChhcnRpY2xlKSA9PiBg44CKJHthcnRpY2xlLmNuVGl0bGUgfHwgYXJ0aWNsZS50aXRsZX3jgItgKQ0KICAgICAgICAgIC5qb2luKCJcbiIpOw0KDQogICAgICAgIC8vIOiOt+WPluaWh+eroOWGheWuuQ0KICAgICAgICBjb25zdCBhcnRpY2xlc1Jlc3BvbnNlID0gYXdhaXQgZ2V0TGlzdEJ5SWRzKA0KICAgICAgICAgIHRoaXMuY2hlY2tlZENpdGllcy5qb2luKCIsIikNCiAgICAgICAgKTsNCiAgICAgICAgaWYgKCFhcnRpY2xlc1Jlc3BvbnNlLmRhdGE/Lmxlbmd0aCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigi6I635Y+W5paH56ug5YaF5a655aSx6LSlIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmoLzlvI/ljJbmlofnq6DlhoXlrrkNCiAgICAgICAgY29uc3QgYXJ0aWNsZXNDb250ZW50ID0gYXJ0aWNsZXNSZXNwb25zZS5kYXRhDQogICAgICAgICAgLm1hcCgoYXJ0aWNsZSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHRpdGxlID0NCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LmNuVGl0bGUgfHwNCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LnRpdGxlIHx8DQogICAgICAgICAgICAgICIiOw0KICAgICAgICAgICAgY29uc3QgY29udGVudCA9IGFydGljbGUuY29udGVudCB8fCAiIjsNCiAgICAgICAgICAgIHJldHVybiBg44CQ56ysICR7aW5kZXggKyAxfSDnr4fmlofnq6DjgJHjgIoke3RpdGxlfeOAi1xuXG4ke2NvbnRlbnR9YDsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5qb2luKCJcblxuLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4iKTsNCg0KICAgICAgICAvLyDmt7vliqDnlKjmiLfmtojmga8NCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaCh7DQogICAgICAgICAgcm9sZTogInVzZXIiLA0KICAgICAgICAgIGNvbnRlbnQ6IGDluK7miJHmt7Hluqbop6Por7vku6XkuIske3RoaXMuY2hlY2tlZENpdGllcy5sZW5ndGh956+H5paH56ug77yaXG4ke3RpdGxlc31gLA0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDliJvlu7pBSea2iOaBrw0KICAgICAgICBjb25zdCBhaU1lc3NhZ2UgPSB7DQogICAgICAgICAgcm9sZTogImFzc2lzdGFudCIsDQogICAgICAgICAgY29udGVudDogIiIsDQogICAgICAgIH07DQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goYWlNZXNzYWdlKTsNCg0KICAgICAgICAvLyDmnoTlu7rmj5DnpLror40NCiAgICAgICAgY29uc3QgcHJvbXB0ID0NCiAgICAgICAgICB0aGlzLmFydGljbGVBaVByb21wdA0KICAgICAgICAgICAgLnJlcGxhY2UoImFydGljbGVMZW5ndGgiLCB0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoKQ0KICAgICAgICAgICAgLnJlcGxhY2UoL1wmZ3Q7L2csICI+IikgKw0KICAgICAgICAgIGAqKuS7peS4i+aYr+W+heWkhOeQhueahOaWh+eroO+8mioqXG5cbiR7YXJ0aWNsZXNDb250ZW50fWA7DQoNCiAgICAgICAgLy8g6LCD55SoQUnmjqXlj6MNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkaWZ5QWlRYSgNCiAgICAgICAgICBhcnRpY2xlc0NvbnRlbnQsDQogICAgICAgICAgInN0cmVhbWluZyIsDQogICAgICAgICAgImRpZnkuYXJ0aWNsZS5hcGlrZXkiDQogICAgICAgICk7DQogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFJ5o6l5Y+j6LCD55So5aSx6LSlIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlpITnkIbmtYHlvI/lk43lupQNCiAgICAgICAgY29uc3QgcmVhZGVyID0gcmVzcG9uc2UuYm9keS5nZXRSZWFkZXIoKTsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gcmVhZGVyOw0KICAgICAgICBjb25zdCBkZWNvZGVyID0gbmV3IFRleHREZWNvZGVyKCk7DQogICAgICAgIGxldCBidWZmZXIgPSAiIjsNCiAgICAgICAgbGV0IHBlbmRpbmdCdWZmZXIgPSAiIjsgLy8g55So5LqO5a2Y5YKo5b6F5aSE55CG55qE5LiN5a6M5pW05pWw5o2uDQogICAgICAgIGxldCBpc0luVGhpbmtUYWcgPSBmYWxzZTsgLy8g5paw5aKe77ya5qCH6K6w5piv5ZCm5ZyodGhpbmvmoIfnrb7lhoUNCg0KICAgICAgICAvLyDlsIZVbmljb2Rl6L2s5LmJ5a2X56ymKFx1WFhYWCnovazmjaLkuLrlrp7pmYXlrZfnrKYNCiAgICAgICAgY29uc3QgZGVjb2RlVW5pY29kZSA9IChzdHIpID0+IHsNCiAgICAgICAgICByZXR1cm4gc3RyLnJlcGxhY2UoL1xcdVtcZEEtRmEtZl17NH0vZywgKG1hdGNoKSA9PiB7DQogICAgICAgICAgICByZXR1cm4gU3RyaW5nLmZyb21DaGFyQ29kZShwYXJzZUludChtYXRjaC5yZXBsYWNlKC9cXHUvZywgIiIpLCAxNikpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9Ow0KDQogICAgICAgIC8vIOabtOaWsOWGheWuueeahOWHveaVsA0KICAgICAgICBjb25zdCB1cGRhdGVDb250ZW50ID0gKG5ld0NvbnRlbnQpID0+IHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgY29uc3QgcmVuZGVyZWRDb250ZW50ID0gbWFya2VkKG5ld0NvbnRlbnQsIHRoaXMubWFya2Rvd25PcHRpb25zKTsNCiAgICAgICAgICAgIGFpTWVzc2FnZS5jb250ZW50ID0gcmVuZGVyZWRDb250ZW50Ow0KDQogICAgICAgICAgICAvLyDnoa7kv53mtojmga/lrrnlmajmu5rliqjliLDlupXpg6gNCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgY2hhdE1lc3NhZ2VzID0gdGhpcy4kcmVmcy5jaGF0TWVzc2FnZXM7DQogICAgICAgICAgICAgIGlmIChjaGF0TWVzc2FnZXMpIHsNCiAgICAgICAgICAgICAgICBjaGF0TWVzc2FnZXMuc2Nyb2xsVG9wID0gY2hhdE1lc3NhZ2VzLnNjcm9sbEhlaWdodDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIua4suafk+WGheWuueaXtuWHuumUmToiLCBlcnJvcik7DQogICAgICAgICAgfQ0KICAgICAgICB9Ow0KDQogICAgICAgIC8vIOWkhOeQhua1geW8j+WTjeW6lA0KICAgICAgICB3aGlsZSAodHJ1ZSkgew0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3suS4reaWrQ0KICAgICAgICAgIGlmICh0aGlzLmlzQWJvcnRlZCkgew0KICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBYm9ydEVycm9yIik7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29uc3QgeyBkb25lLCB2YWx1ZSB9ID0gYXdhaXQgcmVhZGVyLnJlYWQoKTsNCg0KICAgICAgICAgIGlmIChkb25lKSB7DQogICAgICAgICAgICAvLyDlpITnkIbmnIDlkI7lj6/og73liankvZnnmoTmlbDmja4NCiAgICAgICAgICAgIGlmIChwZW5kaW5nQnVmZmVyKSB7DQogICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgY29uc3QgbGFzdERhdGEgPSBKU09OLnBhcnNlKHBlbmRpbmdCdWZmZXIpOw0KICAgICAgICAgICAgICAgIGlmIChsYXN0RGF0YS5hbnN3ZXIpIHsNCiAgICAgICAgICAgICAgICAgIC8vIOino+eggVVuaWNvZGXovazkuYnlrZfnrKYNCiAgICAgICAgICAgICAgICAgIGNvbnN0IGRlY29kZWRBbnN3ZXIgPSBkZWNvZGVVbmljb2RlKGxhc3REYXRhLmFuc3dlcik7DQogICAgICAgICAgICAgICAgICBidWZmZXIgKz0gZGVjb2RlZEFuc3dlcjsNCiAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQoYnVmZmVyKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oIuWkhOeQhuacgOWQjueahOaVsOaNruaXtuWHuumUmToiLCBlKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgY29uc3QgY2h1bmsgPSBkZWNvZGVyLmRlY29kZSh2YWx1ZSk7DQogICAgICAgICAgcGVuZGluZ0J1ZmZlciArPSBjaHVuazsNCg0KICAgICAgICAgIC8vIOWkhOeQhuWujOaVtOeahOaVsOaNruihjA0KICAgICAgICAgIHdoaWxlIChwZW5kaW5nQnVmZmVyLmluY2x1ZGVzKCJcbiIpKSB7DQogICAgICAgICAgICBjb25zdCBuZXdsaW5lSW5kZXggPSBwZW5kaW5nQnVmZmVyLmluZGV4T2YoIlxuIik7DQogICAgICAgICAgICBjb25zdCBsaW5lID0gcGVuZGluZ0J1ZmZlci5zbGljZSgwLCBuZXdsaW5lSW5kZXgpLnRyaW0oKTsNCiAgICAgICAgICAgIHBlbmRpbmdCdWZmZXIgPSBwZW5kaW5nQnVmZmVyLnNsaWNlKG5ld2xpbmVJbmRleCArIDEpOw0KDQogICAgICAgICAgICBpZiAoIWxpbmUgfHwgbGluZSA9PT0gImRhdGE6IiB8fCAhbGluZS5zdGFydHNXaXRoKCJkYXRhOiIpKSB7DQogICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICBjb25zdCBkYXRhID0gbGluZS5zbGljZSg1KS50cmltKCk7DQogICAgICAgICAgICAgIGlmIChkYXRhID09PSAiW0RPTkVdIikgew0KICAgICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgY29uc3QganNvbkRhdGEgPSBKU09OLnBhcnNlKGRhdGEpOw0KICAgICAgICAgICAgICBpZiAoIWpzb25EYXRhLmFuc3dlcikgew0KICAgICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLy8g6Lez6L+H54m55q6K5a2X56ymDQogICAgICAgICAgICAgIGlmIChqc29uRGF0YS5hbnN3ZXIgPT09ICJgYGAiIHx8IGpzb25EYXRhLmFuc3dlciA9PT0gIm1hcmtkb3duIikgew0KICAgICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLy8g6Kej56CBVW5pY29kZei9rOS5ieWtl+espg0KICAgICAgICAgICAgICBsZXQgYW5zd2VyID0gZGVjb2RlVW5pY29kZShqc29uRGF0YS5hbnN3ZXIpOw0KDQogICAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuWMheWQqzx0aGluaz7lvIDlp4vmoIfnrb4NCiAgICAgICAgICAgICAgaWYgKGFuc3dlci5pbmNsdWRlcygiPHRoaW5rPiIpKSB7DQogICAgICAgICAgICAgICAgaXNJblRoaW5rVGFnID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICBjb250aW51ZTsgLy8g6Lez6L+H5YyF5ZCrPHRoaW5rPueahOmDqOWIhg0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5YyF5ZCrPC90aGluaz7nu5PmnZ/moIfnrb4NCiAgICAgICAgICAgICAgaWYgKGFuc3dlci5pbmNsdWRlcygiPC90aGluaz4iKSkgew0KICAgICAgICAgICAgICAgIGlzSW5UaGlua1RhZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgIGNvbnRpbnVlOyAvLyDot7Pov4fljIXlkKs8L3RoaW5rPueahOmDqOWIhg0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLy8g5Y+q5pyJ5LiN5ZyodGhpbmvmoIfnrb7lhoXnmoTlhoXlrrnmiY3kvJrooqvmt7vliqDliLBidWZmZXLkuK0NCiAgICAgICAgICAgICAgaWYgKCFpc0luVGhpbmtUYWcgJiYgYW5zd2VyKSB7DQogICAgICAgICAgICAgICAgYnVmZmVyICs9IGFuc3dlcjsNCiAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KGJ1ZmZlcik7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHsNCiAgICAgICAgICAgICAgY29uc29sZS53YXJuKCLop6PmnpDmlbDmja7ooYzml7blh7rplJk6Iiwgew0KICAgICAgICAgICAgICAgIGxpbmUsDQogICAgICAgICAgICAgICAgZXJyb3I6IHBhcnNlRXJyb3IubWVzc2FnZSwNCiAgICAgICAgICAgICAgICBwZW5kaW5nQnVmZmVyLA0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCJBSeino+ivu+WHuumUmToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZXJyb3IubWVzc2FnZSB8fCAiQUnop6Por7vlpLHotKXvvIzor7fnqI3lkI7ph43or5UiKTsNCiAgICAgICAgaWYgKHRoaXMuY2hhdE1lc3NhZ2VzWzFdKSB7DQogICAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXNbMV0uY29udGVudCA9ICLmirHmrYnvvIzmnI3liqHlmajnuYHlv5nvvIzor7fnqI3lkI7lho3or5UiOw0KICAgICAgICB9DQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSBudWxsOw0KICAgICAgICBpZiAodGhpcy5haURpYWxvZ1Zpc2libGUpIHsNCiAgICAgICAgICB0aGlzLmlzVGhpbmtpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyBPbGxhbWENCiAgICBhc3luYyBvbGxhbWFBaUNoYXQoKSB7DQogICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+WFiOmAieaLqeimgeino+ivu+eahOaWh+eroCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5pyJ5q2j5Zyo6L+b6KGM55qE6K+35rGC77yM5Lit5pat5a6DDQogICAgICBpZiAodGhpcy5pc1JlcXVlc3RpbmcpIHsNCiAgICAgICAgdGhpcy5pc0Fib3J0ZWQgPSB0cnVlOw0KICAgICAgICBpZiAodGhpcy5jdXJyZW50UmVhZGVyKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuY3VycmVudFJlYWRlci5jYW5jZWwoKTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBjb25zb2xlLmxvZygi5Lit5pat5LmL5YmN55qE6K+35rGC5aSx6LSlIiwgZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC8vIOetieW+heS5i+WJjeeahOivt+axgueKtuaAgea4heeQhuWujOaIkA0KICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSB0cnVlOw0KICAgICAgdGhpcy5pc0Fib3J0ZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMuYWlEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzID0gW107DQogICAgICB0aGlzLmlzVGhpbmtpbmcgPSB0cnVlOw0KDQogICAgICB0cnkgew0KICAgICAgICAvLyDojrflj5bpgInkuK3nmoTmlofnq6ANCiAgICAgICAgY29uc3Qgc2VsZWN0ZWRBcnRpY2xlcyA9IHRoaXMuQXJ0aWNsZUxpc3QuZmlsdGVyKChhcnRpY2xlKSA9Pg0KICAgICAgICAgIHRoaXMuY2hlY2tlZENpdGllcy5pbmNsdWRlcyhhcnRpY2xlLmlkKQ0KICAgICAgICApOw0KICAgICAgICBjb25zdCB0aXRsZXMgPSBzZWxlY3RlZEFydGljbGVzDQogICAgICAgICAgLm1hcCgoYXJ0aWNsZSkgPT4gYOOAiiR7YXJ0aWNsZS5jblRpdGxlIHx8IGFydGljbGUudGl0bGV944CLYCkNCiAgICAgICAgICAuam9pbigiXG4iKTsNCg0KICAgICAgICAvLyDojrflj5bmlofnq6DlhoXlrrkNCiAgICAgICAgY29uc3QgYXJ0aWNsZXNSZXNwb25zZSA9IGF3YWl0IGdldExpc3RCeUlkcygNCiAgICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMuam9pbigiLCIpDQogICAgICAgICk7DQogICAgICAgIGlmICghYXJ0aWNsZXNSZXNwb25zZS5kYXRhPy5sZW5ndGgpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIuiOt+WPluaWh+eroOWGheWuueWksei0pSIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qC85byP5YyW5paH56ug5YaF5a65DQogICAgICAgIGNvbnN0IGFydGljbGVzQ29udGVudCA9IGFydGljbGVzUmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIC5tYXAoKGFydGljbGUsIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB0aXRsZSA9DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy5jblRpdGxlIHx8DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy50aXRsZSB8fA0KICAgICAgICAgICAgICAiIjsNCiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSBhcnRpY2xlLmNvbnRlbnQgfHwgIiI7DQogICAgICAgICAgICByZXR1cm4gYOOAkOesrCAke2luZGV4ICsgMX0g56+H5paH56ug44CR44CKJHt0aXRsZX3jgItcblxuJHtjb250ZW50fWA7DQogICAgICAgICAgfSkNCiAgICAgICAgICAuam9pbigiXG5cbi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuIik7DQoNCiAgICAgICAgLy8g5re75Yqg55So5oi35raI5oGvDQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goew0KICAgICAgICAgIHJvbGU6ICJ1c2VyIiwNCiAgICAgICAgICBjb250ZW50OiBg5biu5oiR5rex5bqm6Kej6K+75Lul5LiLJHt0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3Rofeevh+aWh+eroO+8mlxuJHt0aXRsZXN9YCwNCiAgICAgICAgfSk7DQoNCiAgICAgICAgLy8g5Yib5bu6QUnmtojmga8NCiAgICAgICAgY29uc3QgYWlNZXNzYWdlID0gew0KICAgICAgICAgIHJvbGU6ICJhc3Npc3RhbnQiLA0KICAgICAgICAgIGNvbnRlbnQ6ICIiLA0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLmNoYXRNZXNzYWdlcy5wdXNoKGFpTWVzc2FnZSk7DQoNCiAgICAgICAgLy8g5p6E5bu65o+Q56S66K+NDQogICAgICAgIGNvbnN0IHByb21wdCA9DQogICAgICAgICAgdGhpcy5hcnRpY2xlQWlQcm9tcHQNCiAgICAgICAgICAgIC5yZXBsYWNlKCJhcnRpY2xlTGVuZ3RoIiwgdGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCkNCiAgICAgICAgICAgIC5yZXBsYWNlKC9cJmd0Oy9nLCAiPiIpICsNCiAgICAgICAgICBgKirku6XkuIvmmK/lvoXlpITnkIbnmoTmlofnq6DvvJoqKlxuXG4ke2FydGljbGVzQ29udGVudH1gOw0KDQogICAgICAgIC8vIOiwg+eUqEFJ5o6l5Y+jDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgb2xsYW1hQWlRYShwcm9tcHQsIHRydWUpOw0KICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBSeaOpeWPo+iwg+eUqOWksei0pSIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aSE55CG5rWB5byP5ZON5bqUDQogICAgICAgIGNvbnN0IHJlYWRlciA9IHJlc3BvbnNlLmJvZHkuZ2V0UmVhZGVyKCk7DQogICAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IHJlYWRlcjsgLy8g5L+d5a2Y5b2T5YmN55qEIHJlYWRlcg0KICAgICAgICBjb25zdCBkZWNvZGVyID0gbmV3IFRleHREZWNvZGVyKCk7DQogICAgICAgIGxldCBidWZmZXIgPSAiIjsNCiAgICAgICAgbGV0IGxhc3RVcGRhdGVUaW1lID0gRGF0ZS5ub3coKTsNCiAgICAgICAgbGV0IGlzVGhpbmtDb250ZW50ID0gZmFsc2U7DQogICAgICAgIGxldCB0ZW1wQnVmZmVyID0gIiI7DQoNCiAgICAgICAgLy8g5pu05paw5YaF5a6555qE5Ye95pWwDQogICAgICAgIGNvbnN0IHVwZGF0ZUNvbnRlbnQgPSAobmV3Q29udGVudCkgPT4gew0KICAgICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gRGF0ZS5ub3coKTsNCiAgICAgICAgICAvLyDmjqfliLbmm7TmlrDpopHnjofvvIzpgb/lhY3ov4fkuo7popHnuYHnmoRET03mm7TmlrANCiAgICAgICAgICBpZiAoY3VycmVudFRpbWUgLSBsYXN0VXBkYXRlVGltZSA+PSA1MCkgew0KICAgICAgICAgICAgYWlNZXNzYWdlLmNvbnRlbnQgPSBuZXdDb250ZW50Ow0KICAgICAgICAgICAgbGFzdFVwZGF0ZVRpbWUgPSBjdXJyZW50VGltZTsNCiAgICAgICAgICAgIC8vIOehruS/nea2iOaBr+WuueWZqOa7muWKqOWIsOW6lemDqA0KICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICBjb25zdCBjaGF0TWVzc2FnZXMgPSB0aGlzLiRyZWZzLmNoYXRNZXNzYWdlczsNCiAgICAgICAgICAgICAgaWYgKGNoYXRNZXNzYWdlcykgew0KICAgICAgICAgICAgICAgIGNoYXRNZXNzYWdlcy5zY3JvbGxUb3AgPSBjaGF0TWVzc2FnZXMuc2Nyb2xsSGVpZ2h0Ow0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH07DQoNCiAgICAgICAgLy8g5aSE55CG5rWB5byP5ZON5bqUDQogICAgICAgIGNvbnN0IHByb2Nlc3NTdHJlYW0gPSBhc3luYyAoKSA9PiB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIHdoaWxlICh0cnVlKSB7DQogICAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3suS4reaWrQ0KICAgICAgICAgICAgICBpZiAodGhpcy5pc0Fib3J0ZWQpIHsNCiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFib3J0RXJyb3IiKTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIGNvbnN0IHsgZG9uZSwgdmFsdWUgfSA9IGF3YWl0IHJlYWRlci5yZWFkKCk7DQogICAgICAgICAgICAgIGlmIChkb25lKSB7DQogICAgICAgICAgICAgICAgaWYgKGJ1ZmZlci5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KGJ1ZmZlcik7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgY29uc3QgY2h1bmsgPSBkZWNvZGVyLmRlY29kZSh2YWx1ZSk7DQogICAgICAgICAgICAgIGNvbnN0IGxpbmVzID0gY2h1bmsuc3BsaXQoIlxuIikuZmlsdGVyKChsaW5lKSA9PiBsaW5lLnRyaW0oKSk7DQoNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBsaW5lIG9mIGxpbmVzKSB7DQogICAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICAgIGNvbnN0IGpzb25EYXRhID0gSlNPTi5wYXJzZShsaW5lKTsNCiAgICAgICAgICAgICAgICAgIGlmICghanNvbkRhdGEucmVzcG9uc2UpIGNvbnRpbnVlOw0KDQogICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGpzb25EYXRhLnJlc3BvbnNlOw0KDQogICAgICAgICAgICAgICAgICAvLyDot7Pov4fnibnmrorlrZfnrKYNCiAgICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZSA9PT0gImBgYCIgfHwgcmVzcG9uc2UgPT09ICJtYXJrZG93biIpIHsNCiAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXIgKz0gcmVzcG9uc2U7DQoNCiAgICAgICAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuWMheWQq+WujOaVtOeahHRoaW5r5qCH562+5a+5DQogICAgICAgICAgICAgICAgICB3aGlsZSAodHJ1ZSkgew0KICAgICAgICAgICAgICAgICAgICBjb25zdCB0aGlua1N0YXJ0SW5kZXggPSB0ZW1wQnVmZmVyLmluZGV4T2YoIjx0aGluaz4iKTsNCiAgICAgICAgICAgICAgICAgICAgY29uc3QgdGhpbmtFbmRJbmRleCA9IHRlbXBCdWZmZXIuaW5kZXhPZigiPC90aGluaz4iKTsNCg0KICAgICAgICAgICAgICAgICAgICBpZiAodGhpbmtTdGFydEluZGV4ID09PSAtMSAmJiB0aGlua0VuZEluZGV4ID09PSAtMSkgew0KICAgICAgICAgICAgICAgICAgICAgIC8vIOayoeaciXRoaW5r5qCH562+77yM55u05o6l5re75Yqg5YiwYnVmZmVyDQogICAgICAgICAgICAgICAgICAgICAgaWYgKCFpc1RoaW5rQ29udGVudCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgYnVmZmVyICs9IHRlbXBCdWZmZXI7DQogICAgICAgICAgICAgICAgICAgICAgICAvLyDkvb/nlKhtYXJrZWTmuLLmn5NtYXJrZG93buWGheWuuQ0KICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChtYXJrZWQoYnVmZmVyLCB0aGlzLm1hcmtkb3duT3B0aW9ucykpOw0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICB0ZW1wQnVmZmVyID0gIiI7DQogICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpbmtTdGFydEluZGV4ICE9PSAtMSAmJiB0aGlua0VuZEluZGV4ID09PSAtMSkgew0KICAgICAgICAgICAgICAgICAgICAgIC8vIOWPquacieW8gOWni+agh+etvu+8jOetieW+hee7k+adn+agh+etvg0KICAgICAgICAgICAgICAgICAgICAgIGlzVGhpbmtDb250ZW50ID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgICBpZiAodGhpbmtTdGFydEluZGV4ID4gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgYnVmZmVyICs9IHRlbXBCdWZmZXIuc3Vic3RyaW5nKDAsIHRoaW5rU3RhcnRJbmRleCk7DQogICAgICAgICAgICAgICAgICAgICAgICAvLyDkvb/nlKhtYXJrZWTmuLLmn5NtYXJrZG93buWGheWuuQ0KICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChtYXJrZWQoYnVmZmVyLCB0aGlzLm1hcmtkb3duT3B0aW9ucykpOw0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICB0ZW1wQnVmZmVyID0gdGVtcEJ1ZmZlci5zdWJzdHJpbmcodGhpbmtTdGFydEluZGV4KTsNCiAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlua1N0YXJ0SW5kZXggPT09IC0xICYmIHRoaW5rRW5kSW5kZXggIT09IC0xKSB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g5Y+q5pyJ57uT5p2f5qCH562+77yM56e76Zmk5LmL5YmN55qE5YaF5a65DQogICAgICAgICAgICAgICAgICAgICAgaXNUaGlua0NvbnRlbnQgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICB0ZW1wQnVmZmVyID0gdGVtcEJ1ZmZlci5zdWJzdHJpbmcodGhpbmtFbmRJbmRleCArIDgpOw0KICAgICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgIC8vIOacieWujOaVtOeahHRoaW5r5qCH562+5a+5DQogICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaW5rU3RhcnRJbmRleCA+IDApIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJ1ZmZlciArPSB0ZW1wQnVmZmVyLnN1YnN0cmluZygwLCB0aGlua1N0YXJ0SW5kZXgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgLy8g5L2/55SobWFya2Vk5riy5p+TbWFya2Rvd27lhoXlrrkNCiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQobWFya2VkKGJ1ZmZlciwgdGhpcy5tYXJrZG93bk9wdGlvbnMpKTsNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgdGVtcEJ1ZmZlciA9IHRlbXBCdWZmZXIuc3Vic3RyaW5nKHRoaW5rRW5kSW5kZXggKyA4KTsNCiAgICAgICAgICAgICAgICAgICAgICBpc1RoaW5rQ29udGVudCA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikgew0KICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCLml6DmlYjnmoRKU09O6KGM77yM5bey6Lez6L+HIiwgew0KICAgICAgICAgICAgICAgICAgICBsaW5lLA0KICAgICAgICAgICAgICAgICAgICBlcnJvcjogcGFyc2VFcnJvci5tZXNzYWdlLA0KICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSBjYXRjaCAoc3RyZWFtRXJyb3IpIHsNCiAgICAgICAgICAgIGlmIChzdHJlYW1FcnJvci5tZXNzYWdlID09PSAiQWJvcnRFcnJvciIpIHsNCiAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBYm9ydEVycm9yIik7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCLlpITnkIbmtYHlvI/lk43lupTml7blh7rplJk6Iiwgc3RyZWFtRXJyb3IpOw0KICAgICAgICAgICAgdGhyb3cgc3RyZWFtRXJyb3I7DQogICAgICAgICAgfQ0KICAgICAgICB9Ow0KDQogICAgICAgIGF3YWl0IHByb2Nlc3NTdHJlYW0oKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIC8vIOWIpOaWreaYr+WQpuaYr+S4reaWreWvvOiHtOeahOmUmeivrw0KICAgICAgICBpZiAoZXJyb3IubWVzc2FnZSA9PT0gIkFib3J0RXJyb3IiKSB7DQogICAgICAgICAgY29uc29sZS5sb2coIuivt+axguW3suiiq+S4reaWrSIpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBjb25zb2xlLmVycm9yKCJBSeino+ivu+WHuumUmToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZXJyb3IubWVzc2FnZSB8fCAiQUnop6Por7vlpLHotKXvvIzor7fnqI3lkI7ph43or5UiKTsNCiAgICAgICAgaWYgKHRoaXMuY2hhdE1lc3NhZ2VzWzFdKSB7DQogICAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXNbMV0uY29udGVudCA9ICLmirHmrYnvvIzmnI3liqHlmajnuYHlv5nvvIzor7fnqI3lkI7lho3or5UiOw0KICAgICAgICB9DQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSBudWxsOyAvLyDmuIXnkIblvZPliY3nmoQgcmVhZGVyDQogICAgICAgIC8vIOWPquacieWcqOayoeacieiiq+S4reaWreeahOaDheWGteS4i+aJjemHjee9rueKtuaAgQ0KICAgICAgICBpZiAodGhpcy5haURpYWxvZ1Zpc2libGUpIHsNCiAgICAgICAgICB0aGlzLmlzVGhpbmtpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyBkZWVwc2Vlaw0KICAgIGFzeW5jIGRlZXBzZWVrQWlDaGF0KCkgew0KICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7flhYjpgInmi6nopoHop6Por7vnmoTmlofnq6AiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOacieato+WcqOi/m+ihjOeahOivt+axgu+8jOS4reaWreWugw0KICAgICAgaWYgKHRoaXMuaXNSZXF1ZXN0aW5nKSB7DQogICAgICAgIHRoaXMuaXNBYm9ydGVkID0gdHJ1ZTsNCiAgICAgICAgaWYgKHRoaXMuY3VycmVudFJlYWRlcikgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBhd2FpdCB0aGlzLmN1cnJlbnRSZWFkZXIuY2FuY2VsKCk7DQogICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgY29uc29sZS5sb2coIuS4reaWreS5i+WJjeeahOivt+axguWksei0pSIsIGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDnrYnlvoXkuYvliY3nmoTor7fmsYLnirbmgIHmuIXnkIblrozmiJANCiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gdHJ1ZTsNCiAgICAgIHRoaXMuaXNBYm9ydGVkID0gZmFsc2U7DQogICAgICB0aGlzLmFpRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmNoYXRNZXNzYWdlcyA9IFtdOw0KICAgICAgdGhpcy5pc1RoaW5raW5nID0gdHJ1ZTsNCg0KICAgICAgY29uc3Qgc2VsZWN0ZWRBcnRpY2xlcyA9IHRoaXMuQXJ0aWNsZUxpc3QuZmlsdGVyKChhcnRpY2xlKSA9Pg0KICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMuaW5jbHVkZXMoYXJ0aWNsZS5pZCkNCiAgICAgICk7DQogICAgICBjb25zdCB0aXRsZXMgPSBzZWxlY3RlZEFydGljbGVzDQogICAgICAgIC5tYXAoKGFydGljbGUpID0+IGDjgIoke2FydGljbGUuY25UaXRsZSB8fCBhcnRpY2xlLnRpdGxlfeOAi2ApDQogICAgICAgIC5qb2luKCJcbiIpOw0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBhcnRpY2xlc1Jlc3BvbnNlID0gYXdhaXQgZ2V0TGlzdEJ5SWRzKA0KICAgICAgICAgIHRoaXMuY2hlY2tlZENpdGllcy5qb2luKCIsIikNCiAgICAgICAgKTsNCiAgICAgICAgaWYgKCFhcnRpY2xlc1Jlc3BvbnNlLmRhdGEgfHwgIWFydGljbGVzUmVzcG9uc2UuZGF0YS5sZW5ndGgpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkZhaWxlZCB0byBnZXQgYXJ0aWNsZSBjb250ZW50cyIpOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgYXJ0aWNsZXNDb250ZW50ID0gYXJ0aWNsZXNSZXNwb25zZS5kYXRhDQogICAgICAgICAgLm1hcCgoYXJ0aWNsZSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHRpdGxlID0NCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LmNuVGl0bGUgfHwNCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LnRpdGxlIHx8DQogICAgICAgICAgICAgICIiOw0KICAgICAgICAgICAgY29uc3QgY29udGVudCA9IGFydGljbGUuY29udGVudCB8fCAiIjsNCiAgICAgICAgICAgIHJldHVybiBg44CQ56ysICR7aW5kZXggKyAxfSDnr4fmlofnq6DjgJHjgIoke3RpdGxlfeOAi1xuXG4ke2NvbnRlbnR9YDsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5qb2luKCJcblxuLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4iKTsNCg0KICAgICAgICAvLyDmt7vliqDnlKjmiLfmtojmga8NCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaCh7DQogICAgICAgICAgcm9sZTogInVzZXIiLA0KICAgICAgICAgIGNvbnRlbnQ6IGDluK7miJHmt7Hluqbop6Por7vku6XkuIske3RoaXMuY2hlY2tlZENpdGllcy5sZW5ndGh956+H5paH56ug77yaXG4ke3RpdGxlc31gLA0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDliJvlu7pBSea2iOaBr+W5tua3u+WKoOWIsOWvueivneS4rQ0KICAgICAgICBjb25zdCBhaU1lc3NhZ2UgPSB7DQogICAgICAgICAgcm9sZTogImFzc2lzdGFudCIsDQogICAgICAgICAgY29udGVudDogIiIsDQogICAgICAgIH07DQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goYWlNZXNzYWdlKTsNCiAgICAgICAgdGhpcy5pc1RoaW5raW5nID0gdHJ1ZTsNCg0KICAgICAgICBjb25zdCBwcm9tcHQgPQ0KICAgICAgICAgIHRoaXMuYXJ0aWNsZUFpUHJvbXB0DQogICAgICAgICAgICAucmVwbGFjZSgiYXJ0aWNsZUxlbmd0aCIsIHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGgpDQogICAgICAgICAgICAucmVwbGFjZSgvXCZndDsvZywgIj4iKSArDQogICAgICAgICAgYFxuXG4qKuS7peS4i+aYr+W+heWkhOeQhueahOaWh+eroO+8mioqXG5cbiR7YXJ0aWNsZXNDb250ZW50fWA7DQoNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkZWVwc2Vla0FpUWEocHJvbXB0LCB0cnVlKTsNCg0KICAgICAgICBpZiAocmVzcG9uc2Uub2spIHsNCiAgICAgICAgICBjb25zdCByZWFkZXIgPSByZXNwb25zZS5ib2R5LmdldFJlYWRlcigpOw0KICAgICAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IHJlYWRlcjsgLy8g5L+d5a2Y5b2T5YmN55qEIHJlYWRlcg0KICAgICAgICAgIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTsNCiAgICAgICAgICBsZXQgYnVmZmVyID0gIiI7DQogICAgICAgICAgbGV0IGxhc3RVcGRhdGVUaW1lID0gRGF0ZS5ub3coKTsNCg0KICAgICAgICAgIGNvbnN0IHVwZGF0ZUNvbnRlbnQgPSAobmV3Q29udGVudCkgPT4gew0KICAgICAgICAgICAgY29uc3QgY3VycmVudFRpbWUgPSBEYXRlLm5vdygpOw0KICAgICAgICAgICAgaWYgKGN1cnJlbnRUaW1lIC0gbGFzdFVwZGF0ZVRpbWUgPj0gNTApIHsNCiAgICAgICAgICAgICAgYWlNZXNzYWdlLmNvbnRlbnQgPSBuZXdDb250ZW50Ow0KICAgICAgICAgICAgICBsYXN0VXBkYXRlVGltZSA9IGN1cnJlbnRUaW1lOw0KICAgICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgICAgY29uc3QgY2hhdE1lc3NhZ2VzID0gdGhpcy4kcmVmcy5jaGF0TWVzc2FnZXM7DQogICAgICAgICAgICAgICAgaWYgKGNoYXRNZXNzYWdlcykgew0KICAgICAgICAgICAgICAgICAgY2hhdE1lc3NhZ2VzLnNjcm9sbFRvcCA9IGNoYXRNZXNzYWdlcy5zY3JvbGxIZWlnaHQ7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgd2hpbGUgKHRydWUpIHsNCiAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3suS4reaWrQ0KICAgICAgICAgICAgaWYgKHRoaXMuaXNBYm9ydGVkKSB7DQogICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQWJvcnRFcnJvciIpOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpOw0KICAgICAgICAgICAgaWYgKGRvbmUpIHsNCiAgICAgICAgICAgICAgaWYgKGJ1ZmZlci5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChidWZmZXIpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBjb25zdCBjaHVuayA9IGRlY29kZXIuZGVjb2RlKHZhbHVlKTsNCiAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgIGNvbnN0IGxpbmVzID0gY2h1bmsuc3BsaXQoIlxuIik7DQoNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBsaW5lIG9mIGxpbmVzKSB7DQogICAgICAgICAgICAgICAgaWYgKCFsaW5lLnRyaW0oKSB8fCAhbGluZS5zdGFydHNXaXRoKCJkYXRhOiAiKSkgY29udGludWU7DQoNCiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gbGluZS5zbGljZSg1KTsNCiAgICAgICAgICAgICAgICBpZiAoZGF0YSA9PT0gIltET05FXSIpIGJyZWFrOw0KDQogICAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICAgIGNvbnN0IGpzb25EYXRhID0gSlNPTi5wYXJzZShkYXRhKTsNCiAgICAgICAgICAgICAgICAgIGlmIChqc29uRGF0YS5jaG9pY2VzPy5bMF0/LmRlbHRhPy5jb250ZW50KSB7DQogICAgICAgICAgICAgICAgICAgIGxldCBjb250ZW50ID0ganNvbkRhdGEuY2hvaWNlc1swXS5kZWx0YS5jb250ZW50Ow0KDQogICAgICAgICAgICAgICAgICAgIC8vIOi3s+i/h+eJueauiuWtl+espg0KICAgICAgICAgICAgICAgICAgICBpZiAoY29udGVudCA9PT0gImBgYCIgfHwgY29udGVudCA9PT0gIm1hcmtkb3duIikgew0KICAgICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgICAgYnVmZmVyICs9IGNvbnRlbnQ7DQogICAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQoYnVmZmVyKTsNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7DQogICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCJFcnJvciBwYXJzaW5nIEpTT046IiwgcGFyc2VFcnJvcik7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIkVycm9yIHByb2Nlc3NpbmcgY2h1bms6IiwgZSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiUmVxdWVzdCBmYWlsZWQiKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgLy8g5Yik5pat5piv5ZCm5piv5Lit5pat5a+86Ie055qE6ZSZ6K+vDQogICAgICAgIGlmIChlcnJvci5tZXNzYWdlID09PSAiQWJvcnRFcnJvciIpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygi6K+35rGC5bey6KKr5Lit5patIik7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIkFJIENoYXQgRXJyb3I6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCJBSeino+ivu+Wksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICBpZiAodGhpcy5jaGF0TWVzc2FnZXNbMV0pIHsNCiAgICAgICAgICB0aGlzLmNoYXRNZXNzYWdlc1sxXS5jb250ZW50ID0gIuaKseatie+8jOacjeWKoeWZqOe5geW/me+8jOivt+eojeWQjuWGjeivlSI7DQogICAgICAgIH0NCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IG51bGw7IC8vIOa4heeQhuW9k+WJjeeahCByZWFkZXINCiAgICAgICAgLy8g5Y+q5pyJ5Zyo5rKh5pyJ6KKr5Lit5pat55qE5oOF5Ya15LiL5omN6YeN572u54q25oCBDQogICAgICAgIGlmICh0aGlzLmFpRGlhbG9nVmlzaWJsZSkgew0KICAgICAgICAgIHRoaXMuaXNUaGlua2luZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWFs+mXrUFJ5a+56K+dDQogICAgY2xvc2VBaURpYWxvZygpIHsNCiAgICAgIHRoaXMuaXNBYm9ydGVkID0gdHJ1ZTsgLy8g6K6+572u5Lit5pat5qCH5b+XDQogICAgICBpZiAodGhpcy5jdXJyZW50UmVhZGVyKSB7DQogICAgICAgIHRoaXMuY3VycmVudFJlYWRlci5jYW5jZWwoKTsgLy8g5Lit5pat5b2T5YmN55qE6K+75Y+WDQogICAgICB9DQogICAgICB0aGlzLmFpRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5jaGF0TWVzc2FnZXMgPSBbXTsNCiAgICAgIHRoaXMuaXNUaGlua2luZyA9IGZhbHNlOw0KICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IG51bGw7DQogICAgfSwNCiAgICBhcnRpY2xlQWlDaGF0KCkgew0KICAgICAgaWYgKHRoaXMuYWlQbGF0Zm9ybSA9PT0gImRpZnkiKSB7DQogICAgICAgIHRoaXMuZGlmeUFpQ2hhdCgpOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLmFpUGxhdGZvcm0gPT09ICJvbGxhbWEiKSB7DQogICAgICAgIHRoaXMub2xsYW1hQWlDaGF0KCk7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWlQbGF0Zm9ybSA9PT0gImRlZXBzZWVrIikgew0KICAgICAgICB0aGlzLmRlZXBzZWVrQWlDaGF0KCk7DQogICAgICB9DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["MainArticle.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm+DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MainArticle.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\r\n  <div class=\"main\" v-loading=\"loading\">\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Monitor'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    >\r\n                    </span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    是否与科技有关:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\" @click=\"resultEvent('BatchGeneration')\"></i>\r\n          </p> -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-document-add\"\r\n              style=\"font-size: 24px\"\r\n              title=\"发布到每日最新热点\"\r\n              @click=\"publishHot\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\" v-if=\"$route.query.domain\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek深度解读\"\r\n              @click=\"articleAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n              >Deepseek深度解读</span\r\n            >\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          :style=\"\r\n            $route.query.domain\r\n              ? 'height: calc(100vh - 170px)'\r\n              : 'height: calc(100vh - 389px)'\r\n          \"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    是否与科技有关:\r\n                    <span class=\"infomation\">\r\n                      {{ $route.query.domain ? \"有\" : getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleUpdate(item)\"\r\n                  v-hasPermi=\"['article:articleList:edit']\"\r\n                  >{{ ` 修改 ` }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  v-hasPermi=\"['article:list:remove']\"\r\n                  >{{ `删除` }}</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Special'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    是否与科技有关:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'infoInter'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n            </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Wechat'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(50vh - 200px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_kqcb\">\r\n              <div>\r\n                <div class=\"ArticlTop\" style=\"width: 55%\">\r\n                  <p style=\"line-height: 20px\">\r\n                    <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                      {{ null }} </el-checkbox\r\n                    >&nbsp;&nbsp;\r\n                    <a href=\"#\"\r\n                      ><span style=\"padding: 0 10px 0 0\"\r\n                        >{{\r\n                          (currentPage - 1) * pageSize + key + 1\r\n                        }}&nbsp;</span\r\n                      ><span\r\n                        class=\"title_Article\"\r\n                        @click=\"openNewView(item)\"\r\n                        v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                      ></span\r\n                      >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                    >\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3; cursor: pointer\"\r\n                    >\r\n                      原文链接\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n                <div class=\"info_flex\" style=\"width: auto\">\r\n                  <div class=\"ArticlBottom\">\r\n                    <div>\r\n                      类型:\r\n                      <span class=\"infomation\">\r\n                        {{ typeHandle(item.sourceType) }}\r\n                      </span>\r\n                    </div>\r\n                    <div>\r\n                      媒体来源:\r\n                      <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.author\">\r\n                      作者:\r\n                      <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                    </div>\r\n                    <div>\r\n                      发布时间:\r\n                      <span class=\"infomation\">{{\r\n                        formatPublishTime(\r\n                          item.publishTime,\r\n                          item.webstePublishTime\r\n                        )\r\n                      }}</span>\r\n                    </div>\r\n                    <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                    <!-- <p>标签A</p> -->\r\n                  </div>\r\n                  <div class=\"ArticlBottom\">\r\n                    <div v-if=\"item.industryName\">\r\n                      所属行业:\r\n                      <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.domain\">\r\n                      所属领域:\r\n                      <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p> -->\r\n            <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            <!-- </div> -->\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'artificialIntelligence' || flag == 'networkSecurity'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div\r\n              class=\"Articl_left\"\r\n              :style=\"item.fileUrl ? 'width: 85%' : 'width: 100%'\"\r\n            >\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题:</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    是否与科技有关:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\" v-if=\"item.fileUrl\">\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <!-- <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"primary\" plain @click=\"handleUpdate(item)\"\r\n                    v-hasPermi=\"['article:articleList:edit']\">{{ ` 修改 ` }}</el-button>\r\n                </p>\r\n                <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"danger\" plain @click=\"handleDelete(item)\"\r\n                    v-hasPermi=\"['article:list:remove']\">{{ `删除` }}</el-button>\r\n                </p> -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'zhikubaogao'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-upload2\"\r\n              style=\"font-size: 22px; color: #1296db\"\r\n              title=\"批量导入报告\"\r\n              @click=\"openBatchImportDialog\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载报告\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除报告\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek报告解读\"\r\n              @click=\"reportAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"reportAiChat\"\r\n              >Deepseek报告解读</span\r\n            >\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          style=\"height: calc(100vh - 310px)\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <!-- <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span> -->\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <div>\r\n                    是否与科技有关:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  >删除</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"pagination\" ref=\"pagination\">\r\n      <el-pagination\r\n        :small=\"false\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"currentPage\"\r\n        :page-sizes=\"[10, 20, 30, 40, 50]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"->, total, sizes, prev, pager, next, jumper\"\r\n        :total=\"total\"\r\n      ></el-pagination>\r\n    </div>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title\r\n      :visible.sync=\"tagDialog\"\r\n      width=\"20%\"\r\n      :before-close=\"closeTag\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form\r\n        label-position=\"left\"\r\n        label-width=\"40px\"\r\n        :model=\"formLabelAlign\"\r\n        ref=\"ruleForm\"\r\n      >\r\n        <el-form-item label=\"行业\" prop=\"industry\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.industry\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            placeholder=\"请选择行业\"\r\n            :filterable=\"true\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.industryName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"领域\" prop=\"domain\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.domain\"\r\n            filterable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            clearable\r\n            placeholder=\"请选择领域\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.fieldName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"标签\" prop=\"tag\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.tag\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n            filterable\r\n            allow-create\r\n            default-first-option\r\n            placeholder=\"请添加文章标签\"\r\n          >\r\n            <!-- <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>-->\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeTag\" size=\"mini\">取 消</el-button>\r\n        <el-button @click=\"SubmitTag\" type=\"primary\" size=\"mini\"\r\n          >确 定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n    <el-drawer\r\n      @open=\"openDrawer\"\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"drawer\"\r\n      custom-class=\"drawer_box\"\r\n      direction=\"rtl\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <div\r\n        v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n        style=\"width: 100%; text-align: right; padding-right: 10px\"\r\n      >\r\n        <el-button\r\n          @click=\"documentDownload('drawer')\"\r\n          :type=\"drawerInfo.fileUrl ? 'primary' : 'info'\"\r\n          size=\"mini\"\r\n          plain\r\n          :disabled=\"!drawerInfo.fileUrl\"\r\n          >{{ \"附件下载\" }}</el-button\r\n        >\r\n        <!-- <el-button @click=\"resultEvent('drawer')\" type=\"primary\" size=\"mini\" plain>\r\n          {{ drawerInfo.snapshotUrl\r\n            ? '下载快照' : '生成快照' }}\r\n        </el-button> -->\r\n        <!-- v-if=\"translationBtnShow\" -->\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          plain\r\n          @click=\"translateEvent(drawerInfo)\"\r\n          >翻译内容</el-button\r\n        >\r\n      </div>\r\n      <template slot=\"title\">\r\n        <span class=\"drawer_Title\">{{\r\n          drawerInfo.cnTitle || drawerInfo.title\r\n        }}</span>\r\n      </template>\r\n      <div class=\"drawer_Style\">\r\n        <p class=\"title\">\r\n          {{ drawerInfo.cnTitle || drawerInfo.title }}\r\n          <!-- v-if=\"!drawerInfo.cnTitle\" -->\r\n          <i\r\n            class=\"icon-taizhangtranslate\"\r\n            @click=\"translateTitle(drawerInfo)\"\r\n          ></i>\r\n        </p>\r\n        <p style=\"text-align: center\">\r\n          <span class=\"source\">{{ drawerInfo.sourceName }}</span>\r\n          <span class=\"time\">{{ drawerInfo.publishTime }}</span>\r\n        </p>\r\n        <div\r\n          style=\"user-select: text !important; line-height: 30px\"\r\n          v-html=\"\r\n            drawerInfo.cnContent &&\r\n            drawerInfo.cnContent.replace(/<img\\b[^>]*>/gi, '')\r\n          \"\r\n        ></div>\r\n        <el-empty\r\n          description=\"当前文章暂无数据\"\r\n          v-if=\"!drawerInfo.cnContent\"\r\n        ></el-empty>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"'修改文章'\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"dialog_Box\">\r\n        <el-form\r\n          :model=\"form\"\r\n          class=\"form_Style\"\r\n          label-position=\"top\"\r\n          ref=\"form\"\r\n          :rules=\"rules\"\r\n          size=\"mini\"\r\n        >\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"文章标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"中文标题\" prop=\"cnTitle\">\r\n                <el-input v-model=\"form.cnTitle\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n                <el-select\r\n                  v-model=\"form.sourceType\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.id\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"媒体来源\" prop=\"sourceName\">\r\n                <el-select\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  filterable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeLists\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.name\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发布时间\" prop=\"publishTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.publishTime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"datetime\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  placeholder=\"选择日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"文章作者\" prop=\"author\">\r\n                <el-input\r\n                  v-model=\"form.author\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"原文链接\" prop=\"originalUrl\">\r\n            <el-input v-model=\"form.originalUrl\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"摘要\" prop=\"summary\">\r\n            <el-input\r\n              v-model=\"form.summary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文摘要\" prop=\"cnSummary\">\r\n            <el-input\r\n              v-model=\"form.cnSummary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文内容\" prop=\"cnContent\">\r\n            <editor v-model=\"form.cnContent\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <el-form-item label=\"文章内容\" prop=\"content\">\r\n            <editor v-model=\"form.content\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <!-- <el-form-item label=\"封面图片\" prop=\"cover\">\r\n            <el-upload action=\"#\" ref=\"upload\" :limit=\"3\" :on-exceed=\"exceed\" list-type=\"picture-card\"\r\n              :auto-upload=\"false\" :headers=\"vertifyUpload.headers\" :file-list=\"fileList\" :on-change=\"handleChange\"\r\n              :http-request=\"requestLoad\">\r\n              <i slot=\"default\" class=\"el-icon-plus\"></i>\r\n              <div slot=\"file\" slot-scope=\"{ file }\">\r\n                <img class=\"el-upload-list__item-thumbnail\" :src=\"file.url\" alt=\"文件缩略图加载失败\" />\r\n                <span class=\"el-upload-list__item-actions\">\r\n                  <span v-if=\"!disabled\" class=\"el-upload-list__item-delete\" @click=\"handleRemove(file)\">\r\n                    <i class=\"el-icon-delete\"></i>\r\n                  </span>\r\n                </span>\r\n              </div>\r\n            </el-upload>\r\n          </el-form-item>\r\n          <el-form-item label=\"上传附件\" prop=\"fileUrl\">\r\n            <el-upload class=\"upload-demo\" :action=\"fileUrlurl\" :before-upload=\"beforeUploadUrl\" multiple :limit=\"1\"\r\n              :http-request=\"uploadUrlRequest\" :on-success=\"uploadUrlSuccess\" :file-list=\"fileUrlList\"\r\n              :on-exceed=\"uploadUrlExceed\" :on-remove=\"uploadUrlRemove\">\r\n              <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>\r\n            </el-upload>\r\n          </el-form-item> -->\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量导入弹框 -->\r\n    <el-dialog\r\n      title=\"批量导入报告\"\r\n      :visible.sync=\"batchImportVisible\"\r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"batch-import-container\">\r\n        <!-- 文件选择区域 -->\r\n        <div class=\"file-select-area\">\r\n          <el-upload\r\n            ref=\"batchUpload\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :show-file-list=\"false\"\r\n            multiple\r\n            :on-change=\"handleFileSelect\"\r\n            accept=\".pdf,.doc,.docx,.txt\"\r\n          >\r\n            <el-button type=\"primary\" icon=\"el-icon-upload\">选择文件</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">\r\n              支持选择多个文件，格式：PDF、DOC、DOCX、TXT\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n\r\n        <!-- 文件列表表格 -->\r\n        <div class=\"file-table-area\" v-if=\"batchImportFiles.length > 0\">\r\n          <el-table\r\n            :data=\"batchImportFiles\"\r\n            border\r\n            style=\"width: 100%; margin-top: 20px\"\r\n            max-height=\"300\"\r\n          >\r\n            <el-table-column prop=\"fileName\" label=\"文件名称\" width=\"300\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.fileName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"sourceName\" label=\"数据源名称\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input\r\n                  v-model=\"scope.row.sourceName\"\r\n                  placeholder=\"请输入数据源名称\"\r\n                  size=\"small\"\r\n                ></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"removeFile(scope.$index)\"\r\n                  style=\"color: #f56c6c\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelBatchImport\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"confirmBatchImport\"\r\n          :disabled=\"batchImportFiles.length === 0\"\r\n        >\r\n          确 认\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- Deepseek报告解读弹窗 -->\r\n    <deepseek-report-dialog\r\n      :visible.sync=\"showDeepseekDialog\"\r\n      :article-data=\"currentArticle\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport request from \"@/utils/request\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { MessageBox } from \"element-ui\";\r\nimport axios from \"axios\";\r\nimport { getListClassify } from \"@/api/article/classify\";\r\nimport { saveAs } from \"file-saver\";\r\nimport { blobValidate, tansParams } from \"@/utils/ruoyi\";\r\nimport { articleListEdit, uploadCover } from \"@/api/articleCrawler/list\";\r\nimport DeepseekReportDialog from \"./DeepseekReportDialog.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\nimport { getListByIds } from \"@/api/article/articleHistory\";\r\n\r\nexport default {\r\n  props: {\r\n    downLoadShow: {\r\n      /* 下载按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    editShow: {\r\n      /* 编辑按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    copyShow: {\r\n      /* 复制按钮 */ reuqired: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    height: {\r\n      type: Number,\r\n      default: 655,\r\n    },\r\n    currentPage: {\r\n      reuqired: true,\r\n      default: 1,\r\n    },\r\n    pageSize: {\r\n      reuqired: true,\r\n      default: 50,\r\n    },\r\n    total: {\r\n      reuqired: true,\r\n      default: 0,\r\n    },\r\n    ArticleList: {\r\n      required: true,\r\n      default: [],\r\n    },\r\n    flag: {\r\n      required: true,\r\n    },\r\n    SeachData: {\r\n      required: true,\r\n    },\r\n    keywords: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    // 报告类型字段\r\n    sourceType: {\r\n      default: \"\",\r\n    },\r\n  },\r\n  components: {\r\n    DeepseekReportDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      regExpImg: /^\\n$/,\r\n      reportId: \"\",\r\n      reportOptions: [],\r\n      dialogVisible: false,\r\n      checkedCities: [] /* 多选 */,\r\n      checked: false /* 全选 */,\r\n      html: \"\",\r\n      text: \"\",\r\n      that: this,\r\n      tagShow: false,\r\n      isIndeterminate: true,\r\n      count: 0,\r\n      separate: {},\r\n      /* 标签功能 */\r\n      tagDialog: false,\r\n      formLabelAlign: {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      },\r\n      options: [],\r\n      options1: [],\r\n      tagItem: {} /* 标签对象 */,\r\n      areaList: [] /* 领域 */,\r\n      industry: [] /* 行业 */,\r\n      num: 0,\r\n      timer: null,\r\n      drawer: false,\r\n      drawerInfo: {},\r\n      AreaId: null,\r\n      translationBtnShow: null,\r\n      open: false,\r\n      sourceTypeList: [], // 数据源分类\r\n      sourceLists: [], // 数据源列表\r\n      sourceTypeLists: [],\r\n      form: {}, // 表单参数\r\n      rules: {\r\n        // 表单校验\r\n        title: [{ required: true, message: \"文章标题为必填项\" }],\r\n        content: [{ required: true, message: \"文章详情为必填项\" }],\r\n        publishTime: [{ required: true, message: \"发布时间为必填项\" }],\r\n        cnTitle: [{ required: true, message: \"中文名称为必填项\" }],\r\n        sourceType: [{ required: true, message: \"平台类型为必填项\" }],\r\n        originalUrl: [{ required: true, message: \"原文为必填项\" }],\r\n        summary: [{ required: true, message: \"请填写摘要\" }],\r\n        // cnSummary: [{ required: true, message: '请填写中文摘要' }],\r\n        sn: [{ required: true, message: \"请填写文章地址唯一识别号\" }],\r\n      },\r\n      vertifyUpload: {\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: {\r\n          Authorization: \"Bearer \" + getToken(),\r\n          ContentType: \"application/json;charset=utf-8\",\r\n        },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/article/articleList/cover\",\r\n      },\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      fileUrlurl:\r\n        process.env.VUE_APP_BASE_API + \"/article/articleList/upload/file\",\r\n      showSummary: true,\r\n      // 批量导入相关数据\r\n      batchImportVisible: false,\r\n      batchImportFiles: [],\r\n      // Deepseek报告解读弹窗\r\n      showDeepseekDialog: false,\r\n      currentArticle: {},\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n    };\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    dialogVisible: function (newVal, oldVal) {\r\n      if (newVal) {\r\n        API.getNewBuilt({ sourceType: this.sourceType }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 'formLabelAlign.industry': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options1 = this.industry\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    // 'formLabelAlign.domain': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options = this.areaList\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    \"SeachData.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        this.Refresh();\r\n      },\r\n      deep: true,\r\n    },\r\n    \"form.sourceType\": {\r\n      handler(newVal, oldVal) {\r\n        this.sourceTypeLists = this.sourceLists.filter((item) => {\r\n          return item.type == newVal;\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    if (\r\n      this.flag !== \"MonitorUse\" &&\r\n      this.flag !== \"specialSubjectUse\" &&\r\n      this.flag !== \"Wechat\"\r\n    ) {\r\n      this.openDialog();\r\n    }\r\n    if (this.flag !== \"Wechat\") {\r\n      getListClassify().then((res) => {\r\n        this.sourceTypeList = res.data;\r\n      });\r\n      API.getSourceList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.sourceLists = data.data;\r\n        }\r\n      });\r\n    }\r\n    if (this.$route.query.domain) {\r\n      getConfigKey(\"sys.ai.platform\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.aiPlatform = res.msg;\r\n        }\r\n      });\r\n      getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.articleAiPrompt = res.msg;\r\n        }\r\n      });\r\n      // 获取用户头像\r\n      this.userAvatar = this.$store.getters.avatar;\r\n    }\r\n\r\n    this.showSummary = true;\r\n  },\r\n  updated() {},\r\n  filters: {},\r\n  methods: {\r\n    // 处理科技相关字段的显示映射\r\n    getTechnologyLabel(value) {\r\n      const mapping = {\r\n        0: \"无\",\r\n        1: \"有\",\r\n        2: \"其他\",\r\n        3: \"待定\"\r\n      };\r\n      return mapping[value] || \"未定义\";\r\n    },\r\n    // 处理发布时间的显示\r\n    formatPublishTime(publishTime, webstePublishTime) {\r\n      // 格式化publishTime为年月日\r\n      const formattedPublishTime = this.parseTime(publishTime, \"{y}-{m}-{d}\");\r\n\r\n      // 如果webstePublishTime不存在，直接返回publishTime\r\n      if (!webstePublishTime) {\r\n        return formattedPublishTime;\r\n      }\r\n\r\n      let formattedWebsteTime = \"\";\r\n      // 处理不同格式的webstePublishTime\r\n      if (webstePublishTime) {\r\n        // 处理2025-04-12 10:09:21.971191格式（包含连字符的标准格式）\r\n        if (webstePublishTime.includes(\"-\")) {\r\n          const dateMatch = webstePublishTime.match(/(\\d{4})-(\\d{2})-(\\d{2})/);\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2];\r\n            const day = dateMatch[3];\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年04月14日 11:29:22格式（中文年月日格式，带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\") &&\r\n          webstePublishTime.includes(\"日\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})日/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年4月15格式（中文年月格式，不带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025/04/14 11:29:22格式（斜杠分隔的格式）\r\n        else if (webstePublishTime.includes(\"/\")) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})\\/(\\d{1,2})\\/(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        } else {\r\n          // 其他格式直接使用原值\r\n          formattedWebsteTime = webstePublishTime;\r\n        }\r\n      }\r\n\r\n      // 比较年月日是否相同\r\n      if (formattedPublishTime === formattedWebsteTime) {\r\n        return formattedPublishTime;\r\n      } else {\r\n        return `${formattedPublishTime} / ${webstePublishTime}`;\r\n      }\r\n    },\r\n\r\n    // 检查文本是否有实际内容（去除HTML标签后）\r\n    hasActualContent(text) {\r\n      if (!text) {\r\n        return false;\r\n      }\r\n      // 去除HTML标签\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      // 检查是否有中文、英文、数字等实际内容\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n    // 关键字替换\r\n    changeColor(str) {\r\n      let Str = str;\r\n      if (Str) {\r\n        let keywords = this.keywords.split(\",\");\r\n        keywords.map((keyitem, keyindex) => {\r\n          if (keyitem && keyitem.length > 0) {\r\n            // 匹配关键字正则\r\n            let replaceReg = new RegExp(keyitem, \"g\");\r\n            // 高亮替换v-html值\r\n            let replaceString =\r\n              '<span class=\"highlight\"' +\r\n              ' style=\"color: red;\">' +\r\n              keyitem +\r\n              \"</span>\";\r\n            Str = Str.replace(replaceReg, replaceString);\r\n          }\r\n        });\r\n      }\r\n      return Str;\r\n    },\r\n    /* 下载Excel */\r\n    async downLoadExcel() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要导出的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.flag == \"specialSubjectUse\") {\r\n        API.downLoadExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n        });\r\n      } else {\r\n        await API.downLoadExportExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n\r\n          // saveAs(blob, `source_${new Date().getTime()}.xlsx`)\r\n        });\r\n      }\r\n    },\r\n    batchDelete() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要删除的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.checkedCities.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 发布到每日最新热点 */\r\n    publishHot() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({\r\n          message: \"请选择要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.checkedCities.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 返回顶部动画 */\r\n    mainScorll() {\r\n      var scrollStep = -this.$refs.scroll.scrollTop / (800 / 15); // 计算每一步滚动的距离\r\n      var scrollInterval = setInterval(() => {\r\n        if (this.$refs.scroll.scrollTop !== 0) {\r\n          this.$refs.scroll.scrollBy(0, scrollStep); // 按照给定步长滚动窗口\r\n        } else {\r\n          clearInterval(scrollInterval); // 到达顶部时清除定时器\r\n        }\r\n      }, 15);\r\n    },\r\n    scrollChange() {\r\n      clearTimeout(this.timer);\r\n      this.timer = setTimeout(() => {\r\n        this.stopScroll();\r\n      }, 500);\r\n      // this.$refs.pagination.style.opacity = 0\r\n      // this.$refs.pagination.style.transition = '0'\r\n    } /* 滚动事件 */,\r\n    stopScroll() {\r\n      // this.$refs.pagination.style.transition = '1s'\r\n      // this.$refs.pagination.style.opacity = 1\r\n    },\r\n    /* 下载 */\r\n    downLoad() {\r\n      this.dialogVisible = true;\r\n    },\r\n    /* 每页条数变化 */\r\n    handleSizeChange(num) {\r\n      this.$emit(\"handleSizeChange\", num);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 页码变化 */\r\n    handleCurrentChange(current) {\r\n      this.$emit(\"handleCurrentChange\", current);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 收藏 */\r\n    async collect(item) {\r\n      /* 点击列表收藏 */\r\n      if (item.id) {\r\n        this.checkedCities = [item.id];\r\n      }\r\n      /* 未选择提示 */\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要收藏的文章\", type: \"info\" });\r\n        return;\r\n      }\r\n      /* 收藏 */\r\n      if (!item.favorites) {\r\n        let res = await API.collectApi([item.id]);\r\n        if (res.code) {\r\n          this.$message({\r\n            message: \"收藏成功,请前往个人中心查看\",\r\n            type: \"success\",\r\n          });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"收藏失败\", type: \"info\" });\r\n      } else {\r\n        let res = await API.cocelCollect([item.id]);\r\n        if (res.code) {\r\n          this.$message({ message: \"已取消收藏\", type: \"success\" });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"取消收藏失败\", type: \"info\" });\r\n      }\r\n    },\r\n    /* 一键复制 */\r\n    copyText(item) {\r\n      navigator.clipboard\r\n        .writeText(item.cnTitle)\r\n        .then(() => {\r\n          this.$message({ message: \"已成功复制到剪贴板\", type: \"success\" });\r\n        })\r\n        .catch(function () {\r\n          alert(\"复制失败\");\r\n        });\r\n    },\r\n    typeHandle(data) {\r\n      if (data == 1) {\r\n        return \"微信公众号\";\r\n      } else if (data == 2) {\r\n        return \"网站\";\r\n      } else if (data == 3) {\r\n        return \"手动录入\";\r\n      }\r\n    },\r\n    /* 选择事件 */\r\n    handleCheckedCitiesChange(value) {\r\n      this.checkedCities = value;\r\n    },\r\n    /* 全选 */\r\n    handleCheckAllChange(val) {\r\n      this.checkedCities = val ? this.ArticleList.map((item) => item.id) : [];\r\n      this.isIndeterminate = false;\r\n    },\r\n    /* 刷新 */\r\n    Refresh() {\r\n      this.$emit(\"Refresh\");\r\n    },\r\n    /*确定添加到报告 */\r\n    async reportSubmit() {\r\n      this.dialogVisible = false;\r\n      let keyWordList = [];\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      /* 单独添加 */\r\n      if (this.separate.id) {\r\n        // let keyword = Object.keys(this.separate.keywordCount)\r\n        keyWordList.push({\r\n          reportId: this.reportId,\r\n          listId: this.separate.id,\r\n          listSn: this.separate.sn,\r\n        });\r\n      } else {\r\n        /* 批量添加 */\r\n        if (this.checkedCities == \"\")\r\n          return this.$message({\r\n            message: \"请选择要添加的数据\",\r\n            type: \"warning\",\r\n          });\r\n        this.checkedCities.forEach((item) => {\r\n          let article = this.ArticleList.filter((value) => value.id == item);\r\n          keyWordList.push({\r\n            reportId: this.reportId,\r\n            listId: item,\r\n            listSn: article[0].sn,\r\n          });\r\n        });\r\n      }\r\n      let res = await API.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.$emit(\"Refresh\");\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.separate = {};\r\n      this.reportId = \"\";\r\n      this.checkedCities = [];\r\n      this.checked = false;\r\n    },\r\n    /* 单独添加报告 */\r\n    separateAdd(item) {\r\n      this.dialogVisible = true;\r\n      this.separate = item;\r\n    },\r\n    /* 跳转新页面 */\r\n    openNewView(item, isLink) {\r\n      if (isLink) {\r\n        if (item.originalUrl) {\r\n          window.open(item.originalUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n        return;\r\n      }\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n      // this.drawerInfo = item\r\n      // this.drawer = true\r\n    },\r\n    /* 文章打标签 */\r\n    tagHandler(item) {\r\n      this.tagDialog = true;\r\n      this.tagItem = item;\r\n      if (item.industry) {\r\n        this.formLabelAlign.industry = item.industry\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      if (item.domain) {\r\n        this.formLabelAlign.domain = item.domain\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      this.formLabelAlign.tag = item.tags ? item.tags.split(\",\") : \"\";\r\n    },\r\n    /* 获取领域和分类 */\r\n    async openDialog() {\r\n      await API.areaList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.areaList = data.data;\r\n          this.options = data.data;\r\n          API.industry().then((value) => {\r\n            this.industry = value.data;\r\n            this.options1 = value.data;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 筛选领域 */\r\n    remoteEvent(query) {\r\n      this.options = this.areaList.filter((item) => item.fieldName == query);\r\n    },\r\n    /* 筛选行业 */\r\n    remoteIndustry(query) {\r\n      this.options1 = this.industry.filter(\r\n        (item) => item.industryName == query\r\n      );\r\n    },\r\n    async SubmitTag() {\r\n      let params = {\r\n        domain: String(this.formLabelAlign.domain),\r\n        industry: String(this.formLabelAlign.industry),\r\n        tags: String(this.formLabelAlign.tag),\r\n        articleId: String(this.tagItem.id),\r\n        docId: this.tagItem.docId ? String(this.tagItem.docId) : \"\",\r\n      };\r\n      let res = await API.tagAdd(params);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"保存成功\", type: \"success\" });\r\n        setTimeout(() => {\r\n          this.Refresh();\r\n        }, 1000);\r\n      } else {\r\n        this.$message({ message: \"保存失败\", type: \"error\" });\r\n      }\r\n      this.closeTag();\r\n    },\r\n    closeTag() {\r\n      this.$refs[\"ruleForm\"].resetFields();\r\n      this.formLabelAlign = {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      };\r\n      this.tagDialog = false;\r\n    },\r\n    async hotIncrease(item) {\r\n      let isWhether = JSON.parse(item.isWhether);\r\n      let res = await API.tagAdd({\r\n        articleId: item.id,\r\n        isWhether: +!Boolean(isWhether),\r\n      });\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"操作成功\", type: \"success\" });\r\n        this.Refresh();\r\n      } else {\r\n        this.$message({ message: \"操作失败\", type: \"error\" });\r\n      }\r\n    },\r\n    async openDrawer() {\r\n      let docId = this.drawerInfo.docId;\r\n      await API.AreaInfo(this.drawerInfo.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.drawerInfo = res.data;\r\n          this.drawerInfo.docId = docId;\r\n          /* 将字符串中的\\n替换为<br> */\r\n          this.translationBtnShow = !this.drawerInfo.cnContent;\r\n          if (this.drawerInfo.cnContent || this.drawerInfo.content) {\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\\\n/g, (a, b, c) => {\r\n              return \"<br>\";\r\n            });\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\${[^}]+}/g, \"<br>\");\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(\"|xa0\", \"\");\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /* 所属行业处理 */\r\n    industryHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.industry) {\r\n        ids = item.industry.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.industry.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.industryName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    domainHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.domain) {\r\n        ids = item.domain.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.areaList.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.fieldName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    /* 快照生成 */\r\n    resultEvent(item) {\r\n      if (item == \"BatchGeneration\" && this.checkedCities.length == 0) {\r\n        this.$message.warning(\"请先选择文章\");\r\n        return;\r\n      }\r\n      let ids = null;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (item == \"drawer\") {\r\n        ids = [this.drawerInfo.id];\r\n        if (this.drawerInfo.snapshotUrl) zhuangtai = \"查看\";\r\n        url = this.drawerInfo.snapshotUrl;\r\n      } else if (item == \"BatchGeneration\") {\r\n        ids = this.checkedCities;\r\n      } else {\r\n        ids = [item.id];\r\n        if (item.snapshotUrl) zhuangtai = \"查看\";\r\n        url = item.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        if (this.flag == \"MonitorUse\") {\r\n          API.downLoadExportKe(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        } else {\r\n          API.downLoadExportZhuan(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        }\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n    /* 附件下载 */\r\n    async documentDownload(item) {\r\n      this.loading = true;\r\n      if (item.fileUrl) {\r\n        const urls = item.fileUrl.split(\",\");\r\n        for (const [index, url] of urls.entries()) {\r\n          if (url.indexOf(\"https://\") === -1) {\r\n            setTimeout(async () => {\r\n              await this.downLoadFun(url, index, item.cnTitle || item.title);\r\n            }, index * 500);\r\n          } else {\r\n            this.$message.error(\"附件还没同步到当前系统，暂时无法下载\");\r\n          }\r\n        }\r\n      }\r\n      this.loading = false;\r\n    },\r\n\r\n    async downLoadFun(url, index, title) {\r\n      let formData = new FormData();\r\n      formData.append(\"fileUrl\", url);\r\n\r\n      try {\r\n        const response = await API.downloadFile(formData);\r\n        const isBlob = blobValidate(response);\r\n\r\n        if (isBlob) {\r\n          const blob = new Blob([response]);\r\n          let list = url.split(\"/\");\r\n          let fileName = list[list.length - 1];\r\n          saveAs(blob, fileName);\r\n        } else {\r\n          const resText = await response.text();\r\n          const rspObj = JSON.parse(resText);\r\n          const errMsg =\r\n            errorCode[rspObj.code] || rspObj.msg || errorCode[\"default\"];\r\n          this.$message.error(errMsg);\r\n        }\r\n      } catch (err) {\r\n        // this.$message.error(`Error downloading file: ${err}`);\r\n      } finally {\r\n        // 确保 loading 在每次下载后都设置为 false\r\n        this.loading = false;\r\n      }\r\n\r\n      // 之前的附件下载\r\n      // if (item.annexUrl) {\r\n      //   /* 有文件地址 直接下载 */\r\n      //   let formData = new FormData()\r\n      //   formData.append('id', item.id)\r\n      //   this.loading = true\r\n      //   API.documentDownloadKe(formData).then(res => {\r\n      //     let a = document.createElement('a')\r\n      //     a.href = URL.createObjectURL(res.data)\r\n      //     a.download = res.headers['content-disposition'].split('filename=')[1]\r\n      //     a.click()\r\n      //     this.loading = false\r\n      //   })\r\n      // } else {\r\n      //   /* 没有文件格式 申请下载 */\r\n      //   API.documentDownload(id).then(response => {\r\n      //     if (response.code == 200) {\r\n      //       this.$msgbox({\r\n      //         title: '提示',\r\n      //         message: '附件正在同步中，请稍后下载',\r\n      //         showCancelButton: true,\r\n      //         confirmButtonText: '关闭',\r\n      //         cancelButtonText: '取消',\r\n      //         showCancelButton: false,\r\n      //         beforeClose: (action, instance, done) => {\r\n      //           done()\r\n      //         }\r\n      //       })\r\n      //     } else {\r\n      //       this.$message({ message: '附件下载失败', type: 'error' })\r\n      //     }\r\n      //   }).catch(err => { })\r\n      // }\r\n    },\r\n    // 翻译标题\r\n    translateTitle(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.title,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"title\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    // 翻译文章\r\n    translateEvent(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.content,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"content\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          this.translationBtnShow = false;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      API.AreaInfo(row.id).then((response) => {\r\n        this.form = response.data;\r\n        this.form.sourceType = Number(this.form.sourceType);\r\n        this.form.docId = row.docId;\r\n        // this.fileUrlList = this.form.fileUrl ? this.form.fileUrl.split(\",\").map(item => {\r\n        //   return {\r\n        //     name: item,\r\n        //     url: item\r\n        //   }\r\n        // }) : []\r\n        this.open = true;\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal\r\n        .confirm('是否确认删除该条文章？\"')\r\n        .then(() => {\r\n          return API.monitoringEsRemove({ id: row.id, docId: row.docId });\r\n        })\r\n        .then(() => {\r\n          this.Refresh();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          let queryForm = JSON.parse(JSON.stringify(this.form));\r\n          // let cover = String(this.fileList.map(item => item.path))\r\n          // queryForm.cover = cover\r\n          articleListEdit(queryForm).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.Refresh();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 自定义上传 */\r\n    async requestLoad(file) {\r\n      let data = new FormData();\r\n      data.append(\"cover\", file.file);\r\n      await uploadCover(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.fileList.map((item) => {\r\n            if (item.uid == file.file.uid) {\r\n              item.path = response.imgUrl;\r\n            }\r\n          });\r\n          this.$message({ message: \"上传成功\", type: \"success\" });\r\n        } else {\r\n          this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    /* 文件超出限制 */\r\n    exceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传三个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    /* 移除文件 */\r\n    handleRemove(file) {\r\n      this.fileList = this.fileList.filter((item) => item !== file);\r\n    },\r\n    // 文件更改\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList;\r\n      this.$refs.upload.submit();\r\n    },\r\n    // 上传附件校验\r\n    beforeUploadUrl(file) {\r\n      // 判断文件是否为excel\r\n      let fileName = file.name\r\n          .substring(file.name.lastIndexOf(\".\") + 1)\r\n          .toLowerCase(),\r\n        condition =\r\n          fileName == \"pdf\" ||\r\n          fileName == \"doc\" ||\r\n          fileName == \"xls\" ||\r\n          fileName == \"ppt\" ||\r\n          fileName == \"xlsx\" ||\r\n          fileName == \"pptx\" ||\r\n          fileName == \"docx\";\r\n      let fileSize = file.size / 1024 / 1024 < 10;\r\n      if (!condition) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件必须是pdf,doc,xls,ppt,xlsx,pptx,docx格式\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      /* 文件大小限制 */\r\n      if (!fileSize) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件的大小不能超过 10MB!\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      return condition && fileSize;\r\n    },\r\n    // 文件上传成功\r\n    uploadUrlSuccess(res, file) {\r\n      this.$message({ message: \"上传成功\", type: \"success\" });\r\n    },\r\n    // 文件上传超出限制\r\n    uploadUrlExceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传1个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    // 文件上传方法\r\n    uploadUrlRequest(file) {\r\n      if (this.form.originalUrl != null && this.form.originalUrl != \"\") {\r\n        if (\r\n          this.form.originalUrl.match(\r\n            /(http|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/\r\n          )\r\n        ) {\r\n          let data = new FormData();\r\n          data.append(\"file\", file.file);\r\n          data.append(\"originalUrl\", this.form.originalUrl);\r\n\r\n          API.uploadFile(data).then((response) => {\r\n            if (response.code == 200) {\r\n              this.$message({ message: \"上传成功\", type: \"success\" });\r\n              this.form.fileUrl = response.data;\r\n            } else {\r\n              this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n              this.form.fileUrl = \"\";\r\n            }\r\n          });\r\n        } else {\r\n          this.$message({ message: \"请填写正确的原文链接\", type: \"warning\" });\r\n          this.fileUrlList = [];\r\n        }\r\n      } else {\r\n        this.$message({ message: \"请填写原文链接\", type: \"warning\" });\r\n        this.fileUrlList = [];\r\n      }\r\n    },\r\n    // 删除附件\r\n    uploadUrlRemove() {\r\n      API.removeFile({ filePath: this.form.fileUrl }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$message({ message: \"删除成功\", type: \"success\" });\r\n          this.fileUrlList = [];\r\n          this.form.fileUrl = \"\";\r\n        } else {\r\n          this.$message({ message: \"删除失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        articleSn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceName: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: null,\r\n        deptId: null,\r\n        content: null,\r\n        cnContent: null,\r\n        fileUrl: null,\r\n        industry: null,\r\n        domain: null,\r\n        tmpUrl: null,\r\n        isFinish: null,\r\n        groupId: null,\r\n        appId: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 批量导入相关方法\r\n    // 打开批量导入弹框\r\n    openBatchImportDialog() {\r\n      this.batchImportVisible = true;\r\n      this.batchImportFiles = [];\r\n    },\r\n    // 文件选择处理\r\n    handleFileSelect(file, fileList) {\r\n      // 将新选择的文件添加到列表中\r\n      const newFiles = fileList.map((item) => ({\r\n        fileName: item.name,\r\n        file: item.raw,\r\n        sourceName: \"\",\r\n      }));\r\n      this.batchImportFiles = newFiles;\r\n    },\r\n    // 删除文件\r\n    removeFile(index) {\r\n      this.batchImportFiles.splice(index, 1);\r\n    },\r\n    // 取消批量导入\r\n    cancelBatchImport() {\r\n      this.batchImportVisible = false;\r\n      this.batchImportFiles = [];\r\n      // 清空文件选择器\r\n      this.$refs.batchUpload.clearFiles();\r\n    },\r\n    // 确认批量导入\r\n    async confirmBatchImport() {\r\n      // 验证数据源名称是否都已填写\r\n      const emptySourceNames = this.batchImportFiles.filter(\r\n        (item) => !item.sourceName.trim()\r\n      );\r\n      if (emptySourceNames.length > 0) {\r\n        this.$message({\r\n          message: \"请为所有文件填写数据源名称\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 创建FormData对象\r\n        const formData = new FormData();\r\n\r\n        // 添加文件到FormData\r\n        this.batchImportFiles.forEach((item) => {\r\n          formData.append(\"files\", item.file);\r\n        });\r\n\r\n        // 获取数据源名称数组\r\n        const sourceNames = this.batchImportFiles\r\n          .map((item) => item.sourceName)\r\n          .join(\",\");\r\n\r\n        formData.append(\"sourceNames\", sourceNames);\r\n\r\n        // 调用批量导入API，传递FormData和sourceNames参数\r\n        const response = await API.batchImportReports(formData);\r\n\r\n        if (response.code === 200) {\r\n          this.$message({\r\n            message: \"批量导入成功\",\r\n            type: \"success\",\r\n          });\r\n          this.batchImportVisible = false;\r\n          this.batchImportFiles = [];\r\n          this.$refs.batchUpload.clearFiles();\r\n          // 刷新列表\r\n          this.Refresh();\r\n        } else {\r\n          this.$message({\r\n            message: response.msg || \"批量导入失败\",\r\n            type: \"error\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"批量导入错误:\", error);\r\n        this.$message({\r\n          message: \"批量导入失败，请稍后重试\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    reportAiChat() {\r\n      this.showDeepseekDialog = true;\r\n      if (this.checkedCities.length === 0) {\r\n        this.$message({ message: \"请选择要解读的文章\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.checkedCities.length > 1) {\r\n        this.$message({ message: \"请只选择一篇文章进行解读\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      // 获取选中的文章\r\n      const selectedArticleId = this.checkedCities[0];\r\n      const selectedArticle = this.ArticleList.find(\r\n        (item) => item.id === selectedArticleId\r\n      );\r\n\r\n      if (selectedArticle) {\r\n        this.currentArticle = selectedArticle;\r\n        this.showDeepseekDialog = true;\r\n      } else {\r\n        this.$message({ message: \"未找到选中的文章\", type: \"error\" });\r\n      }\r\n    },\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.checkedCities.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* drawer样式修改 */\r\n.main ::v-deep .el-drawer.drawer_box {\r\n  width: 700px !important;\r\n}\r\n\r\n.MainArticle {\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  // margin-top: 10px;\r\n  user-select: text;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    align-items: center;\r\n    border-bottom: solid 1px #e2e2e2;\r\n  }\r\n\r\n  .leftBtnGroup {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    gap: 15px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    & > p {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .leftBtnGroup2 {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    gap: 15px;\r\n    flex: 1;\r\n    padding-left: 20px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      line-height: 36px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .scollBox {\r\n    overflow-y: auto;\r\n    height: calc(100vh - 389px);\r\n    transition: transform 5s ease;\r\n\r\n    .Articl {\r\n      display: flex;\r\n      width: 100%;\r\n      border-bottom: solid 1px #d4d4d4;\r\n\r\n      .Articl_left {\r\n        width: 85%;\r\n        padding-bottom: 16px;\r\n      }\r\n\r\n      .Articl_kqcb {\r\n        width: 100%;\r\n        padding-bottom: 16px;\r\n        & > div:first-child {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n        }\r\n      }\r\n\r\n      .ArticlTop {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding: 0 0 0 20px;\r\n        font-size: 15px;\r\n      }\r\n\r\n      .ArticlMain {\r\n        padding: 0 0 0 30px;\r\n        color: #3f3f3f;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .ArticlMain > span:hover {\r\n        color: #1889f3;\r\n        border-bottom: solid 1px #0798f8;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .info_flex {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n      }\r\n\r\n      .ArticlBottom {\r\n        padding: 0 0 0 30px;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 0 25px;\r\n        line-height: 24px;\r\n        color: #9b9b9b;\r\n        font-size: 14px;\r\n\r\n        div {\r\n          text-overflow: ellipsis;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .linkStyle:hover {\r\n          border-bottom: solid 1px #1889f3;\r\n        }\r\n\r\n        .infomation {\r\n          color: #464749;\r\n          font-size: 14px;\r\n          margin-left: 8px;\r\n        }\r\n\r\n        p {\r\n          border: solid 1px #f0a147;\r\n          width: 45px;\r\n          height: 25px;\r\n          line-height: 25px;\r\n          font-size: 14px;\r\n          color: #f0a147;\r\n          text-align: center;\r\n        }\r\n      }\r\n\r\n      .imgBox {\r\n        padding: 0 20px 0 30px;\r\n        display: flex;\r\n        gap: 15px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.pagination {\r\n  z-index: 2;\r\n  background-color: rgba(255, 255, 255, 1);\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  padding: 10px 0;\r\n}\r\n\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n  line-height: 16px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n\r\n.drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n.drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n.btnBox {\r\n  z-index: 2;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n\r\n  & > p {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 批量导入弹框样式\r\n.batch-import-container {\r\n  .file-select-area {\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    background-color: #fafafa;\r\n\r\n    &:hover {\r\n      border-color: #409eff;\r\n    }\r\n  }\r\n\r\n  .file-table-area {\r\n    margin-top: 20px;\r\n\r\n    .el-table {\r\n      border-radius: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #1296db;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #1296db;\r\n}\r\n\r\n.deepseek-text {\r\n  color: #1296db; // 使用与图标相同的颜色\r\n  margin-left: 4px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style>\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n</style>\r\n"]}]}