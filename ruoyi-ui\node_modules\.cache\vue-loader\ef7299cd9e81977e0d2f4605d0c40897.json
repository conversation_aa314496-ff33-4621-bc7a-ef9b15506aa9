{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue", "mtime": 1754107687334}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQVBJIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHJlcXVlc3QgZnJvbSAiQC91dGlscy9yZXF1ZXN0IjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCmltcG9ydCB7IE1lc3NhZ2VCb3ggfSBmcm9tICJlbGVtZW50LXVpIjsNCmltcG9ydCBheGlvcyBmcm9tICJheGlvcyI7DQppbXBvcnQgeyBnZXRMaXN0Q2xhc3NpZnkgfSBmcm9tICJAL2FwaS9hcnRpY2xlL2NsYXNzaWZ5IjsNCmltcG9ydCB7IHNhdmVBcyB9IGZyb20gImZpbGUtc2F2ZXIiOw0KaW1wb3J0IHsgYmxvYlZhbGlkYXRlLCB0YW5zUGFyYW1zIH0gZnJvbSAiQC91dGlscy9ydW95aSI7DQppbXBvcnQgeyBhcnRpY2xlTGlzdEVkaXQsIHVwbG9hZENvdmVyIH0gZnJvbSAiQC9hcGkvYXJ0aWNsZUNyYXdsZXIvbGlzdCI7DQppbXBvcnQgRGVlcHNlZWtSZXBvcnREaWFsb2cgZnJvbSAiLi9EZWVwc2Vla1JlcG9ydERpYWxvZy52dWUiOw0KaW1wb3J0IHsgZGVlcHNlZWtBaVFhLCBkaWZ5QWlRYSwgb2xsYW1hQWlRYSB9IGZyb20gIkAvYXBpL2luZm9Fc2NhbGF0aW9uL2FpIjsNCmltcG9ydCB7IG1hcmtlZCB9IGZyb20gIm1hcmtlZCI7DQppbXBvcnQgeyBnZXRDb25maWdLZXkgfSBmcm9tICJAL2FwaS9zeXN0ZW0vY29uZmlnIjsNCmltcG9ydCB7IGdldExpc3RCeUlkcyB9IGZyb20gIkAvYXBpL2FydGljbGUvYXJ0aWNsZUhpc3RvcnkiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgZG93bkxvYWRTaG93OiB7DQogICAgICAvKiDkuIvovb3mjInpkq4gKi8gcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUsDQogICAgfSwNCiAgICBlZGl0U2hvdzogew0KICAgICAgLyog57yW6L6R5oyJ6ZKuICovIHJlcXVpcmVkOiBmYWxzZSwNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlLA0KICAgIH0sDQogICAgY29weVNob3c6IHsNCiAgICAgIC8qIOWkjeWItuaMiemSriAqLyByZXVxaXJlZDogZmFsc2UsDQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZSwNCiAgICB9LA0KICAgIGhlaWdodDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogNjU1LA0KICAgIH0sDQogICAgY3VycmVudFBhZ2U6IHsNCiAgICAgIHJldXFpcmVkOiB0cnVlLA0KICAgICAgZGVmYXVsdDogMSwNCiAgICB9LA0KICAgIHBhZ2VTaXplOiB7DQogICAgICByZXVxaXJlZDogdHJ1ZSwNCiAgICAgIGRlZmF1bHQ6IDUwLA0KICAgIH0sDQogICAgdG90YWw6IHsNCiAgICAgIHJldXFpcmVkOiB0cnVlLA0KICAgICAgZGVmYXVsdDogMCwNCiAgICB9LA0KICAgIEFydGljbGVMaXN0OiB7DQogICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgIGRlZmF1bHQ6IFtdLA0KICAgIH0sDQogICAgZmxhZzogew0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgfSwNCiAgICBTZWFjaERhdGE6IHsNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgIH0sDQogICAga2V5d29yZHM6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICIiLA0KICAgIH0sDQogICAgLy8g5oql5ZGK57G75Z6L5a2X5q61DQogICAgc291cmNlVHlwZTogew0KICAgICAgZGVmYXVsdDogIiIsDQogICAgfSwNCiAgfSwNCiAgY29tcG9uZW50czogew0KICAgIERlZXBzZWVrUmVwb3J0RGlhbG9nLA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHJlZ0V4cEltZzogL15cbiQvLA0KICAgICAgcmVwb3J0SWQ6ICIiLA0KICAgICAgcmVwb3J0T3B0aW9uczogW10sDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNoZWNrZWRDaXRpZXM6IFtdIC8qIOWkmumAiSAqLywNCiAgICAgIGNoZWNrZWQ6IGZhbHNlIC8qIOWFqOmAiSAqLywNCiAgICAgIGh0bWw6ICIiLA0KICAgICAgdGV4dDogIiIsDQogICAgICB0aGF0OiB0aGlzLA0KICAgICAgdGFnU2hvdzogZmFsc2UsDQogICAgICBpc0luZGV0ZXJtaW5hdGU6IHRydWUsDQogICAgICBjb3VudDogMCwNCiAgICAgIHNlcGFyYXRlOiB7fSwNCiAgICAgIC8qIOagh+etvuWKn+iDvSAqLw0KICAgICAgdGFnRGlhbG9nOiBmYWxzZSwNCiAgICAgIGZvcm1MYWJlbEFsaWduOiB7DQogICAgICAgIHRhZzogIiIsDQogICAgICAgIGluZHVzdHJ5OiAiIiwNCiAgICAgICAgZG9tYWluOiAiIiwNCiAgICAgIH0sDQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIG9wdGlvbnMxOiBbXSwNCiAgICAgIHRhZ0l0ZW06IHt9IC8qIOagh+etvuWvueixoSAqLywNCiAgICAgIGFyZWFMaXN0OiBbXSAvKiDpoobln58gKi8sDQogICAgICBpbmR1c3RyeTogW10gLyog6KGM5LiaICovLA0KICAgICAgbnVtOiAwLA0KICAgICAgdGltZXI6IG51bGwsDQogICAgICBkcmF3ZXI6IGZhbHNlLA0KICAgICAgZHJhd2VySW5mbzoge30sDQogICAgICBBcmVhSWQ6IG51bGwsDQogICAgICB0cmFuc2xhdGlvbkJ0blNob3c6IG51bGwsDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIHNvdXJjZVR5cGVMaXN0OiBbXSwgLy8g5pWw5o2u5rqQ5YiG57G7DQogICAgICBzb3VyY2VMaXN0czogW10sIC8vIOaVsOaNrua6kOWIl+ihqA0KICAgICAgc291cmNlVHlwZUxpc3RzOiBbXSwNCiAgICAgIGZvcm06IHt9LCAvLyDooajljZXlj4LmlbANCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgICB0aXRsZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlofnq6DmoIfpopjkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBjb250ZW50OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaWh+eroOivpuaDheS4uuW/heWhq+mhuSIgfV0sDQogICAgICAgIHB1Ymxpc2hUaW1lOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWPkeW4g+aXtumXtOS4uuW/heWhq+mhuSIgfV0sDQogICAgICAgIGNuVGl0bGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lit5paH5ZCN56ew5Li65b+F5aGr6aG5IiB9XSwNCiAgICAgICAgc291cmNlVHlwZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlubPlj7DnsbvlnovkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBvcmlnaW5hbFVybDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLljp/mlofkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBzdW1tYXJ5OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+Whq+WGmeaRmOimgSIgfV0sDQogICAgICAgIC8vIGNuU3VtbWFyeTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnkuK3mlofmkZjopoEnIH1dLA0KICAgICAgICBzbjogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7floavlhpnmlofnq6DlnLDlnYDllK/kuIDor4bliKvlj7ciIH1dLA0KICAgICAgfSwNCiAgICAgIHZlcnRpZnlVcGxvYWQ6IHsNCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gNCiAgICAgICAgaGVhZGVyczogew0KICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCksDQogICAgICAgICAgQ29udGVudFR5cGU6ICJhcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTgiLA0KICAgICAgICB9LA0KICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYANCiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9hcnRpY2xlL2FydGljbGVMaXN0L2NvdmVyIiwNCiAgICAgIH0sDQogICAgICBmaWxlTGlzdDogW10sDQogICAgICBmaWxlVXJsTGlzdDogW10sDQogICAgICBmaWxlVXJsdXJsOg0KICAgICAgICBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9hcnRpY2xlL2FydGljbGVMaXN0L3VwbG9hZC9maWxlIiwNCiAgICAgIHNob3dTdW1tYXJ5OiB0cnVlLA0KICAgICAgLy8g5om56YeP5a+85YWl55u45YWz5pWw5o2uDQogICAgICBiYXRjaEltcG9ydFZpc2libGU6IGZhbHNlLA0KICAgICAgYmF0Y2hJbXBvcnRGaWxlczogW10sDQogICAgICAvLyBEZWVwc2Vla+aKpeWRiuino+ivu+W8ueeqlw0KICAgICAgc2hvd0RlZXBzZWVrRGlhbG9nOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRBcnRpY2xlOiB7fSwNCiAgICAgIC8vIGFp55u45YWzDQogICAgICBhaURpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgY2hhdE1lc3NhZ2VzOiBbXSwNCiAgICAgIGlzVGhpbmtpbmc6IGZhbHNlLA0KICAgICAgdXNlckF2YXRhcjogIiIsIC8vIOeUqOaIt+WktOWDjw0KICAgICAgc3RyZWFtaW5nTWVzc2FnZTogIiIsIC8vIOa3u+WKoOeUqOS6juWtmOWCqOato+WcqOa1geW8j+i+k+WHuueahOa2iOaBrw0KICAgICAgbWFya2Rvd25PcHRpb25zOiB7DQogICAgICAgIGdmbTogdHJ1ZSwNCiAgICAgICAgYnJlYWtzOiB0cnVlLA0KICAgICAgICBoZWFkZXJJZHM6IHRydWUsDQogICAgICAgIG1hbmdsZTogZmFsc2UsDQogICAgICAgIGhlYWRlclByZWZpeDogIiIsDQogICAgICAgIHBlZGFudGljOiBmYWxzZSwNCiAgICAgICAgc2FuaXRpemU6IGZhbHNlLA0KICAgICAgICBzbWFydExpc3RzOiB0cnVlLA0KICAgICAgICBzbWFydHlwYW50czogdHJ1ZSwNCiAgICAgICAgeGh0bWw6IHRydWUsDQogICAgICB9LA0KICAgICAgaXNSZXF1ZXN0aW5nOiBmYWxzZSwgLy8g5qCH6K6w5piv5ZCm5q2j5Zyo6K+35rGC5LitDQogICAgICBpc0Fib3J0ZWQ6IGZhbHNlLCAvLyDmoIforrDmmK/lkKblt7LkuK3mlq0NCiAgICAgIGN1cnJlbnRSZWFkZXI6IG51bGwsIC8vIOW9k+WJjeeahCByZWFkZXINCiAgICAgIGFpUGxhdGZvcm06ICIiLA0KICAgICAgYXJ0aWNsZUFpUHJvbXB0OiAiIiwNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDoge30sDQogIHdhdGNoOiB7DQogICAgZGlhbG9nVmlzaWJsZTogZnVuY3Rpb24gKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICBpZiAobmV3VmFsKSB7DQogICAgICAgIEFQSS5nZXROZXdCdWlsdCh7IHNvdXJjZVR5cGU6IHRoaXMuc291cmNlVHlwZSB9KS50aGVuKChkYXRhKSA9PiB7DQogICAgICAgICAgaWYgKGRhdGEuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMucmVwb3J0T3B0aW9ucyA9IGRhdGEuZGF0YTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLmiqXlkYrliJfooajojrflj5blpLHotKXkuoYiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyAnZm9ybUxhYmVsQWxpZ24uaW5kdXN0cnknOiB7DQogICAgLy8gICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgIC8vICAgICBpZiAobmV3VmFsID09ICcnKSB7DQogICAgLy8gICAgICAgdGhpcy5vcHRpb25zMSA9IHRoaXMuaW5kdXN0cnkNCiAgICAvLyAgICAgfQ0KICAgIC8vICAgfSwNCiAgICAvLyAgIGRlZXA6IHRydWUNCiAgICAvLyB9LA0KICAgIC8vICdmb3JtTGFiZWxBbGlnbi5kb21haW4nOiB7DQogICAgLy8gICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgIC8vICAgICBpZiAobmV3VmFsID09ICcnKSB7DQogICAgLy8gICAgICAgdGhpcy5vcHRpb25zID0gdGhpcy5hcmVhTGlzdA0KICAgIC8vICAgICB9DQogICAgLy8gICB9LA0KICAgIC8vICAgZGVlcDogdHJ1ZQ0KICAgIC8vIH0sDQogICAgIlNlYWNoRGF0YS5zb3J0TW9kZSI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgdGhpcy5SZWZyZXNoKCk7DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICB9LA0KICAgICJmb3JtLnNvdXJjZVR5cGUiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIHRoaXMuc291cmNlVHlwZUxpc3RzID0gdGhpcy5zb3VyY2VMaXN0cy5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS50eXBlID09IG5ld1ZhbDsNCiAgICAgICAgfSk7DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICB9LA0KICB9LA0KICBtb3VudGVkKCkge30sDQogIGNyZWF0ZWQoKSB7DQogICAgaWYgKA0KICAgICAgdGhpcy5mbGFnICE9PSAiTW9uaXRvclVzZSIgJiYNCiAgICAgIHRoaXMuZmxhZyAhPT0gInNwZWNpYWxTdWJqZWN0VXNlIiAmJg0KICAgICAgdGhpcy5mbGFnICE9PSAiV2VjaGF0Ig0KICAgICkgew0KICAgICAgdGhpcy5vcGVuRGlhbG9nKCk7DQogICAgfQ0KICAgIGlmICh0aGlzLmZsYWcgIT09ICJXZWNoYXQiKSB7DQogICAgICBnZXRMaXN0Q2xhc3NpZnkoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5zb3VyY2VUeXBlTGlzdCA9IHJlcy5kYXRhOw0KICAgICAgfSk7DQogICAgICBBUEkuZ2V0U291cmNlTGlzdCgpLnRoZW4oKGRhdGEpID0+IHsNCiAgICAgICAgaWYgKGRhdGEuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnNvdXJjZUxpc3RzID0gZGF0YS5kYXRhOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9DQogICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgew0KICAgICAgZ2V0Q29uZmlnS2V5KCJzeXMuYWkucGxhdGZvcm0iKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuYWlQbGF0Zm9ybSA9IHJlcy5tc2c7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgZ2V0Q29uZmlnS2V5KCJ3ZWNoYXQuYWkuYXJ0aWNsZVByb21wdCIpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5hcnRpY2xlQWlQcm9tcHQgPSByZXMubXNnOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIC8vIOiOt+WPlueUqOaIt+WktOWDjw0KICAgICAgdGhpcy51c2VyQXZhdGFyID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5hdmF0YXI7DQogICAgfQ0KDQogICAgdGhpcy5zaG93U3VtbWFyeSA9IHRydWU7DQogIH0sDQogIHVwZGF0ZWQoKSB7fSwNCiAgZmlsdGVyczoge30sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDlpITnkIbnp5HmioDnm7jlhbPlrZfmrrXnmoTmmL7npLrmmKDlsIQNCiAgICBnZXRUZWNobm9sb2d5TGFiZWwodmFsdWUpIHsNCiAgICAgIGNvbnN0IG1hcHBpbmcgPSB7DQogICAgICAgIDA6ICLlkKYiLA0KICAgICAgICAxOiAi5pivIiwNCiAgICAgICAgMjogIuWFtuS7liIsDQogICAgICAgIDM6ICLlvoXlrpoiLA0KICAgICAgfTsNCiAgICAgIHJldHVybiBtYXBwaW5nW3ZhbHVlXTsNCiAgICB9LA0KICAgIC8vIOWkhOeQhuWPkeW4g+aXtumXtOeahOaYvuekug0KICAgIGZvcm1hdFB1Ymxpc2hUaW1lKHB1Ymxpc2hUaW1lLCB3ZWJzdGVQdWJsaXNoVGltZSkgew0KICAgICAgLy8g5qC85byP5YyWcHVibGlzaFRpbWXkuLrlubTmnIjml6UNCiAgICAgIGNvbnN0IGZvcm1hdHRlZFB1Ymxpc2hUaW1lID0gdGhpcy5wYXJzZVRpbWUocHVibGlzaFRpbWUsICJ7eX0te219LXtkfSIpOw0KDQogICAgICAvLyDlpoLmnpx3ZWJzdGVQdWJsaXNoVGltZeS4jeWtmOWcqO+8jOebtOaOpei/lOWbnnB1Ymxpc2hUaW1lDQogICAgICBpZiAoIXdlYnN0ZVB1Ymxpc2hUaW1lKSB7DQogICAgICAgIHJldHVybiBmb3JtYXR0ZWRQdWJsaXNoVGltZTsNCiAgICAgIH0NCg0KICAgICAgbGV0IGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSAiIjsNCiAgICAgIC8vIOWkhOeQhuS4jeWQjOagvOW8j+eahHdlYnN0ZVB1Ymxpc2hUaW1lDQogICAgICBpZiAod2Vic3RlUHVibGlzaFRpbWUpIHsNCiAgICAgICAgLy8g5aSE55CGMjAyNS0wNC0xMiAxMDowOToyMS45NzExOTHmoLzlvI/vvIjljIXlkKvov57lrZfnrKbnmoTmoIflh4bmoLzlvI/vvIkNCiAgICAgICAgaWYgKHdlYnN0ZVB1Ymxpc2hUaW1lLmluY2x1ZGVzKCItIikpIHsNCiAgICAgICAgICBjb25zdCBkYXRlTWF0Y2ggPSB3ZWJzdGVQdWJsaXNoVGltZS5tYXRjaCgvKFxkezR9KS0oXGR7Mn0pLShcZHsyfSkvKTsNCiAgICAgICAgICBpZiAoZGF0ZU1hdGNoKSB7DQogICAgICAgICAgICBjb25zdCB5ZWFyID0gZGF0ZU1hdGNoWzFdOw0KICAgICAgICAgICAgY29uc3QgbW9udGggPSBkYXRlTWF0Y2hbMl07DQogICAgICAgICAgICBjb25zdCBkYXkgPSBkYXRlTWF0Y2hbM107DQogICAgICAgICAgICBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YDsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgZm9ybWF0dGVkV2Vic3RlVGltZSA9IHdlYnN0ZVB1Ymxpc2hUaW1lOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDlpITnkIYyMDI15bm0MDTmnIgxNOaXpSAxMToyOToyMuagvOW8j++8iOS4reaWh+W5tOaciOaXpeagvOW8j++8jOW4piLml6Ui5a2X77yJDQogICAgICAgIGVsc2UgaWYgKA0KICAgICAgICAgIHdlYnN0ZVB1Ymxpc2hUaW1lLmluY2x1ZGVzKCLlubQiKSAmJg0KICAgICAgICAgIHdlYnN0ZVB1Ymxpc2hUaW1lLmluY2x1ZGVzKCLmnIgiKSAmJg0KICAgICAgICAgIHdlYnN0ZVB1Ymxpc2hUaW1lLmluY2x1ZGVzKCLml6UiKQ0KICAgICAgICApIHsNCiAgICAgICAgICBjb25zdCBkYXRlTWF0Y2ggPSB3ZWJzdGVQdWJsaXNoVGltZS5tYXRjaCgNCiAgICAgICAgICAgIC8oXGR7NH0p5bm0KFxkezEsMn0p5pyIKFxkezEsMn0p5pelLw0KICAgICAgICAgICk7DQogICAgICAgICAgaWYgKGRhdGVNYXRjaCkgew0KICAgICAgICAgICAgY29uc3QgeWVhciA9IGRhdGVNYXRjaFsxXTsNCiAgICAgICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZU1hdGNoWzJdLnBhZFN0YXJ0KDIsICIwIik7DQogICAgICAgICAgICBjb25zdCBkYXkgPSBkYXRlTWF0Y2hbM10ucGFkU3RhcnQoMiwgIjAiKTsNCiAgICAgICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX1gOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gd2Vic3RlUHVibGlzaFRpbWU7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC8vIOWkhOeQhjIwMjXlubQ05pyIMTXmoLzlvI/vvIjkuK3mloflubTmnIjmoLzlvI/vvIzkuI3luKYi5pelIuWtl++8iQ0KICAgICAgICBlbHNlIGlmICgNCiAgICAgICAgICB3ZWJzdGVQdWJsaXNoVGltZS5pbmNsdWRlcygi5bm0IikgJiYNCiAgICAgICAgICB3ZWJzdGVQdWJsaXNoVGltZS5pbmNsdWRlcygi5pyIIikNCiAgICAgICAgKSB7DQogICAgICAgICAgY29uc3QgZGF0ZU1hdGNoID0gd2Vic3RlUHVibGlzaFRpbWUubWF0Y2goDQogICAgICAgICAgICAvKFxkezR9KeW5tChcZHsxLDJ9KeaciChcZHsxLDJ9KS8NCiAgICAgICAgICApOw0KICAgICAgICAgIGlmIChkYXRlTWF0Y2gpIHsNCiAgICAgICAgICAgIGNvbnN0IHllYXIgPSBkYXRlTWF0Y2hbMV07DQogICAgICAgICAgICBjb25zdCBtb250aCA9IGRhdGVNYXRjaFsyXS5wYWRTdGFydCgyLCAiMCIpOw0KICAgICAgICAgICAgY29uc3QgZGF5ID0gZGF0ZU1hdGNoWzNdLnBhZFN0YXJ0KDIsICIwIik7DQogICAgICAgICAgICBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YDsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgZm9ybWF0dGVkV2Vic3RlVGltZSA9IHdlYnN0ZVB1Ymxpc2hUaW1lOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDlpITnkIYyMDI1LzA0LzE0IDExOjI5OjIy5qC85byP77yI5pac5p2g5YiG6ZqU55qE5qC85byP77yJDQogICAgICAgIGVsc2UgaWYgKHdlYnN0ZVB1Ymxpc2hUaW1lLmluY2x1ZGVzKCIvIikpIHsNCiAgICAgICAgICBjb25zdCBkYXRlTWF0Y2ggPSB3ZWJzdGVQdWJsaXNoVGltZS5tYXRjaCgNCiAgICAgICAgICAgIC8oXGR7NH0pXC8oXGR7MSwyfSlcLyhcZHsxLDJ9KS8NCiAgICAgICAgICApOw0KICAgICAgICAgIGlmIChkYXRlTWF0Y2gpIHsNCiAgICAgICAgICAgIGNvbnN0IHllYXIgPSBkYXRlTWF0Y2hbMV07DQogICAgICAgICAgICBjb25zdCBtb250aCA9IGRhdGVNYXRjaFsyXS5wYWRTdGFydCgyLCAiMCIpOw0KICAgICAgICAgICAgY29uc3QgZGF5ID0gZGF0ZU1hdGNoWzNdLnBhZFN0YXJ0KDIsICIwIik7DQogICAgICAgICAgICBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YDsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgZm9ybWF0dGVkV2Vic3RlVGltZSA9IHdlYnN0ZVB1Ymxpc2hUaW1lOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlhbbku5bmoLzlvI/nm7TmjqXkvb/nlKjljp/lgLwNCiAgICAgICAgICBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gd2Vic3RlUHVibGlzaFRpbWU7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5q+U6L6D5bm05pyI5pel5piv5ZCm55u45ZCMDQogICAgICBpZiAoZm9ybWF0dGVkUHVibGlzaFRpbWUgPT09IGZvcm1hdHRlZFdlYnN0ZVRpbWUpIHsNCiAgICAgICAgcmV0dXJuIGZvcm1hdHRlZFB1Ymxpc2hUaW1lOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIGAke2Zvcm1hdHRlZFB1Ymxpc2hUaW1lfSAvICR7d2Vic3RlUHVibGlzaFRpbWV9YDsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5qOA5p+l5paH5pys5piv5ZCm5pyJ5a6e6ZmF5YaF5a6577yI5Y676ZmkSFRNTOagh+etvuWQju+8iQ0KICAgIGhhc0FjdHVhbENvbnRlbnQodGV4dCkgew0KICAgICAgaWYgKCF0ZXh0KSB7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCiAgICAgIC8vIOWOu+mZpEhUTUzmoIfnrb4NCiAgICAgIGNvbnN0IGNvbnRlbnRXaXRob3V0VGFncyA9IHRleHQucmVwbGFjZSgvPFtePl0qPi9nLCAiIik7DQogICAgICAvLyDmo4Dmn6XmmK/lkKbmnInkuK3mlofjgIHoi7HmlofjgIHmlbDlrZfnrYnlrp7pmYXlhoXlrrkNCiAgICAgIHJldHVybiAvW1x1NGUwMC1cdTlmYTVhLXpBLVowLTldLy50ZXN0KGNvbnRlbnRXaXRob3V0VGFncyk7DQogICAgfSwNCiAgICAvLyDlhbPplK7lrZfmm7/mjaINCiAgICBjaGFuZ2VDb2xvcihzdHIpIHsNCiAgICAgIGxldCBTdHIgPSBzdHI7DQogICAgICBpZiAoU3RyKSB7DQogICAgICAgIGxldCBrZXl3b3JkcyA9IHRoaXMua2V5d29yZHMuc3BsaXQoIiwiKTsNCiAgICAgICAga2V5d29yZHMubWFwKChrZXlpdGVtLCBrZXlpbmRleCkgPT4gew0KICAgICAgICAgIGlmIChrZXlpdGVtICYmIGtleWl0ZW0ubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgLy8g5Yy56YWN5YWz6ZSu5a2X5q2j5YiZDQogICAgICAgICAgICBsZXQgcmVwbGFjZVJlZyA9IG5ldyBSZWdFeHAoa2V5aXRlbSwgImciKTsNCiAgICAgICAgICAgIC8vIOmrmOS6ruabv+aNonYtaHRtbOWAvA0KICAgICAgICAgICAgbGV0IHJlcGxhY2VTdHJpbmcgPQ0KICAgICAgICAgICAgICAnPHNwYW4gY2xhc3M9ImhpZ2hsaWdodCInICsNCiAgICAgICAgICAgICAgJyBzdHlsZT0iY29sb3I6IHJlZDsiPicgKw0KICAgICAgICAgICAgICBrZXlpdGVtICsNCiAgICAgICAgICAgICAgIjwvc3Bhbj4iOw0KICAgICAgICAgICAgU3RyID0gU3RyLnJlcGxhY2UocmVwbGFjZVJlZywgcmVwbGFjZVN0cmluZyk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIHJldHVybiBTdHI7DQogICAgfSwNCiAgICAvKiDkuIvovb1FeGNlbCAqLw0KICAgIGFzeW5jIGRvd25Mb2FkRXhjZWwoKSB7DQogICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi6K+36YCJ5oup6KaB5a+85Ye655qE5pWw5o2uIiwgdHlwZTogIndhcm5pbmciIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLmZsYWcgPT0gInNwZWNpYWxTdWJqZWN0VXNlIikgew0KICAgICAgICBBUEkuZG93bkxvYWRFeGNlbCh0aGlzLmNoZWNrZWRDaXRpZXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgbGV0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJhIik7DQogICAgICAgICAgYS5ocmVmID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwocmVzcG9uc2UpOw0KICAgICAgICAgIGEuZG93bmxvYWQgPSBgc291cmNlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgOw0KICAgICAgICAgIGEuY2xpY2soKTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBhd2FpdCBBUEkuZG93bkxvYWRFeHBvcnRFeGNlbCh0aGlzLmNoZWNrZWRDaXRpZXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgbGV0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJhIik7DQogICAgICAgICAgYS5ocmVmID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwocmVzcG9uc2UpOw0KICAgICAgICAgIGEuZG93bmxvYWQgPSBgc291cmNlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgOw0KICAgICAgICAgIGEuY2xpY2soKTsNCg0KICAgICAgICAgIC8vIHNhdmVBcyhibG9iLCBgc291cmNlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGJhdGNoRGVsZXRlKCkgew0KICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuivt+mAieaLqeimgeWIoOmZpOeahOaVsOaNriIsIHR5cGU6ICJ3YXJuaW5nIiB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm56Gu6K6k5Yig6Zmk5bey5Yu+6YCJ55qE5pWw5o2u6aG5PyIpDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICBBUEkuYmF0Y2hSZW1vdmUodGhpcy5jaGVja2VkQ2l0aWVzLmpvaW4oIiwiKSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICAgICAgdGhpcy4kZW1pdCgiUmVmcmVzaCIpOw0KICAgICAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzID0gW107DQogICAgICAgICAgfSk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiDlj5HluIPliLDmr4/ml6XmnIDmlrDng63ngrkgKi8NCiAgICBwdWJsaXNoSG90KCkgew0KICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6KaB5Y+R5biD5Yiw5q+P5pel5pyA5paw54Ot54K555qE5pWw5o2uIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTlj5HluIPlt7Lli77pgInnmoTmlbDmja7pobnliLDmr4/ml6XmnIDmlrDng63ngrk/IikNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIEFQSS5wdWJsaXNoRXZlcnlkYXlIb3QodGhpcy5jaGVja2VkQ2l0aWVzLmpvaW4oIiwiKSkudGhlbigoKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgdHlwZTogInN1Y2Nlc3MiLCBtZXNzYWdlOiAi5Y+R5biD5oiQ5YqfISIgfSk7DQogICAgICAgICAgICB0aGlzLiRlbWl0KCJSZWZyZXNoIik7DQogICAgICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMgPSBbXTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qIOi/lOWbnumhtumDqOWKqOeUuyAqLw0KICAgIG1haW5TY29ybGwoKSB7DQogICAgICB2YXIgc2Nyb2xsU3RlcCA9IC10aGlzLiRyZWZzLnNjcm9sbC5zY3JvbGxUb3AgLyAoODAwIC8gMTUpOyAvLyDorqHnrpfmr4/kuIDmraXmu5rliqjnmoTot53nprsNCiAgICAgIHZhciBzY3JvbGxJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMuc2Nyb2xsLnNjcm9sbFRvcCAhPT0gMCkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuc2Nyb2xsLnNjcm9sbEJ5KDAsIHNjcm9sbFN0ZXApOyAvLyDmjInnhafnu5nlrprmraXplb/mu5rliqjnqpflj6MNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjbGVhckludGVydmFsKHNjcm9sbEludGVydmFsKTsgLy8g5Yiw6L6+6aG26YOo5pe25riF6Zmk5a6a5pe25ZmoDQogICAgICAgIH0NCiAgICAgIH0sIDE1KTsNCiAgICB9LA0KICAgIHNjcm9sbENoYW5nZSgpIHsNCiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnRpbWVyKTsNCiAgICAgIHRoaXMudGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgdGhpcy5zdG9wU2Nyb2xsKCk7DQogICAgICB9LCA1MDApOw0KICAgICAgLy8gdGhpcy4kcmVmcy5wYWdpbmF0aW9uLnN0eWxlLm9wYWNpdHkgPSAwDQogICAgICAvLyB0aGlzLiRyZWZzLnBhZ2luYXRpb24uc3R5bGUudHJhbnNpdGlvbiA9ICcwJw0KICAgIH0gLyog5rua5Yqo5LqL5Lu2ICovLA0KICAgIHN0b3BTY3JvbGwoKSB7DQogICAgICAvLyB0aGlzLiRyZWZzLnBhZ2luYXRpb24uc3R5bGUudHJhbnNpdGlvbiA9ICcxcycNCiAgICAgIC8vIHRoaXMuJHJlZnMucGFnaW5hdGlvbi5zdHlsZS5vcGFjaXR5ID0gMQ0KICAgIH0sDQogICAgLyog5LiL6L29ICovDQogICAgZG93bkxvYWQoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLyog5q+P6aG15p2h5pWw5Y+Y5YyWICovDQogICAgaGFuZGxlU2l6ZUNoYW5nZShudW0pIHsNCiAgICAgIHRoaXMuJGVtaXQoImhhbmRsZVNpemVDaGFuZ2UiLCBudW0pOw0KICAgICAgdGhpcy5tYWluU2NvcmxsKCk7DQogICAgICB0aGlzLmNoZWNrZWQgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8qIOmhteeggeWPmOWMliAqLw0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UoY3VycmVudCkgew0KICAgICAgdGhpcy4kZW1pdCgiaGFuZGxlQ3VycmVudENoYW5nZSIsIGN1cnJlbnQpOw0KICAgICAgdGhpcy5tYWluU2NvcmxsKCk7DQogICAgICB0aGlzLmNoZWNrZWQgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8qIOaUtuiXjyAqLw0KICAgIGFzeW5jIGNvbGxlY3QoaXRlbSkgew0KICAgICAgLyog54K55Ye75YiX6KGo5pS26JePICovDQogICAgICBpZiAoaXRlbS5pZCkgew0KICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMgPSBbaXRlbS5pZF07DQogICAgICB9DQogICAgICAvKiDmnKrpgInmi6nmj5DnpLogKi8NCiAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLor7fpgInmi6nopoHmlLbol4/nmoTmlofnq6AiLCB0eXBlOiAiaW5mbyIgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIC8qIOaUtuiXjyAqLw0KICAgICAgaWYgKCFpdGVtLmZhdm9yaXRlcykgew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgQVBJLmNvbGxlY3RBcGkoW2l0ZW0uaWRdKTsNCiAgICAgICAgaWYgKHJlcy5jb2RlKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAi5pS26JeP5oiQ5YqfLOivt+WJjeW+gOS4quS6uuS4reW/g+afpeeciyIsDQogICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy4kZW1pdCgiUmVmcmVzaCIpOw0KICAgICAgICAgIHRoaXMuY2hlY2tlZENpdGllcyA9IFtdOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuaUtuiXj+Wksei0pSIsIHR5cGU6ICJpbmZvIiB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCByZXMgPSBhd2FpdCBBUEkuY29jZWxDb2xsZWN0KFtpdGVtLmlkXSk7DQogICAgICAgIGlmIChyZXMuY29kZSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5bey5Y+W5raI5pS26JePIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICAgIHRoaXMuJGVtaXQoIlJlZnJlc2giKTsNCiAgICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMgPSBbXTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLlj5bmtojmlLbol4/lpLHotKUiLCB0eXBlOiAiaW5mbyIgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiDkuIDplK7lpI3liLYgKi8NCiAgICBjb3B5VGV4dChpdGVtKSB7DQogICAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkDQogICAgICAgIC53cml0ZVRleHQoaXRlbS5jblRpdGxlKQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLlt7LmiJDlip/lpI3liLbliLDliarotLTmnb8iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaChmdW5jdGlvbiAoKSB7DQogICAgICAgICAgYWxlcnQoIuWkjeWItuWksei0pSIpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIHR5cGVIYW5kbGUoZGF0YSkgew0KICAgICAgaWYgKGRhdGEgPT0gMSkgew0KICAgICAgICByZXR1cm4gIuW+ruS/oeWFrOS8l+WPtyI7DQogICAgICB9IGVsc2UgaWYgKGRhdGEgPT0gMikgew0KICAgICAgICByZXR1cm4gIue9keermSI7DQogICAgICB9IGVsc2UgaWYgKGRhdGEgPT0gMykgew0KICAgICAgICByZXR1cm4gIuaJi+WKqOW9leWFpSI7DQogICAgICB9DQogICAgfSwNCiAgICAvKiDpgInmi6nkuovku7YgKi8NCiAgICBoYW5kbGVDaGVja2VkQ2l0aWVzQ2hhbmdlKHZhbHVlKSB7DQogICAgICB0aGlzLmNoZWNrZWRDaXRpZXMgPSB2YWx1ZTsNCiAgICB9LA0KICAgIC8qIOWFqOmAiSAqLw0KICAgIGhhbmRsZUNoZWNrQWxsQ2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzID0gdmFsID8gdGhpcy5BcnRpY2xlTGlzdC5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpIDogW107DQogICAgICB0aGlzLmlzSW5kZXRlcm1pbmF0ZSA9IGZhbHNlOw0KICAgIH0sDQogICAgLyog5Yi35pawICovDQogICAgUmVmcmVzaCgpIHsNCiAgICAgIHRoaXMuJGVtaXQoIlJlZnJlc2giKTsNCiAgICB9LA0KICAgIC8q56Gu5a6a5re75Yqg5Yiw5oql5ZGKICovDQogICAgYXN5bmMgcmVwb3J0U3VibWl0KCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICBsZXQga2V5V29yZExpc3QgPSBbXTsNCiAgICAgIGlmICghdGhpcy5yZXBvcnRJZCkNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nopoHmt7vliqDliLDnmoTmiqXlkYoiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICAvKiDljZXni6zmt7vliqAgKi8NCiAgICAgIGlmICh0aGlzLnNlcGFyYXRlLmlkKSB7DQogICAgICAgIC8vIGxldCBrZXl3b3JkID0gT2JqZWN0LmtleXModGhpcy5zZXBhcmF0ZS5rZXl3b3JkQ291bnQpDQogICAgICAgIGtleVdvcmRMaXN0LnB1c2goew0KICAgICAgICAgIHJlcG9ydElkOiB0aGlzLnJlcG9ydElkLA0KICAgICAgICAgIGxpc3RJZDogdGhpcy5zZXBhcmF0ZS5pZCwNCiAgICAgICAgICBsaXN0U246IHRoaXMuc2VwYXJhdGUuc24sDQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLyog5om56YeP5re75YqgICovDQogICAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMgPT0gIiIpDQogICAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeimgea3u+WKoOeahOaVsOaNriIsDQogICAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgICAgfSk7DQogICAgICAgIHRoaXMuY2hlY2tlZENpdGllcy5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgbGV0IGFydGljbGUgPSB0aGlzLkFydGljbGVMaXN0LmZpbHRlcigodmFsdWUpID0+IHZhbHVlLmlkID09IGl0ZW0pOw0KICAgICAgICAgIGtleVdvcmRMaXN0LnB1c2goew0KICAgICAgICAgICAgcmVwb3J0SWQ6IHRoaXMucmVwb3J0SWQsDQogICAgICAgICAgICBsaXN0SWQ6IGl0ZW0sDQogICAgICAgICAgICBsaXN0U246IGFydGljbGVbMF0uc24sDQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgbGV0IHJlcyA9IGF3YWl0IEFQSS5BZGRSZXBvcnQoa2V5V29yZExpc3QpOw0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuW3sua3u+WKoOWIsOaKpeWRiiIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgdGhpcy4kZW1pdCgiUmVmcmVzaCIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIua3u+WKoOWIsOaKpeWRiuWksei0pSzor7fogZTns7vnrqHnkIblkZgiLA0KICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgdGhpcy5zZXBhcmF0ZSA9IHt9Ow0KICAgICAgdGhpcy5yZXBvcnRJZCA9ICIiOw0KICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzID0gW107DQogICAgICB0aGlzLmNoZWNrZWQgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8qIOWNleeLrOa3u+WKoOaKpeWRiiAqLw0KICAgIHNlcGFyYXRlQWRkKGl0ZW0pIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLnNlcGFyYXRlID0gaXRlbTsNCiAgICB9LA0KICAgIC8qIOi3s+i9rOaWsOmhtemdoiAqLw0KICAgIG9wZW5OZXdWaWV3KGl0ZW0sIGlzTGluaykgew0KICAgICAgaWYgKGlzTGluaykgew0KICAgICAgICBpZiAoaXRlbS5vcmlnaW5hbFVybCkgew0KICAgICAgICAgIHdpbmRvdy5vcGVuKGl0ZW0ub3JpZ2luYWxVcmwpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuivpeaWh+eroOayoeacieWOn+aWh+mTvuaOpSIgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHdpbmRvdy5vcGVuKA0KICAgICAgICBgL2V4cHJlc3NEZXRhaWxzP2lkPSR7aXRlbS5pZH0mZG9jSWQ9JHtpdGVtLmRvY0lkfSZzb3VyY2VUeXBlPSR7aXRlbS5zb3VyY2VUeXBlfWAsDQogICAgICAgICJfYmxhbmsiDQogICAgICApOw0KICAgICAgLy8gdGhpcy5kcmF3ZXJJbmZvID0gaXRlbQ0KICAgICAgLy8gdGhpcy5kcmF3ZXIgPSB0cnVlDQogICAgfSwNCiAgICAvKiDmlofnq6DmiZPmoIfnrb4gKi8NCiAgICB0YWdIYW5kbGVyKGl0ZW0pIHsNCiAgICAgIHRoaXMudGFnRGlhbG9nID0gdHJ1ZTsNCiAgICAgIHRoaXMudGFnSXRlbSA9IGl0ZW07DQogICAgICBpZiAoaXRlbS5pbmR1c3RyeSkgew0KICAgICAgICB0aGlzLmZvcm1MYWJlbEFsaWduLmluZHVzdHJ5ID0gaXRlbS5pbmR1c3RyeQ0KICAgICAgICAgIC5zcGxpdCgiLCIpDQogICAgICAgICAgLm1hcCgoZGF0YSkgPT4gTnVtYmVyKGRhdGEpKTsNCiAgICAgIH0NCiAgICAgIGlmIChpdGVtLmRvbWFpbikgew0KICAgICAgICB0aGlzLmZvcm1MYWJlbEFsaWduLmRvbWFpbiA9IGl0ZW0uZG9tYWluDQogICAgICAgICAgLnNwbGl0KCIsIikNCiAgICAgICAgICAubWFwKChkYXRhKSA9PiBOdW1iZXIoZGF0YSkpOw0KICAgICAgfQ0KICAgICAgdGhpcy5mb3JtTGFiZWxBbGlnbi50YWcgPSBpdGVtLnRhZ3MgPyBpdGVtLnRhZ3Muc3BsaXQoIiwiKSA6ICIiOw0KICAgIH0sDQogICAgLyog6I635Y+W6aKG5Z+f5ZKM5YiG57G7ICovDQogICAgYXN5bmMgb3BlbkRpYWxvZygpIHsNCiAgICAgIGF3YWl0IEFQSS5hcmVhTGlzdCgpLnRoZW4oKGRhdGEpID0+IHsNCiAgICAgICAgaWYgKGRhdGEuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmFyZWFMaXN0ID0gZGF0YS5kYXRhOw0KICAgICAgICAgIHRoaXMub3B0aW9ucyA9IGRhdGEuZGF0YTsNCiAgICAgICAgICBBUEkuaW5kdXN0cnkoKS50aGVuKCh2YWx1ZSkgPT4gew0KICAgICAgICAgICAgdGhpcy5pbmR1c3RyeSA9IHZhbHVlLmRhdGE7DQogICAgICAgICAgICB0aGlzLm9wdGlvbnMxID0gdmFsdWUuZGF0YTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiDnrZvpgInpoobln58gKi8NCiAgICByZW1vdGVFdmVudChxdWVyeSkgew0KICAgICAgdGhpcy5vcHRpb25zID0gdGhpcy5hcmVhTGlzdC5maWx0ZXIoKGl0ZW0pID0+IGl0ZW0uZmllbGROYW1lID09IHF1ZXJ5KTsNCiAgICB9LA0KICAgIC8qIOetm+mAieihjOS4miAqLw0KICAgIHJlbW90ZUluZHVzdHJ5KHF1ZXJ5KSB7DQogICAgICB0aGlzLm9wdGlvbnMxID0gdGhpcy5pbmR1c3RyeS5maWx0ZXIoDQogICAgICAgIChpdGVtKSA9PiBpdGVtLmluZHVzdHJ5TmFtZSA9PSBxdWVyeQ0KICAgICAgKTsNCiAgICB9LA0KICAgIGFzeW5jIFN1Ym1pdFRhZygpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIGRvbWFpbjogU3RyaW5nKHRoaXMuZm9ybUxhYmVsQWxpZ24uZG9tYWluKSwNCiAgICAgICAgaW5kdXN0cnk6IFN0cmluZyh0aGlzLmZvcm1MYWJlbEFsaWduLmluZHVzdHJ5KSwNCiAgICAgICAgdGFnczogU3RyaW5nKHRoaXMuZm9ybUxhYmVsQWxpZ24udGFnKSwNCiAgICAgICAgYXJ0aWNsZUlkOiBTdHJpbmcodGhpcy50YWdJdGVtLmlkKSwNCiAgICAgICAgZG9jSWQ6IHRoaXMudGFnSXRlbS5kb2NJZCA/IFN0cmluZyh0aGlzLnRhZ0l0ZW0uZG9jSWQpIDogIiIsDQogICAgICB9Ow0KICAgICAgbGV0IHJlcyA9IGF3YWl0IEFQSS50YWdBZGQocGFyYW1zKTsNCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLkv53lrZjmiJDlip8iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRoaXMuUmVmcmVzaCgpOw0KICAgICAgICB9LCAxMDAwKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5L+d5a2Y5aSx6LSlIiwgdHlwZTogImVycm9yIiB9KTsNCiAgICAgIH0NCiAgICAgIHRoaXMuY2xvc2VUYWcoKTsNCiAgICB9LA0KICAgIGNsb3NlVGFnKCkgew0KICAgICAgdGhpcy4kcmVmc1sicnVsZUZvcm0iXS5yZXNldEZpZWxkcygpOw0KICAgICAgdGhpcy5mb3JtTGFiZWxBbGlnbiA9IHsNCiAgICAgICAgdGFnOiAiIiwNCiAgICAgICAgaW5kdXN0cnk6ICIiLA0KICAgICAgICBkb21haW46ICIiLA0KICAgICAgfTsNCiAgICAgIHRoaXMudGFnRGlhbG9nID0gZmFsc2U7DQogICAgfSwNCiAgICBhc3luYyBob3RJbmNyZWFzZShpdGVtKSB7DQogICAgICBsZXQgaXNXaGV0aGVyID0gSlNPTi5wYXJzZShpdGVtLmlzV2hldGhlcik7DQogICAgICBsZXQgcmVzID0gYXdhaXQgQVBJLnRhZ0FkZCh7DQogICAgICAgIGFydGljbGVJZDogaXRlbS5pZCwNCiAgICAgICAgaXNXaGV0aGVyOiArIUJvb2xlYW4oaXNXaGV0aGVyKSwNCiAgICAgIH0pOw0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgdGhpcy5SZWZyZXNoKCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuaTjeS9nOWksei0pSIsIHR5cGU6ICJlcnJvciIgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBvcGVuRHJhd2VyKCkgew0KICAgICAgbGV0IGRvY0lkID0gdGhpcy5kcmF3ZXJJbmZvLmRvY0lkOw0KICAgICAgYXdhaXQgQVBJLkFyZWFJbmZvKHRoaXMuZHJhd2VySW5mby5pZCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmRyYXdlckluZm8gPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLmRyYXdlckluZm8uZG9jSWQgPSBkb2NJZDsNCiAgICAgICAgICAvKiDlsIblrZfnrKbkuLLkuK3nmoRcbuabv+aNouS4ujxicj4gKi8NCiAgICAgICAgICB0aGlzLnRyYW5zbGF0aW9uQnRuU2hvdyA9ICF0aGlzLmRyYXdlckluZm8uY25Db250ZW50Ow0KICAgICAgICAgIGlmICh0aGlzLmRyYXdlckluZm8uY25Db250ZW50IHx8IHRoaXMuZHJhd2VySW5mby5jb250ZW50KSB7DQogICAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50ID0gKA0KICAgICAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50IHx8IHRoaXMuZHJhd2VySW5mby5jb250ZW50DQogICAgICAgICAgICApLnJlcGxhY2UoL1xcbi9nLCAoYSwgYiwgYykgPT4gew0KICAgICAgICAgICAgICByZXR1cm4gIjxicj4iOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50ID0gKA0KICAgICAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25Db250ZW50IHx8IHRoaXMuZHJhd2VySW5mby5jb250ZW50DQogICAgICAgICAgICApLnJlcGxhY2UoL1wke1tefV0rfS9nLCAiPGJyPiIpOw0KICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCA9ICgNCiAgICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCB8fCB0aGlzLmRyYXdlckluZm8uY29udGVudA0KICAgICAgICAgICAgKS5yZXBsYWNlKCJ8eGEwIiwgIiIpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiDmiYDlsZ7ooYzkuJrlpITnkIYgKi8NCiAgICBpbmR1c3RyeUhhbmRsZShpdGVtKSB7DQogICAgICBsZXQgaWRzID0gW10sDQogICAgICAgIHN0ciA9ICIiOw0KICAgICAgaWYgKGl0ZW0uaW5kdXN0cnkpIHsNCiAgICAgICAgaWRzID0gaXRlbS5pbmR1c3RyeS5zcGxpdCgiLCIpOw0KICAgICAgfQ0KICAgICAgaWRzLmZvckVhY2goKGRhdGEpID0+IHsNCiAgICAgICAgdGhpcy5pbmR1c3RyeS5tYXAoKGVsZSkgPT4gew0KICAgICAgICAgIGlmIChlbGUuaWQgPT0gZGF0YSkgew0KICAgICAgICAgICAgaWYgKHN0ciA9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgc3RyID0gIiI7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBzdHIgKz0gZWxlLmluZHVzdHJ5TmFtZSArICIgIjsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgICByZXR1cm4gc3RyOw0KICAgIH0sDQogICAgZG9tYWluSGFuZGxlKGl0ZW0pIHsNCiAgICAgIGxldCBpZHMgPSBbXSwNCiAgICAgICAgc3RyID0gIiI7DQogICAgICBpZiAoaXRlbS5kb21haW4pIHsNCiAgICAgICAgaWRzID0gaXRlbS5kb21haW4uc3BsaXQoIiwiKTsNCiAgICAgIH0NCiAgICAgIGlkcy5mb3JFYWNoKChkYXRhKSA9PiB7DQogICAgICAgIHRoaXMuYXJlYUxpc3QubWFwKChlbGUpID0+IHsNCiAgICAgICAgICBpZiAoZWxlLmlkID09IGRhdGEpIHsNCiAgICAgICAgICAgIGlmIChzdHIgPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgIHN0ciA9ICIiOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgc3RyICs9IGVsZS5maWVsZE5hbWUgKyAiICI7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIHN0cjsNCiAgICB9LA0KICAgIC8qIOW/q+eFp+eUn+aIkCAqLw0KICAgIHJlc3VsdEV2ZW50KGl0ZW0pIHsNCiAgICAgIGlmIChpdGVtID09ICJCYXRjaEdlbmVyYXRpb24iICYmIHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieaLqeaWh+eroCIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBsZXQgaWRzID0gbnVsbDsNCiAgICAgIGxldCB6aHVhbmd0YWkgPSAi55Sf5oiQIjsNCiAgICAgIGxldCB1cmwgPSAiIjsNCiAgICAgIGlmIChpdGVtID09ICJkcmF3ZXIiKSB7DQogICAgICAgIGlkcyA9IFt0aGlzLmRyYXdlckluZm8uaWRdOw0KICAgICAgICBpZiAodGhpcy5kcmF3ZXJJbmZvLnNuYXBzaG90VXJsKSB6aHVhbmd0YWkgPSAi5p+l55yLIjsNCiAgICAgICAgdXJsID0gdGhpcy5kcmF3ZXJJbmZvLnNuYXBzaG90VXJsOw0KICAgICAgfSBlbHNlIGlmIChpdGVtID09ICJCYXRjaEdlbmVyYXRpb24iKSB7DQogICAgICAgIGlkcyA9IHRoaXMuY2hlY2tlZENpdGllczsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlkcyA9IFtpdGVtLmlkXTsNCiAgICAgICAgaWYgKGl0ZW0uc25hcHNob3RVcmwpIHpodWFuZ3RhaSA9ICLmn6XnnIsiOw0KICAgICAgICB1cmwgPSBpdGVtLnNuYXBzaG90VXJsOw0KICAgICAgfQ0KICAgICAgaWYgKHpodWFuZ3RhaSA9PSAi55Sf5oiQIikgew0KICAgICAgICBpZiAodGhpcy5mbGFnID09ICJNb25pdG9yVXNlIikgew0KICAgICAgICAgIEFQSS5kb3duTG9hZEV4cG9ydEtlKGlkcykNCiAgICAgICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtc2dib3goew0KICAgICAgICAgICAgICAgICAgdGl0bGU6ICLmj5DnpLoiLA0KICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuW/q+eFp+ato+WcqOeUn+aIkOS4re+8jOivt+eojeWQjuafpeeciyIsDQogICAgICAgICAgICAgICAgICBzaG93Q2FuY2VsQnV0dG9uOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLlhbPpl60iLA0KICAgICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgICAgICAgICAgICBzaG93Q2FuY2VsQnV0dG9uOiBmYWxzZSwNCiAgICAgICAgICAgICAgICAgIGJlZm9yZUNsb3NlOiAoYWN0aW9uLCBpbnN0YW5jZSwgZG9uZSkgPT4gew0KICAgICAgICAgICAgICAgICAgICBkb25lKCk7DQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIueUs+ivt+Wksei0pe+8jOivt+iBlOezu+euoeeQhuWRmO+8jOehruiupOmHh+mbhuWZqOaYr+WQpuato+W4uCIsDQogICAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgLmNhdGNoKChlcnIpID0+IHt9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBBUEkuZG93bkxvYWRFeHBvcnRaaHVhbihpZHMpDQogICAgICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbXNnYm94KHsNCiAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5o+Q56S6IiwNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLlv6vnhafmraPlnKjnlJ/miJDkuK3vvIzor7fnqI3lkI7mn6XnnIsiLA0KICAgICAgICAgICAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi5YWz6ZetIiwNCiAgICAgICAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgICAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogZmFsc2UsDQogICAgICAgICAgICAgICAgICBiZWZvcmVDbG9zZTogKGFjdGlvbiwgaW5zdGFuY2UsIGRvbmUpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgZG9uZSgpOw0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLnlLPor7flpLHotKXvvIzor7fogZTns7vnrqHnkIblkZjvvIznoa7orqTph4fpm4blmajmmK/lkKbmraPluLgiLA0KICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7fSk7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHVybCA9IHVybC5yZXBsYWNlKG5ldyBSZWdFeHAoIi9ob21lL2xvY2FsL2RweC9zZXJ2ZXItYXBpLyIsICJnIiksICIvIik7DQogICAgICAgIHVybCA9IHVybC5yZXBsYWNlKG5ldyBSZWdFeHAoIi9ob21lL2xvY2FsL2RweC8iLCAiZyIpLCAiLyIpOw0KICAgICAgICB3aW5kb3cub3Blbih3aW5kb3cubG9jYXRpb24ub3JpZ2luICsgdXJsLCAiX2JsYW5rIik7DQogICAgICB9DQogICAgfSwNCiAgICAvKiDpmYTku7bkuIvovb0gKi8NCiAgICBhc3luYyBkb2N1bWVudERvd25sb2FkKGl0ZW0pIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBpZiAoaXRlbS5maWxlVXJsKSB7DQogICAgICAgIGNvbnN0IHVybHMgPSBpdGVtLmZpbGVVcmwuc3BsaXQoIiwiKTsNCiAgICAgICAgZm9yIChjb25zdCBbaW5kZXgsIHVybF0gb2YgdXJscy5lbnRyaWVzKCkpIHsNCiAgICAgICAgICBpZiAodXJsLmluZGV4T2YoImh0dHBzOi8vIikgPT09IC0xKSB7DQogICAgICAgICAgICBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHsNCiAgICAgICAgICAgICAgYXdhaXQgdGhpcy5kb3duTG9hZEZ1bih1cmwsIGluZGV4LCBpdGVtLmNuVGl0bGUgfHwgaXRlbS50aXRsZSk7DQogICAgICAgICAgICB9LCBpbmRleCAqIDUwMCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIumZhOS7tui/mOayoeWQjOatpeWIsOW9k+WJjeezu+e7n++8jOaaguaXtuaXoOazleS4i+i9vSIpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgfSwNCg0KICAgIGFzeW5jIGRvd25Mb2FkRnVuKHVybCwgaW5kZXgsIHRpdGxlKSB7DQogICAgICBsZXQgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsNCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgiZmlsZVVybCIsIHVybCk7DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgQVBJLmRvd25sb2FkRmlsZShmb3JtRGF0YSk7DQogICAgICAgIGNvbnN0IGlzQmxvYiA9IGJsb2JWYWxpZGF0ZShyZXNwb25zZSk7DQoNCiAgICAgICAgaWYgKGlzQmxvYikgew0KICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbcmVzcG9uc2VdKTsNCiAgICAgICAgICBsZXQgbGlzdCA9IHVybC5zcGxpdCgiLyIpOw0KICAgICAgICAgIGxldCBmaWxlTmFtZSA9IGxpc3RbbGlzdC5sZW5ndGggLSAxXTsNCiAgICAgICAgICBzYXZlQXMoYmxvYiwgZmlsZU5hbWUpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnN0IHJlc1RleHQgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7DQogICAgICAgICAgY29uc3QgcnNwT2JqID0gSlNPTi5wYXJzZShyZXNUZXh0KTsNCiAgICAgICAgICBjb25zdCBlcnJNc2cgPQ0KICAgICAgICAgICAgZXJyb3JDb2RlW3JzcE9iai5jb2RlXSB8fCByc3BPYmoubXNnIHx8IGVycm9yQ29kZVsiZGVmYXVsdCJdOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZXJyTXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyKSB7DQogICAgICAgIC8vIHRoaXMuJG1lc3NhZ2UuZXJyb3IoYEVycm9yIGRvd25sb2FkaW5nIGZpbGU6ICR7ZXJyfWApOw0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgLy8g56Gu5L+dIGxvYWRpbmcg5Zyo5q+P5qyh5LiL6L295ZCO6YO96K6+572u5Li6IGZhbHNlDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfQ0KDQogICAgICAvLyDkuYvliY3nmoTpmYTku7bkuIvovb0NCiAgICAgIC8vIGlmIChpdGVtLmFubmV4VXJsKSB7DQogICAgICAvLyAgIC8qIOacieaWh+S7tuWcsOWdgCDnm7TmjqXkuIvovb0gKi8NCiAgICAgIC8vICAgbGV0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgIC8vICAgZm9ybURhdGEuYXBwZW5kKCdpZCcsIGl0ZW0uaWQpDQogICAgICAvLyAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIC8vICAgQVBJLmRvY3VtZW50RG93bmxvYWRLZShmb3JtRGF0YSkudGhlbihyZXMgPT4gew0KICAgICAgLy8gICAgIGxldCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpDQogICAgICAvLyAgICAgYS5ocmVmID0gVVJMLmNyZWF0ZU9iamVjdFVSTChyZXMuZGF0YSkNCiAgICAgIC8vICAgICBhLmRvd25sb2FkID0gcmVzLmhlYWRlcnNbJ2NvbnRlbnQtZGlzcG9zaXRpb24nXS5zcGxpdCgnZmlsZW5hbWU9JylbMV0NCiAgICAgIC8vICAgICBhLmNsaWNrKCkNCiAgICAgIC8vICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgLy8gICB9KQ0KICAgICAgLy8gfSBlbHNlIHsNCiAgICAgIC8vICAgLyog5rKh5pyJ5paH5Lu25qC85byPIOeUs+ivt+S4i+i9vSAqLw0KICAgICAgLy8gICBBUEkuZG9jdW1lbnREb3dubG9hZChpZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAvLyAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAvLyAgICAgICB0aGlzLiRtc2dib3goew0KICAgICAgLy8gICAgICAgICB0aXRsZTogJ+aPkOekuicsDQogICAgICAvLyAgICAgICAgIG1lc3NhZ2U6ICfpmYTku7bmraPlnKjlkIzmraXkuK3vvIzor7fnqI3lkI7kuIvovb0nLA0KICAgICAgLy8gICAgICAgICBzaG93Q2FuY2VsQnV0dG9uOiB0cnVlLA0KICAgICAgLy8gICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+WFs+mXrScsDQogICAgICAvLyAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgLy8gICAgICAgICBzaG93Q2FuY2VsQnV0dG9uOiBmYWxzZSwNCiAgICAgIC8vICAgICAgICAgYmVmb3JlQ2xvc2U6IChhY3Rpb24sIGluc3RhbmNlLCBkb25lKSA9PiB7DQogICAgICAvLyAgICAgICAgICAgZG9uZSgpDQogICAgICAvLyAgICAgICAgIH0NCiAgICAgIC8vICAgICAgIH0pDQogICAgICAvLyAgICAgfSBlbHNlIHsNCiAgICAgIC8vICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAn6ZmE5Lu25LiL6L295aSx6LSlJywgdHlwZTogJ2Vycm9yJyB9KQ0KICAgICAgLy8gICAgIH0NCiAgICAgIC8vICAgfSkuY2F0Y2goZXJyID0+IHsgfSkNCiAgICAgIC8vIH0NCiAgICB9LA0KICAgIC8vIOe/u+ivkeagh+mimA0KICAgIHRyYW5zbGF0ZVRpdGxlKHJvdykgew0KICAgICAgY29uc3QgbG9hZGluZyA9IHRoaXMuJGxvYWRpbmcoew0KICAgICAgICBsb2NrOiB0cnVlLA0KICAgICAgICB0ZXh0OiAiTG9hZGluZyIsDQogICAgICAgIHNwaW5uZXI6ICJlbC1pY29uLWxvYWRpbmciLA0KICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwNCiAgICAgIH0pOw0KICAgICAgQVBJLnRyYW5zbGF0aW9uVGl0bGUoew0KICAgICAgICBvcmlnaW5hbFRleHQ6IHJvdy50aXRsZSwNCiAgICAgICAgZG9jSWQ6IHJvdy5kb2NJZCwNCiAgICAgICAgaWQ6IHJvdy5pZCwNCiAgICAgICAgdHJhbnNsYXRpb25GaWVsZDogInRpdGxlIiwNCiAgICAgICAgdHJhbnNsYXRpb25UeXBlOiAxLA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5jblRpdGxlID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdFsNCiAgICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3QuZmluZEluZGV4KCh2YWx1ZSkgPT4gdmFsdWUuaWQgPT0gcm93LmlkKQ0KICAgICAgICAgIF0uY25UaXRsZSA9IHJlcy5kYXRhOw0KICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3RbDQogICAgICAgICAgICB0aGlzLkFydGljbGVMaXN0LmZpbmRJbmRleCgodmFsdWUpID0+IHZhbHVlLmlkID09IHJvdy5pZCkNCiAgICAgICAgICBdLmlzVHJhbnNsYXRlZCA9IDE7DQogICAgICAgICAgbG9hZGluZy5jbG9zZSgpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycikgPT4gew0KICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDnv7vor5Hmlofnq6ANCiAgICB0cmFuc2xhdGVFdmVudChyb3cpIHsNCiAgICAgIGNvbnN0IGxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsNCiAgICAgICAgbG9jazogdHJ1ZSwNCiAgICAgICAgdGV4dDogIkxvYWRpbmciLA0KICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwNCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIsDQogICAgICB9KTsNCiAgICAgIEFQSS50cmFuc2xhdGlvblRpdGxlKHsNCiAgICAgICAgb3JpZ2luYWxUZXh0OiByb3cuY29udGVudCwNCiAgICAgICAgZG9jSWQ6IHJvdy5kb2NJZCwNCiAgICAgICAgaWQ6IHJvdy5pZCwNCiAgICAgICAgdHJhbnNsYXRpb25GaWVsZDogImNvbnRlbnQiLA0KICAgICAgICB0cmFuc2xhdGlvblR5cGU6IDEsDQogICAgICB9KQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCA9IHJlcy5kYXRhOw0KICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3RbDQogICAgICAgICAgICB0aGlzLkFydGljbGVMaXN0LmZpbmRJbmRleCgodmFsdWUpID0+IHZhbHVlLmlkID09IHJvdy5pZCkNCiAgICAgICAgICBdLmNuQ29udGVudCA9IHJlcy5kYXRhOw0KICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3RbDQogICAgICAgICAgICB0aGlzLkFydGljbGVMaXN0LmZpbmRJbmRleCgodmFsdWUpID0+IHZhbHVlLmlkID09IHJvdy5pZCkNCiAgICAgICAgICBdLmlzVHJhbnNsYXRlZCA9IDE7DQogICAgICAgICAgdGhpcy50cmFuc2xhdGlvbkJ0blNob3cgPSBmYWxzZTsNCiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7DQogICAgICAgICAgbG9hZGluZy5jbG9zZSgpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBBUEkuQXJlYUluZm8ocm93LmlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLmZvcm0uc291cmNlVHlwZSA9IE51bWJlcih0aGlzLmZvcm0uc291cmNlVHlwZSk7DQogICAgICAgIHRoaXMuZm9ybS5kb2NJZCA9IHJvdy5kb2NJZDsNCiAgICAgICAgLy8gdGhpcy5maWxlVXJsTGlzdCA9IHRoaXMuZm9ybS5maWxlVXJsID8gdGhpcy5mb3JtLmZpbGVVcmwuc3BsaXQoIiwiKS5tYXAoaXRlbSA9PiB7DQogICAgICAgIC8vICAgcmV0dXJuIHsNCiAgICAgICAgLy8gICAgIG5hbWU6IGl0ZW0sDQogICAgICAgIC8vICAgICB1cmw6IGl0ZW0NCiAgICAgICAgLy8gICB9DQogICAgICAgIC8vIH0pIDogW10NCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTor6XmnaHmlofnq6DvvJ8iJykNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHJldHVybiBBUEkubW9uaXRvcmluZ0VzUmVtb3ZlKHsgaWQ6IHJvdy5pZCwgZG9jSWQ6IHJvdy5kb2NJZCB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuUmVmcmVzaCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGxldCBxdWVyeUZvcm0gPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuZm9ybSkpOw0KICAgICAgICAgIC8vIGxldCBjb3ZlciA9IFN0cmluZyh0aGlzLmZpbGVMaXN0Lm1hcChpdGVtID0+IGl0ZW0ucGF0aCkpDQogICAgICAgICAgLy8gcXVlcnlGb3JtLmNvdmVyID0gY292ZXINCiAgICAgICAgICBhcnRpY2xlTGlzdEVkaXQocXVlcnlGb3JtKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuUmVmcmVzaCgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qIOiHquWumuS5ieS4iuS8oCAqLw0KICAgIGFzeW5jIHJlcXVlc3RMb2FkKGZpbGUpIHsNCiAgICAgIGxldCBkYXRhID0gbmV3IEZvcm1EYXRhKCk7DQogICAgICBkYXRhLmFwcGVuZCgiY292ZXIiLCBmaWxlLmZpbGUpOw0KICAgICAgYXdhaXQgdXBsb2FkQ292ZXIoZGF0YSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5maWxlTGlzdC5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIGlmIChpdGVtLnVpZCA9PSBmaWxlLmZpbGUudWlkKSB7DQogICAgICAgICAgICAgIGl0ZW0ucGF0aCA9IHJlc3BvbnNlLmltZ1VybDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuS4iuS8oOaIkOWKnyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuS4iuS8oOWksei0pSzor7fnqI3lgJnph43or5UiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qIOaWh+S7tui2heWHuumZkOWItiAqLw0KICAgIGV4Y2VlZCgpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICBtZXNzYWdlOiAi5paH5Lu25LiK5Lyg6LaF5Ye66ZmQ5Yi2LOacgOWkmuWPr+S7peS4iuS8oOS4ieS4quaWh+S7tiIsDQogICAgICAgIHR5cGU6ICJpbmZvIiwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyog56e76Zmk5paH5Lu2ICovDQogICAgaGFuZGxlUmVtb3ZlKGZpbGUpIHsNCiAgICAgIHRoaXMuZmlsZUxpc3QgPSB0aGlzLmZpbGVMaXN0LmZpbHRlcigoaXRlbSkgPT4gaXRlbSAhPT0gZmlsZSk7DQogICAgfSwNCiAgICAvLyDmlofku7bmm7TmlLkNCiAgICBoYW5kbGVDaGFuZ2UoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuZmlsZUxpc3QgPSBmaWxlTGlzdDsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOw0KICAgIH0sDQogICAgLy8g5LiK5Lyg6ZmE5Lu25qCh6aqMDQogICAgYmVmb3JlVXBsb2FkVXJsKGZpbGUpIHsNCiAgICAgIC8vIOWIpOaWreaWh+S7tuaYr+WQpuS4umV4Y2VsDQogICAgICBsZXQgZmlsZU5hbWUgPSBmaWxlLm5hbWUNCiAgICAgICAgICAuc3Vic3RyaW5nKGZpbGUubmFtZS5sYXN0SW5kZXhPZigiLiIpICsgMSkNCiAgICAgICAgICAudG9Mb3dlckNhc2UoKSwNCiAgICAgICAgY29uZGl0aW9uID0NCiAgICAgICAgICBmaWxlTmFtZSA9PSAicGRmIiB8fA0KICAgICAgICAgIGZpbGVOYW1lID09ICJkb2MiIHx8DQogICAgICAgICAgZmlsZU5hbWUgPT0gInhscyIgfHwNCiAgICAgICAgICBmaWxlTmFtZSA9PSAicHB0IiB8fA0KICAgICAgICAgIGZpbGVOYW1lID09ICJ4bHN4IiB8fA0KICAgICAgICAgIGZpbGVOYW1lID09ICJwcHR4IiB8fA0KICAgICAgICAgIGZpbGVOYW1lID09ICJkb2N4IjsNCiAgICAgIGxldCBmaWxlU2l6ZSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTA7DQogICAgICBpZiAoIWNvbmRpdGlvbikgew0KICAgICAgICB0aGlzLiRub3RpZnkoew0KICAgICAgICAgIHRpdGxlOiAi6K2m5ZGKIiwNCiAgICAgICAgICBtZXNzYWdlOiAi5LiK5Lyg5paH5Lu25b+F6aG75pivcGRmLGRvYyx4bHMscHB0LHhsc3gscHB0eCxkb2N45qC85byPIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgLyog5paH5Lu25aSn5bCP6ZmQ5Yi2ICovDQogICAgICBpZiAoIWZpbGVTaXplKSB7DQogICAgICAgIHRoaXMuJG5vdGlmeSh7DQogICAgICAgICAgdGl0bGU6ICLorablkYoiLA0KICAgICAgICAgIG1lc3NhZ2U6ICLkuIrkvKDmlofku7bnmoTlpKflsI/kuI3og73otoXov4cgMTBNQiEiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICByZXR1cm4gY29uZGl0aW9uICYmIGZpbGVTaXplOw0KICAgIH0sDQogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5YqfDQogICAgdXBsb2FkVXJsU3VjY2VzcyhyZXMsIGZpbGUpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5LiK5Lyg5oiQ5YqfIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgIH0sDQogICAgLy8g5paH5Lu25LiK5Lyg6LaF5Ye66ZmQ5Yi2DQogICAgdXBsb2FkVXJsRXhjZWVkKCkgew0KICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgIG1lc3NhZ2U6ICLmlofku7bkuIrkvKDotoXlh7rpmZDliLYs5pyA5aSa5Y+v5Lul5LiK5LygMeS4quaWh+S7tiIsDQogICAgICAgIHR5cGU6ICJpbmZvIiwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5paH5Lu25LiK5Lyg5pa55rOVDQogICAgdXBsb2FkVXJsUmVxdWVzdChmaWxlKSB7DQogICAgICBpZiAodGhpcy5mb3JtLm9yaWdpbmFsVXJsICE9IG51bGwgJiYgdGhpcy5mb3JtLm9yaWdpbmFsVXJsICE9ICIiKSB7DQogICAgICAgIGlmICgNCiAgICAgICAgICB0aGlzLmZvcm0ub3JpZ2luYWxVcmwubWF0Y2goDQogICAgICAgICAgICAvKGh0dHB8aHR0cHMpOlwvXC9bXHdcLV9dKyhcLltcd1wtX10rKSsoW1x3XC1cLixAP149JSY6L35cKyNdKltcd1wtXEA/Xj0lJi9+XCsjXSk/Lw0KICAgICAgICAgICkNCiAgICAgICAgKSB7DQogICAgICAgICAgbGV0IGRhdGEgPSBuZXcgRm9ybURhdGEoKTsNCiAgICAgICAgICBkYXRhLmFwcGVuZCgiZmlsZSIsIGZpbGUuZmlsZSk7DQogICAgICAgICAgZGF0YS5hcHBlbmQoIm9yaWdpbmFsVXJsIiwgdGhpcy5mb3JtLm9yaWdpbmFsVXJsKTsNCg0KICAgICAgICAgIEFQSS51cGxvYWRGaWxlKGRhdGEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLkuIrkvKDmiJDlip8iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgICAgICAgIHRoaXMuZm9ybS5maWxlVXJsID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5LiK5Lyg5aSx6LSlLOivt+eojeWAmemHjeivlSIsIHR5cGU6ICJlcnJvciIgfSk7DQogICAgICAgICAgICAgIHRoaXMuZm9ybS5maWxlVXJsID0gIiI7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLor7floavlhpnmraPnoa7nmoTljp/mlofpk77mjqUiLCB0eXBlOiAid2FybmluZyIgfSk7DQogICAgICAgICAgdGhpcy5maWxlVXJsTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuivt+Whq+WGmeWOn+aWh+mTvuaOpSIsIHR5cGU6ICJ3YXJuaW5nIiB9KTsNCiAgICAgICAgdGhpcy5maWxlVXJsTGlzdCA9IFtdOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5Yig6Zmk6ZmE5Lu2DQogICAgdXBsb2FkVXJsUmVtb3ZlKCkgew0KICAgICAgQVBJLnJlbW92ZUZpbGUoeyBmaWxlUGF0aDogdGhpcy5mb3JtLmZpbGVVcmwgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLliKDpmaTmiJDlip8iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgICAgdGhpcy5maWxlVXJsTGlzdCA9IFtdOw0KICAgICAgICAgIHRoaXMuZm9ybS5maWxlVXJsID0gIiI7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLliKDpmaTlpLHotKUs6K+356iN5YCZ6YeN6K+VIiwgdHlwZTogImVycm9yIiB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgYXJ0aWNsZVNuOiBudWxsLA0KICAgICAgICB0aXRsZTogbnVsbCwNCiAgICAgICAgY25UaXRsZTogbnVsbCwNCiAgICAgICAgc291cmNlVHlwZTogbnVsbCwNCiAgICAgICAgc291cmNlTmFtZTogbnVsbCwNCiAgICAgICAgc291cmNlU246IG51bGwsDQogICAgICAgIG9yaWdpbmFsVXJsOiBudWxsLA0KICAgICAgICBzaG9ydFVybDogbnVsbCwNCiAgICAgICAgYXV0aG9yOiBudWxsLA0KICAgICAgICBkZXNjcmlwdGlvbjogbnVsbCwNCiAgICAgICAgc3VtbWFyeTogbnVsbCwNCiAgICAgICAgY25TdW1tYXJ5OiBudWxsLA0KICAgICAgICBjb3ZlcjogbnVsbCwNCiAgICAgICAgcHVibGlzaFR5cGU6IG51bGwsDQogICAgICAgIHB1Ymxpc2hDb2RlOiBudWxsLA0KICAgICAgICBwdWJsaXNoQXJlYTogbnVsbCwNCiAgICAgICAgcHVibGlzaFRpbWU6IG51bGwsDQogICAgICAgIG51bWJlckxpa2VzOiBudWxsLA0KICAgICAgICBudW1iZXJSZWFkczogbnVsbCwNCiAgICAgICAgbnVtYmVyQ29sbGVjdHM6IG51bGwsDQogICAgICAgIG51bWJlclNoYXJlczogbnVsbCwNCiAgICAgICAgbnVtYmVyQ29tbWVudHM6IG51bGwsDQogICAgICAgIGVtb3Rpb246IG51bGwsDQogICAgICAgIHN0YXR1czogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVzZXJJZDogbnVsbCwNCiAgICAgICAgZGVwdElkOiBudWxsLA0KICAgICAgICBjb250ZW50OiBudWxsLA0KICAgICAgICBjbkNvbnRlbnQ6IG51bGwsDQogICAgICAgIGZpbGVVcmw6IG51bGwsDQogICAgICAgIGluZHVzdHJ5OiBudWxsLA0KICAgICAgICBkb21haW46IG51bGwsDQogICAgICAgIHRtcFVybDogbnVsbCwNCiAgICAgICAgaXNGaW5pc2g6IG51bGwsDQogICAgICAgIGdyb3VwSWQ6IG51bGwsDQogICAgICAgIGFwcElkOiBudWxsLA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvLyDmibnph4/lr7zlhaXnm7jlhbPmlrnms5UNCiAgICAvLyDmiZPlvIDmibnph4/lr7zlhaXlvLnmoYYNCiAgICBvcGVuQmF0Y2hJbXBvcnREaWFsb2coKSB7DQogICAgICB0aGlzLmJhdGNoSW1wb3J0VmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmJhdGNoSW1wb3J0RmlsZXMgPSBbXTsNCiAgICB9LA0KICAgIC8vIOaWh+S7tumAieaLqeWkhOeQhg0KICAgIGhhbmRsZUZpbGVTZWxlY3QoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIC8vIOWwhuaWsOmAieaLqeeahOaWh+S7tua3u+WKoOWIsOWIl+ihqOS4rQ0KICAgICAgY29uc3QgbmV3RmlsZXMgPSBmaWxlTGlzdC5tYXAoKGl0ZW0pID0+ICh7DQogICAgICAgIGZpbGVOYW1lOiBpdGVtLm5hbWUsDQogICAgICAgIGZpbGU6IGl0ZW0ucmF3LA0KICAgICAgICBzb3VyY2VOYW1lOiAiIiwNCiAgICAgIH0pKTsNCiAgICAgIHRoaXMuYmF0Y2hJbXBvcnRGaWxlcyA9IG5ld0ZpbGVzOw0KICAgIH0sDQogICAgLy8g5Yig6Zmk5paH5Lu2DQogICAgcmVtb3ZlRmlsZShpbmRleCkgew0KICAgICAgdGhpcy5iYXRjaEltcG9ydEZpbGVzLnNwbGljZShpbmRleCwgMSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmibnph4/lr7zlhaUNCiAgICBjYW5jZWxCYXRjaEltcG9ydCgpIHsNCiAgICAgIHRoaXMuYmF0Y2hJbXBvcnRWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLmJhdGNoSW1wb3J0RmlsZXMgPSBbXTsNCiAgICAgIC8vIOa4heepuuaWh+S7tumAieaLqeWZqA0KICAgICAgdGhpcy4kcmVmcy5iYXRjaFVwbG9hZC5jbGVhckZpbGVzKCk7DQogICAgfSwNCiAgICAvLyDnoa7orqTmibnph4/lr7zlhaUNCiAgICBhc3luYyBjb25maXJtQmF0Y2hJbXBvcnQoKSB7DQogICAgICAvLyDpqozor4HmlbDmja7mupDlkI3np7DmmK/lkKbpg73lt7LloavlhpkNCiAgICAgIGNvbnN0IGVtcHR5U291cmNlTmFtZXMgPSB0aGlzLmJhdGNoSW1wb3J0RmlsZXMuZmlsdGVyKA0KICAgICAgICAoaXRlbSkgPT4gIWl0ZW0uc291cmNlTmFtZS50cmltKCkNCiAgICAgICk7DQogICAgICBpZiAoZW1wdHlTb3VyY2VOYW1lcy5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7fkuLrmiYDmnInmlofku7bloavlhpnmlbDmja7mupDlkI3np7AiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCg0KICAgICAgICAvLyDliJvlu7pGb3JtRGF0YeWvueixoQ0KICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpOw0KDQogICAgICAgIC8vIOa3u+WKoOaWh+S7tuWIsEZvcm1EYXRhDQogICAgICAgIHRoaXMuYmF0Y2hJbXBvcnRGaWxlcy5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgZm9ybURhdGEuYXBwZW5kKCJmaWxlcyIsIGl0ZW0uZmlsZSk7DQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOiOt+WPluaVsOaNrua6kOWQjeensOaVsOe7hA0KICAgICAgICBjb25zdCBzb3VyY2VOYW1lcyA9IHRoaXMuYmF0Y2hJbXBvcnRGaWxlcw0KICAgICAgICAgIC5tYXAoKGl0ZW0pID0+IGl0ZW0uc291cmNlTmFtZSkNCiAgICAgICAgICAuam9pbigiLCIpOw0KDQogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgic291cmNlTmFtZXMiLCBzb3VyY2VOYW1lcyk7DQoNCiAgICAgICAgLy8g6LCD55So5om56YeP5a+85YWlQVBJ77yM5Lyg6YCSRm9ybURhdGHlkoxzb3VyY2VOYW1lc+WPguaVsA0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IEFQSS5iYXRjaEltcG9ydFJlcG9ydHMoZm9ybURhdGEpOw0KDQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLmibnph4/lr7zlhaXmiJDlip8iLA0KICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuYmF0Y2hJbXBvcnRWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5iYXRjaEltcG9ydEZpbGVzID0gW107DQogICAgICAgICAgdGhpcy4kcmVmcy5iYXRjaFVwbG9hZC5jbGVhckZpbGVzKCk7DQogICAgICAgICAgLy8g5Yi35paw5YiX6KGoDQogICAgICAgICAgdGhpcy5SZWZyZXNoKCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5tc2cgfHwgIuaJuemHj+WvvOWFpeWksei0pSIsDQogICAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLmibnph4/lr7zlhaXplJnor686IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi5om56YeP5a+85YWl5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIiwNCiAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICB9KTsNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQogICAgcmVwb3J0QWlDaGF0KCkgew0KICAgICAgdGhpcy5zaG93RGVlcHNlZWtEaWFsb2cgPSB0cnVlOw0KICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLor7fpgInmi6nopoHop6Por7vnmoTmlofnq6AiLCB0eXBlOiAid2FybmluZyIgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPiAxKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi6K+35Y+q6YCJ5oup5LiA56+H5paH56ug6L+b6KGM6Kej6K+7IiwgdHlwZTogIndhcm5pbmciIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOiOt+WPlumAieS4reeahOaWh+eroA0KICAgICAgY29uc3Qgc2VsZWN0ZWRBcnRpY2xlSWQgPSB0aGlzLmNoZWNrZWRDaXRpZXNbMF07DQogICAgICBjb25zdCBzZWxlY3RlZEFydGljbGUgPSB0aGlzLkFydGljbGVMaXN0LmZpbmQoDQogICAgICAgIChpdGVtKSA9PiBpdGVtLmlkID09PSBzZWxlY3RlZEFydGljbGVJZA0KICAgICAgKTsNCg0KICAgICAgaWYgKHNlbGVjdGVkQXJ0aWNsZSkgew0KICAgICAgICB0aGlzLmN1cnJlbnRBcnRpY2xlID0gc2VsZWN0ZWRBcnRpY2xlOw0KICAgICAgICB0aGlzLnNob3dEZWVwc2Vla0RpYWxvZyA9IHRydWU7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuacquaJvuWIsOmAieS4reeahOaWh+eroCIsIHR5cGU6ICJlcnJvciIgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyBhaeebuOWFsw0KICAgIC8vIGRpZnkNCiAgICBhc3luYyBkaWZ5QWlDaGF0KCkgew0KICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7flhYjpgInmi6nopoHop6Por7vnmoTmlofnq6AiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOacieato+WcqOi/m+ihjOeahOivt+axgu+8jOS4reaWreWugw0KICAgICAgaWYgKHRoaXMuaXNSZXF1ZXN0aW5nKSB7DQogICAgICAgIHRoaXMuaXNBYm9ydGVkID0gdHJ1ZTsNCiAgICAgICAgaWYgKHRoaXMuY3VycmVudFJlYWRlcikgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBhd2FpdCB0aGlzLmN1cnJlbnRSZWFkZXIuY2FuY2VsKCk7DQogICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgY29uc29sZS5sb2coIuS4reaWreS5i+WJjeeahOivt+axguWksei0pSIsIGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSB0cnVlOw0KICAgICAgdGhpcy5pc0Fib3J0ZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMuYWlEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzID0gW107DQogICAgICB0aGlzLmlzVGhpbmtpbmcgPSB0cnVlOw0KDQogICAgICB0cnkgew0KICAgICAgICAvLyDojrflj5bpgInkuK3nmoTmlofnq6ANCiAgICAgICAgY29uc3Qgc2VsZWN0ZWRBcnRpY2xlcyA9IHRoaXMuQXJ0aWNsZUxpc3QuZmlsdGVyKChhcnRpY2xlKSA9Pg0KICAgICAgICAgIHRoaXMuY2hlY2tlZENpdGllcy5pbmNsdWRlcyhhcnRpY2xlLmlkKQ0KICAgICAgICApOw0KICAgICAgICBjb25zdCB0aXRsZXMgPSBzZWxlY3RlZEFydGljbGVzDQogICAgICAgICAgLm1hcCgoYXJ0aWNsZSkgPT4gYOOAiiR7YXJ0aWNsZS5jblRpdGxlIHx8IGFydGljbGUudGl0bGV944CLYCkNCiAgICAgICAgICAuam9pbigiXG4iKTsNCg0KICAgICAgICAvLyDojrflj5bmlofnq6DlhoXlrrkNCiAgICAgICAgY29uc3QgYXJ0aWNsZXNSZXNwb25zZSA9IGF3YWl0IGdldExpc3RCeUlkcygNCiAgICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMuam9pbigiLCIpDQogICAgICAgICk7DQogICAgICAgIGlmICghYXJ0aWNsZXNSZXNwb25zZS5kYXRhPy5sZW5ndGgpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIuiOt+WPluaWh+eroOWGheWuueWksei0pSIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qC85byP5YyW5paH56ug5YaF5a65DQogICAgICAgIGNvbnN0IGFydGljbGVzQ29udGVudCA9IGFydGljbGVzUmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIC5tYXAoKGFydGljbGUsIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB0aXRsZSA9DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy5jblRpdGxlIHx8DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy50aXRsZSB8fA0KICAgICAgICAgICAgICAiIjsNCiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSBhcnRpY2xlLmNvbnRlbnQgfHwgIiI7DQogICAgICAgICAgICByZXR1cm4gYOOAkOesrCAke2luZGV4ICsgMX0g56+H5paH56ug44CR44CKJHt0aXRsZX3jgItcblxuJHtjb250ZW50fWA7DQogICAgICAgICAgfSkNCiAgICAgICAgICAuam9pbigiXG5cbi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuIik7DQoNCiAgICAgICAgLy8g5re75Yqg55So5oi35raI5oGvDQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goew0KICAgICAgICAgIHJvbGU6ICJ1c2VyIiwNCiAgICAgICAgICBjb250ZW50OiBg5biu5oiR5rex5bqm6Kej6K+75Lul5LiLJHt0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3Rofeevh+aWh+eroO+8mlxuJHt0aXRsZXN9YCwNCiAgICAgICAgfSk7DQoNCiAgICAgICAgLy8g5Yib5bu6QUnmtojmga8NCiAgICAgICAgY29uc3QgYWlNZXNzYWdlID0gew0KICAgICAgICAgIHJvbGU6ICJhc3Npc3RhbnQiLA0KICAgICAgICAgIGNvbnRlbnQ6ICIiLA0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLmNoYXRNZXNzYWdlcy5wdXNoKGFpTWVzc2FnZSk7DQoNCiAgICAgICAgLy8g5p6E5bu65o+Q56S66K+NDQogICAgICAgIGNvbnN0IHByb21wdCA9DQogICAgICAgICAgdGhpcy5hcnRpY2xlQWlQcm9tcHQNCiAgICAgICAgICAgIC5yZXBsYWNlKCJhcnRpY2xlTGVuZ3RoIiwgdGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCkNCiAgICAgICAgICAgIC5yZXBsYWNlKC9cJmd0Oy9nLCAiPiIpICsNCiAgICAgICAgICBgKirku6XkuIvmmK/lvoXlpITnkIbnmoTmlofnq6DvvJoqKlxuXG4ke2FydGljbGVzQ29udGVudH1gOw0KDQogICAgICAgIC8vIOiwg+eUqEFJ5o6l5Y+jDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGlmeUFpUWEoDQogICAgICAgICAgYXJ0aWNsZXNDb250ZW50LA0KICAgICAgICAgICJzdHJlYW1pbmciLA0KICAgICAgICAgICJkaWZ5LmFydGljbGUuYXBpa2V5Ig0KICAgICAgICApOw0KICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBSeaOpeWPo+iwg+eUqOWksei0pSIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aSE55CG5rWB5byP5ZON5bqUDQogICAgICAgIGNvbnN0IHJlYWRlciA9IHJlc3BvbnNlLmJvZHkuZ2V0UmVhZGVyKCk7DQogICAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IHJlYWRlcjsNCiAgICAgICAgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpOw0KICAgICAgICBsZXQgYnVmZmVyID0gIiI7DQogICAgICAgIGxldCBwZW5kaW5nQnVmZmVyID0gIiI7IC8vIOeUqOS6juWtmOWCqOW+heWkhOeQhueahOS4jeWujOaVtOaVsOaNrg0KICAgICAgICBsZXQgaXNJblRoaW5rVGFnID0gZmFsc2U7IC8vIOaWsOWinu+8muagh+iusOaYr+WQpuWcqHRoaW5r5qCH562+5YaFDQoNCiAgICAgICAgLy8g5bCGVW5pY29kZei9rOS5ieWtl+espihcdVhYWFgp6L2s5o2i5Li65a6e6ZmF5a2X56ymDQogICAgICAgIGNvbnN0IGRlY29kZVVuaWNvZGUgPSAoc3RyKSA9PiB7DQogICAgICAgICAgcmV0dXJuIHN0ci5yZXBsYWNlKC9cXHVbXGRBLUZhLWZdezR9L2csIChtYXRjaCkgPT4gew0KICAgICAgICAgICAgcmV0dXJuIFN0cmluZy5mcm9tQ2hhckNvZGUocGFyc2VJbnQobWF0Y2gucmVwbGFjZSgvXFx1L2csICIiKSwgMTYpKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDmm7TmlrDlhoXlrrnnmoTlh73mlbANCiAgICAgICAgY29uc3QgdXBkYXRlQ29udGVudCA9IChuZXdDb250ZW50KSA9PiB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGNvbnN0IHJlbmRlcmVkQ29udGVudCA9IG1hcmtlZChuZXdDb250ZW50LCB0aGlzLm1hcmtkb3duT3B0aW9ucyk7DQogICAgICAgICAgICBhaU1lc3NhZ2UuY29udGVudCA9IHJlbmRlcmVkQ29udGVudDsNCg0KICAgICAgICAgICAgLy8g56Gu5L+d5raI5oGv5a655Zmo5rua5Yqo5Yiw5bqV6YOoDQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGNoYXRNZXNzYWdlcyA9IHRoaXMuJHJlZnMuY2hhdE1lc3NhZ2VzOw0KICAgICAgICAgICAgICBpZiAoY2hhdE1lc3NhZ2VzKSB7DQogICAgICAgICAgICAgICAgY2hhdE1lc3NhZ2VzLnNjcm9sbFRvcCA9IGNoYXRNZXNzYWdlcy5zY3JvbGxIZWlnaHQ7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCLmuLLmn5PlhoXlrrnml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICAgIH0NCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDlpITnkIbmtYHlvI/lk43lupQNCiAgICAgICAgd2hpbGUgKHRydWUpIHsNCiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7LkuK3mlq0NCiAgICAgICAgICBpZiAodGhpcy5pc0Fib3J0ZWQpIHsNCiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQWJvcnRFcnJvciIpOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IHsgZG9uZSwgdmFsdWUgfSA9IGF3YWl0IHJlYWRlci5yZWFkKCk7DQoNCiAgICAgICAgICBpZiAoZG9uZSkgew0KICAgICAgICAgICAgLy8g5aSE55CG5pyA5ZCO5Y+v6IO95Ymp5L2Z55qE5pWw5o2uDQogICAgICAgICAgICBpZiAocGVuZGluZ0J1ZmZlcikgew0KICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgIGNvbnN0IGxhc3REYXRhID0gSlNPTi5wYXJzZShwZW5kaW5nQnVmZmVyKTsNCiAgICAgICAgICAgICAgICBpZiAobGFzdERhdGEuYW5zd2VyKSB7DQogICAgICAgICAgICAgICAgICAvLyDop6PnoIFVbmljb2Rl6L2s5LmJ5a2X56ymDQogICAgICAgICAgICAgICAgICBjb25zdCBkZWNvZGVkQW5zd2VyID0gZGVjb2RlVW5pY29kZShsYXN0RGF0YS5hbnN3ZXIpOw0KICAgICAgICAgICAgICAgICAgYnVmZmVyICs9IGRlY29kZWRBbnN3ZXI7DQogICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KGJ1ZmZlcik7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCLlpITnkIbmnIDlkI7nmoTmlbDmja7ml7blh7rplJk6IiwgZSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpOw0KICAgICAgICAgIHBlbmRpbmdCdWZmZXIgKz0gY2h1bms7DQoNCiAgICAgICAgICAvLyDlpITnkIblrozmlbTnmoTmlbDmja7ooYwNCiAgICAgICAgICB3aGlsZSAocGVuZGluZ0J1ZmZlci5pbmNsdWRlcygiXG4iKSkgew0KICAgICAgICAgICAgY29uc3QgbmV3bGluZUluZGV4ID0gcGVuZGluZ0J1ZmZlci5pbmRleE9mKCJcbiIpOw0KICAgICAgICAgICAgY29uc3QgbGluZSA9IHBlbmRpbmdCdWZmZXIuc2xpY2UoMCwgbmV3bGluZUluZGV4KS50cmltKCk7DQogICAgICAgICAgICBwZW5kaW5nQnVmZmVyID0gcGVuZGluZ0J1ZmZlci5zbGljZShuZXdsaW5lSW5kZXggKyAxKTsNCg0KICAgICAgICAgICAgaWYgKCFsaW5lIHx8IGxpbmUgPT09ICJkYXRhOiIgfHwgIWxpbmUuc3RhcnRzV2l0aCgiZGF0YToiKSkgew0KICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGxpbmUuc2xpY2UoNSkudHJpbSgpOw0KICAgICAgICAgICAgICBpZiAoZGF0YSA9PT0gIltET05FXSIpIHsNCiAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIGNvbnN0IGpzb25EYXRhID0gSlNPTi5wYXJzZShkYXRhKTsNCiAgICAgICAgICAgICAgaWYgKCFqc29uRGF0YS5hbnN3ZXIpIHsNCiAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOi3s+i/h+eJueauiuWtl+espg0KICAgICAgICAgICAgICBpZiAoanNvbkRhdGEuYW5zd2VyID09PSAiYGBgIiB8fCBqc29uRGF0YS5hbnN3ZXIgPT09ICJtYXJrZG93biIpIHsNCiAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOino+eggVVuaWNvZGXovazkuYnlrZfnrKYNCiAgICAgICAgICAgICAgbGV0IGFuc3dlciA9IGRlY29kZVVuaWNvZGUoanNvbkRhdGEuYW5zd2VyKTsNCg0KICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbljIXlkKs8dGhpbms+5byA5aeL5qCH562+DQogICAgICAgICAgICAgIGlmIChhbnN3ZXIuaW5jbHVkZXMoIjx0aGluaz4iKSkgew0KICAgICAgICAgICAgICAgIGlzSW5UaGlua1RhZyA9IHRydWU7DQogICAgICAgICAgICAgICAgY29udGludWU7IC8vIOi3s+i/h+WMheWQqzx0aGluaz7nmoTpg6jliIYNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuWMheWQqzwvdGhpbms+57uT5p2f5qCH562+DQogICAgICAgICAgICAgIGlmIChhbnN3ZXIuaW5jbHVkZXMoIjwvdGhpbms+IikpIHsNCiAgICAgICAgICAgICAgICBpc0luVGhpbmtUYWcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICBjb250aW51ZTsgLy8g6Lez6L+H5YyF5ZCrPC90aGluaz7nmoTpg6jliIYNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOWPquacieS4jeWcqHRoaW5r5qCH562+5YaF55qE5YaF5a655omN5Lya6KKr5re75Yqg5YiwYnVmZmVy5LitDQogICAgICAgICAgICAgIGlmICghaXNJblRoaW5rVGFnICYmIGFuc3dlcikgew0KICAgICAgICAgICAgICAgIGJ1ZmZlciArPSBhbnN3ZXI7DQogICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChidWZmZXIpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7DQogICAgICAgICAgICAgIGNvbnNvbGUud2Fybigi6Kej5p6Q5pWw5o2u6KGM5pe25Ye66ZSZOiIsIHsNCiAgICAgICAgICAgICAgICBsaW5lLA0KICAgICAgICAgICAgICAgIGVycm9yOiBwYXJzZUVycm9yLm1lc3NhZ2UsDQogICAgICAgICAgICAgICAgcGVuZGluZ0J1ZmZlciwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigiQUnop6Por7vlh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgIkFJ6Kej6K+75aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgIGlmICh0aGlzLmNoYXRNZXNzYWdlc1sxXSkgew0KICAgICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzWzFdLmNvbnRlbnQgPSAi5oqx5q2J77yM5pyN5Yqh5Zmo57mB5b+Z77yM6K+356iN5ZCO5YaN6K+VIjsNCiAgICAgICAgfQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gbnVsbDsNCiAgICAgICAgaWYgKHRoaXMuYWlEaWFsb2dWaXNpYmxlKSB7DQogICAgICAgICAgdGhpcy5pc1RoaW5raW5nID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8gT2xsYW1hDQogICAgYXN5bmMgb2xsYW1hQWlDaGF0KCkgew0KICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7flhYjpgInmi6nopoHop6Por7vnmoTmlofnq6AiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOacieato+WcqOi/m+ihjOeahOivt+axgu+8jOS4reaWreWugw0KICAgICAgaWYgKHRoaXMuaXNSZXF1ZXN0aW5nKSB7DQogICAgICAgIHRoaXMuaXNBYm9ydGVkID0gdHJ1ZTsNCiAgICAgICAgaWYgKHRoaXMuY3VycmVudFJlYWRlcikgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBhd2FpdCB0aGlzLmN1cnJlbnRSZWFkZXIuY2FuY2VsKCk7DQogICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgY29uc29sZS5sb2coIuS4reaWreS5i+WJjeeahOivt+axguWksei0pSIsIGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDnrYnlvoXkuYvliY3nmoTor7fmsYLnirbmgIHmuIXnkIblrozmiJANCiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gdHJ1ZTsNCiAgICAgIHRoaXMuaXNBYm9ydGVkID0gZmFsc2U7DQogICAgICB0aGlzLmFpRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmNoYXRNZXNzYWdlcyA9IFtdOw0KICAgICAgdGhpcy5pc1RoaW5raW5nID0gdHJ1ZTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6I635Y+W6YCJ5Lit55qE5paH56ugDQogICAgICAgIGNvbnN0IHNlbGVjdGVkQXJ0aWNsZXMgPSB0aGlzLkFydGljbGVMaXN0LmZpbHRlcigoYXJ0aWNsZSkgPT4NCiAgICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMuaW5jbHVkZXMoYXJ0aWNsZS5pZCkNCiAgICAgICAgKTsNCiAgICAgICAgY29uc3QgdGl0bGVzID0gc2VsZWN0ZWRBcnRpY2xlcw0KICAgICAgICAgIC5tYXAoKGFydGljbGUpID0+IGDjgIoke2FydGljbGUuY25UaXRsZSB8fCBhcnRpY2xlLnRpdGxlfeOAi2ApDQogICAgICAgICAgLmpvaW4oIlxuIik7DQoNCiAgICAgICAgLy8g6I635Y+W5paH56ug5YaF5a65DQogICAgICAgIGNvbnN0IGFydGljbGVzUmVzcG9uc2UgPSBhd2FpdCBnZXRMaXN0QnlJZHMoDQogICAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzLmpvaW4oIiwiKQ0KICAgICAgICApOw0KICAgICAgICBpZiAoIWFydGljbGVzUmVzcG9uc2UuZGF0YT8ubGVuZ3RoKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCLojrflj5bmlofnq6DlhoXlrrnlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOagvOW8j+WMluaWh+eroOWGheWuuQ0KICAgICAgICBjb25zdCBhcnRpY2xlc0NvbnRlbnQgPSBhcnRpY2xlc1Jlc3BvbnNlLmRhdGENCiAgICAgICAgICAubWFwKChhcnRpY2xlLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgY29uc3QgdGl0bGUgPQ0KICAgICAgICAgICAgICBzZWxlY3RlZEFydGljbGVzW2luZGV4XT8uY25UaXRsZSB8fA0KICAgICAgICAgICAgICBzZWxlY3RlZEFydGljbGVzW2luZGV4XT8udGl0bGUgfHwNCiAgICAgICAgICAgICAgIiI7DQogICAgICAgICAgICBjb25zdCBjb250ZW50ID0gYXJ0aWNsZS5jb250ZW50IHx8ICIiOw0KICAgICAgICAgICAgcmV0dXJuIGDjgJDnrKwgJHtpbmRleCArIDF9IOevh+aWh+eroOOAkeOAiiR7dGl0bGV944CLXG5cbiR7Y29udGVudH1gOw0KICAgICAgICAgIH0pDQogICAgICAgICAgLmpvaW4oIlxuXG4tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiIpOw0KDQogICAgICAgIC8vIOa3u+WKoOeUqOaIt+a2iOaBrw0KICAgICAgICB0aGlzLmNoYXRNZXNzYWdlcy5wdXNoKHsNCiAgICAgICAgICByb2xlOiAidXNlciIsDQogICAgICAgICAgY29udGVudDogYOW4ruaIkea3seW6puino+ivu+S7peS4iyR7dGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aH3nr4fmlofnq6DvvJpcbiR7dGl0bGVzfWAsDQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOWIm+W7ukFJ5raI5oGvDQogICAgICAgIGNvbnN0IGFpTWVzc2FnZSA9IHsNCiAgICAgICAgICByb2xlOiAiYXNzaXN0YW50IiwNCiAgICAgICAgICBjb250ZW50OiAiIiwNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaChhaU1lc3NhZ2UpOw0KDQogICAgICAgIC8vIOaehOW7uuaPkOekuuivjQ0KICAgICAgICBjb25zdCBwcm9tcHQgPQ0KICAgICAgICAgIHRoaXMuYXJ0aWNsZUFpUHJvbXB0DQogICAgICAgICAgICAucmVwbGFjZSgiYXJ0aWNsZUxlbmd0aCIsIHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGgpDQogICAgICAgICAgICAucmVwbGFjZSgvXCZndDsvZywgIj4iKSArDQogICAgICAgICAgYCoq5Lul5LiL5piv5b6F5aSE55CG55qE5paH56ug77yaKipcblxuJHthcnRpY2xlc0NvbnRlbnR9YDsNCg0KICAgICAgICAvLyDosIPnlKhBSeaOpeWPow0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG9sbGFtYUFpUWEocHJvbXB0LCB0cnVlKTsNCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQUnmjqXlj6PosIPnlKjlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWkhOeQhua1geW8j+WTjeW6lA0KICAgICAgICBjb25zdCByZWFkZXIgPSByZXNwb25zZS5ib2R5LmdldFJlYWRlcigpOw0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSByZWFkZXI7IC8vIOS/neWtmOW9k+WJjeeahCByZWFkZXINCiAgICAgICAgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpOw0KICAgICAgICBsZXQgYnVmZmVyID0gIiI7DQogICAgICAgIGxldCBsYXN0VXBkYXRlVGltZSA9IERhdGUubm93KCk7DQogICAgICAgIGxldCBpc1RoaW5rQ29udGVudCA9IGZhbHNlOw0KICAgICAgICBsZXQgdGVtcEJ1ZmZlciA9ICIiOw0KDQogICAgICAgIC8vIOabtOaWsOWGheWuueeahOWHveaVsA0KICAgICAgICBjb25zdCB1cGRhdGVDb250ZW50ID0gKG5ld0NvbnRlbnQpID0+IHsNCiAgICAgICAgICBjb25zdCBjdXJyZW50VGltZSA9IERhdGUubm93KCk7DQogICAgICAgICAgLy8g5o6n5Yi25pu05paw6aKR546H77yM6YG/5YWN6L+H5LqO6aKR57mB55qERE9N5pu05pawDQogICAgICAgICAgaWYgKGN1cnJlbnRUaW1lIC0gbGFzdFVwZGF0ZVRpbWUgPj0gNTApIHsNCiAgICAgICAgICAgIGFpTWVzc2FnZS5jb250ZW50ID0gbmV3Q29udGVudDsNCiAgICAgICAgICAgIGxhc3RVcGRhdGVUaW1lID0gY3VycmVudFRpbWU7DQogICAgICAgICAgICAvLyDnoa7kv53mtojmga/lrrnlmajmu5rliqjliLDlupXpg6gNCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgY2hhdE1lc3NhZ2VzID0gdGhpcy4kcmVmcy5jaGF0TWVzc2FnZXM7DQogICAgICAgICAgICAgIGlmIChjaGF0TWVzc2FnZXMpIHsNCiAgICAgICAgICAgICAgICBjaGF0TWVzc2FnZXMuc2Nyb2xsVG9wID0gY2hhdE1lc3NhZ2VzLnNjcm9sbEhlaWdodDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9Ow0KDQogICAgICAgIC8vIOWkhOeQhua1geW8j+WTjeW6lA0KICAgICAgICBjb25zdCBwcm9jZXNzU3RyZWFtID0gYXN5bmMgKCkgPT4gew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICB3aGlsZSAodHJ1ZSkgew0KICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7LkuK3mlq0NCiAgICAgICAgICAgICAgaWYgKHRoaXMuaXNBYm9ydGVkKSB7DQogICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBYm9ydEVycm9yIik7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpOw0KICAgICAgICAgICAgICBpZiAoZG9uZSkgew0KICAgICAgICAgICAgICAgIGlmIChidWZmZXIubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChidWZmZXIpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpOw0KICAgICAgICAgICAgICBjb25zdCBsaW5lcyA9IGNodW5rLnNwbGl0KCJcbiIpLmZpbHRlcigobGluZSkgPT4gbGluZS50cmltKCkpOw0KDQogICAgICAgICAgICAgIGZvciAoY29uc3QgbGluZSBvZiBsaW5lcykgew0KICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICBjb25zdCBqc29uRGF0YSA9IEpTT04ucGFyc2UobGluZSk7DQogICAgICAgICAgICAgICAgICBpZiAoIWpzb25EYXRhLnJlc3BvbnNlKSBjb250aW51ZTsNCg0KICAgICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBqc29uRGF0YS5yZXNwb25zZTsNCg0KICAgICAgICAgICAgICAgICAgLy8g6Lez6L+H54m55q6K5a2X56ymDQogICAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UgPT09ICJgYGAiIHx8IHJlc3BvbnNlID09PSAibWFya2Rvd24iKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlOw0KICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICB0ZW1wQnVmZmVyICs9IHJlc3BvbnNlOw0KDQogICAgICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbljIXlkKvlrozmlbTnmoR0aGlua+agh+etvuWvuQ0KICAgICAgICAgICAgICAgICAgd2hpbGUgKHRydWUpIHsNCiAgICAgICAgICAgICAgICAgICAgY29uc3QgdGhpbmtTdGFydEluZGV4ID0gdGVtcEJ1ZmZlci5pbmRleE9mKCI8dGhpbms+Iik7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IHRoaW5rRW5kSW5kZXggPSB0ZW1wQnVmZmVyLmluZGV4T2YoIjwvdGhpbms+Iik7DQoNCiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaW5rU3RhcnRJbmRleCA9PT0gLTEgJiYgdGhpbmtFbmRJbmRleCA9PT0gLTEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDmsqHmnIl0aGlua+agh+etvu+8jOebtOaOpea3u+WKoOWIsGJ1ZmZlcg0KICAgICAgICAgICAgICAgICAgICAgIGlmICghaXNUaGlua0NvbnRlbnQpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJ1ZmZlciArPSB0ZW1wQnVmZmVyOw0KICAgICAgICAgICAgICAgICAgICAgICAgLy8g5L2/55SobWFya2Vk5riy5p+TbWFya2Rvd27lhoXlrrkNCiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQobWFya2VkKGJ1ZmZlciwgdGhpcy5tYXJrZG93bk9wdGlvbnMpKTsNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgdGVtcEJ1ZmZlciA9ICIiOw0KICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHRoaW5rU3RhcnRJbmRleCAhPT0gLTEgJiYgdGhpbmtFbmRJbmRleCA9PT0gLTEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDlj6rmnInlvIDlp4vmoIfnrb7vvIznrYnlvoXnu5PmnZ/moIfnrb4NCiAgICAgICAgICAgICAgICAgICAgICBpc1RoaW5rQ29udGVudCA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgICAgaWYgKHRoaW5rU3RhcnRJbmRleCA+IDApIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJ1ZmZlciArPSB0ZW1wQnVmZmVyLnN1YnN0cmluZygwLCB0aGlua1N0YXJ0SW5kZXgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgLy8g5L2/55SobWFya2Vk5riy5p+TbWFya2Rvd27lhoXlrrkNCiAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQobWFya2VkKGJ1ZmZlciwgdGhpcy5tYXJrZG93bk9wdGlvbnMpKTsNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgdGVtcEJ1ZmZlciA9IHRlbXBCdWZmZXIuc3Vic3RyaW5nKHRoaW5rU3RhcnRJbmRleCk7DQogICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpbmtTdGFydEluZGV4ID09PSAtMSAmJiB0aGlua0VuZEluZGV4ICE9PSAtMSkgew0KICAgICAgICAgICAgICAgICAgICAgIC8vIOWPquaciee7k+adn+agh+etvu+8jOenu+mZpOS5i+WJjeeahOWGheWuuQ0KICAgICAgICAgICAgICAgICAgICAgIGlzVGhpbmtDb250ZW50ID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgdGVtcEJ1ZmZlciA9IHRlbXBCdWZmZXIuc3Vic3RyaW5nKHRoaW5rRW5kSW5kZXggKyA4KTsNCiAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDmnInlrozmlbTnmoR0aGlua+agh+etvuWvuQ0KICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlua1N0YXJ0SW5kZXggPiAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBidWZmZXIgKz0gdGVtcEJ1ZmZlci5zdWJzdHJpbmcoMCwgdGhpbmtTdGFydEluZGV4KTsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOS9v+eUqG1hcmtlZOa4suafk21hcmtkb3du5YaF5a65DQogICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KG1hcmtlZChidWZmZXIsIHRoaXMubWFya2Rvd25PcHRpb25zKSk7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXIgPSB0ZW1wQnVmZmVyLnN1YnN0cmluZyh0aGlua0VuZEluZGV4ICsgOCk7DQogICAgICAgICAgICAgICAgICAgICAgaXNUaGlua0NvbnRlbnQgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHsNCiAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybigi5peg5pWI55qESlNPTuihjO+8jOW3sui3s+i/hyIsIHsNCiAgICAgICAgICAgICAgICAgICAgbGluZSwNCiAgICAgICAgICAgICAgICAgICAgZXJyb3I6IHBhcnNlRXJyb3IubWVzc2FnZSwNCiAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gY2F0Y2ggKHN0cmVhbUVycm9yKSB7DQogICAgICAgICAgICBpZiAoc3RyZWFtRXJyb3IubWVzc2FnZSA9PT0gIkFib3J0RXJyb3IiKSB7DQogICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQWJvcnRFcnJvciIpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5aSE55CG5rWB5byP5ZON5bqU5pe25Ye66ZSZOiIsIHN0cmVhbUVycm9yKTsNCiAgICAgICAgICAgIHRocm93IHN0cmVhbUVycm9yOw0KICAgICAgICAgIH0NCiAgICAgICAgfTsNCg0KICAgICAgICBhd2FpdCBwcm9jZXNzU3RyZWFtKCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAvLyDliKTmlq3mmK/lkKbmmK/kuK3mlq3lr7zoh7TnmoTplJnor68NCiAgICAgICAgaWYgKGVycm9yLm1lc3NhZ2UgPT09ICJBYm9ydEVycm9yIikgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCLor7fmsYLlt7LooqvkuK3mlq0iKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgY29uc29sZS5lcnJvcigiQUnop6Por7vlh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgIkFJ6Kej6K+75aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgIGlmICh0aGlzLmNoYXRNZXNzYWdlc1sxXSkgew0KICAgICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzWzFdLmNvbnRlbnQgPSAi5oqx5q2J77yM5pyN5Yqh5Zmo57mB5b+Z77yM6K+356iN5ZCO5YaN6K+VIjsNCiAgICAgICAgfQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gbnVsbDsgLy8g5riF55CG5b2T5YmN55qEIHJlYWRlcg0KICAgICAgICAvLyDlj6rmnInlnKjmsqHmnInooqvkuK3mlq3nmoTmg4XlhrXkuIvmiY3ph43nva7nirbmgIENCiAgICAgICAgaWYgKHRoaXMuYWlEaWFsb2dWaXNpYmxlKSB7DQogICAgICAgICAgdGhpcy5pc1RoaW5raW5nID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8gZGVlcHNlZWsNCiAgICBhc3luYyBkZWVwc2Vla0FpQ2hhdCgpIHsNCiAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+35YWI6YCJ5oup6KaB6Kej6K+755qE5paH56ugIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmnInmraPlnKjov5vooYznmoTor7fmsYLvvIzkuK3mlq3lroMNCiAgICAgIGlmICh0aGlzLmlzUmVxdWVzdGluZykgew0KICAgICAgICB0aGlzLmlzQWJvcnRlZCA9IHRydWU7DQogICAgICAgIGlmICh0aGlzLmN1cnJlbnRSZWFkZXIpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgYXdhaXQgdGhpcy5jdXJyZW50UmVhZGVyLmNhbmNlbCgpOw0KICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCLkuK3mlq3kuYvliY3nmoTor7fmsYLlpLHotKUiLCBlKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8g562J5b6F5LmL5YmN55qE6K+35rGC54q25oCB5riF55CG5a6M5oiQDQogICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IHRydWU7DQogICAgICB0aGlzLmlzQWJvcnRlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5haURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5jaGF0TWVzc2FnZXMgPSBbXTsNCiAgICAgIHRoaXMuaXNUaGlua2luZyA9IHRydWU7DQoNCiAgICAgIGNvbnN0IHNlbGVjdGVkQXJ0aWNsZXMgPSB0aGlzLkFydGljbGVMaXN0LmZpbHRlcigoYXJ0aWNsZSkgPT4NCiAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzLmluY2x1ZGVzKGFydGljbGUuaWQpDQogICAgICApOw0KICAgICAgY29uc3QgdGl0bGVzID0gc2VsZWN0ZWRBcnRpY2xlcw0KICAgICAgICAubWFwKChhcnRpY2xlKSA9PiBg44CKJHthcnRpY2xlLmNuVGl0bGUgfHwgYXJ0aWNsZS50aXRsZX3jgItgKQ0KICAgICAgICAuam9pbigiXG4iKTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgYXJ0aWNsZXNSZXNwb25zZSA9IGF3YWl0IGdldExpc3RCeUlkcygNCiAgICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMuam9pbigiLCIpDQogICAgICAgICk7DQogICAgICAgIGlmICghYXJ0aWNsZXNSZXNwb25zZS5kYXRhIHx8ICFhcnRpY2xlc1Jlc3BvbnNlLmRhdGEubGVuZ3RoKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJGYWlsZWQgdG8gZ2V0IGFydGljbGUgY29udGVudHMiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IGFydGljbGVzQ29udGVudCA9IGFydGljbGVzUmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIC5tYXAoKGFydGljbGUsIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB0aXRsZSA9DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy5jblRpdGxlIHx8DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy50aXRsZSB8fA0KICAgICAgICAgICAgICAiIjsNCiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSBhcnRpY2xlLmNvbnRlbnQgfHwgIiI7DQogICAgICAgICAgICByZXR1cm4gYOOAkOesrCAke2luZGV4ICsgMX0g56+H5paH56ug44CR44CKJHt0aXRsZX3jgItcblxuJHtjb250ZW50fWA7DQogICAgICAgICAgfSkNCiAgICAgICAgICAuam9pbigiXG5cbi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuIik7DQoNCiAgICAgICAgLy8g5re75Yqg55So5oi35raI5oGvDQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goew0KICAgICAgICAgIHJvbGU6ICJ1c2VyIiwNCiAgICAgICAgICBjb250ZW50OiBg5biu5oiR5rex5bqm6Kej6K+75Lul5LiLJHt0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3Rofeevh+aWh+eroO+8mlxuJHt0aXRsZXN9YCwNCiAgICAgICAgfSk7DQoNCiAgICAgICAgLy8g5Yib5bu6QUnmtojmga/lubbmt7vliqDliLDlr7nor53kuK0NCiAgICAgICAgY29uc3QgYWlNZXNzYWdlID0gew0KICAgICAgICAgIHJvbGU6ICJhc3Npc3RhbnQiLA0KICAgICAgICAgIGNvbnRlbnQ6ICIiLA0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLmNoYXRNZXNzYWdlcy5wdXNoKGFpTWVzc2FnZSk7DQogICAgICAgIHRoaXMuaXNUaGlua2luZyA9IHRydWU7DQoNCiAgICAgICAgY29uc3QgcHJvbXB0ID0NCiAgICAgICAgICB0aGlzLmFydGljbGVBaVByb21wdA0KICAgICAgICAgICAgLnJlcGxhY2UoImFydGljbGVMZW5ndGgiLCB0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoKQ0KICAgICAgICAgICAgLnJlcGxhY2UoL1wmZ3Q7L2csICI+IikgKw0KICAgICAgICAgIGBcblxuKirku6XkuIvmmK/lvoXlpITnkIbnmoTmlofnq6DvvJoqKlxuXG4ke2FydGljbGVzQ29udGVudH1gOw0KDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGVlcHNlZWtBaVFhKHByb21wdCwgdHJ1ZSk7DQoNCiAgICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7DQogICAgICAgICAgY29uc3QgcmVhZGVyID0gcmVzcG9uc2UuYm9keS5nZXRSZWFkZXIoKTsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSByZWFkZXI7IC8vIOS/neWtmOW9k+WJjeeahCByZWFkZXINCiAgICAgICAgICBjb25zdCBkZWNvZGVyID0gbmV3IFRleHREZWNvZGVyKCk7DQogICAgICAgICAgbGV0IGJ1ZmZlciA9ICIiOw0KICAgICAgICAgIGxldCBsYXN0VXBkYXRlVGltZSA9IERhdGUubm93KCk7DQoNCiAgICAgICAgICBjb25zdCB1cGRhdGVDb250ZW50ID0gKG5ld0NvbnRlbnQpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gRGF0ZS5ub3coKTsNCiAgICAgICAgICAgIGlmIChjdXJyZW50VGltZSAtIGxhc3RVcGRhdGVUaW1lID49IDUwKSB7DQogICAgICAgICAgICAgIGFpTWVzc2FnZS5jb250ZW50ID0gbmV3Q29udGVudDsNCiAgICAgICAgICAgICAgbGFzdFVwZGF0ZVRpbWUgPSBjdXJyZW50VGltZTsNCiAgICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICAgIGNvbnN0IGNoYXRNZXNzYWdlcyA9IHRoaXMuJHJlZnMuY2hhdE1lc3NhZ2VzOw0KICAgICAgICAgICAgICAgIGlmIChjaGF0TWVzc2FnZXMpIHsNCiAgICAgICAgICAgICAgICAgIGNoYXRNZXNzYWdlcy5zY3JvbGxUb3AgPSBjaGF0TWVzc2FnZXMuc2Nyb2xsSGVpZ2h0Ow0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfTsNCg0KICAgICAgICAgIHdoaWxlICh0cnVlKSB7DQogICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7LkuK3mlq0NCiAgICAgICAgICAgIGlmICh0aGlzLmlzQWJvcnRlZCkgew0KICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFib3J0RXJyb3IiKTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgY29uc3QgeyBkb25lLCB2YWx1ZSB9ID0gYXdhaXQgcmVhZGVyLnJlYWQoKTsNCiAgICAgICAgICAgIGlmIChkb25lKSB7DQogICAgICAgICAgICAgIGlmIChidWZmZXIubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQoYnVmZmVyKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgY29uc3QgY2h1bmsgPSBkZWNvZGVyLmRlY29kZSh2YWx1ZSk7DQogICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICBjb25zdCBsaW5lcyA9IGNodW5rLnNwbGl0KCJcbiIpOw0KDQogICAgICAgICAgICAgIGZvciAoY29uc3QgbGluZSBvZiBsaW5lcykgew0KICAgICAgICAgICAgICAgIGlmICghbGluZS50cmltKCkgfHwgIWxpbmUuc3RhcnRzV2l0aCgiZGF0YTogIikpIGNvbnRpbnVlOw0KDQogICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGxpbmUuc2xpY2UoNSk7DQogICAgICAgICAgICAgICAgaWYgKGRhdGEgPT09ICJbRE9ORV0iKSBicmVhazsNCg0KICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICBjb25zdCBqc29uRGF0YSA9IEpTT04ucGFyc2UoZGF0YSk7DQogICAgICAgICAgICAgICAgICBpZiAoanNvbkRhdGEuY2hvaWNlcz8uWzBdPy5kZWx0YT8uY29udGVudCkgew0KICAgICAgICAgICAgICAgICAgICBsZXQgY29udGVudCA9IGpzb25EYXRhLmNob2ljZXNbMF0uZGVsdGEuY29udGVudDsNCg0KICAgICAgICAgICAgICAgICAgICAvLyDot7Pov4fnibnmrorlrZfnrKYNCiAgICAgICAgICAgICAgICAgICAgaWYgKGNvbnRlbnQgPT09ICJgYGAiIHx8IGNvbnRlbnQgPT09ICJtYXJrZG93biIpIHsNCiAgICAgICAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICAgIGJ1ZmZlciArPSBjb250ZW50Ow0KICAgICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KGJ1ZmZlcik7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikgew0KICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigiRXJyb3IgcGFyc2luZyBKU09OOiIsIHBhcnNlRXJyb3IpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCJFcnJvciBwcm9jZXNzaW5nIGNodW5rOiIsIGUpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIlJlcXVlc3QgZmFpbGVkIik7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIC8vIOWIpOaWreaYr+WQpuaYr+S4reaWreWvvOiHtOeahOmUmeivrw0KICAgICAgICBpZiAoZXJyb3IubWVzc2FnZSA9PT0gIkFib3J0RXJyb3IiKSB7DQogICAgICAgICAgY29uc29sZS5sb2coIuivt+axguW3suiiq+S4reaWrSIpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBjb25zb2xlLmVycm9yKCJBSSBDaGF0IEVycm9yOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigiQUnop6Por7vlpLHotKXvvIzor7fnqI3lkI7ph43or5UiKTsNCiAgICAgICAgaWYgKHRoaXMuY2hhdE1lc3NhZ2VzWzFdKSB7DQogICAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXNbMV0uY29udGVudCA9ICLmirHmrYnvvIzmnI3liqHlmajnuYHlv5nvvIzor7fnqI3lkI7lho3or5UiOw0KICAgICAgICB9DQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSBudWxsOyAvLyDmuIXnkIblvZPliY3nmoQgcmVhZGVyDQogICAgICAgIC8vIOWPquacieWcqOayoeacieiiq+S4reaWreeahOaDheWGteS4i+aJjemHjee9rueKtuaAgQ0KICAgICAgICBpZiAodGhpcy5haURpYWxvZ1Zpc2libGUpIHsNCiAgICAgICAgICB0aGlzLmlzVGhpbmtpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDlhbPpl61BSeWvueivnQ0KICAgIGNsb3NlQWlEaWFsb2coKSB7DQogICAgICB0aGlzLmlzQWJvcnRlZCA9IHRydWU7IC8vIOiuvue9ruS4reaWreagh+W/lw0KICAgICAgaWYgKHRoaXMuY3VycmVudFJlYWRlcikgew0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIuY2FuY2VsKCk7IC8vIOS4reaWreW9k+WJjeeahOivu+WPlg0KICAgICAgfQ0KICAgICAgdGhpcy5haURpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzID0gW107DQogICAgICB0aGlzLmlzVGhpbmtpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gZmFsc2U7DQogICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSBudWxsOw0KICAgIH0sDQogICAgYXJ0aWNsZUFpQ2hhdCgpIHsNCiAgICAgIGlmICh0aGlzLmFpUGxhdGZvcm0gPT09ICJkaWZ5Iikgew0KICAgICAgICB0aGlzLmRpZnlBaUNoYXQoKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5haVBsYXRmb3JtID09PSAib2xsYW1hIikgew0KICAgICAgICB0aGlzLm9sbGFtYUFpQ2hhdCgpOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLmFpUGxhdGZvcm0gPT09ICJkZWVwc2VlayIpIHsNCiAgICAgICAgdGhpcy5kZWVwc2Vla0FpQ2hhdCgpOw0KICAgICAgfQ0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["MainArticle.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu+DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MainArticle.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\r\n  <div class=\"main\" v-loading=\"loading\">\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Monitor'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    >\r\n                    </span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\" @click=\"resultEvent('BatchGeneration')\"></i>\r\n          </p> -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-document-add\"\r\n              style=\"font-size: 24px\"\r\n              title=\"发布到每日最新热点\"\r\n              @click=\"publishHot\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\" v-if=\"$route.query.domain\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek深度解读\"\r\n              @click=\"articleAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n              >Deepseek深度解读</span\r\n            >\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          :style=\"\r\n            $route.query.domain\r\n              ? 'height: calc(100vh - 170px)'\r\n              : 'height: calc(100vh - 389px)'\r\n          \"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{\r\n                        $route.query.domain\r\n                          ? \"是\"\r\n                          : getTechnologyLabel(item.isTechnology)\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleUpdate(item)\"\r\n                  v-hasPermi=\"['article:articleList:edit']\"\r\n                  >{{ ` 修改 ` }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  v-hasPermi=\"['article:list:remove']\"\r\n                  >{{ `删除` }}</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Special'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'infoInter'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n            </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Wechat'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(50vh - 200px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_kqcb\">\r\n              <div>\r\n                <div class=\"ArticlTop\" style=\"width: 55%\">\r\n                  <p style=\"line-height: 20px\">\r\n                    <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                      {{ null }} </el-checkbox\r\n                    >&nbsp;&nbsp;\r\n                    <a href=\"#\"\r\n                      ><span style=\"padding: 0 10px 0 0\"\r\n                        >{{\r\n                          (currentPage - 1) * pageSize + key + 1\r\n                        }}&nbsp;</span\r\n                      ><span\r\n                        class=\"title_Article\"\r\n                        @click=\"openNewView(item)\"\r\n                        v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                      ></span\r\n                      >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                    >\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3; cursor: pointer\"\r\n                    >\r\n                      原文链接\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n                <div class=\"info_flex\" style=\"width: auto\">\r\n                  <div class=\"ArticlBottom\">\r\n                    <div>\r\n                      类型:\r\n                      <span class=\"infomation\">\r\n                        {{ typeHandle(item.sourceType) }}\r\n                      </span>\r\n                    </div>\r\n                    <div>\r\n                      媒体来源:\r\n                      <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.author\">\r\n                      作者:\r\n                      <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                    </div>\r\n                    <div>\r\n                      发布时间:\r\n                      <span class=\"infomation\">{{\r\n                        formatPublishTime(\r\n                          item.publishTime,\r\n                          item.webstePublishTime\r\n                        )\r\n                      }}</span>\r\n                    </div>\r\n                    <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                    <!-- <p>标签A</p> -->\r\n                  </div>\r\n                  <div class=\"ArticlBottom\">\r\n                    <div v-if=\"item.industryName\">\r\n                      所属行业:\r\n                      <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.domain\">\r\n                      所属领域:\r\n                      <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p> -->\r\n            <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            <!-- </div> -->\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'artificialIntelligence' || flag == 'networkSecurity'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div\r\n              class=\"Articl_left\"\r\n              :style=\"item.fileUrl ? 'width: 85%' : 'width: 100%'\"\r\n            >\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题:</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\" v-if=\"item.fileUrl\">\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <!-- <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"primary\" plain @click=\"handleUpdate(item)\"\r\n                    v-hasPermi=\"['article:articleList:edit']\">{{ ` 修改 ` }}</el-button>\r\n                </p>\r\n                <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"danger\" plain @click=\"handleDelete(item)\"\r\n                    v-hasPermi=\"['article:list:remove']\">{{ `删除` }}</el-button>\r\n                </p> -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'zhikubaogao'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-upload2\"\r\n              style=\"font-size: 22px; color: #1296db\"\r\n              title=\"批量导入报告\"\r\n              @click=\"openBatchImportDialog\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载报告\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除报告\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek报告解读\"\r\n              @click=\"reportAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"reportAiChat\"\r\n              >Deepseek报告解读</span\r\n            >\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          style=\"height: calc(100vh - 310px)\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <!-- <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span> -->\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  >删除</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"pagination\" ref=\"pagination\">\r\n      <el-pagination\r\n        :small=\"false\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"currentPage\"\r\n        :page-sizes=\"[10, 20, 30, 40, 50]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"->, total, sizes, prev, pager, next, jumper\"\r\n        :total=\"total\"\r\n      ></el-pagination>\r\n    </div>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title\r\n      :visible.sync=\"tagDialog\"\r\n      width=\"20%\"\r\n      :before-close=\"closeTag\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form\r\n        label-position=\"left\"\r\n        label-width=\"40px\"\r\n        :model=\"formLabelAlign\"\r\n        ref=\"ruleForm\"\r\n      >\r\n        <el-form-item label=\"行业\" prop=\"industry\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.industry\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            placeholder=\"请选择行业\"\r\n            :filterable=\"true\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.industryName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"领域\" prop=\"domain\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.domain\"\r\n            filterable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            clearable\r\n            placeholder=\"请选择领域\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.fieldName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"标签\" prop=\"tag\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.tag\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n            filterable\r\n            allow-create\r\n            default-first-option\r\n            placeholder=\"请添加文章标签\"\r\n          >\r\n            <!-- <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>-->\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeTag\" size=\"mini\">取 消</el-button>\r\n        <el-button @click=\"SubmitTag\" type=\"primary\" size=\"mini\"\r\n          >确 定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n    <el-drawer\r\n      @open=\"openDrawer\"\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"drawer\"\r\n      custom-class=\"drawer_box\"\r\n      direction=\"rtl\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <div\r\n        v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n        style=\"width: 100%; text-align: right; padding-right: 10px\"\r\n      >\r\n        <el-button\r\n          @click=\"documentDownload('drawer')\"\r\n          :type=\"drawerInfo.fileUrl ? 'primary' : 'info'\"\r\n          size=\"mini\"\r\n          plain\r\n          :disabled=\"!drawerInfo.fileUrl\"\r\n          >{{ \"附件下载\" }}</el-button\r\n        >\r\n        <!-- <el-button @click=\"resultEvent('drawer')\" type=\"primary\" size=\"mini\" plain>\r\n          {{ drawerInfo.snapshotUrl\r\n            ? '下载快照' : '生成快照' }}\r\n        </el-button> -->\r\n        <!-- v-if=\"translationBtnShow\" -->\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          plain\r\n          @click=\"translateEvent(drawerInfo)\"\r\n          >翻译内容</el-button\r\n        >\r\n      </div>\r\n      <template slot=\"title\">\r\n        <span class=\"drawer_Title\">{{\r\n          drawerInfo.cnTitle || drawerInfo.title\r\n        }}</span>\r\n      </template>\r\n      <div class=\"drawer_Style\">\r\n        <p class=\"title\">\r\n          {{ drawerInfo.cnTitle || drawerInfo.title }}\r\n          <!-- v-if=\"!drawerInfo.cnTitle\" -->\r\n          <i\r\n            class=\"icon-taizhangtranslate\"\r\n            @click=\"translateTitle(drawerInfo)\"\r\n          ></i>\r\n        </p>\r\n        <p style=\"text-align: center\">\r\n          <span class=\"source\">{{ drawerInfo.sourceName }}</span>\r\n          <span class=\"time\">{{ drawerInfo.publishTime }}</span>\r\n        </p>\r\n        <div\r\n          style=\"user-select: text !important; line-height: 30px\"\r\n          v-html=\"\r\n            drawerInfo.cnContent &&\r\n            drawerInfo.cnContent.replace(/<img\\b[^>]*>/gi, '')\r\n          \"\r\n        ></div>\r\n        <el-empty\r\n          description=\"当前文章暂无数据\"\r\n          v-if=\"!drawerInfo.cnContent\"\r\n        ></el-empty>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"'修改文章'\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"dialog_Box\">\r\n        <el-form\r\n          :model=\"form\"\r\n          class=\"form_Style\"\r\n          label-position=\"top\"\r\n          ref=\"form\"\r\n          :rules=\"rules\"\r\n          size=\"mini\"\r\n        >\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"文章标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"中文标题\" prop=\"cnTitle\">\r\n                <el-input v-model=\"form.cnTitle\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n                <el-select\r\n                  v-model=\"form.sourceType\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.id\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"媒体来源\" prop=\"sourceName\">\r\n                <el-select\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  filterable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeLists\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.name\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发布时间\" prop=\"publishTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.publishTime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"datetime\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  placeholder=\"选择日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"文章作者\" prop=\"author\">\r\n                <el-input\r\n                  v-model=\"form.author\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"原文链接\" prop=\"originalUrl\">\r\n            <el-input v-model=\"form.originalUrl\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"摘要\" prop=\"summary\">\r\n            <el-input\r\n              v-model=\"form.summary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文摘要\" prop=\"cnSummary\">\r\n            <el-input\r\n              v-model=\"form.cnSummary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文内容\" prop=\"cnContent\">\r\n            <editor v-model=\"form.cnContent\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <el-form-item label=\"文章内容\" prop=\"content\">\r\n            <editor v-model=\"form.content\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <!-- <el-form-item label=\"封面图片\" prop=\"cover\">\r\n            <el-upload action=\"#\" ref=\"upload\" :limit=\"3\" :on-exceed=\"exceed\" list-type=\"picture-card\"\r\n              :auto-upload=\"false\" :headers=\"vertifyUpload.headers\" :file-list=\"fileList\" :on-change=\"handleChange\"\r\n              :http-request=\"requestLoad\">\r\n              <i slot=\"default\" class=\"el-icon-plus\"></i>\r\n              <div slot=\"file\" slot-scope=\"{ file }\">\r\n                <img class=\"el-upload-list__item-thumbnail\" :src=\"file.url\" alt=\"文件缩略图加载失败\" />\r\n                <span class=\"el-upload-list__item-actions\">\r\n                  <span v-if=\"!disabled\" class=\"el-upload-list__item-delete\" @click=\"handleRemove(file)\">\r\n                    <i class=\"el-icon-delete\"></i>\r\n                  </span>\r\n                </span>\r\n              </div>\r\n            </el-upload>\r\n          </el-form-item>\r\n          <el-form-item label=\"上传附件\" prop=\"fileUrl\">\r\n            <el-upload class=\"upload-demo\" :action=\"fileUrlurl\" :before-upload=\"beforeUploadUrl\" multiple :limit=\"1\"\r\n              :http-request=\"uploadUrlRequest\" :on-success=\"uploadUrlSuccess\" :file-list=\"fileUrlList\"\r\n              :on-exceed=\"uploadUrlExceed\" :on-remove=\"uploadUrlRemove\">\r\n              <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>\r\n            </el-upload>\r\n          </el-form-item> -->\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量导入弹框 -->\r\n    <el-dialog\r\n      title=\"批量导入报告\"\r\n      :visible.sync=\"batchImportVisible\"\r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"batch-import-container\">\r\n        <!-- 文件选择区域 -->\r\n        <div class=\"file-select-area\">\r\n          <el-upload\r\n            ref=\"batchUpload\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :show-file-list=\"false\"\r\n            multiple\r\n            :on-change=\"handleFileSelect\"\r\n            accept=\".pdf,.doc,.docx,.txt\"\r\n          >\r\n            <el-button type=\"primary\" icon=\"el-icon-upload\">选择文件</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">\r\n              支持选择多个文件，格式：PDF、DOC、DOCX、TXT\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n\r\n        <!-- 文件列表表格 -->\r\n        <div class=\"file-table-area\" v-if=\"batchImportFiles.length > 0\">\r\n          <el-table\r\n            :data=\"batchImportFiles\"\r\n            border\r\n            style=\"width: 100%; margin-top: 20px\"\r\n            max-height=\"300\"\r\n          >\r\n            <el-table-column prop=\"fileName\" label=\"文件名称\" width=\"300\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.fileName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"sourceName\" label=\"数据源名称\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input\r\n                  v-model=\"scope.row.sourceName\"\r\n                  placeholder=\"请输入数据源名称\"\r\n                  size=\"small\"\r\n                ></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"removeFile(scope.$index)\"\r\n                  style=\"color: #f56c6c\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelBatchImport\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"confirmBatchImport\"\r\n          :disabled=\"batchImportFiles.length === 0\"\r\n        >\r\n          确 认\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- Deepseek报告解读弹窗 -->\r\n    <deepseek-report-dialog\r\n      :visible.sync=\"showDeepseekDialog\"\r\n      :article-data=\"currentArticle\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport request from \"@/utils/request\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { MessageBox } from \"element-ui\";\r\nimport axios from \"axios\";\r\nimport { getListClassify } from \"@/api/article/classify\";\r\nimport { saveAs } from \"file-saver\";\r\nimport { blobValidate, tansParams } from \"@/utils/ruoyi\";\r\nimport { articleListEdit, uploadCover } from \"@/api/articleCrawler/list\";\r\nimport DeepseekReportDialog from \"./DeepseekReportDialog.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\nimport { getListByIds } from \"@/api/article/articleHistory\";\r\n\r\nexport default {\r\n  props: {\r\n    downLoadShow: {\r\n      /* 下载按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    editShow: {\r\n      /* 编辑按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    copyShow: {\r\n      /* 复制按钮 */ reuqired: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    height: {\r\n      type: Number,\r\n      default: 655,\r\n    },\r\n    currentPage: {\r\n      reuqired: true,\r\n      default: 1,\r\n    },\r\n    pageSize: {\r\n      reuqired: true,\r\n      default: 50,\r\n    },\r\n    total: {\r\n      reuqired: true,\r\n      default: 0,\r\n    },\r\n    ArticleList: {\r\n      required: true,\r\n      default: [],\r\n    },\r\n    flag: {\r\n      required: true,\r\n    },\r\n    SeachData: {\r\n      required: true,\r\n    },\r\n    keywords: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    // 报告类型字段\r\n    sourceType: {\r\n      default: \"\",\r\n    },\r\n  },\r\n  components: {\r\n    DeepseekReportDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      regExpImg: /^\\n$/,\r\n      reportId: \"\",\r\n      reportOptions: [],\r\n      dialogVisible: false,\r\n      checkedCities: [] /* 多选 */,\r\n      checked: false /* 全选 */,\r\n      html: \"\",\r\n      text: \"\",\r\n      that: this,\r\n      tagShow: false,\r\n      isIndeterminate: true,\r\n      count: 0,\r\n      separate: {},\r\n      /* 标签功能 */\r\n      tagDialog: false,\r\n      formLabelAlign: {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      },\r\n      options: [],\r\n      options1: [],\r\n      tagItem: {} /* 标签对象 */,\r\n      areaList: [] /* 领域 */,\r\n      industry: [] /* 行业 */,\r\n      num: 0,\r\n      timer: null,\r\n      drawer: false,\r\n      drawerInfo: {},\r\n      AreaId: null,\r\n      translationBtnShow: null,\r\n      open: false,\r\n      sourceTypeList: [], // 数据源分类\r\n      sourceLists: [], // 数据源列表\r\n      sourceTypeLists: [],\r\n      form: {}, // 表单参数\r\n      rules: {\r\n        // 表单校验\r\n        title: [{ required: true, message: \"文章标题为必填项\" }],\r\n        content: [{ required: true, message: \"文章详情为必填项\" }],\r\n        publishTime: [{ required: true, message: \"发布时间为必填项\" }],\r\n        cnTitle: [{ required: true, message: \"中文名称为必填项\" }],\r\n        sourceType: [{ required: true, message: \"平台类型为必填项\" }],\r\n        originalUrl: [{ required: true, message: \"原文为必填项\" }],\r\n        summary: [{ required: true, message: \"请填写摘要\" }],\r\n        // cnSummary: [{ required: true, message: '请填写中文摘要' }],\r\n        sn: [{ required: true, message: \"请填写文章地址唯一识别号\" }],\r\n      },\r\n      vertifyUpload: {\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: {\r\n          Authorization: \"Bearer \" + getToken(),\r\n          ContentType: \"application/json;charset=utf-8\",\r\n        },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/article/articleList/cover\",\r\n      },\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      fileUrlurl:\r\n        process.env.VUE_APP_BASE_API + \"/article/articleList/upload/file\",\r\n      showSummary: true,\r\n      // 批量导入相关数据\r\n      batchImportVisible: false,\r\n      batchImportFiles: [],\r\n      // Deepseek报告解读弹窗\r\n      showDeepseekDialog: false,\r\n      currentArticle: {},\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n    };\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    dialogVisible: function (newVal, oldVal) {\r\n      if (newVal) {\r\n        API.getNewBuilt({ sourceType: this.sourceType }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 'formLabelAlign.industry': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options1 = this.industry\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    // 'formLabelAlign.domain': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options = this.areaList\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    \"SeachData.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        this.Refresh();\r\n      },\r\n      deep: true,\r\n    },\r\n    \"form.sourceType\": {\r\n      handler(newVal, oldVal) {\r\n        this.sourceTypeLists = this.sourceLists.filter((item) => {\r\n          return item.type == newVal;\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    if (\r\n      this.flag !== \"MonitorUse\" &&\r\n      this.flag !== \"specialSubjectUse\" &&\r\n      this.flag !== \"Wechat\"\r\n    ) {\r\n      this.openDialog();\r\n    }\r\n    if (this.flag !== \"Wechat\") {\r\n      getListClassify().then((res) => {\r\n        this.sourceTypeList = res.data;\r\n      });\r\n      API.getSourceList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.sourceLists = data.data;\r\n        }\r\n      });\r\n    }\r\n    if (this.$route.query.domain) {\r\n      getConfigKey(\"sys.ai.platform\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.aiPlatform = res.msg;\r\n        }\r\n      });\r\n      getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.articleAiPrompt = res.msg;\r\n        }\r\n      });\r\n      // 获取用户头像\r\n      this.userAvatar = this.$store.getters.avatar;\r\n    }\r\n\r\n    this.showSummary = true;\r\n  },\r\n  updated() {},\r\n  filters: {},\r\n  methods: {\r\n    // 处理科技相关字段的显示映射\r\n    getTechnologyLabel(value) {\r\n      const mapping = {\r\n        0: \"否\",\r\n        1: \"是\",\r\n        2: \"其他\",\r\n        3: \"待定\",\r\n      };\r\n      return mapping[value];\r\n    },\r\n    // 处理发布时间的显示\r\n    formatPublishTime(publishTime, webstePublishTime) {\r\n      // 格式化publishTime为年月日\r\n      const formattedPublishTime = this.parseTime(publishTime, \"{y}-{m}-{d}\");\r\n\r\n      // 如果webstePublishTime不存在，直接返回publishTime\r\n      if (!webstePublishTime) {\r\n        return formattedPublishTime;\r\n      }\r\n\r\n      let formattedWebsteTime = \"\";\r\n      // 处理不同格式的webstePublishTime\r\n      if (webstePublishTime) {\r\n        // 处理2025-04-12 10:09:21.971191格式（包含连字符的标准格式）\r\n        if (webstePublishTime.includes(\"-\")) {\r\n          const dateMatch = webstePublishTime.match(/(\\d{4})-(\\d{2})-(\\d{2})/);\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2];\r\n            const day = dateMatch[3];\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年04月14日 11:29:22格式（中文年月日格式，带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\") &&\r\n          webstePublishTime.includes(\"日\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})日/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年4月15格式（中文年月格式，不带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025/04/14 11:29:22格式（斜杠分隔的格式）\r\n        else if (webstePublishTime.includes(\"/\")) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})\\/(\\d{1,2})\\/(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        } else {\r\n          // 其他格式直接使用原值\r\n          formattedWebsteTime = webstePublishTime;\r\n        }\r\n      }\r\n\r\n      // 比较年月日是否相同\r\n      if (formattedPublishTime === formattedWebsteTime) {\r\n        return formattedPublishTime;\r\n      } else {\r\n        return `${formattedPublishTime} / ${webstePublishTime}`;\r\n      }\r\n    },\r\n\r\n    // 检查文本是否有实际内容（去除HTML标签后）\r\n    hasActualContent(text) {\r\n      if (!text) {\r\n        return false;\r\n      }\r\n      // 去除HTML标签\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      // 检查是否有中文、英文、数字等实际内容\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n    // 关键字替换\r\n    changeColor(str) {\r\n      let Str = str;\r\n      if (Str) {\r\n        let keywords = this.keywords.split(\",\");\r\n        keywords.map((keyitem, keyindex) => {\r\n          if (keyitem && keyitem.length > 0) {\r\n            // 匹配关键字正则\r\n            let replaceReg = new RegExp(keyitem, \"g\");\r\n            // 高亮替换v-html值\r\n            let replaceString =\r\n              '<span class=\"highlight\"' +\r\n              ' style=\"color: red;\">' +\r\n              keyitem +\r\n              \"</span>\";\r\n            Str = Str.replace(replaceReg, replaceString);\r\n          }\r\n        });\r\n      }\r\n      return Str;\r\n    },\r\n    /* 下载Excel */\r\n    async downLoadExcel() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要导出的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.flag == \"specialSubjectUse\") {\r\n        API.downLoadExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n        });\r\n      } else {\r\n        await API.downLoadExportExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n\r\n          // saveAs(blob, `source_${new Date().getTime()}.xlsx`)\r\n        });\r\n      }\r\n    },\r\n    batchDelete() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要删除的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.checkedCities.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 发布到每日最新热点 */\r\n    publishHot() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({\r\n          message: \"请选择要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.checkedCities.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 返回顶部动画 */\r\n    mainScorll() {\r\n      var scrollStep = -this.$refs.scroll.scrollTop / (800 / 15); // 计算每一步滚动的距离\r\n      var scrollInterval = setInterval(() => {\r\n        if (this.$refs.scroll.scrollTop !== 0) {\r\n          this.$refs.scroll.scrollBy(0, scrollStep); // 按照给定步长滚动窗口\r\n        } else {\r\n          clearInterval(scrollInterval); // 到达顶部时清除定时器\r\n        }\r\n      }, 15);\r\n    },\r\n    scrollChange() {\r\n      clearTimeout(this.timer);\r\n      this.timer = setTimeout(() => {\r\n        this.stopScroll();\r\n      }, 500);\r\n      // this.$refs.pagination.style.opacity = 0\r\n      // this.$refs.pagination.style.transition = '0'\r\n    } /* 滚动事件 */,\r\n    stopScroll() {\r\n      // this.$refs.pagination.style.transition = '1s'\r\n      // this.$refs.pagination.style.opacity = 1\r\n    },\r\n    /* 下载 */\r\n    downLoad() {\r\n      this.dialogVisible = true;\r\n    },\r\n    /* 每页条数变化 */\r\n    handleSizeChange(num) {\r\n      this.$emit(\"handleSizeChange\", num);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 页码变化 */\r\n    handleCurrentChange(current) {\r\n      this.$emit(\"handleCurrentChange\", current);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 收藏 */\r\n    async collect(item) {\r\n      /* 点击列表收藏 */\r\n      if (item.id) {\r\n        this.checkedCities = [item.id];\r\n      }\r\n      /* 未选择提示 */\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要收藏的文章\", type: \"info\" });\r\n        return;\r\n      }\r\n      /* 收藏 */\r\n      if (!item.favorites) {\r\n        let res = await API.collectApi([item.id]);\r\n        if (res.code) {\r\n          this.$message({\r\n            message: \"收藏成功,请前往个人中心查看\",\r\n            type: \"success\",\r\n          });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"收藏失败\", type: \"info\" });\r\n      } else {\r\n        let res = await API.cocelCollect([item.id]);\r\n        if (res.code) {\r\n          this.$message({ message: \"已取消收藏\", type: \"success\" });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"取消收藏失败\", type: \"info\" });\r\n      }\r\n    },\r\n    /* 一键复制 */\r\n    copyText(item) {\r\n      navigator.clipboard\r\n        .writeText(item.cnTitle)\r\n        .then(() => {\r\n          this.$message({ message: \"已成功复制到剪贴板\", type: \"success\" });\r\n        })\r\n        .catch(function () {\r\n          alert(\"复制失败\");\r\n        });\r\n    },\r\n    typeHandle(data) {\r\n      if (data == 1) {\r\n        return \"微信公众号\";\r\n      } else if (data == 2) {\r\n        return \"网站\";\r\n      } else if (data == 3) {\r\n        return \"手动录入\";\r\n      }\r\n    },\r\n    /* 选择事件 */\r\n    handleCheckedCitiesChange(value) {\r\n      this.checkedCities = value;\r\n    },\r\n    /* 全选 */\r\n    handleCheckAllChange(val) {\r\n      this.checkedCities = val ? this.ArticleList.map((item) => item.id) : [];\r\n      this.isIndeterminate = false;\r\n    },\r\n    /* 刷新 */\r\n    Refresh() {\r\n      this.$emit(\"Refresh\");\r\n    },\r\n    /*确定添加到报告 */\r\n    async reportSubmit() {\r\n      this.dialogVisible = false;\r\n      let keyWordList = [];\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      /* 单独添加 */\r\n      if (this.separate.id) {\r\n        // let keyword = Object.keys(this.separate.keywordCount)\r\n        keyWordList.push({\r\n          reportId: this.reportId,\r\n          listId: this.separate.id,\r\n          listSn: this.separate.sn,\r\n        });\r\n      } else {\r\n        /* 批量添加 */\r\n        if (this.checkedCities == \"\")\r\n          return this.$message({\r\n            message: \"请选择要添加的数据\",\r\n            type: \"warning\",\r\n          });\r\n        this.checkedCities.forEach((item) => {\r\n          let article = this.ArticleList.filter((value) => value.id == item);\r\n          keyWordList.push({\r\n            reportId: this.reportId,\r\n            listId: item,\r\n            listSn: article[0].sn,\r\n          });\r\n        });\r\n      }\r\n      let res = await API.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.$emit(\"Refresh\");\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.separate = {};\r\n      this.reportId = \"\";\r\n      this.checkedCities = [];\r\n      this.checked = false;\r\n    },\r\n    /* 单独添加报告 */\r\n    separateAdd(item) {\r\n      this.dialogVisible = true;\r\n      this.separate = item;\r\n    },\r\n    /* 跳转新页面 */\r\n    openNewView(item, isLink) {\r\n      if (isLink) {\r\n        if (item.originalUrl) {\r\n          window.open(item.originalUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n        return;\r\n      }\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n      // this.drawerInfo = item\r\n      // this.drawer = true\r\n    },\r\n    /* 文章打标签 */\r\n    tagHandler(item) {\r\n      this.tagDialog = true;\r\n      this.tagItem = item;\r\n      if (item.industry) {\r\n        this.formLabelAlign.industry = item.industry\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      if (item.domain) {\r\n        this.formLabelAlign.domain = item.domain\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      this.formLabelAlign.tag = item.tags ? item.tags.split(\",\") : \"\";\r\n    },\r\n    /* 获取领域和分类 */\r\n    async openDialog() {\r\n      await API.areaList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.areaList = data.data;\r\n          this.options = data.data;\r\n          API.industry().then((value) => {\r\n            this.industry = value.data;\r\n            this.options1 = value.data;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 筛选领域 */\r\n    remoteEvent(query) {\r\n      this.options = this.areaList.filter((item) => item.fieldName == query);\r\n    },\r\n    /* 筛选行业 */\r\n    remoteIndustry(query) {\r\n      this.options1 = this.industry.filter(\r\n        (item) => item.industryName == query\r\n      );\r\n    },\r\n    async SubmitTag() {\r\n      let params = {\r\n        domain: String(this.formLabelAlign.domain),\r\n        industry: String(this.formLabelAlign.industry),\r\n        tags: String(this.formLabelAlign.tag),\r\n        articleId: String(this.tagItem.id),\r\n        docId: this.tagItem.docId ? String(this.tagItem.docId) : \"\",\r\n      };\r\n      let res = await API.tagAdd(params);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"保存成功\", type: \"success\" });\r\n        setTimeout(() => {\r\n          this.Refresh();\r\n        }, 1000);\r\n      } else {\r\n        this.$message({ message: \"保存失败\", type: \"error\" });\r\n      }\r\n      this.closeTag();\r\n    },\r\n    closeTag() {\r\n      this.$refs[\"ruleForm\"].resetFields();\r\n      this.formLabelAlign = {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      };\r\n      this.tagDialog = false;\r\n    },\r\n    async hotIncrease(item) {\r\n      let isWhether = JSON.parse(item.isWhether);\r\n      let res = await API.tagAdd({\r\n        articleId: item.id,\r\n        isWhether: +!Boolean(isWhether),\r\n      });\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"操作成功\", type: \"success\" });\r\n        this.Refresh();\r\n      } else {\r\n        this.$message({ message: \"操作失败\", type: \"error\" });\r\n      }\r\n    },\r\n    async openDrawer() {\r\n      let docId = this.drawerInfo.docId;\r\n      await API.AreaInfo(this.drawerInfo.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.drawerInfo = res.data;\r\n          this.drawerInfo.docId = docId;\r\n          /* 将字符串中的\\n替换为<br> */\r\n          this.translationBtnShow = !this.drawerInfo.cnContent;\r\n          if (this.drawerInfo.cnContent || this.drawerInfo.content) {\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\\\n/g, (a, b, c) => {\r\n              return \"<br>\";\r\n            });\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\${[^}]+}/g, \"<br>\");\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(\"|xa0\", \"\");\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /* 所属行业处理 */\r\n    industryHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.industry) {\r\n        ids = item.industry.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.industry.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.industryName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    domainHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.domain) {\r\n        ids = item.domain.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.areaList.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.fieldName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    /* 快照生成 */\r\n    resultEvent(item) {\r\n      if (item == \"BatchGeneration\" && this.checkedCities.length == 0) {\r\n        this.$message.warning(\"请先选择文章\");\r\n        return;\r\n      }\r\n      let ids = null;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (item == \"drawer\") {\r\n        ids = [this.drawerInfo.id];\r\n        if (this.drawerInfo.snapshotUrl) zhuangtai = \"查看\";\r\n        url = this.drawerInfo.snapshotUrl;\r\n      } else if (item == \"BatchGeneration\") {\r\n        ids = this.checkedCities;\r\n      } else {\r\n        ids = [item.id];\r\n        if (item.snapshotUrl) zhuangtai = \"查看\";\r\n        url = item.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        if (this.flag == \"MonitorUse\") {\r\n          API.downLoadExportKe(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        } else {\r\n          API.downLoadExportZhuan(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        }\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n    /* 附件下载 */\r\n    async documentDownload(item) {\r\n      this.loading = true;\r\n      if (item.fileUrl) {\r\n        const urls = item.fileUrl.split(\",\");\r\n        for (const [index, url] of urls.entries()) {\r\n          if (url.indexOf(\"https://\") === -1) {\r\n            setTimeout(async () => {\r\n              await this.downLoadFun(url, index, item.cnTitle || item.title);\r\n            }, index * 500);\r\n          } else {\r\n            this.$message.error(\"附件还没同步到当前系统，暂时无法下载\");\r\n          }\r\n        }\r\n      }\r\n      this.loading = false;\r\n    },\r\n\r\n    async downLoadFun(url, index, title) {\r\n      let formData = new FormData();\r\n      formData.append(\"fileUrl\", url);\r\n\r\n      try {\r\n        const response = await API.downloadFile(formData);\r\n        const isBlob = blobValidate(response);\r\n\r\n        if (isBlob) {\r\n          const blob = new Blob([response]);\r\n          let list = url.split(\"/\");\r\n          let fileName = list[list.length - 1];\r\n          saveAs(blob, fileName);\r\n        } else {\r\n          const resText = await response.text();\r\n          const rspObj = JSON.parse(resText);\r\n          const errMsg =\r\n            errorCode[rspObj.code] || rspObj.msg || errorCode[\"default\"];\r\n          this.$message.error(errMsg);\r\n        }\r\n      } catch (err) {\r\n        // this.$message.error(`Error downloading file: ${err}`);\r\n      } finally {\r\n        // 确保 loading 在每次下载后都设置为 false\r\n        this.loading = false;\r\n      }\r\n\r\n      // 之前的附件下载\r\n      // if (item.annexUrl) {\r\n      //   /* 有文件地址 直接下载 */\r\n      //   let formData = new FormData()\r\n      //   formData.append('id', item.id)\r\n      //   this.loading = true\r\n      //   API.documentDownloadKe(formData).then(res => {\r\n      //     let a = document.createElement('a')\r\n      //     a.href = URL.createObjectURL(res.data)\r\n      //     a.download = res.headers['content-disposition'].split('filename=')[1]\r\n      //     a.click()\r\n      //     this.loading = false\r\n      //   })\r\n      // } else {\r\n      //   /* 没有文件格式 申请下载 */\r\n      //   API.documentDownload(id).then(response => {\r\n      //     if (response.code == 200) {\r\n      //       this.$msgbox({\r\n      //         title: '提示',\r\n      //         message: '附件正在同步中，请稍后下载',\r\n      //         showCancelButton: true,\r\n      //         confirmButtonText: '关闭',\r\n      //         cancelButtonText: '取消',\r\n      //         showCancelButton: false,\r\n      //         beforeClose: (action, instance, done) => {\r\n      //           done()\r\n      //         }\r\n      //       })\r\n      //     } else {\r\n      //       this.$message({ message: '附件下载失败', type: 'error' })\r\n      //     }\r\n      //   }).catch(err => { })\r\n      // }\r\n    },\r\n    // 翻译标题\r\n    translateTitle(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.title,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"title\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    // 翻译文章\r\n    translateEvent(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.content,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"content\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          this.translationBtnShow = false;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      API.AreaInfo(row.id).then((response) => {\r\n        this.form = response.data;\r\n        this.form.sourceType = Number(this.form.sourceType);\r\n        this.form.docId = row.docId;\r\n        // this.fileUrlList = this.form.fileUrl ? this.form.fileUrl.split(\",\").map(item => {\r\n        //   return {\r\n        //     name: item,\r\n        //     url: item\r\n        //   }\r\n        // }) : []\r\n        this.open = true;\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal\r\n        .confirm('是否确认删除该条文章？\"')\r\n        .then(() => {\r\n          return API.monitoringEsRemove({ id: row.id, docId: row.docId });\r\n        })\r\n        .then(() => {\r\n          this.Refresh();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          let queryForm = JSON.parse(JSON.stringify(this.form));\r\n          // let cover = String(this.fileList.map(item => item.path))\r\n          // queryForm.cover = cover\r\n          articleListEdit(queryForm).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.Refresh();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 自定义上传 */\r\n    async requestLoad(file) {\r\n      let data = new FormData();\r\n      data.append(\"cover\", file.file);\r\n      await uploadCover(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.fileList.map((item) => {\r\n            if (item.uid == file.file.uid) {\r\n              item.path = response.imgUrl;\r\n            }\r\n          });\r\n          this.$message({ message: \"上传成功\", type: \"success\" });\r\n        } else {\r\n          this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    /* 文件超出限制 */\r\n    exceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传三个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    /* 移除文件 */\r\n    handleRemove(file) {\r\n      this.fileList = this.fileList.filter((item) => item !== file);\r\n    },\r\n    // 文件更改\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList;\r\n      this.$refs.upload.submit();\r\n    },\r\n    // 上传附件校验\r\n    beforeUploadUrl(file) {\r\n      // 判断文件是否为excel\r\n      let fileName = file.name\r\n          .substring(file.name.lastIndexOf(\".\") + 1)\r\n          .toLowerCase(),\r\n        condition =\r\n          fileName == \"pdf\" ||\r\n          fileName == \"doc\" ||\r\n          fileName == \"xls\" ||\r\n          fileName == \"ppt\" ||\r\n          fileName == \"xlsx\" ||\r\n          fileName == \"pptx\" ||\r\n          fileName == \"docx\";\r\n      let fileSize = file.size / 1024 / 1024 < 10;\r\n      if (!condition) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件必须是pdf,doc,xls,ppt,xlsx,pptx,docx格式\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      /* 文件大小限制 */\r\n      if (!fileSize) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件的大小不能超过 10MB!\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      return condition && fileSize;\r\n    },\r\n    // 文件上传成功\r\n    uploadUrlSuccess(res, file) {\r\n      this.$message({ message: \"上传成功\", type: \"success\" });\r\n    },\r\n    // 文件上传超出限制\r\n    uploadUrlExceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传1个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    // 文件上传方法\r\n    uploadUrlRequest(file) {\r\n      if (this.form.originalUrl != null && this.form.originalUrl != \"\") {\r\n        if (\r\n          this.form.originalUrl.match(\r\n            /(http|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/\r\n          )\r\n        ) {\r\n          let data = new FormData();\r\n          data.append(\"file\", file.file);\r\n          data.append(\"originalUrl\", this.form.originalUrl);\r\n\r\n          API.uploadFile(data).then((response) => {\r\n            if (response.code == 200) {\r\n              this.$message({ message: \"上传成功\", type: \"success\" });\r\n              this.form.fileUrl = response.data;\r\n            } else {\r\n              this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n              this.form.fileUrl = \"\";\r\n            }\r\n          });\r\n        } else {\r\n          this.$message({ message: \"请填写正确的原文链接\", type: \"warning\" });\r\n          this.fileUrlList = [];\r\n        }\r\n      } else {\r\n        this.$message({ message: \"请填写原文链接\", type: \"warning\" });\r\n        this.fileUrlList = [];\r\n      }\r\n    },\r\n    // 删除附件\r\n    uploadUrlRemove() {\r\n      API.removeFile({ filePath: this.form.fileUrl }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$message({ message: \"删除成功\", type: \"success\" });\r\n          this.fileUrlList = [];\r\n          this.form.fileUrl = \"\";\r\n        } else {\r\n          this.$message({ message: \"删除失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        articleSn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceName: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: null,\r\n        deptId: null,\r\n        content: null,\r\n        cnContent: null,\r\n        fileUrl: null,\r\n        industry: null,\r\n        domain: null,\r\n        tmpUrl: null,\r\n        isFinish: null,\r\n        groupId: null,\r\n        appId: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 批量导入相关方法\r\n    // 打开批量导入弹框\r\n    openBatchImportDialog() {\r\n      this.batchImportVisible = true;\r\n      this.batchImportFiles = [];\r\n    },\r\n    // 文件选择处理\r\n    handleFileSelect(file, fileList) {\r\n      // 将新选择的文件添加到列表中\r\n      const newFiles = fileList.map((item) => ({\r\n        fileName: item.name,\r\n        file: item.raw,\r\n        sourceName: \"\",\r\n      }));\r\n      this.batchImportFiles = newFiles;\r\n    },\r\n    // 删除文件\r\n    removeFile(index) {\r\n      this.batchImportFiles.splice(index, 1);\r\n    },\r\n    // 取消批量导入\r\n    cancelBatchImport() {\r\n      this.batchImportVisible = false;\r\n      this.batchImportFiles = [];\r\n      // 清空文件选择器\r\n      this.$refs.batchUpload.clearFiles();\r\n    },\r\n    // 确认批量导入\r\n    async confirmBatchImport() {\r\n      // 验证数据源名称是否都已填写\r\n      const emptySourceNames = this.batchImportFiles.filter(\r\n        (item) => !item.sourceName.trim()\r\n      );\r\n      if (emptySourceNames.length > 0) {\r\n        this.$message({\r\n          message: \"请为所有文件填写数据源名称\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 创建FormData对象\r\n        const formData = new FormData();\r\n\r\n        // 添加文件到FormData\r\n        this.batchImportFiles.forEach((item) => {\r\n          formData.append(\"files\", item.file);\r\n        });\r\n\r\n        // 获取数据源名称数组\r\n        const sourceNames = this.batchImportFiles\r\n          .map((item) => item.sourceName)\r\n          .join(\",\");\r\n\r\n        formData.append(\"sourceNames\", sourceNames);\r\n\r\n        // 调用批量导入API，传递FormData和sourceNames参数\r\n        const response = await API.batchImportReports(formData);\r\n\r\n        if (response.code === 200) {\r\n          this.$message({\r\n            message: \"批量导入成功\",\r\n            type: \"success\",\r\n          });\r\n          this.batchImportVisible = false;\r\n          this.batchImportFiles = [];\r\n          this.$refs.batchUpload.clearFiles();\r\n          // 刷新列表\r\n          this.Refresh();\r\n        } else {\r\n          this.$message({\r\n            message: response.msg || \"批量导入失败\",\r\n            type: \"error\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"批量导入错误:\", error);\r\n        this.$message({\r\n          message: \"批量导入失败，请稍后重试\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    reportAiChat() {\r\n      this.showDeepseekDialog = true;\r\n      if (this.checkedCities.length === 0) {\r\n        this.$message({ message: \"请选择要解读的文章\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.checkedCities.length > 1) {\r\n        this.$message({ message: \"请只选择一篇文章进行解读\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      // 获取选中的文章\r\n      const selectedArticleId = this.checkedCities[0];\r\n      const selectedArticle = this.ArticleList.find(\r\n        (item) => item.id === selectedArticleId\r\n      );\r\n\r\n      if (selectedArticle) {\r\n        this.currentArticle = selectedArticle;\r\n        this.showDeepseekDialog = true;\r\n      } else {\r\n        this.$message({ message: \"未找到选中的文章\", type: \"error\" });\r\n      }\r\n    },\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.checkedCities.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* drawer样式修改 */\r\n.main ::v-deep .el-drawer.drawer_box {\r\n  width: 700px !important;\r\n}\r\n\r\n.MainArticle {\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  // margin-top: 10px;\r\n  user-select: text;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    align-items: center;\r\n    border-bottom: solid 1px #e2e2e2;\r\n  }\r\n\r\n  .leftBtnGroup {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    gap: 15px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    & > p {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .leftBtnGroup2 {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    gap: 15px;\r\n    flex: 1;\r\n    padding-left: 20px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      line-height: 36px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .scollBox {\r\n    overflow-y: auto;\r\n    height: calc(100vh - 389px);\r\n    transition: transform 5s ease;\r\n\r\n    .Articl {\r\n      display: flex;\r\n      width: 100%;\r\n      border-bottom: solid 1px #d4d4d4;\r\n\r\n      .Articl_left {\r\n        width: 85%;\r\n        padding-bottom: 16px;\r\n      }\r\n\r\n      .Articl_kqcb {\r\n        width: 100%;\r\n        padding-bottom: 16px;\r\n        & > div:first-child {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n        }\r\n      }\r\n\r\n      .ArticlTop {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding: 0 0 0 20px;\r\n        font-size: 15px;\r\n      }\r\n\r\n      .ArticlMain {\r\n        padding: 0 0 0 30px;\r\n        color: #3f3f3f;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .ArticlMain > span:hover {\r\n        color: #1889f3;\r\n        border-bottom: solid 1px #0798f8;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .info_flex {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n      }\r\n\r\n      .ArticlBottom {\r\n        padding: 0 0 0 30px;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 0 25px;\r\n        line-height: 24px;\r\n        color: #9b9b9b;\r\n        font-size: 14px;\r\n\r\n        div {\r\n          text-overflow: ellipsis;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .linkStyle:hover {\r\n          border-bottom: solid 1px #1889f3;\r\n        }\r\n\r\n        .infomation {\r\n          color: #464749;\r\n          font-size: 14px;\r\n          margin-left: 8px;\r\n        }\r\n\r\n        p {\r\n          border: solid 1px #f0a147;\r\n          width: 45px;\r\n          height: 25px;\r\n          line-height: 25px;\r\n          font-size: 14px;\r\n          color: #f0a147;\r\n          text-align: center;\r\n        }\r\n      }\r\n\r\n      .imgBox {\r\n        padding: 0 20px 0 30px;\r\n        display: flex;\r\n        gap: 15px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.pagination {\r\n  z-index: 2;\r\n  background-color: rgba(255, 255, 255, 1);\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  padding: 10px 0;\r\n}\r\n\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n  line-height: 16px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n\r\n.drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n.drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n.btnBox {\r\n  z-index: 2;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n\r\n  & > p {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 批量导入弹框样式\r\n.batch-import-container {\r\n  .file-select-area {\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    background-color: #fafafa;\r\n\r\n    &:hover {\r\n      border-color: #409eff;\r\n    }\r\n  }\r\n\r\n  .file-table-area {\r\n    margin-top: 20px;\r\n\r\n    .el-table {\r\n      border-radius: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #1296db;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #1296db;\r\n}\r\n\r\n.deepseek-text {\r\n  color: #1296db; // 使用与图标相同的颜色\r\n  margin-left: 4px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style>\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n</style>\r\n"]}]}