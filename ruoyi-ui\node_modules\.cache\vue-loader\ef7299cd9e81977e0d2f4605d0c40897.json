{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue", "mtime": 1754108349423}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQVBJIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHJlcXVlc3QgZnJvbSAiQC91dGlscy9yZXF1ZXN0IjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCmltcG9ydCB7IE1lc3NhZ2VCb3ggfSBmcm9tICJlbGVtZW50LXVpIjsNCmltcG9ydCBheGlvcyBmcm9tICJheGlvcyI7DQppbXBvcnQgeyBnZXRMaXN0Q2xhc3NpZnkgfSBmcm9tICJAL2FwaS9hcnRpY2xlL2NsYXNzaWZ5IjsNCmltcG9ydCB7IHNhdmVBcyB9IGZyb20gImZpbGUtc2F2ZXIiOw0KaW1wb3J0IHsgYmxvYlZhbGlkYXRlLCB0YW5zUGFyYW1zIH0gZnJvbSAiQC91dGlscy9ydW95aSI7DQppbXBvcnQgeyBhcnRpY2xlTGlzdEVkaXQsIHVwbG9hZENvdmVyIH0gZnJvbSAiQC9hcGkvYXJ0aWNsZUNyYXdsZXIvbGlzdCI7DQppbXBvcnQgRGVlcHNlZWtSZXBvcnREaWFsb2cgZnJvbSAiLi9EZWVwc2Vla1JlcG9ydERpYWxvZy52dWUiOw0KaW1wb3J0IHsgZGVlcHNlZWtBaVFhLCBkaWZ5QWlRYSwgb2xsYW1hQWlRYSB9IGZyb20gIkAvYXBpL2luZm9Fc2NhbGF0aW9uL2FpIjsNCmltcG9ydCB7IG1hcmtlZCB9IGZyb20gIm1hcmtlZCI7DQppbXBvcnQgeyBnZXRDb25maWdLZXkgfSBmcm9tICJAL2FwaS9zeXN0ZW0vY29uZmlnIjsNCmltcG9ydCB7IGdldExpc3RCeUlkcyB9IGZyb20gIkAvYXBpL2FydGljbGUvYXJ0aWNsZUhpc3RvcnkiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgZG93bkxvYWRTaG93OiB7DQogICAgICAvKiDkuIvovb3mjInpkq4gKi8gcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUsDQogICAgfSwNCiAgICBlZGl0U2hvdzogew0KICAgICAgLyog57yW6L6R5oyJ6ZKuICovIHJlcXVpcmVkOiBmYWxzZSwNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlLA0KICAgIH0sDQogICAgY29weVNob3c6IHsNCiAgICAgIC8qIOWkjeWItuaMiemSriAqLyByZXVxaXJlZDogZmFsc2UsDQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZSwNCiAgICB9LA0KICAgIGhlaWdodDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogNjU1LA0KICAgIH0sDQogICAgY3VycmVudFBhZ2U6IHsNCiAgICAgIHJldXFpcmVkOiB0cnVlLA0KICAgICAgZGVmYXVsdDogMSwNCiAgICB9LA0KICAgIHBhZ2VTaXplOiB7DQogICAgICByZXVxaXJlZDogdHJ1ZSwNCiAgICAgIGRlZmF1bHQ6IDUwLA0KICAgIH0sDQogICAgdG90YWw6IHsNCiAgICAgIHJldXFpcmVkOiB0cnVlLA0KICAgICAgZGVmYXVsdDogMCwNCiAgICB9LA0KICAgIEFydGljbGVMaXN0OiB7DQogICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgIGRlZmF1bHQ6IFtdLA0KICAgIH0sDQogICAgZmxhZzogew0KICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgfSwNCiAgICBTZWFjaERhdGE6IHsNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgIH0sDQogICAga2V5d29yZHM6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICIiLA0KICAgIH0sDQogICAgLy8g5oql5ZGK57G75Z6L5a2X5q61DQogICAgc291cmNlVHlwZTogew0KICAgICAgZGVmYXVsdDogIiIsDQogICAgfSwNCiAgfSwNCiAgY29tcG9uZW50czogew0KICAgIERlZXBzZWVrUmVwb3J0RGlhbG9nLA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHJlZ0V4cEltZzogL15cbiQvLA0KICAgICAgcmVwb3J0SWQ6ICIiLA0KICAgICAgcmVwb3J0T3B0aW9uczogW10sDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNoZWNrZWRDaXRpZXM6IFtdIC8qIOWkmumAiSAqLywNCiAgICAgIGNoZWNrZWQ6IGZhbHNlIC8qIOWFqOmAiSAqLywNCiAgICAgIGh0bWw6ICIiLA0KICAgICAgdGV4dDogIiIsDQogICAgICB0aGF0OiB0aGlzLA0KICAgICAgdGFnU2hvdzogZmFsc2UsDQogICAgICBpc0luZGV0ZXJtaW5hdGU6IHRydWUsDQogICAgICBjb3VudDogMCwNCiAgICAgIHNlcGFyYXRlOiB7fSwNCiAgICAgIC8qIOagh+etvuWKn+iDvSAqLw0KICAgICAgdGFnRGlhbG9nOiBmYWxzZSwNCiAgICAgIGZvcm1MYWJlbEFsaWduOiB7DQogICAgICAgIHRhZzogIiIsDQogICAgICAgIGluZHVzdHJ5OiAiIiwNCiAgICAgICAgZG9tYWluOiAiIiwNCiAgICAgIH0sDQogICAgICBvcHRpb25zOiBbXSwNCiAgICAgIG9wdGlvbnMxOiBbXSwNCiAgICAgIHRhZ0l0ZW06IHt9IC8qIOagh+etvuWvueixoSAqLywNCiAgICAgIGFyZWFMaXN0OiBbXSAvKiDpoobln58gKi8sDQogICAgICBpbmR1c3RyeTogW10gLyog6KGM5LiaICovLA0KICAgICAgbnVtOiAwLA0KICAgICAgdGltZXI6IG51bGwsDQogICAgICBkcmF3ZXI6IGZhbHNlLA0KICAgICAgZHJhd2VySW5mbzoge30sDQogICAgICBBcmVhSWQ6IG51bGwsDQogICAgICB0cmFuc2xhdGlvbkJ0blNob3c6IG51bGwsDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIHNvdXJjZVR5cGVMaXN0OiBbXSwgLy8g5pWw5o2u5rqQ5YiG57G7DQogICAgICBzb3VyY2VMaXN0czogW10sIC8vIOaVsOaNrua6kOWIl+ihqA0KICAgICAgc291cmNlVHlwZUxpc3RzOiBbXSwNCiAgICAgIGZvcm06IHt9LCAvLyDooajljZXlj4LmlbANCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgICB0aXRsZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmlofnq6DmoIfpopjkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBjb250ZW50OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaWh+eroOivpuaDheS4uuW/heWhq+mhuSIgfV0sDQogICAgICAgIHB1Ymxpc2hUaW1lOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWPkeW4g+aXtumXtOS4uuW/heWhq+mhuSIgfV0sDQogICAgICAgIGNuVGl0bGU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lit5paH5ZCN56ew5Li65b+F5aGr6aG5IiB9XSwNCiAgICAgICAgc291cmNlVHlwZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlubPlj7DnsbvlnovkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBvcmlnaW5hbFVybDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLljp/mlofkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBzdW1tYXJ5OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+Whq+WGmeaRmOimgSIgfV0sDQogICAgICAgIC8vIGNuU3VtbWFyeTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnkuK3mlofmkZjopoEnIH1dLA0KICAgICAgICBzbjogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7floavlhpnmlofnq6DlnLDlnYDllK/kuIDor4bliKvlj7ciIH1dLA0KICAgICAgfSwNCiAgICAgIHZlcnRpZnlVcGxvYWQ6IHsNCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gNCiAgICAgICAgaGVhZGVyczogew0KICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCksDQogICAgICAgICAgQ29udGVudFR5cGU6ICJhcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTgiLA0KICAgICAgICB9LA0KICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYANCiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9hcnRpY2xlL2FydGljbGVMaXN0L2NvdmVyIiwNCiAgICAgIH0sDQogICAgICBmaWxlTGlzdDogW10sDQogICAgICBmaWxlVXJsTGlzdDogW10sDQogICAgICBmaWxlVXJsdXJsOg0KICAgICAgICBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9hcnRpY2xlL2FydGljbGVMaXN0L3VwbG9hZC9maWxlIiwNCiAgICAgIHNob3dTdW1tYXJ5OiB0cnVlLA0KICAgICAgLy8g5om56YeP5a+85YWl55u45YWz5pWw5o2uDQogICAgICBiYXRjaEltcG9ydFZpc2libGU6IGZhbHNlLA0KICAgICAgYmF0Y2hJbXBvcnRGaWxlczogW10sDQogICAgICAvLyBEZWVwc2Vla+aKpeWRiuino+ivu+W8ueeqlw0KICAgICAgc2hvd0RlZXBzZWVrRGlhbG9nOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRBcnRpY2xlOiB7fSwNCiAgICAgIC8vIGFp55u45YWzDQogICAgICBhaURpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgY2hhdE1lc3NhZ2VzOiBbXSwNCiAgICAgIGlzVGhpbmtpbmc6IGZhbHNlLA0KICAgICAgdXNlckF2YXRhcjogIiIsIC8vIOeUqOaIt+WktOWDjw0KICAgICAgc3RyZWFtaW5nTWVzc2FnZTogIiIsIC8vIOa3u+WKoOeUqOS6juWtmOWCqOato+WcqOa1geW8j+i+k+WHuueahOa2iOaBrw0KICAgICAgbWFya2Rvd25PcHRpb25zOiB7DQogICAgICAgIGdmbTogdHJ1ZSwNCiAgICAgICAgYnJlYWtzOiB0cnVlLA0KICAgICAgICBoZWFkZXJJZHM6IHRydWUsDQogICAgICAgIG1hbmdsZTogZmFsc2UsDQogICAgICAgIGhlYWRlclByZWZpeDogIiIsDQogICAgICAgIHBlZGFudGljOiBmYWxzZSwNCiAgICAgICAgc2FuaXRpemU6IGZhbHNlLA0KICAgICAgICBzbWFydExpc3RzOiB0cnVlLA0KICAgICAgICBzbWFydHlwYW50czogdHJ1ZSwNCiAgICAgICAgeGh0bWw6IHRydWUsDQogICAgICB9LA0KICAgICAgaXNSZXF1ZXN0aW5nOiBmYWxzZSwgLy8g5qCH6K6w5piv5ZCm5q2j5Zyo6K+35rGC5LitDQogICAgICBpc0Fib3J0ZWQ6IGZhbHNlLCAvLyDmoIforrDmmK/lkKblt7LkuK3mlq0NCiAgICAgIGN1cnJlbnRSZWFkZXI6IG51bGwsIC8vIOW9k+WJjeeahCByZWFkZXINCiAgICAgIGFpUGxhdGZvcm06ICIiLA0KICAgICAgYXJ0aWNsZUFpUHJvbXB0OiAiIiwNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDoge30sDQogIHdhdGNoOiB7DQogICAgZGlhbG9nVmlzaWJsZTogZnVuY3Rpb24gKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICBpZiAobmV3VmFsKSB7DQogICAgICAgIEFQSS5nZXROZXdCdWlsdCh7IHNvdXJjZVR5cGU6IHRoaXMuc291cmNlVHlwZSB9KS50aGVuKChkYXRhKSA9PiB7DQogICAgICAgICAgaWYgKGRhdGEuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMucmVwb3J0T3B0aW9ucyA9IGRhdGEuZGF0YTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLmiqXlkYrliJfooajojrflj5blpLHotKXkuoYiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyAnZm9ybUxhYmVsQWxpZ24uaW5kdXN0cnknOiB7DQogICAgLy8gICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgIC8vICAgICBpZiAobmV3VmFsID09ICcnKSB7DQogICAgLy8gICAgICAgdGhpcy5vcHRpb25zMSA9IHRoaXMuaW5kdXN0cnkNCiAgICAvLyAgICAgfQ0KICAgIC8vICAgfSwNCiAgICAvLyAgIGRlZXA6IHRydWUNCiAgICAvLyB9LA0KICAgIC8vICdmb3JtTGFiZWxBbGlnbi5kb21haW4nOiB7DQogICAgLy8gICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgIC8vICAgICBpZiAobmV3VmFsID09ICcnKSB7DQogICAgLy8gICAgICAgdGhpcy5vcHRpb25zID0gdGhpcy5hcmVhTGlzdA0KICAgIC8vICAgICB9DQogICAgLy8gICB9LA0KICAgIC8vICAgZGVlcDogdHJ1ZQ0KICAgIC8vIH0sDQogICAgIlNlYWNoRGF0YS5zb3J0TW9kZSI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgdGhpcy5SZWZyZXNoKCk7DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICB9LA0KICAgICJmb3JtLnNvdXJjZVR5cGUiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIHRoaXMuc291cmNlVHlwZUxpc3RzID0gdGhpcy5zb3VyY2VMaXN0cy5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS50eXBlID09IG5ld1ZhbDsNCiAgICAgICAgfSk7DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICB9LA0KICB9LA0KICBtb3VudGVkKCkge30sDQogIGNyZWF0ZWQoKSB7DQogICAgaWYgKA0KICAgICAgdGhpcy5mbGFnICE9PSAiTW9uaXRvclVzZSIgJiYNCiAgICAgIHRoaXMuZmxhZyAhPT0gInNwZWNpYWxTdWJqZWN0VXNlIiAmJg0KICAgICAgdGhpcy5mbGFnICE9PSAiV2VjaGF0Ig0KICAgICkgew0KICAgICAgdGhpcy5vcGVuRGlhbG9nKCk7DQogICAgfQ0KICAgIGlmICh0aGlzLmZsYWcgIT09ICJXZWNoYXQiKSB7DQogICAgICBnZXRMaXN0Q2xhc3NpZnkoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5zb3VyY2VUeXBlTGlzdCA9IHJlcy5kYXRhOw0KICAgICAgfSk7DQogICAgICBBUEkuZ2V0U291cmNlTGlzdCgpLnRoZW4oKGRhdGEpID0+IHsNCiAgICAgICAgaWYgKGRhdGEuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnNvdXJjZUxpc3RzID0gZGF0YS5kYXRhOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9DQogICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LmRvbWFpbikgew0KICAgICAgZ2V0Q29uZmlnS2V5KCJzeXMuYWkucGxhdGZvcm0iKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuYWlQbGF0Zm9ybSA9IHJlcy5tc2c7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgZ2V0Q29uZmlnS2V5KCJ3ZWNoYXQuYWkuYXJ0aWNsZVByb21wdCIpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5hcnRpY2xlQWlQcm9tcHQgPSByZXMubXNnOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIC8vIOiOt+WPlueUqOaIt+WktOWDjw0KICAgICAgdGhpcy51c2VyQXZhdGFyID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5hdmF0YXI7DQogICAgfQ0KDQogICAgdGhpcy5zaG93U3VtbWFyeSA9IHRydWU7DQogIH0sDQogIHVwZGF0ZWQoKSB7fSwNCiAgZmlsdGVyczoge30sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDlpITnkIbnp5HmioDnm7jlhbPlrZfmrrXnmoTmmL7npLrmmKDlsIQNCiAgICBnZXRUZWNobm9sb2d5TGFiZWwodmFsdWUpIHsNCiAgICAgIGNvbnN0IG1hcHBpbmcgPSB7DQogICAgICAgIDA6ICLlkKYiLA0KICAgICAgICAxOiAi5pivIiwNCiAgICAgICAgMjogIuWFtuS7liIsDQogICAgICAgIDM6ICLlvoXlrpoiLA0KICAgICAgfTsNCiAgICAgIHJldHVybiBtYXBwaW5nW3ZhbHVlXTsNCiAgICB9LA0KICAgIC8vIOWuieWFqOWkhOeQhuaRmOimgeWGheWuue+8jOmBv+WFjW51bGzosIPnlKhyZXBsYWNl5oql6ZSZDQogICAgZ2V0U2FmZVN1bW1hcnkoaXRlbSkgew0KICAgICAgY29uc3QgY25TdW1tYXJ5ID0gaXRlbS5jblN1bW1hcnkgfHwgJyc7DQogICAgICBjb25zdCBzdW1tYXJ5ID0gaXRlbS5zdW1tYXJ5IHx8ICcnOw0KDQogICAgICBjb25zdCBwcm9jZXNzZWRDblN1bW1hcnkgPSBjblN1bW1hcnkgPyBjblN1bW1hcnkucmVwbGFjZSgNCiAgICAgICAgL3B8c3Ryb25nfGVtfGJ8ZGl2fHN2Z3xoMXxoMnxoM3xoNHxoNXxoNnx1bHxsaXxhL2csDQogICAgICAgICdzcGFuJw0KICAgICAgKSA6ICcnOw0KDQogICAgICBjb25zdCBwcm9jZXNzZWRTdW1tYXJ5ID0gc3VtbWFyeSA/IHN1bW1hcnkucmVwbGFjZSgNCiAgICAgICAgL3B8c3Ryb25nfGVtfGJ8ZGl2fHN2Z3xoMXxoMnxoM3xoNHxoNXxoNnx1bHxsaXxhL2csDQogICAgICAgICdzcGFuJw0KICAgICAgKSA6ICcnOw0KDQogICAgICByZXR1cm4gcHJvY2Vzc2VkQ25TdW1tYXJ5IHx8IHByb2Nlc3NlZFN1bW1hcnk7DQogICAgfSwNCiAgICAvLyDlpITnkIblj5HluIPml7bpl7TnmoTmmL7npLoNCiAgICBmb3JtYXRQdWJsaXNoVGltZShwdWJsaXNoVGltZSwgd2Vic3RlUHVibGlzaFRpbWUpIHsNCiAgICAgIC8vIOagvOW8j+WMlnB1Ymxpc2hUaW1l5Li65bm05pyI5pelDQogICAgICBjb25zdCBmb3JtYXR0ZWRQdWJsaXNoVGltZSA9IHRoaXMucGFyc2VUaW1lKHB1Ymxpc2hUaW1lLCAie3l9LXttfS17ZH0iKTsNCg0KICAgICAgLy8g5aaC5p6cd2Vic3RlUHVibGlzaFRpbWXkuI3lrZjlnKjvvIznm7TmjqXov5Tlm55wdWJsaXNoVGltZQ0KICAgICAgaWYgKCF3ZWJzdGVQdWJsaXNoVGltZSkgew0KICAgICAgICByZXR1cm4gZm9ybWF0dGVkUHVibGlzaFRpbWU7DQogICAgICB9DQoNCiAgICAgIGxldCBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gIiI7DQogICAgICAvLyDlpITnkIbkuI3lkIzmoLzlvI/nmoR3ZWJzdGVQdWJsaXNoVGltZQ0KICAgICAgaWYgKHdlYnN0ZVB1Ymxpc2hUaW1lKSB7DQogICAgICAgIC8vIOWkhOeQhjIwMjUtMDQtMTIgMTA6MDk6MjEuOTcxMTkx5qC85byP77yI5YyF5ZCr6L+e5a2X56ym55qE5qCH5YeG5qC85byP77yJDQogICAgICAgIGlmICh3ZWJzdGVQdWJsaXNoVGltZS5pbmNsdWRlcygiLSIpKSB7DQogICAgICAgICAgY29uc3QgZGF0ZU1hdGNoID0gd2Vic3RlUHVibGlzaFRpbWUubWF0Y2goLyhcZHs0fSktKFxkezJ9KS0oXGR7Mn0pLyk7DQogICAgICAgICAgaWYgKGRhdGVNYXRjaCkgew0KICAgICAgICAgICAgY29uc3QgeWVhciA9IGRhdGVNYXRjaFsxXTsNCiAgICAgICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZU1hdGNoWzJdOw0KICAgICAgICAgICAgY29uc3QgZGF5ID0gZGF0ZU1hdGNoWzNdOw0KICAgICAgICAgICAgZm9ybWF0dGVkV2Vic3RlVGltZSA9IGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWA7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSB3ZWJzdGVQdWJsaXNoVGltZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8g5aSE55CGMjAyNeW5tDA05pyIMTTml6UgMTE6Mjk6MjLmoLzlvI/vvIjkuK3mloflubTmnIjml6XmoLzlvI/vvIzluKYi5pelIuWtl++8iQ0KICAgICAgICBlbHNlIGlmICgNCiAgICAgICAgICB3ZWJzdGVQdWJsaXNoVGltZS5pbmNsdWRlcygi5bm0IikgJiYNCiAgICAgICAgICB3ZWJzdGVQdWJsaXNoVGltZS5pbmNsdWRlcygi5pyIIikgJiYNCiAgICAgICAgICB3ZWJzdGVQdWJsaXNoVGltZS5pbmNsdWRlcygi5pelIikNCiAgICAgICAgKSB7DQogICAgICAgICAgY29uc3QgZGF0ZU1hdGNoID0gd2Vic3RlUHVibGlzaFRpbWUubWF0Y2goDQogICAgICAgICAgICAvKFxkezR9KeW5tChcZHsxLDJ9KeaciChcZHsxLDJ9KeaXpS8NCiAgICAgICAgICApOw0KICAgICAgICAgIGlmIChkYXRlTWF0Y2gpIHsNCiAgICAgICAgICAgIGNvbnN0IHllYXIgPSBkYXRlTWF0Y2hbMV07DQogICAgICAgICAgICBjb25zdCBtb250aCA9IGRhdGVNYXRjaFsyXS5wYWRTdGFydCgyLCAiMCIpOw0KICAgICAgICAgICAgY29uc3QgZGF5ID0gZGF0ZU1hdGNoWzNdLnBhZFN0YXJ0KDIsICIwIik7DQogICAgICAgICAgICBmb3JtYXR0ZWRXZWJzdGVUaW1lID0gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YDsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgZm9ybWF0dGVkV2Vic3RlVGltZSA9IHdlYnN0ZVB1Ymxpc2hUaW1lOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDlpITnkIYyMDI15bm0NOaciDE15qC85byP77yI5Lit5paH5bm05pyI5qC85byP77yM5LiN5bimIuaXpSLlrZfvvIkNCiAgICAgICAgZWxzZSBpZiAoDQogICAgICAgICAgd2Vic3RlUHVibGlzaFRpbWUuaW5jbHVkZXMoIuW5tCIpICYmDQogICAgICAgICAgd2Vic3RlUHVibGlzaFRpbWUuaW5jbHVkZXMoIuaciCIpDQogICAgICAgICkgew0KICAgICAgICAgIGNvbnN0IGRhdGVNYXRjaCA9IHdlYnN0ZVB1Ymxpc2hUaW1lLm1hdGNoKA0KICAgICAgICAgICAgLyhcZHs0fSnlubQoXGR7MSwyfSnmnIgoXGR7MSwyfSkvDQogICAgICAgICAgKTsNCiAgICAgICAgICBpZiAoZGF0ZU1hdGNoKSB7DQogICAgICAgICAgICBjb25zdCB5ZWFyID0gZGF0ZU1hdGNoWzFdOw0KICAgICAgICAgICAgY29uc3QgbW9udGggPSBkYXRlTWF0Y2hbMl0ucGFkU3RhcnQoMiwgIjAiKTsNCiAgICAgICAgICAgIGNvbnN0IGRheSA9IGRhdGVNYXRjaFszXS5wYWRTdGFydCgyLCAiMCIpOw0KICAgICAgICAgICAgZm9ybWF0dGVkV2Vic3RlVGltZSA9IGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWA7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSB3ZWJzdGVQdWJsaXNoVGltZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8g5aSE55CGMjAyNS8wNC8xNCAxMToyOToyMuagvOW8j++8iOaWnOadoOWIhumalOeahOagvOW8j++8iQ0KICAgICAgICBlbHNlIGlmICh3ZWJzdGVQdWJsaXNoVGltZS5pbmNsdWRlcygiLyIpKSB7DQogICAgICAgICAgY29uc3QgZGF0ZU1hdGNoID0gd2Vic3RlUHVibGlzaFRpbWUubWF0Y2goDQogICAgICAgICAgICAvKFxkezR9KVwvKFxkezEsMn0pXC8oXGR7MSwyfSkvDQogICAgICAgICAgKTsNCiAgICAgICAgICBpZiAoZGF0ZU1hdGNoKSB7DQogICAgICAgICAgICBjb25zdCB5ZWFyID0gZGF0ZU1hdGNoWzFdOw0KICAgICAgICAgICAgY29uc3QgbW9udGggPSBkYXRlTWF0Y2hbMl0ucGFkU3RhcnQoMiwgIjAiKTsNCiAgICAgICAgICAgIGNvbnN0IGRheSA9IGRhdGVNYXRjaFszXS5wYWRTdGFydCgyLCAiMCIpOw0KICAgICAgICAgICAgZm9ybWF0dGVkV2Vic3RlVGltZSA9IGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWA7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGZvcm1hdHRlZFdlYnN0ZVRpbWUgPSB3ZWJzdGVQdWJsaXNoVGltZTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5YW25LuW5qC85byP55u05o6l5L2/55So5Y6f5YC8DQogICAgICAgICAgZm9ybWF0dGVkV2Vic3RlVGltZSA9IHdlYnN0ZVB1Ymxpc2hUaW1lOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOavlOi+g+W5tOaciOaXpeaYr+WQpuebuOWQjA0KICAgICAgaWYgKGZvcm1hdHRlZFB1Ymxpc2hUaW1lID09PSBmb3JtYXR0ZWRXZWJzdGVUaW1lKSB7DQogICAgICAgIHJldHVybiBmb3JtYXR0ZWRQdWJsaXNoVGltZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBgJHtmb3JtYXR0ZWRQdWJsaXNoVGltZX0gLyAke3dlYnN0ZVB1Ymxpc2hUaW1lfWA7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOajgOafpeaWh+acrOaYr+WQpuacieWunumZheWGheWuue+8iOWOu+mZpEhUTUzmoIfnrb7lkI7vvIkNCiAgICBoYXNBY3R1YWxDb250ZW50KHRleHQpIHsNCiAgICAgIGlmICghdGV4dCkgew0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgICAvLyDljrvpmaRIVE1M5qCH562+DQogICAgICBjb25zdCBjb250ZW50V2l0aG91dFRhZ3MgPSB0ZXh0LnJlcGxhY2UoLzxbXj5dKj4vZywgIiIpOw0KICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ5Lit5paH44CB6Iux5paH44CB5pWw5a2X562J5a6e6ZmF5YaF5a65DQogICAgICByZXR1cm4gL1tcdTRlMDAtXHU5ZmE1YS16QS1aMC05XS8udGVzdChjb250ZW50V2l0aG91dFRhZ3MpOw0KICAgIH0sDQogICAgLy8g5YWz6ZSu5a2X5pu/5o2iDQogICAgY2hhbmdlQ29sb3Ioc3RyKSB7DQogICAgICBsZXQgU3RyID0gc3RyOw0KICAgICAgaWYgKFN0cikgew0KICAgICAgICBsZXQga2V5d29yZHMgPSB0aGlzLmtleXdvcmRzLnNwbGl0KCIsIik7DQogICAgICAgIGtleXdvcmRzLm1hcCgoa2V5aXRlbSwga2V5aW5kZXgpID0+IHsNCiAgICAgICAgICBpZiAoa2V5aXRlbSAmJiBrZXlpdGVtLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIC8vIOWMuemFjeWFs+mUruWtl+ato+WImQ0KICAgICAgICAgICAgbGV0IHJlcGxhY2VSZWcgPSBuZXcgUmVnRXhwKGtleWl0ZW0sICJnIik7DQogICAgICAgICAgICAvLyDpq5jkuq7mm7/mjaJ2LWh0bWzlgLwNCiAgICAgICAgICAgIGxldCByZXBsYWNlU3RyaW5nID0NCiAgICAgICAgICAgICAgJzxzcGFuIGNsYXNzPSJoaWdobGlnaHQiJyArDQogICAgICAgICAgICAgICcgc3R5bGU9ImNvbG9yOiByZWQ7Ij4nICsNCiAgICAgICAgICAgICAga2V5aXRlbSArDQogICAgICAgICAgICAgICI8L3NwYW4+IjsNCiAgICAgICAgICAgIFN0ciA9IFN0ci5yZXBsYWNlKHJlcGxhY2VSZWcsIHJlcGxhY2VTdHJpbmcpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICByZXR1cm4gU3RyOw0KICAgIH0sDQogICAgLyog5LiL6L29RXhjZWwgKi8NCiAgICBhc3luYyBkb3duTG9hZEV4Y2VsKCkgew0KICAgICAgaWYgKHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuivt+mAieaLqeimgeWvvOWHuueahOaVsOaNriIsIHR5cGU6ICJ3YXJuaW5nIiB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5mbGFnID09ICJzcGVjaWFsU3ViamVjdFVzZSIpIHsNCiAgICAgICAgQVBJLmRvd25Mb2FkRXhjZWwodGhpcy5jaGVja2VkQ2l0aWVzKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIGxldCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOw0KICAgICAgICAgIGEuaHJlZiA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKHJlc3BvbnNlKTsNCiAgICAgICAgICBhLmRvd25sb2FkID0gYHNvdXJjZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YDsNCiAgICAgICAgICBhLmNsaWNrKCk7DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgYXdhaXQgQVBJLmRvd25Mb2FkRXhwb3J0RXhjZWwodGhpcy5jaGVja2VkQ2l0aWVzKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIGxldCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOw0KICAgICAgICAgIGEuaHJlZiA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKHJlc3BvbnNlKTsNCiAgICAgICAgICBhLmRvd25sb2FkID0gYHNvdXJjZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YDsNCiAgICAgICAgICBhLmNsaWNrKCk7DQoNCiAgICAgICAgICAvLyBzYXZlQXMoYmxvYiwgYHNvdXJjZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBiYXRjaERlbGV0ZSgpIHsNCiAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLor7fpgInmi6nopoHliKDpmaTnmoTmlbDmja4iLCB0eXBlOiAid2FybmluZyIgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruiupOWIoOmZpOW3suWLvumAieeahOaVsOaNrumhuT8iKQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgQVBJLmJhdGNoUmVtb3ZlKHRoaXMuY2hlY2tlZENpdGllcy5qb2luKCIsIikpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoIlJlZnJlc2giKTsNCiAgICAgICAgICAgIHRoaXMuY2hlY2tlZENpdGllcyA9IFtdOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyog5Y+R5biD5Yiw5q+P5pel5pyA5paw54Ot54K5ICovDQogICAgcHVibGlzaEhvdCgpIHsNCiAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeimgeWPkeW4g+WIsOavj+aXpeacgOaWsOeDreeCueeahOaVsOaNriIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm56Gu6K6k5Y+R5biD5bey5Yu+6YCJ55qE5pWw5o2u6aG55Yiw5q+P5pel5pyA5paw54Ot54K5PyIpDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICBBUEkucHVibGlzaEV2ZXJ5ZGF5SG90KHRoaXMuY2hlY2tlZENpdGllcy5qb2luKCIsIikpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IHR5cGU6ICJzdWNjZXNzIiwgbWVzc2FnZTogIuWPkeW4g+aIkOWKnyEiIH0pOw0KICAgICAgICAgICAgdGhpcy4kZW1pdCgiUmVmcmVzaCIpOw0KICAgICAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzID0gW107DQogICAgICAgICAgfSk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiDov5Tlm57pobbpg6jliqjnlLsgKi8NCiAgICBtYWluU2NvcmxsKCkgew0KICAgICAgdmFyIHNjcm9sbFN0ZXAgPSAtdGhpcy4kcmVmcy5zY3JvbGwuc2Nyb2xsVG9wIC8gKDgwMCAvIDE1KTsgLy8g6K6h566X5q+P5LiA5q2l5rua5Yqo55qE6Led56a7DQogICAgICB2YXIgc2Nyb2xsSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICAgIGlmICh0aGlzLiRyZWZzLnNjcm9sbC5zY3JvbGxUb3AgIT09IDApIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnNjcm9sbC5zY3JvbGxCeSgwLCBzY3JvbGxTdGVwKTsgLy8g5oyJ54Wn57uZ5a6a5q2l6ZW/5rua5Yqo56qX5Y+jDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY2xlYXJJbnRlcnZhbChzY3JvbGxJbnRlcnZhbCk7IC8vIOWIsOi+vumhtumDqOaXtua4hemZpOWumuaXtuWZqA0KICAgICAgICB9DQogICAgICB9LCAxNSk7DQogICAgfSwNCiAgICBzY3JvbGxDaGFuZ2UoKSB7DQogICAgICBjbGVhclRpbWVvdXQodGhpcy50aW1lcik7DQogICAgICB0aGlzLnRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMuc3RvcFNjcm9sbCgpOw0KICAgICAgfSwgNTAwKTsNCiAgICAgIC8vIHRoaXMuJHJlZnMucGFnaW5hdGlvbi5zdHlsZS5vcGFjaXR5ID0gMA0KICAgICAgLy8gdGhpcy4kcmVmcy5wYWdpbmF0aW9uLnN0eWxlLnRyYW5zaXRpb24gPSAnMCcNCiAgICB9IC8qIOa7muWKqOS6i+S7tiAqLywNCiAgICBzdG9wU2Nyb2xsKCkgew0KICAgICAgLy8gdGhpcy4kcmVmcy5wYWdpbmF0aW9uLnN0eWxlLnRyYW5zaXRpb24gPSAnMXMnDQogICAgICAvLyB0aGlzLiRyZWZzLnBhZ2luYXRpb24uc3R5bGUub3BhY2l0eSA9IDENCiAgICB9LA0KICAgIC8qIOS4i+i9vSAqLw0KICAgIGRvd25Mb2FkKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qIOavj+mhteadoeaVsOWPmOWMliAqLw0KICAgIGhhbmRsZVNpemVDaGFuZ2UobnVtKSB7DQogICAgICB0aGlzLiRlbWl0KCJoYW5kbGVTaXplQ2hhbmdlIiwgbnVtKTsNCiAgICAgIHRoaXMubWFpblNjb3JsbCgpOw0KICAgICAgdGhpcy5jaGVja2VkID0gZmFsc2U7DQogICAgfSwNCiAgICAvKiDpobXnoIHlj5jljJYgKi8NCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKGN1cnJlbnQpIHsNCiAgICAgIHRoaXMuJGVtaXQoImhhbmRsZUN1cnJlbnRDaGFuZ2UiLCBjdXJyZW50KTsNCiAgICAgIHRoaXMubWFpblNjb3JsbCgpOw0KICAgICAgdGhpcy5jaGVja2VkID0gZmFsc2U7DQogICAgfSwNCiAgICAvKiDmlLbol48gKi8NCiAgICBhc3luYyBjb2xsZWN0KGl0ZW0pIHsNCiAgICAgIC8qIOeCueWHu+WIl+ihqOaUtuiXjyAqLw0KICAgICAgaWYgKGl0ZW0uaWQpIHsNCiAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzID0gW2l0ZW0uaWRdOw0KICAgICAgfQ0KICAgICAgLyog5pyq6YCJ5oup5o+Q56S6ICovDQogICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi6K+36YCJ5oup6KaB5pS26JeP55qE5paH56ugIiwgdHlwZTogImluZm8iIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICAvKiDmlLbol48gKi8NCiAgICAgIGlmICghaXRlbS5mYXZvcml0ZXMpIHsNCiAgICAgICAgbGV0IHJlcyA9IGF3YWl0IEFQSS5jb2xsZWN0QXBpKFtpdGVtLmlkXSk7DQogICAgICAgIGlmIChyZXMuY29kZSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogIuaUtuiXj+aIkOWKnyzor7fliY3lvoDkuKrkurrkuK3lv4Pmn6XnnIsiLA0KICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuJGVtaXQoIlJlZnJlc2giKTsNCiAgICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMgPSBbXTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLmlLbol4/lpLHotKUiLCB0eXBlOiAiaW5mbyIgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgQVBJLmNvY2VsQ29sbGVjdChbaXRlbS5pZF0pOw0KICAgICAgICBpZiAocmVzLmNvZGUpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuW3suWPlua2iOaUtuiXjyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgICB0aGlzLiRlbWl0KCJSZWZyZXNoIik7DQogICAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzID0gW107DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5Y+W5raI5pS26JeP5aSx6LSlIiwgdHlwZTogImluZm8iIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyog5LiA6ZSu5aSN5Yi2ICovDQogICAgY29weVRleHQoaXRlbSkgew0KICAgICAgbmF2aWdhdG9yLmNsaXBib2FyZA0KICAgICAgICAud3JpdGVUZXh0KGl0ZW0uY25UaXRsZSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5bey5oiQ5Yqf5aSN5Yi25Yiw5Ymq6LS05p2/IiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goZnVuY3Rpb24gKCkgew0KICAgICAgICAgIGFsZXJ0KCLlpI3liLblpLHotKUiKTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICB0eXBlSGFuZGxlKGRhdGEpIHsNCiAgICAgIGlmIChkYXRhID09IDEpIHsNCiAgICAgICAgcmV0dXJuICLlvq7kv6HlhazkvJflj7ciOw0KICAgICAgfSBlbHNlIGlmIChkYXRhID09IDIpIHsNCiAgICAgICAgcmV0dXJuICLnvZHnq5kiOw0KICAgICAgfSBlbHNlIGlmIChkYXRhID09IDMpIHsNCiAgICAgICAgcmV0dXJuICLmiYvliqjlvZXlhaUiOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyog6YCJ5oup5LqL5Lu2ICovDQogICAgaGFuZGxlQ2hlY2tlZENpdGllc0NoYW5nZSh2YWx1ZSkgew0KICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzID0gdmFsdWU7DQogICAgfSwNCiAgICAvKiDlhajpgIkgKi8NCiAgICBoYW5kbGVDaGVja0FsbENoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuY2hlY2tlZENpdGllcyA9IHZhbCA/IHRoaXMuQXJ0aWNsZUxpc3QubWFwKChpdGVtKSA9PiBpdGVtLmlkKSA6IFtdOw0KICAgICAgdGhpcy5pc0luZGV0ZXJtaW5hdGUgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8qIOWIt+aWsCAqLw0KICAgIFJlZnJlc2goKSB7DQogICAgICB0aGlzLiRlbWl0KCJSZWZyZXNoIik7DQogICAgfSwNCiAgICAvKuehruWumua3u+WKoOWIsOaKpeWRiiAqLw0KICAgIGFzeW5jIHJlcG9ydFN1Ym1pdCgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgbGV0IGtleVdvcmRMaXN0ID0gW107DQogICAgICBpZiAoIXRoaXMucmVwb3J0SWQpDQogICAgICAgIHJldHVybiB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6KaB5re75Yqg5Yiw55qE5oql5ZGKIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgLyog5Y2V54us5re75YqgICovDQogICAgICBpZiAodGhpcy5zZXBhcmF0ZS5pZCkgew0KICAgICAgICAvLyBsZXQga2V5d29yZCA9IE9iamVjdC5rZXlzKHRoaXMuc2VwYXJhdGUua2V5d29yZENvdW50KQ0KICAgICAgICBrZXlXb3JkTGlzdC5wdXNoKHsNCiAgICAgICAgICByZXBvcnRJZDogdGhpcy5yZXBvcnRJZCwNCiAgICAgICAgICBsaXN0SWQ6IHRoaXMuc2VwYXJhdGUuaWQsDQogICAgICAgICAgbGlzdFNuOiB0aGlzLnNlcGFyYXRlLnNuLA0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8qIOaJuemHj+a3u+WKoCAqLw0KICAgICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzID09ICIiKQ0KICAgICAgICAgIHJldHVybiB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nopoHmt7vliqDnmoTmlbDmja4iLA0KICAgICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICAgIH0pOw0KICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGxldCBhcnRpY2xlID0gdGhpcy5BcnRpY2xlTGlzdC5maWx0ZXIoKHZhbHVlKSA9PiB2YWx1ZS5pZCA9PSBpdGVtKTsNCiAgICAgICAgICBrZXlXb3JkTGlzdC5wdXNoKHsNCiAgICAgICAgICAgIHJlcG9ydElkOiB0aGlzLnJlcG9ydElkLA0KICAgICAgICAgICAgbGlzdElkOiBpdGVtLA0KICAgICAgICAgICAgbGlzdFNuOiBhcnRpY2xlWzBdLnNuLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGxldCByZXMgPSBhd2FpdCBBUEkuQWRkUmVwb3J0KGtleVdvcmRMaXN0KTsNCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLlt7Lmt7vliqDliLDmiqXlkYoiLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgIHRoaXMuJGVtaXQoIlJlZnJlc2giKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLmt7vliqDliLDmiqXlkYrlpLHotKUs6K+36IGU57O7566h55CG5ZGYIiwNCiAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIHRoaXMuc2VwYXJhdGUgPSB7fTsNCiAgICAgIHRoaXMucmVwb3J0SWQgPSAiIjsNCiAgICAgIHRoaXMuY2hlY2tlZENpdGllcyA9IFtdOw0KICAgICAgdGhpcy5jaGVja2VkID0gZmFsc2U7DQogICAgfSwNCiAgICAvKiDljZXni6zmt7vliqDmiqXlkYogKi8NCiAgICBzZXBhcmF0ZUFkZChpdGVtKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5zZXBhcmF0ZSA9IGl0ZW07DQogICAgfSwNCiAgICAvKiDot7PovazmlrDpobXpnaIgKi8NCiAgICBvcGVuTmV3VmlldyhpdGVtLCBpc0xpbmspIHsNCiAgICAgIGlmIChpc0xpbmspIHsNCiAgICAgICAgaWYgKGl0ZW0ub3JpZ2luYWxVcmwpIHsNCiAgICAgICAgICB3aW5kb3cub3BlbihpdGVtLm9yaWdpbmFsVXJsKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLor6Xmlofnq6DmsqHmnInljp/mlofpk77mjqUiIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB3aW5kb3cub3BlbigNCiAgICAgICAgYC9leHByZXNzRGV0YWlscz9pZD0ke2l0ZW0uaWR9JmRvY0lkPSR7aXRlbS5kb2NJZH0mc291cmNlVHlwZT0ke2l0ZW0uc291cmNlVHlwZX1gLA0KICAgICAgICAiX2JsYW5rIg0KICAgICAgKTsNCiAgICAgIC8vIHRoaXMuZHJhd2VySW5mbyA9IGl0ZW0NCiAgICAgIC8vIHRoaXMuZHJhd2VyID0gdHJ1ZQ0KICAgIH0sDQogICAgLyog5paH56ug5omT5qCH562+ICovDQogICAgdGFnSGFuZGxlcihpdGVtKSB7DQogICAgICB0aGlzLnRhZ0RpYWxvZyA9IHRydWU7DQogICAgICB0aGlzLnRhZ0l0ZW0gPSBpdGVtOw0KICAgICAgaWYgKGl0ZW0uaW5kdXN0cnkpIHsNCiAgICAgICAgdGhpcy5mb3JtTGFiZWxBbGlnbi5pbmR1c3RyeSA9IGl0ZW0uaW5kdXN0cnkNCiAgICAgICAgICAuc3BsaXQoIiwiKQ0KICAgICAgICAgIC5tYXAoKGRhdGEpID0+IE51bWJlcihkYXRhKSk7DQogICAgICB9DQogICAgICBpZiAoaXRlbS5kb21haW4pIHsNCiAgICAgICAgdGhpcy5mb3JtTGFiZWxBbGlnbi5kb21haW4gPSBpdGVtLmRvbWFpbg0KICAgICAgICAgIC5zcGxpdCgiLCIpDQogICAgICAgICAgLm1hcCgoZGF0YSkgPT4gTnVtYmVyKGRhdGEpKTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybUxhYmVsQWxpZ24udGFnID0gaXRlbS50YWdzID8gaXRlbS50YWdzLnNwbGl0KCIsIikgOiAiIjsNCiAgICB9LA0KICAgIC8qIOiOt+WPlumihuWfn+WSjOWIhuexuyAqLw0KICAgIGFzeW5jIG9wZW5EaWFsb2coKSB7DQogICAgICBhd2FpdCBBUEkuYXJlYUxpc3QoKS50aGVuKChkYXRhKSA9PiB7DQogICAgICAgIGlmIChkYXRhLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5hcmVhTGlzdCA9IGRhdGEuZGF0YTsNCiAgICAgICAgICB0aGlzLm9wdGlvbnMgPSBkYXRhLmRhdGE7DQogICAgICAgICAgQVBJLmluZHVzdHJ5KCkudGhlbigodmFsdWUpID0+IHsNCiAgICAgICAgICAgIHRoaXMuaW5kdXN0cnkgPSB2YWx1ZS5kYXRhOw0KICAgICAgICAgICAgdGhpcy5vcHRpb25zMSA9IHZhbHVlLmRhdGE7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyog562b6YCJ6aKG5Z+fICovDQogICAgcmVtb3RlRXZlbnQocXVlcnkpIHsNCiAgICAgIHRoaXMub3B0aW9ucyA9IHRoaXMuYXJlYUxpc3QuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmZpZWxkTmFtZSA9PSBxdWVyeSk7DQogICAgfSwNCiAgICAvKiDnrZvpgInooYzkuJogKi8NCiAgICByZW1vdGVJbmR1c3RyeShxdWVyeSkgew0KICAgICAgdGhpcy5vcHRpb25zMSA9IHRoaXMuaW5kdXN0cnkuZmlsdGVyKA0KICAgICAgICAoaXRlbSkgPT4gaXRlbS5pbmR1c3RyeU5hbWUgPT0gcXVlcnkNCiAgICAgICk7DQogICAgfSwNCiAgICBhc3luYyBTdWJtaXRUYWcoKSB7DQogICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICBkb21haW46IFN0cmluZyh0aGlzLmZvcm1MYWJlbEFsaWduLmRvbWFpbiksDQogICAgICAgIGluZHVzdHJ5OiBTdHJpbmcodGhpcy5mb3JtTGFiZWxBbGlnbi5pbmR1c3RyeSksDQogICAgICAgIHRhZ3M6IFN0cmluZyh0aGlzLmZvcm1MYWJlbEFsaWduLnRhZyksDQogICAgICAgIGFydGljbGVJZDogU3RyaW5nKHRoaXMudGFnSXRlbS5pZCksDQogICAgICAgIGRvY0lkOiB0aGlzLnRhZ0l0ZW0uZG9jSWQgPyBTdHJpbmcodGhpcy50YWdJdGVtLmRvY0lkKSA6ICIiLA0KICAgICAgfTsNCiAgICAgIGxldCByZXMgPSBhd2FpdCBBUEkudGFnQWRkKHBhcmFtcyk7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5L+d5a2Y5oiQ5YqfIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLlJlZnJlc2goKTsNCiAgICAgICAgfSwgMTAwMCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuS/neWtmOWksei0pSIsIHR5cGU6ICJlcnJvciIgfSk7DQogICAgICB9DQogICAgICB0aGlzLmNsb3NlVGFnKCk7DQogICAgfSwNCiAgICBjbG9zZVRhZygpIHsNCiAgICAgIHRoaXMuJHJlZnNbInJ1bGVGb3JtIl0ucmVzZXRGaWVsZHMoKTsNCiAgICAgIHRoaXMuZm9ybUxhYmVsQWxpZ24gPSB7DQogICAgICAgIHRhZzogIiIsDQogICAgICAgIGluZHVzdHJ5OiAiIiwNCiAgICAgICAgZG9tYWluOiAiIiwNCiAgICAgIH07DQogICAgICB0aGlzLnRhZ0RpYWxvZyA9IGZhbHNlOw0KICAgIH0sDQogICAgYXN5bmMgaG90SW5jcmVhc2UoaXRlbSkgew0KICAgICAgbGV0IGlzV2hldGhlciA9IEpTT04ucGFyc2UoaXRlbS5pc1doZXRoZXIpOw0KICAgICAgbGV0IHJlcyA9IGF3YWl0IEFQSS50YWdBZGQoew0KICAgICAgICBhcnRpY2xlSWQ6IGl0ZW0uaWQsDQogICAgICAgIGlzV2hldGhlcjogKyFCb29sZWFuKGlzV2hldGhlciksDQogICAgICB9KTsNCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLmk43kvZzmiJDlip8iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgIHRoaXMuUmVmcmVzaCgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLmk43kvZzlpLHotKUiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgb3BlbkRyYXdlcigpIHsNCiAgICAgIGxldCBkb2NJZCA9IHRoaXMuZHJhd2VySW5mby5kb2NJZDsNCiAgICAgIGF3YWl0IEFQSS5BcmVhSW5mbyh0aGlzLmRyYXdlckluZm8uaWQpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmRvY0lkID0gZG9jSWQ7DQogICAgICAgICAgLyog5bCG5a2X56ym5Liy5Lit55qEXG7mm7/mjaLkuLo8YnI+ICovDQogICAgICAgICAgdGhpcy50cmFuc2xhdGlvbkJ0blNob3cgPSAhdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudDsNCiAgICAgICAgICBpZiAodGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCB8fCB0aGlzLmRyYXdlckluZm8uY29udGVudCkgew0KICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCA9ICgNCiAgICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCB8fCB0aGlzLmRyYXdlckluZm8uY29udGVudA0KICAgICAgICAgICAgKS5yZXBsYWNlKC9cXG4vZywgKGEsIGIsIGMpID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuICI8YnI+IjsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCA9ICgNCiAgICAgICAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCB8fCB0aGlzLmRyYXdlckluZm8uY29udGVudA0KICAgICAgICAgICAgKS5yZXBsYWNlKC9cJHtbXn1dK30vZywgIjxicj4iKTsNCiAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgPSAoDQogICAgICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgfHwgdGhpcy5kcmF3ZXJJbmZvLmNvbnRlbnQNCiAgICAgICAgICAgICkucmVwbGFjZSgifHhhMCIsICIiKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyog5omA5bGe6KGM5Lia5aSE55CGICovDQogICAgaW5kdXN0cnlIYW5kbGUoaXRlbSkgew0KICAgICAgbGV0IGlkcyA9IFtdLA0KICAgICAgICBzdHIgPSAiIjsNCiAgICAgIGlmIChpdGVtLmluZHVzdHJ5KSB7DQogICAgICAgIGlkcyA9IGl0ZW0uaW5kdXN0cnkuc3BsaXQoIiwiKTsNCiAgICAgIH0NCiAgICAgIGlkcy5mb3JFYWNoKChkYXRhKSA9PiB7DQogICAgICAgIHRoaXMuaW5kdXN0cnkubWFwKChlbGUpID0+IHsNCiAgICAgICAgICBpZiAoZWxlLmlkID09IGRhdGEpIHsNCiAgICAgICAgICAgIGlmIChzdHIgPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgIHN0ciA9ICIiOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgc3RyICs9IGVsZS5pbmR1c3RyeU5hbWUgKyAiICI7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIHN0cjsNCiAgICB9LA0KICAgIGRvbWFpbkhhbmRsZShpdGVtKSB7DQogICAgICBsZXQgaWRzID0gW10sDQogICAgICAgIHN0ciA9ICIiOw0KICAgICAgaWYgKGl0ZW0uZG9tYWluKSB7DQogICAgICAgIGlkcyA9IGl0ZW0uZG9tYWluLnNwbGl0KCIsIik7DQogICAgICB9DQogICAgICBpZHMuZm9yRWFjaCgoZGF0YSkgPT4gew0KICAgICAgICB0aGlzLmFyZWFMaXN0Lm1hcCgoZWxlKSA9PiB7DQogICAgICAgICAgaWYgKGVsZS5pZCA9PSBkYXRhKSB7DQogICAgICAgICAgICBpZiAoc3RyID09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICBzdHIgPSAiIjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHN0ciArPSBlbGUuZmllbGROYW1lICsgIiAiOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICAgIHJldHVybiBzdHI7DQogICAgfSwNCiAgICAvKiDlv6vnhafnlJ/miJAgKi8NCiAgICByZXN1bHRFdmVudChpdGVtKSB7DQogICAgICBpZiAoaXRlbSA9PSAiQmF0Y2hHZW5lcmF0aW9uIiAmJiB0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjpgInmi6nmlofnq6AiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgbGV0IGlkcyA9IG51bGw7DQogICAgICBsZXQgemh1YW5ndGFpID0gIueUn+aIkCI7DQogICAgICBsZXQgdXJsID0gIiI7DQogICAgICBpZiAoaXRlbSA9PSAiZHJhd2VyIikgew0KICAgICAgICBpZHMgPSBbdGhpcy5kcmF3ZXJJbmZvLmlkXTsNCiAgICAgICAgaWYgKHRoaXMuZHJhd2VySW5mby5zbmFwc2hvdFVybCkgemh1YW5ndGFpID0gIuafpeeciyI7DQogICAgICAgIHVybCA9IHRoaXMuZHJhd2VySW5mby5zbmFwc2hvdFVybDsNCiAgICAgIH0gZWxzZSBpZiAoaXRlbSA9PSAiQmF0Y2hHZW5lcmF0aW9uIikgew0KICAgICAgICBpZHMgPSB0aGlzLmNoZWNrZWRDaXRpZXM7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBpZHMgPSBbaXRlbS5pZF07DQogICAgICAgIGlmIChpdGVtLnNuYXBzaG90VXJsKSB6aHVhbmd0YWkgPSAi5p+l55yLIjsNCiAgICAgICAgdXJsID0gaXRlbS5zbmFwc2hvdFVybDsNCiAgICAgIH0NCiAgICAgIGlmICh6aHVhbmd0YWkgPT0gIueUn+aIkCIpIHsNCiAgICAgICAgaWYgKHRoaXMuZmxhZyA9PSAiTW9uaXRvclVzZSIpIHsNCiAgICAgICAgICBBUEkuZG93bkxvYWRFeHBvcnRLZShpZHMpDQogICAgICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbXNnYm94KHsNCiAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5o+Q56S6IiwNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLlv6vnhafmraPlnKjnlJ/miJDkuK3vvIzor7fnqI3lkI7mn6XnnIsiLA0KICAgICAgICAgICAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi5YWz6ZetIiwNCiAgICAgICAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgICAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogZmFsc2UsDQogICAgICAgICAgICAgICAgICBiZWZvcmVDbG9zZTogKGFjdGlvbiwgaW5zdGFuY2UsIGRvbmUpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgZG9uZSgpOw0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLnlLPor7flpLHotKXvvIzor7fogZTns7vnrqHnkIblkZjvvIznoa7orqTph4fpm4blmajmmK/lkKbmraPluLgiLA0KICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7fSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgQVBJLmRvd25Mb2FkRXhwb3J0Wmh1YW4oaWRzKQ0KICAgICAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICAgIHRoaXMuJG1zZ2JveCh7DQogICAgICAgICAgICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5b+r54Wn5q2j5Zyo55Sf5oiQ5Lit77yM6K+356iN5ZCO5p+l55yLIiwNCiAgICAgICAgICAgICAgICAgIHNob3dDYW5jZWxCdXR0b246IHRydWUsDQogICAgICAgICAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuWFs+mXrSIsDQogICAgICAgICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICAgICAgICAgIHNob3dDYW5jZWxCdXR0b246IGZhbHNlLA0KICAgICAgICAgICAgICAgICAgYmVmb3JlQ2xvc2U6IChhY3Rpb24sIGluc3RhbmNlLCBkb25lKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGRvbmUoKTsNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi55Sz6K+35aSx6LSl77yM6K+36IGU57O7566h55CG5ZGY77yM56Gu6K6k6YeH6ZuG5Zmo5piv5ZCm5q2j5bi4IiwNCiAgICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICAuY2F0Y2goKGVycikgPT4ge30pOw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICB1cmwgPSB1cmwucmVwbGFjZShuZXcgUmVnRXhwKCIvaG9tZS9sb2NhbC9kcHgvc2VydmVyLWFwaS8iLCAiZyIpLCAiLyIpOw0KICAgICAgICB1cmwgPSB1cmwucmVwbGFjZShuZXcgUmVnRXhwKCIvaG9tZS9sb2NhbC9kcHgvIiwgImciKSwgIi8iKTsNCiAgICAgICAgd2luZG93Lm9wZW4od2luZG93LmxvY2F0aW9uLm9yaWdpbiArIHVybCwgIl9ibGFuayIpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyog6ZmE5Lu25LiL6L29ICovDQogICAgYXN5bmMgZG9jdW1lbnREb3dubG9hZChpdGVtKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgaWYgKGl0ZW0uZmlsZVVybCkgew0KICAgICAgICBjb25zdCB1cmxzID0gaXRlbS5maWxlVXJsLnNwbGl0KCIsIik7DQogICAgICAgIGZvciAoY29uc3QgW2luZGV4LCB1cmxdIG9mIHVybHMuZW50cmllcygpKSB7DQogICAgICAgICAgaWYgKHVybC5pbmRleE9mKCJodHRwczovLyIpID09PSAtMSkgew0KICAgICAgICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7DQogICAgICAgICAgICAgIGF3YWl0IHRoaXMuZG93bkxvYWRGdW4odXJsLCBpbmRleCwgaXRlbS5jblRpdGxlIHx8IGl0ZW0udGl0bGUpOw0KICAgICAgICAgICAgfSwgaW5kZXggKiA1MDApOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLpmYTku7bov5jmsqHlkIzmraXliLDlvZPliY3ns7vnu5/vvIzmmoLml7bml6Dms5XkuIvovb0iKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICBhc3luYyBkb3duTG9hZEZ1bih1cmwsIGluZGV4LCB0aXRsZSkgew0KICAgICAgbGV0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7DQogICAgICBmb3JtRGF0YS5hcHBlbmQoImZpbGVVcmwiLCB1cmwpOw0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IEFQSS5kb3dubG9hZEZpbGUoZm9ybURhdGEpOw0KICAgICAgICBjb25zdCBpc0Jsb2IgPSBibG9iVmFsaWRhdGUocmVzcG9uc2UpOw0KDQogICAgICAgIGlmIChpc0Jsb2IpIHsNCiAgICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW3Jlc3BvbnNlXSk7DQogICAgICAgICAgbGV0IGxpc3QgPSB1cmwuc3BsaXQoIi8iKTsNCiAgICAgICAgICBsZXQgZmlsZU5hbWUgPSBsaXN0W2xpc3QubGVuZ3RoIC0gMV07DQogICAgICAgICAgc2F2ZUFzKGJsb2IsIGZpbGVOYW1lKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zdCByZXNUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpOw0KICAgICAgICAgIGNvbnN0IHJzcE9iaiA9IEpTT04ucGFyc2UocmVzVGV4dCk7DQogICAgICAgICAgY29uc3QgZXJyTXNnID0NCiAgICAgICAgICAgIGVycm9yQ29kZVtyc3BPYmouY29kZV0gfHwgcnNwT2JqLm1zZyB8fCBlcnJvckNvZGVbImRlZmF1bHQiXTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGVyck1zZyk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycikgew0KICAgICAgICAvLyB0aGlzLiRtZXNzYWdlLmVycm9yKGBFcnJvciBkb3dubG9hZGluZyBmaWxlOiAke2Vycn1gKTsNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIC8vIOehruS/nSBsb2FkaW5nIOWcqOavj+asoeS4i+i9veWQjumDveiuvue9ruS4uiBmYWxzZQ0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgLy8g5LmL5YmN55qE6ZmE5Lu25LiL6L29DQogICAgICAvLyBpZiAoaXRlbS5hbm5leFVybCkgew0KICAgICAgLy8gICAvKiDmnInmlofku7blnLDlnYAg55u05o6l5LiL6L29ICovDQogICAgICAvLyAgIGxldCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQogICAgICAvLyAgIGZvcm1EYXRhLmFwcGVuZCgnaWQnLCBpdGVtLmlkKQ0KICAgICAgLy8gICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICAvLyAgIEFQSS5kb2N1bWVudERvd25sb2FkS2UoZm9ybURhdGEpLnRoZW4ocmVzID0+IHsNCiAgICAgIC8vICAgICBsZXQgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKQ0KICAgICAgLy8gICAgIGEuaHJlZiA9IFVSTC5jcmVhdGVPYmplY3RVUkwocmVzLmRhdGEpDQogICAgICAvLyAgICAgYS5kb3dubG9hZCA9IHJlcy5oZWFkZXJzWydjb250ZW50LWRpc3Bvc2l0aW9uJ10uc3BsaXQoJ2ZpbGVuYW1lPScpWzFdDQogICAgICAvLyAgICAgYS5jbGljaygpDQogICAgICAvLyAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIC8vICAgfSkNCiAgICAgIC8vIH0gZWxzZSB7DQogICAgICAvLyAgIC8qIOayoeacieaWh+S7tuagvOW8jyDnlLPor7fkuIvovb0gKi8NCiAgICAgIC8vICAgQVBJLmRvY3VtZW50RG93bmxvYWQoaWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgLy8gICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgLy8gICAgICAgdGhpcy4kbXNnYm94KHsNCiAgICAgIC8vICAgICAgICAgdGl0bGU6ICfmj5DnpLonLA0KICAgICAgLy8gICAgICAgICBtZXNzYWdlOiAn6ZmE5Lu25q2j5Zyo5ZCM5q2l5Lit77yM6K+356iN5ZCO5LiL6L29JywNCiAgICAgIC8vICAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogdHJ1ZSwNCiAgICAgIC8vICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICflhbPpl60nLA0KICAgICAgLy8gICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywNCiAgICAgIC8vICAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogZmFsc2UsDQogICAgICAvLyAgICAgICAgIGJlZm9yZUNsb3NlOiAoYWN0aW9uLCBpbnN0YW5jZSwgZG9uZSkgPT4gew0KICAgICAgLy8gICAgICAgICAgIGRvbmUoKQ0KICAgICAgLy8gICAgICAgICB9DQogICAgICAvLyAgICAgICB9KQ0KICAgICAgLy8gICAgIH0gZWxzZSB7DQogICAgICAvLyAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogJ+mZhOS7tuS4i+i9veWksei0pScsIHR5cGU6ICdlcnJvcicgfSkNCiAgICAgIC8vICAgICB9DQogICAgICAvLyAgIH0pLmNhdGNoKGVyciA9PiB7IH0pDQogICAgICAvLyB9DQogICAgfSwNCiAgICAvLyDnv7vor5HmoIfpopgNCiAgICB0cmFuc2xhdGVUaXRsZShyb3cpIHsNCiAgICAgIGNvbnN0IGxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsNCiAgICAgICAgbG9jazogdHJ1ZSwNCiAgICAgICAgdGV4dDogIkxvYWRpbmciLA0KICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwNCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIsDQogICAgICB9KTsNCiAgICAgIEFQSS50cmFuc2xhdGlvblRpdGxlKHsNCiAgICAgICAgb3JpZ2luYWxUZXh0OiByb3cudGl0bGUsDQogICAgICAgIGRvY0lkOiByb3cuZG9jSWQsDQogICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgIHRyYW5zbGF0aW9uRmllbGQ6ICJ0aXRsZSIsDQogICAgICAgIHRyYW5zbGF0aW9uVHlwZTogMSwNCiAgICAgIH0pDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLmRyYXdlckluZm8uY25UaXRsZSA9IHJlcy5kYXRhOw0KICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3RbDQogICAgICAgICAgICB0aGlzLkFydGljbGVMaXN0LmZpbmRJbmRleCgodmFsdWUpID0+IHZhbHVlLmlkID09IHJvdy5pZCkNCiAgICAgICAgICBdLmNuVGl0bGUgPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLkFydGljbGVMaXN0Ww0KICAgICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdC5maW5kSW5kZXgoKHZhbHVlKSA9PiB2YWx1ZS5pZCA9PSByb3cuaWQpDQogICAgICAgICAgXS5pc1RyYW5zbGF0ZWQgPSAxOw0KICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICBsb2FkaW5nLmNsb3NlKCk7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g57+76K+R5paH56ugDQogICAgdHJhbnNsYXRlRXZlbnQocm93KSB7DQogICAgICBjb25zdCBsb2FkaW5nID0gdGhpcy4kbG9hZGluZyh7DQogICAgICAgIGxvY2s6IHRydWUsDQogICAgICAgIHRleHQ6ICJMb2FkaW5nIiwNCiAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsDQogICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLA0KICAgICAgfSk7DQogICAgICBBUEkudHJhbnNsYXRpb25UaXRsZSh7DQogICAgICAgIG9yaWdpbmFsVGV4dDogcm93LmNvbnRlbnQsDQogICAgICAgIGRvY0lkOiByb3cuZG9jSWQsDQogICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgIHRyYW5zbGF0aW9uRmllbGQ6ICJjb250ZW50IiwNCiAgICAgICAgdHJhbnNsYXRpb25UeXBlOiAxLA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLkFydGljbGVMaXN0Ww0KICAgICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdC5maW5kSW5kZXgoKHZhbHVlKSA9PiB2YWx1ZS5pZCA9PSByb3cuaWQpDQogICAgICAgICAgXS5jbkNvbnRlbnQgPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLkFydGljbGVMaXN0Ww0KICAgICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdC5maW5kSW5kZXgoKHZhbHVlKSA9PiB2YWx1ZS5pZCA9PSByb3cuaWQpDQogICAgICAgICAgXS5pc1RyYW5zbGF0ZWQgPSAxOw0KICAgICAgICAgIHRoaXMudHJhbnNsYXRpb25CdG5TaG93ID0gZmFsc2U7DQogICAgICAgICAgbG9hZGluZy5jbG9zZSgpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycikgPT4gew0KICAgICAgICAgIGxvYWRpbmcuY2xvc2UoKTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgQVBJLkFyZWFJbmZvKHJvdy5pZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5mb3JtLnNvdXJjZVR5cGUgPSBOdW1iZXIodGhpcy5mb3JtLnNvdXJjZVR5cGUpOw0KICAgICAgICB0aGlzLmZvcm0uZG9jSWQgPSByb3cuZG9jSWQ7DQogICAgICAgIC8vIHRoaXMuZmlsZVVybExpc3QgPSB0aGlzLmZvcm0uZmlsZVVybCA/IHRoaXMuZm9ybS5maWxlVXJsLnNwbGl0KCIsIikubWFwKGl0ZW0gPT4gew0KICAgICAgICAvLyAgIHJldHVybiB7DQogICAgICAgIC8vICAgICBuYW1lOiBpdGVtLA0KICAgICAgICAvLyAgICAgdXJsOiBpdGVtDQogICAgICAgIC8vICAgfQ0KICAgICAgICAvLyB9KSA6IFtdDQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6K+l5p2h5paH56ug77yfIicpDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICByZXR1cm4gQVBJLm1vbml0b3JpbmdFc1JlbW92ZSh7IGlkOiByb3cuaWQsIGRvY0lkOiByb3cuZG9jSWQgfSk7DQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLlJlZnJlc2goKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBsZXQgcXVlcnlGb3JtID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmZvcm0pKTsNCiAgICAgICAgICAvLyBsZXQgY292ZXIgPSBTdHJpbmcodGhpcy5maWxlTGlzdC5tYXAoaXRlbSA9PiBpdGVtLnBhdGgpKQ0KICAgICAgICAgIC8vIHF1ZXJ5Rm9ybS5jb3ZlciA9IGNvdmVyDQogICAgICAgICAgYXJ0aWNsZUxpc3RFZGl0KHF1ZXJ5Rm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLlJlZnJlc2goKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiDoh6rlrprkuYnkuIrkvKAgKi8NCiAgICBhc3luYyByZXF1ZXN0TG9hZChmaWxlKSB7DQogICAgICBsZXQgZGF0YSA9IG5ldyBGb3JtRGF0YSgpOw0KICAgICAgZGF0YS5hcHBlbmQoImNvdmVyIiwgZmlsZS5maWxlKTsNCiAgICAgIGF3YWl0IHVwbG9hZENvdmVyKGRhdGEpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZmlsZUxpc3QubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgICBpZiAoaXRlbS51aWQgPT0gZmlsZS5maWxlLnVpZCkgew0KICAgICAgICAgICAgICBpdGVtLnBhdGggPSByZXNwb25zZS5pbWdVcmw7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLkuIrkvKDmiJDlip8iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLkuIrkvKDlpLHotKUs6K+356iN5YCZ6YeN6K+VIiwgdHlwZTogImVycm9yIiB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiDmlofku7botoXlh7rpmZDliLYgKi8NCiAgICBleGNlZWQoKSB7DQogICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgbWVzc2FnZTogIuaWh+S7tuS4iuS8oOi2heWHuumZkOWItizmnIDlpJrlj6/ku6XkuIrkvKDkuInkuKrmlofku7YiLA0KICAgICAgICB0eXBlOiAiaW5mbyIsDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qIOenu+mZpOaWh+S7tiAqLw0KICAgIGhhbmRsZVJlbW92ZShmaWxlKSB7DQogICAgICB0aGlzLmZpbGVMaXN0ID0gdGhpcy5maWxlTGlzdC5maWx0ZXIoKGl0ZW0pID0+IGl0ZW0gIT09IGZpbGUpOw0KICAgIH0sDQogICAgLy8g5paH5Lu25pu05pS5DQogICAgaGFuZGxlQ2hhbmdlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmZpbGVMaXN0ID0gZmlsZUxpc3Q7DQogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5zdWJtaXQoKTsNCiAgICB9LA0KICAgIC8vIOS4iuS8oOmZhOS7tuagoemqjA0KICAgIGJlZm9yZVVwbG9hZFVybChmaWxlKSB7DQogICAgICAvLyDliKTmlq3mlofku7bmmK/lkKbkuLpleGNlbA0KICAgICAgbGV0IGZpbGVOYW1lID0gZmlsZS5uYW1lDQogICAgICAgICAgLnN1YnN0cmluZyhmaWxlLm5hbWUubGFzdEluZGV4T2YoIi4iKSArIDEpDQogICAgICAgICAgLnRvTG93ZXJDYXNlKCksDQogICAgICAgIGNvbmRpdGlvbiA9DQogICAgICAgICAgZmlsZU5hbWUgPT0gInBkZiIgfHwNCiAgICAgICAgICBmaWxlTmFtZSA9PSAiZG9jIiB8fA0KICAgICAgICAgIGZpbGVOYW1lID09ICJ4bHMiIHx8DQogICAgICAgICAgZmlsZU5hbWUgPT0gInBwdCIgfHwNCiAgICAgICAgICBmaWxlTmFtZSA9PSAieGxzeCIgfHwNCiAgICAgICAgICBmaWxlTmFtZSA9PSAicHB0eCIgfHwNCiAgICAgICAgICBmaWxlTmFtZSA9PSAiZG9jeCI7DQogICAgICBsZXQgZmlsZVNpemUgPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwOw0KICAgICAgaWYgKCFjb25kaXRpb24pIHsNCiAgICAgICAgdGhpcy4kbm90aWZ5KHsNCiAgICAgICAgICB0aXRsZTogIuitpuWRiiIsDQogICAgICAgICAgbWVzc2FnZTogIuS4iuS8oOaWh+S7tuW/hemhu+aYr3BkZixkb2MseGxzLHBwdCx4bHN4LHBwdHgsZG9jeOagvOW8jyIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIC8qIOaWh+S7tuWkp+Wwj+mZkOWItiAqLw0KICAgICAgaWYgKCFmaWxlU2l6ZSkgew0KICAgICAgICB0aGlzLiRub3RpZnkoew0KICAgICAgICAgIHRpdGxlOiAi6K2m5ZGKIiwNCiAgICAgICAgICBtZXNzYWdlOiAi5LiK5Lyg5paH5Lu255qE5aSn5bCP5LiN6IO96LaF6L+HIDEwTUIhIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGNvbmRpdGlvbiAmJiBmaWxlU2l6ZTsNCiAgICB9LA0KICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKnw0KICAgIHVwbG9hZFVybFN1Y2Nlc3MocmVzLCBmaWxlKSB7DQogICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuS4iuS8oOaIkOWKnyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICB9LA0KICAgIC8vIOaWh+S7tuS4iuS8oOi2heWHuumZkOWItg0KICAgIHVwbG9hZFVybEV4Y2VlZCgpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICBtZXNzYWdlOiAi5paH5Lu25LiK5Lyg6LaF5Ye66ZmQ5Yi2LOacgOWkmuWPr+S7peS4iuS8oDHkuKrmlofku7YiLA0KICAgICAgICB0eXBlOiAiaW5mbyIsDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOaWh+S7tuS4iuS8oOaWueazlQ0KICAgIHVwbG9hZFVybFJlcXVlc3QoZmlsZSkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5vcmlnaW5hbFVybCAhPSBudWxsICYmIHRoaXMuZm9ybS5vcmlnaW5hbFVybCAhPSAiIikgew0KICAgICAgICBpZiAoDQogICAgICAgICAgdGhpcy5mb3JtLm9yaWdpbmFsVXJsLm1hdGNoKA0KICAgICAgICAgICAgLyhodHRwfGh0dHBzKTpcL1wvW1x3XC1fXSsoXC5bXHdcLV9dKykrKFtcd1wtXC4sQD9ePSUmOi9+XCsjXSpbXHdcLVxAP149JSYvflwrI10pPy8NCiAgICAgICAgICApDQogICAgICAgICkgew0KICAgICAgICAgIGxldCBkYXRhID0gbmV3IEZvcm1EYXRhKCk7DQogICAgICAgICAgZGF0YS5hcHBlbmQoImZpbGUiLCBmaWxlLmZpbGUpOw0KICAgICAgICAgIGRhdGEuYXBwZW5kKCJvcmlnaW5hbFVybCIsIHRoaXMuZm9ybS5vcmlnaW5hbFVybCk7DQoNCiAgICAgICAgICBBUEkudXBsb2FkRmlsZShkYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5LiK5Lyg5oiQ5YqfIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICAgICAgICB0aGlzLmZvcm0uZmlsZVVybCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuS4iuS8oOWksei0pSzor7fnqI3lgJnph43or5UiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgICAgICAgICB0aGlzLmZvcm0uZmlsZVVybCA9ICIiOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi6K+35aGr5YaZ5q2j56Gu55qE5Y6f5paH6ZO+5o6lIiwgdHlwZTogIndhcm5pbmciIH0pOw0KICAgICAgICAgIHRoaXMuZmlsZVVybExpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLor7floavlhpnljp/mlofpk77mjqUiLCB0eXBlOiAid2FybmluZyIgfSk7DQogICAgICAgIHRoaXMuZmlsZVVybExpc3QgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWIoOmZpOmZhOS7tg0KICAgIHVwbG9hZFVybFJlbW92ZSgpIHsNCiAgICAgIEFQSS5yZW1vdmVGaWxlKHsgZmlsZVBhdGg6IHRoaXMuZm9ybS5maWxlVXJsIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICAgIHRoaXMuZmlsZVVybExpc3QgPSBbXTsNCiAgICAgICAgICB0aGlzLmZvcm0uZmlsZVVybCA9ICIiOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlLOivt+eojeWAmemHjeivlSIsIHR5cGU6ICJlcnJvciIgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIGFydGljbGVTbjogbnVsbCwNCiAgICAgICAgdGl0bGU6IG51bGwsDQogICAgICAgIGNuVGl0bGU6IG51bGwsDQogICAgICAgIHNvdXJjZVR5cGU6IG51bGwsDQogICAgICAgIHNvdXJjZU5hbWU6IG51bGwsDQogICAgICAgIHNvdXJjZVNuOiBudWxsLA0KICAgICAgICBvcmlnaW5hbFVybDogbnVsbCwNCiAgICAgICAgc2hvcnRVcmw6IG51bGwsDQogICAgICAgIGF1dGhvcjogbnVsbCwNCiAgICAgICAgZGVzY3JpcHRpb246IG51bGwsDQogICAgICAgIHN1bW1hcnk6IG51bGwsDQogICAgICAgIGNuU3VtbWFyeTogbnVsbCwNCiAgICAgICAgY292ZXI6IG51bGwsDQogICAgICAgIHB1Ymxpc2hUeXBlOiBudWxsLA0KICAgICAgICBwdWJsaXNoQ29kZTogbnVsbCwNCiAgICAgICAgcHVibGlzaEFyZWE6IG51bGwsDQogICAgICAgIHB1Ymxpc2hUaW1lOiBudWxsLA0KICAgICAgICBudW1iZXJMaWtlczogbnVsbCwNCiAgICAgICAgbnVtYmVyUmVhZHM6IG51bGwsDQogICAgICAgIG51bWJlckNvbGxlY3RzOiBudWxsLA0KICAgICAgICBudW1iZXJTaGFyZXM6IG51bGwsDQogICAgICAgIG51bWJlckNvbW1lbnRzOiBudWxsLA0KICAgICAgICBlbW90aW9uOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICB1c2VySWQ6IG51bGwsDQogICAgICAgIGRlcHRJZDogbnVsbCwNCiAgICAgICAgY29udGVudDogbnVsbCwNCiAgICAgICAgY25Db250ZW50OiBudWxsLA0KICAgICAgICBmaWxlVXJsOiBudWxsLA0KICAgICAgICBpbmR1c3RyeTogbnVsbCwNCiAgICAgICAgZG9tYWluOiBudWxsLA0KICAgICAgICB0bXBVcmw6IG51bGwsDQogICAgICAgIGlzRmluaXNoOiBudWxsLA0KICAgICAgICBncm91cElkOiBudWxsLA0KICAgICAgICBhcHBJZDogbnVsbCwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLy8g5om56YeP5a+85YWl55u45YWz5pa55rOVDQogICAgLy8g5omT5byA5om56YeP5a+85YWl5by55qGGDQogICAgb3BlbkJhdGNoSW1wb3J0RGlhbG9nKCkgew0KICAgICAgdGhpcy5iYXRjaEltcG9ydFZpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5iYXRjaEltcG9ydEZpbGVzID0gW107DQogICAgfSwNCiAgICAvLyDmlofku7bpgInmi6nlpITnkIYNCiAgICBoYW5kbGVGaWxlU2VsZWN0KGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICAvLyDlsIbmlrDpgInmi6nnmoTmlofku7bmt7vliqDliLDliJfooajkuK0NCiAgICAgIGNvbnN0IG5ld0ZpbGVzID0gZmlsZUxpc3QubWFwKChpdGVtKSA9PiAoew0KICAgICAgICBmaWxlTmFtZTogaXRlbS5uYW1lLA0KICAgICAgICBmaWxlOiBpdGVtLnJhdywNCiAgICAgICAgc291cmNlTmFtZTogIiIsDQogICAgICB9KSk7DQogICAgICB0aGlzLmJhdGNoSW1wb3J0RmlsZXMgPSBuZXdGaWxlczsNCiAgICB9LA0KICAgIC8vIOWIoOmZpOaWh+S7tg0KICAgIHJlbW92ZUZpbGUoaW5kZXgpIHsNCiAgICAgIHRoaXMuYmF0Y2hJbXBvcnRGaWxlcy5zcGxpY2UoaW5kZXgsIDEpOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5om56YeP5a+85YWlDQogICAgY2FuY2VsQmF0Y2hJbXBvcnQoKSB7DQogICAgICB0aGlzLmJhdGNoSW1wb3J0VmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5iYXRjaEltcG9ydEZpbGVzID0gW107DQogICAgICAvLyDmuIXnqbrmlofku7bpgInmi6nlmagNCiAgICAgIHRoaXMuJHJlZnMuYmF0Y2hVcGxvYWQuY2xlYXJGaWxlcygpOw0KICAgIH0sDQogICAgLy8g56Gu6K6k5om56YeP5a+85YWlDQogICAgYXN5bmMgY29uZmlybUJhdGNoSW1wb3J0KCkgew0KICAgICAgLy8g6aqM6K+B5pWw5o2u5rqQ5ZCN56ew5piv5ZCm6YO95bey5aGr5YaZDQogICAgICBjb25zdCBlbXB0eVNvdXJjZU5hbWVzID0gdGhpcy5iYXRjaEltcG9ydEZpbGVzLmZpbHRlcigNCiAgICAgICAgKGl0ZW0pID0+ICFpdGVtLnNvdXJjZU5hbWUudHJpbSgpDQogICAgICApOw0KICAgICAgaWYgKGVtcHR5U291cmNlTmFtZXMubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+35Li65omA5pyJ5paH5Lu25aGr5YaZ5pWw5o2u5rqQ5ZCN56ewIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQoNCiAgICAgICAgLy8g5Yib5bu6Rm9ybURhdGHlr7nosaENCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsNCg0KICAgICAgICAvLyDmt7vliqDmlofku7bliLBGb3JtRGF0YQ0KICAgICAgICB0aGlzLmJhdGNoSW1wb3J0RmlsZXMuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZCgiZmlsZXMiLCBpdGVtLmZpbGUpOw0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDojrflj5bmlbDmja7mupDlkI3np7DmlbDnu4QNCiAgICAgICAgY29uc3Qgc291cmNlTmFtZXMgPSB0aGlzLmJhdGNoSW1wb3J0RmlsZXMNCiAgICAgICAgICAubWFwKChpdGVtKSA9PiBpdGVtLnNvdXJjZU5hbWUpDQogICAgICAgICAgLmpvaW4oIiwiKTsNCg0KICAgICAgICBmb3JtRGF0YS5hcHBlbmQoInNvdXJjZU5hbWVzIiwgc291cmNlTmFtZXMpOw0KDQogICAgICAgIC8vIOiwg+eUqOaJuemHj+WvvOWFpUFQSe+8jOS8oOmAkkZvcm1EYXRh5ZKMc291cmNlTmFtZXPlj4LmlbANCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBUEkuYmF0Y2hJbXBvcnRSZXBvcnRzKGZvcm1EYXRhKTsNCg0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICBtZXNzYWdlOiAi5om56YeP5a+85YWl5oiQ5YqfIiwNCiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmJhdGNoSW1wb3J0VmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuYmF0Y2hJbXBvcnRGaWxlcyA9IFtdOw0KICAgICAgICAgIHRoaXMuJHJlZnMuYmF0Y2hVcGxvYWQuY2xlYXJGaWxlcygpOw0KICAgICAgICAgIC8vIOWIt+aWsOWIl+ihqA0KICAgICAgICAgIHRoaXMuUmVmcmVzaCgpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UubXNnIHx8ICLmibnph4/lr7zlhaXlpLHotKUiLA0KICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5om56YeP5a+85YWl6ZSZ6K+vOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuaJuemHj+WvvOWFpeWksei0pe+8jOivt+eojeWQjumHjeivlSIsDQogICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgfSk7DQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHJlcG9ydEFpQ2hhdCgpIHsNCiAgICAgIHRoaXMuc2hvd0RlZXBzZWVrRGlhbG9nID0gdHJ1ZTsNCiAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi6K+36YCJ5oup6KaB6Kej6K+755qE5paH56ugIiwgdHlwZTogIndhcm5pbmciIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID4gMSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuivt+WPqumAieaLqeS4gOevh+aWh+eroOi/m+ihjOino+ivuyIsIHR5cGU6ICJ3YXJuaW5nIiB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDojrflj5bpgInkuK3nmoTmlofnq6ANCiAgICAgIGNvbnN0IHNlbGVjdGVkQXJ0aWNsZUlkID0gdGhpcy5jaGVja2VkQ2l0aWVzWzBdOw0KICAgICAgY29uc3Qgc2VsZWN0ZWRBcnRpY2xlID0gdGhpcy5BcnRpY2xlTGlzdC5maW5kKA0KICAgICAgICAoaXRlbSkgPT4gaXRlbS5pZCA9PT0gc2VsZWN0ZWRBcnRpY2xlSWQNCiAgICAgICk7DQoNCiAgICAgIGlmIChzZWxlY3RlZEFydGljbGUpIHsNCiAgICAgICAgdGhpcy5jdXJyZW50QXJ0aWNsZSA9IHNlbGVjdGVkQXJ0aWNsZTsNCiAgICAgICAgdGhpcy5zaG93RGVlcHNlZWtEaWFsb2cgPSB0cnVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLmnKrmib7liLDpgInkuK3nmoTmlofnq6AiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8gYWnnm7jlhbMNCiAgICAvLyBkaWZ5DQogICAgYXN5bmMgZGlmeUFpQ2hhdCgpIHsNCiAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+35YWI6YCJ5oup6KaB6Kej6K+755qE5paH56ugIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmnInmraPlnKjov5vooYznmoTor7fmsYLvvIzkuK3mlq3lroMNCiAgICAgIGlmICh0aGlzLmlzUmVxdWVzdGluZykgew0KICAgICAgICB0aGlzLmlzQWJvcnRlZCA9IHRydWU7DQogICAgICAgIGlmICh0aGlzLmN1cnJlbnRSZWFkZXIpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgYXdhaXQgdGhpcy5jdXJyZW50UmVhZGVyLmNhbmNlbCgpOw0KICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCLkuK3mlq3kuYvliY3nmoTor7fmsYLlpLHotKUiLCBlKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gdHJ1ZTsNCiAgICAgIHRoaXMuaXNBYm9ydGVkID0gZmFsc2U7DQogICAgICB0aGlzLmFpRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmNoYXRNZXNzYWdlcyA9IFtdOw0KICAgICAgdGhpcy5pc1RoaW5raW5nID0gdHJ1ZTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6I635Y+W6YCJ5Lit55qE5paH56ugDQogICAgICAgIGNvbnN0IHNlbGVjdGVkQXJ0aWNsZXMgPSB0aGlzLkFydGljbGVMaXN0LmZpbHRlcigoYXJ0aWNsZSkgPT4NCiAgICAgICAgICB0aGlzLmNoZWNrZWRDaXRpZXMuaW5jbHVkZXMoYXJ0aWNsZS5pZCkNCiAgICAgICAgKTsNCiAgICAgICAgY29uc3QgdGl0bGVzID0gc2VsZWN0ZWRBcnRpY2xlcw0KICAgICAgICAgIC5tYXAoKGFydGljbGUpID0+IGDjgIoke2FydGljbGUuY25UaXRsZSB8fCBhcnRpY2xlLnRpdGxlfeOAi2ApDQogICAgICAgICAgLmpvaW4oIlxuIik7DQoNCiAgICAgICAgLy8g6I635Y+W5paH56ug5YaF5a65DQogICAgICAgIGNvbnN0IGFydGljbGVzUmVzcG9uc2UgPSBhd2FpdCBnZXRMaXN0QnlJZHMoDQogICAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzLmpvaW4oIiwiKQ0KICAgICAgICApOw0KICAgICAgICBpZiAoIWFydGljbGVzUmVzcG9uc2UuZGF0YT8ubGVuZ3RoKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCLojrflj5bmlofnq6DlhoXlrrnlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOagvOW8j+WMluaWh+eroOWGheWuuQ0KICAgICAgICBjb25zdCBhcnRpY2xlc0NvbnRlbnQgPSBhcnRpY2xlc1Jlc3BvbnNlLmRhdGENCiAgICAgICAgICAubWFwKChhcnRpY2xlLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgY29uc3QgdGl0bGUgPQ0KICAgICAgICAgICAgICBzZWxlY3RlZEFydGljbGVzW2luZGV4XT8uY25UaXRsZSB8fA0KICAgICAgICAgICAgICBzZWxlY3RlZEFydGljbGVzW2luZGV4XT8udGl0bGUgfHwNCiAgICAgICAgICAgICAgIiI7DQogICAgICAgICAgICBjb25zdCBjb250ZW50ID0gYXJ0aWNsZS5jb250ZW50IHx8ICIiOw0KICAgICAgICAgICAgcmV0dXJuIGDjgJDnrKwgJHtpbmRleCArIDF9IOevh+aWh+eroOOAkeOAiiR7dGl0bGV944CLXG5cbiR7Y29udGVudH1gOw0KICAgICAgICAgIH0pDQogICAgICAgICAgLmpvaW4oIlxuXG4tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiIpOw0KDQogICAgICAgIC8vIOa3u+WKoOeUqOaIt+a2iOaBrw0KICAgICAgICB0aGlzLmNoYXRNZXNzYWdlcy5wdXNoKHsNCiAgICAgICAgICByb2xlOiAidXNlciIsDQogICAgICAgICAgY29udGVudDogYOW4ruaIkea3seW6puino+ivu+S7peS4iyR7dGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aH3nr4fmlofnq6DvvJpcbiR7dGl0bGVzfWAsDQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOWIm+W7ukFJ5raI5oGvDQogICAgICAgIGNvbnN0IGFpTWVzc2FnZSA9IHsNCiAgICAgICAgICByb2xlOiAiYXNzaXN0YW50IiwNCiAgICAgICAgICBjb250ZW50OiAiIiwNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaChhaU1lc3NhZ2UpOw0KDQogICAgICAgIC8vIOaehOW7uuaPkOekuuivjQ0KICAgICAgICBjb25zdCBwcm9tcHQgPQ0KICAgICAgICAgIHRoaXMuYXJ0aWNsZUFpUHJvbXB0DQogICAgICAgICAgICAucmVwbGFjZSgiYXJ0aWNsZUxlbmd0aCIsIHRoaXMuY2hlY2tlZENpdGllcy5sZW5ndGgpDQogICAgICAgICAgICAucmVwbGFjZSgvXCZndDsvZywgIj4iKSArDQogICAgICAgICAgYCoq5Lul5LiL5piv5b6F5aSE55CG55qE5paH56ug77yaKipcblxuJHthcnRpY2xlc0NvbnRlbnR9YDsNCg0KICAgICAgICAvLyDosIPnlKhBSeaOpeWPow0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRpZnlBaVFhKA0KICAgICAgICAgIGFydGljbGVzQ29udGVudCwNCiAgICAgICAgICAic3RyZWFtaW5nIiwNCiAgICAgICAgICAiZGlmeS5hcnRpY2xlLmFwaWtleSINCiAgICAgICAgKTsNCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQUnmjqXlj6PosIPnlKjlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWkhOeQhua1geW8j+WTjeW6lA0KICAgICAgICBjb25zdCByZWFkZXIgPSByZXNwb25zZS5ib2R5LmdldFJlYWRlcigpOw0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSByZWFkZXI7DQogICAgICAgIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTsNCiAgICAgICAgbGV0IGJ1ZmZlciA9ICIiOw0KICAgICAgICBsZXQgcGVuZGluZ0J1ZmZlciA9ICIiOyAvLyDnlKjkuo7lrZjlgqjlvoXlpITnkIbnmoTkuI3lrozmlbTmlbDmja4NCiAgICAgICAgbGV0IGlzSW5UaGlua1RhZyA9IGZhbHNlOyAvLyDmlrDlop7vvJrmoIforrDmmK/lkKblnKh0aGlua+agh+etvuWGhQ0KDQogICAgICAgIC8vIOWwhlVuaWNvZGXovazkuYnlrZfnrKYoXHVYWFhYKei9rOaNouS4uuWunumZheWtl+espg0KICAgICAgICBjb25zdCBkZWNvZGVVbmljb2RlID0gKHN0cikgPT4gew0KICAgICAgICAgIHJldHVybiBzdHIucmVwbGFjZSgvXFx1W1xkQS1GYS1mXXs0fS9nLCAobWF0Y2gpID0+IHsNCiAgICAgICAgICAgIHJldHVybiBTdHJpbmcuZnJvbUNoYXJDb2RlKHBhcnNlSW50KG1hdGNoLnJlcGxhY2UoL1xcdS9nLCAiIiksIDE2KSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH07DQoNCiAgICAgICAgLy8g5pu05paw5YaF5a6555qE5Ye95pWwDQogICAgICAgIGNvbnN0IHVwZGF0ZUNvbnRlbnQgPSAobmV3Q29udGVudCkgPT4gew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBjb25zdCByZW5kZXJlZENvbnRlbnQgPSBtYXJrZWQobmV3Q29udGVudCwgdGhpcy5tYXJrZG93bk9wdGlvbnMpOw0KICAgICAgICAgICAgYWlNZXNzYWdlLmNvbnRlbnQgPSByZW5kZXJlZENvbnRlbnQ7DQoNCiAgICAgICAgICAgIC8vIOehruS/nea2iOaBr+WuueWZqOa7muWKqOWIsOW6lemDqA0KICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICBjb25zdCBjaGF0TWVzc2FnZXMgPSB0aGlzLiRyZWZzLmNoYXRNZXNzYWdlczsNCiAgICAgICAgICAgICAgaWYgKGNoYXRNZXNzYWdlcykgew0KICAgICAgICAgICAgICAgIGNoYXRNZXNzYWdlcy5zY3JvbGxUb3AgPSBjaGF0TWVzc2FnZXMuc2Nyb2xsSGVpZ2h0Ow0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5riy5p+T5YaF5a655pe25Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgICB9DQogICAgICAgIH07DQoNCiAgICAgICAgLy8g5aSE55CG5rWB5byP5ZON5bqUDQogICAgICAgIHdoaWxlICh0cnVlKSB7DQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5Lit5patDQogICAgICAgICAgaWYgKHRoaXMuaXNBYm9ydGVkKSB7DQogICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFib3J0RXJyb3IiKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpOw0KDQogICAgICAgICAgaWYgKGRvbmUpIHsNCiAgICAgICAgICAgIC8vIOWkhOeQhuacgOWQjuWPr+iDveWJqeS9meeahOaVsOaNrg0KICAgICAgICAgICAgaWYgKHBlbmRpbmdCdWZmZXIpIHsNCiAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICBjb25zdCBsYXN0RGF0YSA9IEpTT04ucGFyc2UocGVuZGluZ0J1ZmZlcik7DQogICAgICAgICAgICAgICAgaWYgKGxhc3REYXRhLmFuc3dlcikgew0KICAgICAgICAgICAgICAgICAgLy8g6Kej56CBVW5pY29kZei9rOS5ieWtl+espg0KICAgICAgICAgICAgICAgICAgY29uc3QgZGVjb2RlZEFuc3dlciA9IGRlY29kZVVuaWNvZGUobGFzdERhdGEuYW5zd2VyKTsNCiAgICAgICAgICAgICAgICAgIGJ1ZmZlciArPSBkZWNvZGVkQW5zd2VyOw0KICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChidWZmZXIpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybigi5aSE55CG5pyA5ZCO55qE5pWw5o2u5pe25Ye66ZSZOiIsIGUpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zdCBjaHVuayA9IGRlY29kZXIuZGVjb2RlKHZhbHVlKTsNCiAgICAgICAgICBwZW5kaW5nQnVmZmVyICs9IGNodW5rOw0KDQogICAgICAgICAgLy8g5aSE55CG5a6M5pW055qE5pWw5o2u6KGMDQogICAgICAgICAgd2hpbGUgKHBlbmRpbmdCdWZmZXIuaW5jbHVkZXMoIlxuIikpIHsNCiAgICAgICAgICAgIGNvbnN0IG5ld2xpbmVJbmRleCA9IHBlbmRpbmdCdWZmZXIuaW5kZXhPZigiXG4iKTsNCiAgICAgICAgICAgIGNvbnN0IGxpbmUgPSBwZW5kaW5nQnVmZmVyLnNsaWNlKDAsIG5ld2xpbmVJbmRleCkudHJpbSgpOw0KICAgICAgICAgICAgcGVuZGluZ0J1ZmZlciA9IHBlbmRpbmdCdWZmZXIuc2xpY2UobmV3bGluZUluZGV4ICsgMSk7DQoNCiAgICAgICAgICAgIGlmICghbGluZSB8fCBsaW5lID09PSAiZGF0YToiIHx8ICFsaW5lLnN0YXJ0c1dpdGgoImRhdGE6IikpIHsNCiAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBsaW5lLnNsaWNlKDUpLnRyaW0oKTsNCiAgICAgICAgICAgICAgaWYgKGRhdGEgPT09ICJbRE9ORV0iKSB7DQogICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICBjb25zdCBqc29uRGF0YSA9IEpTT04ucGFyc2UoZGF0YSk7DQogICAgICAgICAgICAgIGlmICghanNvbkRhdGEuYW5zd2VyKSB7DQogICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDot7Pov4fnibnmrorlrZfnrKYNCiAgICAgICAgICAgICAgaWYgKGpzb25EYXRhLmFuc3dlciA9PT0gImBgYCIgfHwganNvbkRhdGEuYW5zd2VyID09PSAibWFya2Rvd24iKSB7DQogICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDop6PnoIFVbmljb2Rl6L2s5LmJ5a2X56ymDQogICAgICAgICAgICAgIGxldCBhbnN3ZXIgPSBkZWNvZGVVbmljb2RlKGpzb25EYXRhLmFuc3dlcik7DQoNCiAgICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5YyF5ZCrPHRoaW5rPuW8gOWni+agh+etvg0KICAgICAgICAgICAgICBpZiAoYW5zd2VyLmluY2x1ZGVzKCI8dGhpbms+IikpIHsNCiAgICAgICAgICAgICAgICBpc0luVGhpbmtUYWcgPSB0cnVlOw0KICAgICAgICAgICAgICAgIGNvbnRpbnVlOyAvLyDot7Pov4fljIXlkKs8dGhpbms+55qE6YOo5YiGDQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbljIXlkKs8L3RoaW5rPue7k+adn+agh+etvg0KICAgICAgICAgICAgICBpZiAoYW5zd2VyLmluY2x1ZGVzKCI8L3RoaW5rPiIpKSB7DQogICAgICAgICAgICAgICAgaXNJblRoaW5rVGFnID0gZmFsc2U7DQogICAgICAgICAgICAgICAgY29udGludWU7IC8vIOi3s+i/h+WMheWQqzwvdGhpbms+55qE6YOo5YiGDQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDlj6rmnInkuI3lnKh0aGlua+agh+etvuWGheeahOWGheWuueaJjeS8muiiq+a3u+WKoOWIsGJ1ZmZlcuS4rQ0KICAgICAgICAgICAgICBpZiAoIWlzSW5UaGlua1RhZyAmJiBhbnN3ZXIpIHsNCiAgICAgICAgICAgICAgICBidWZmZXIgKz0gYW5zd2VyOw0KICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQoYnVmZmVyKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikgew0KICAgICAgICAgICAgICBjb25zb2xlLndhcm4oIuino+aekOaVsOaNruihjOaXtuWHuumUmToiLCB7DQogICAgICAgICAgICAgICAgbGluZSwNCiAgICAgICAgICAgICAgICBlcnJvcjogcGFyc2VFcnJvci5tZXNzYWdlLA0KICAgICAgICAgICAgICAgIHBlbmRpbmdCdWZmZXIsDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIkFJ6Kej6K+75Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICJBSeino+ivu+Wksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICBpZiAodGhpcy5jaGF0TWVzc2FnZXNbMV0pIHsNCiAgICAgICAgICB0aGlzLmNoYXRNZXNzYWdlc1sxXS5jb250ZW50ID0gIuaKseatie+8jOacjeWKoeWZqOe5geW/me+8jOivt+eojeWQjuWGjeivlSI7DQogICAgICAgIH0NCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IG51bGw7DQogICAgICAgIGlmICh0aGlzLmFpRGlhbG9nVmlzaWJsZSkgew0KICAgICAgICAgIHRoaXMuaXNUaGlua2luZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIE9sbGFtYQ0KICAgIGFzeW5jIG9sbGFtYUFpQ2hhdCgpIHsNCiAgICAgIGlmICh0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+35YWI6YCJ5oup6KaB6Kej6K+755qE5paH56ugIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmnInmraPlnKjov5vooYznmoTor7fmsYLvvIzkuK3mlq3lroMNCiAgICAgIGlmICh0aGlzLmlzUmVxdWVzdGluZykgew0KICAgICAgICB0aGlzLmlzQWJvcnRlZCA9IHRydWU7DQogICAgICAgIGlmICh0aGlzLmN1cnJlbnRSZWFkZXIpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgYXdhaXQgdGhpcy5jdXJyZW50UmVhZGVyLmNhbmNlbCgpOw0KICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCLkuK3mlq3kuYvliY3nmoTor7fmsYLlpLHotKUiLCBlKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8g562J5b6F5LmL5YmN55qE6K+35rGC54q25oCB5riF55CG5a6M5oiQDQogICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IHRydWU7DQogICAgICB0aGlzLmlzQWJvcnRlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5haURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5jaGF0TWVzc2FnZXMgPSBbXTsNCiAgICAgIHRoaXMuaXNUaGlua2luZyA9IHRydWU7DQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiOt+WPlumAieS4reeahOaWh+eroA0KICAgICAgICBjb25zdCBzZWxlY3RlZEFydGljbGVzID0gdGhpcy5BcnRpY2xlTGlzdC5maWx0ZXIoKGFydGljbGUpID0+DQogICAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzLmluY2x1ZGVzKGFydGljbGUuaWQpDQogICAgICAgICk7DQogICAgICAgIGNvbnN0IHRpdGxlcyA9IHNlbGVjdGVkQXJ0aWNsZXMNCiAgICAgICAgICAubWFwKChhcnRpY2xlKSA9PiBg44CKJHthcnRpY2xlLmNuVGl0bGUgfHwgYXJ0aWNsZS50aXRsZX3jgItgKQ0KICAgICAgICAgIC5qb2luKCJcbiIpOw0KDQogICAgICAgIC8vIOiOt+WPluaWh+eroOWGheWuuQ0KICAgICAgICBjb25zdCBhcnRpY2xlc1Jlc3BvbnNlID0gYXdhaXQgZ2V0TGlzdEJ5SWRzKA0KICAgICAgICAgIHRoaXMuY2hlY2tlZENpdGllcy5qb2luKCIsIikNCiAgICAgICAgKTsNCiAgICAgICAgaWYgKCFhcnRpY2xlc1Jlc3BvbnNlLmRhdGE/Lmxlbmd0aCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigi6I635Y+W5paH56ug5YaF5a655aSx6LSlIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmoLzlvI/ljJbmlofnq6DlhoXlrrkNCiAgICAgICAgY29uc3QgYXJ0aWNsZXNDb250ZW50ID0gYXJ0aWNsZXNSZXNwb25zZS5kYXRhDQogICAgICAgICAgLm1hcCgoYXJ0aWNsZSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHRpdGxlID0NCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LmNuVGl0bGUgfHwNCiAgICAgICAgICAgICAgc2VsZWN0ZWRBcnRpY2xlc1tpbmRleF0/LnRpdGxlIHx8DQogICAgICAgICAgICAgICIiOw0KICAgICAgICAgICAgY29uc3QgY29udGVudCA9IGFydGljbGUuY29udGVudCB8fCAiIjsNCiAgICAgICAgICAgIHJldHVybiBg44CQ56ysICR7aW5kZXggKyAxfSDnr4fmlofnq6DjgJHjgIoke3RpdGxlfeOAi1xuXG4ke2NvbnRlbnR9YDsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5qb2luKCJcblxuLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4iKTsNCg0KICAgICAgICAvLyDmt7vliqDnlKjmiLfmtojmga8NCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaCh7DQogICAgICAgICAgcm9sZTogInVzZXIiLA0KICAgICAgICAgIGNvbnRlbnQ6IGDluK7miJHmt7Hluqbop6Por7vku6XkuIske3RoaXMuY2hlY2tlZENpdGllcy5sZW5ndGh956+H5paH56ug77yaXG4ke3RpdGxlc31gLA0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDliJvlu7pBSea2iOaBrw0KICAgICAgICBjb25zdCBhaU1lc3NhZ2UgPSB7DQogICAgICAgICAgcm9sZTogImFzc2lzdGFudCIsDQogICAgICAgICAgY29udGVudDogIiIsDQogICAgICAgIH07DQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goYWlNZXNzYWdlKTsNCg0KICAgICAgICAvLyDmnoTlu7rmj5DnpLror40NCiAgICAgICAgY29uc3QgcHJvbXB0ID0NCiAgICAgICAgICB0aGlzLmFydGljbGVBaVByb21wdA0KICAgICAgICAgICAgLnJlcGxhY2UoImFydGljbGVMZW5ndGgiLCB0aGlzLmNoZWNrZWRDaXRpZXMubGVuZ3RoKQ0KICAgICAgICAgICAgLnJlcGxhY2UoL1wmZ3Q7L2csICI+IikgKw0KICAgICAgICAgIGAqKuS7peS4i+aYr+W+heWkhOeQhueahOaWh+eroO+8mioqXG5cbiR7YXJ0aWNsZXNDb250ZW50fWA7DQoNCiAgICAgICAgLy8g6LCD55SoQUnmjqXlj6MNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBvbGxhbWFBaVFhKHByb21wdCwgdHJ1ZSk7DQogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFJ5o6l5Y+j6LCD55So5aSx6LSlIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlpITnkIbmtYHlvI/lk43lupQNCiAgICAgICAgY29uc3QgcmVhZGVyID0gcmVzcG9uc2UuYm9keS5nZXRSZWFkZXIoKTsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gcmVhZGVyOyAvLyDkv53lrZjlvZPliY3nmoQgcmVhZGVyDQogICAgICAgIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTsNCiAgICAgICAgbGV0IGJ1ZmZlciA9ICIiOw0KICAgICAgICBsZXQgbGFzdFVwZGF0ZVRpbWUgPSBEYXRlLm5vdygpOw0KICAgICAgICBsZXQgaXNUaGlua0NvbnRlbnQgPSBmYWxzZTsNCiAgICAgICAgbGV0IHRlbXBCdWZmZXIgPSAiIjsNCg0KICAgICAgICAvLyDmm7TmlrDlhoXlrrnnmoTlh73mlbANCiAgICAgICAgY29uc3QgdXBkYXRlQ29udGVudCA9IChuZXdDb250ZW50KSA9PiB7DQogICAgICAgICAgY29uc3QgY3VycmVudFRpbWUgPSBEYXRlLm5vdygpOw0KICAgICAgICAgIC8vIOaOp+WItuabtOaWsOmikeeOh++8jOmBv+WFjei/h+S6jumikee5geeahERPTeabtOaWsA0KICAgICAgICAgIGlmIChjdXJyZW50VGltZSAtIGxhc3RVcGRhdGVUaW1lID49IDUwKSB7DQogICAgICAgICAgICBhaU1lc3NhZ2UuY29udGVudCA9IG5ld0NvbnRlbnQ7DQogICAgICAgICAgICBsYXN0VXBkYXRlVGltZSA9IGN1cnJlbnRUaW1lOw0KICAgICAgICAgICAgLy8g56Gu5L+d5raI5oGv5a655Zmo5rua5Yqo5Yiw5bqV6YOoDQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGNoYXRNZXNzYWdlcyA9IHRoaXMuJHJlZnMuY2hhdE1lc3NhZ2VzOw0KICAgICAgICAgICAgICBpZiAoY2hhdE1lc3NhZ2VzKSB7DQogICAgICAgICAgICAgICAgY2hhdE1lc3NhZ2VzLnNjcm9sbFRvcCA9IGNoYXRNZXNzYWdlcy5zY3JvbGxIZWlnaHQ7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDlpITnkIbmtYHlvI/lk43lupQNCiAgICAgICAgY29uc3QgcHJvY2Vzc1N0cmVhbSA9IGFzeW5jICgpID0+IHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgd2hpbGUgKHRydWUpIHsNCiAgICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5Lit5patDQogICAgICAgICAgICAgIGlmICh0aGlzLmlzQWJvcnRlZCkgew0KICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQWJvcnRFcnJvciIpOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgY29uc3QgeyBkb25lLCB2YWx1ZSB9ID0gYXdhaXQgcmVhZGVyLnJlYWQoKTsNCiAgICAgICAgICAgICAgaWYgKGRvbmUpIHsNCiAgICAgICAgICAgICAgICBpZiAoYnVmZmVyLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQoYnVmZmVyKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICBjb25zdCBjaHVuayA9IGRlY29kZXIuZGVjb2RlKHZhbHVlKTsNCiAgICAgICAgICAgICAgY29uc3QgbGluZXMgPSBjaHVuay5zcGxpdCgiXG4iKS5maWx0ZXIoKGxpbmUpID0+IGxpbmUudHJpbSgpKTsNCg0KICAgICAgICAgICAgICBmb3IgKGNvbnN0IGxpbmUgb2YgbGluZXMpIHsNCiAgICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgICAgY29uc3QganNvbkRhdGEgPSBKU09OLnBhcnNlKGxpbmUpOw0KICAgICAgICAgICAgICAgICAgaWYgKCFqc29uRGF0YS5yZXNwb25zZSkgY29udGludWU7DQoNCiAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0ganNvbkRhdGEucmVzcG9uc2U7DQoNCiAgICAgICAgICAgICAgICAgIC8vIOi3s+i/h+eJueauiuWtl+espg0KICAgICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlID09PSAiYGBgIiB8fCByZXNwb25zZSA9PT0gIm1hcmtkb3duIikgew0KICAgICAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgdGVtcEJ1ZmZlciArPSByZXNwb25zZTsNCg0KICAgICAgICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5YyF5ZCr5a6M5pW055qEdGhpbmvmoIfnrb7lr7kNCiAgICAgICAgICAgICAgICAgIHdoaWxlICh0cnVlKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IHRoaW5rU3RhcnRJbmRleCA9IHRlbXBCdWZmZXIuaW5kZXhPZigiPHRoaW5rPiIpOw0KICAgICAgICAgICAgICAgICAgICBjb25zdCB0aGlua0VuZEluZGV4ID0gdGVtcEJ1ZmZlci5pbmRleE9mKCI8L3RoaW5rPiIpOw0KDQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlua1N0YXJ0SW5kZXggPT09IC0xICYmIHRoaW5rRW5kSW5kZXggPT09IC0xKSB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g5rKh5pyJdGhpbmvmoIfnrb7vvIznm7TmjqXmt7vliqDliLBidWZmZXINCiAgICAgICAgICAgICAgICAgICAgICBpZiAoIWlzVGhpbmtDb250ZW50KSB7DQogICAgICAgICAgICAgICAgICAgICAgICBidWZmZXIgKz0gdGVtcEJ1ZmZlcjsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOS9v+eUqG1hcmtlZOa4suafk21hcmtkb3du5YaF5a65DQogICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KG1hcmtlZChidWZmZXIsIHRoaXMubWFya2Rvd25PcHRpb25zKSk7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXIgPSAiIjsNCiAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlua1N0YXJ0SW5kZXggIT09IC0xICYmIHRoaW5rRW5kSW5kZXggPT09IC0xKSB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g5Y+q5pyJ5byA5aeL5qCH562+77yM562J5b6F57uT5p2f5qCH562+DQogICAgICAgICAgICAgICAgICAgICAgaXNUaGlua0NvbnRlbnQgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlua1N0YXJ0SW5kZXggPiAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBidWZmZXIgKz0gdGVtcEJ1ZmZlci5zdWJzdHJpbmcoMCwgdGhpbmtTdGFydEluZGV4KTsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOS9v+eUqG1hcmtlZOa4suafk21hcmtkb3du5YaF5a65DQogICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KG1hcmtlZChidWZmZXIsIHRoaXMubWFya2Rvd25PcHRpb25zKSk7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXIgPSB0ZW1wQnVmZmVyLnN1YnN0cmluZyh0aGlua1N0YXJ0SW5kZXgpOw0KICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHRoaW5rU3RhcnRJbmRleCA9PT0gLTEgJiYgdGhpbmtFbmRJbmRleCAhPT0gLTEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDlj6rmnInnu5PmnZ/moIfnrb7vvIznp7vpmaTkuYvliY3nmoTlhoXlrrkNCiAgICAgICAgICAgICAgICAgICAgICBpc1RoaW5rQ29udGVudCA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXIgPSB0ZW1wQnVmZmVyLnN1YnN0cmluZyh0aGlua0VuZEluZGV4ICsgOCk7DQogICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g5pyJ5a6M5pW055qEdGhpbmvmoIfnrb7lr7kNCiAgICAgICAgICAgICAgICAgICAgICBpZiAodGhpbmtTdGFydEluZGV4ID4gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgYnVmZmVyICs9IHRlbXBCdWZmZXIuc3Vic3RyaW5nKDAsIHRoaW5rU3RhcnRJbmRleCk7DQogICAgICAgICAgICAgICAgICAgICAgICAvLyDkvb/nlKhtYXJrZWTmuLLmn5NtYXJrZG93buWGheWuuQ0KICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChtYXJrZWQoYnVmZmVyLCB0aGlzLm1hcmtkb3duT3B0aW9ucykpOw0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICB0ZW1wQnVmZmVyID0gdGVtcEJ1ZmZlci5zdWJzdHJpbmcodGhpbmtFbmRJbmRleCArIDgpOw0KICAgICAgICAgICAgICAgICAgICAgIGlzVGhpbmtDb250ZW50ID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7DQogICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oIuaXoOaViOeahEpTT07ooYzvvIzlt7Lot7Pov4ciLCB7DQogICAgICAgICAgICAgICAgICAgIGxpbmUsDQogICAgICAgICAgICAgICAgICAgIGVycm9yOiBwYXJzZUVycm9yLm1lc3NhZ2UsDQogICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGNhdGNoIChzdHJlYW1FcnJvcikgew0KICAgICAgICAgICAgaWYgKHN0cmVhbUVycm9yLm1lc3NhZ2UgPT09ICJBYm9ydEVycm9yIikgew0KICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFib3J0RXJyb3IiKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuWkhOeQhua1geW8j+WTjeW6lOaXtuWHuumUmToiLCBzdHJlYW1FcnJvcik7DQogICAgICAgICAgICB0aHJvdyBzdHJlYW1FcnJvcjsNCiAgICAgICAgICB9DQogICAgICAgIH07DQoNCiAgICAgICAgYXdhaXQgcHJvY2Vzc1N0cmVhbSgpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgLy8g5Yik5pat5piv5ZCm5piv5Lit5pat5a+86Ie055qE6ZSZ6K+vDQogICAgICAgIGlmIChlcnJvci5tZXNzYWdlID09PSAiQWJvcnRFcnJvciIpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygi6K+35rGC5bey6KKr5Lit5patIik7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIkFJ6Kej6K+75Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICJBSeino+ivu+Wksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICBpZiAodGhpcy5jaGF0TWVzc2FnZXNbMV0pIHsNCiAgICAgICAgICB0aGlzLmNoYXRNZXNzYWdlc1sxXS5jb250ZW50ID0gIuaKseatie+8jOacjeWKoeWZqOe5geW/me+8jOivt+eojeWQjuWGjeivlSI7DQogICAgICAgIH0NCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IG51bGw7IC8vIOa4heeQhuW9k+WJjeeahCByZWFkZXINCiAgICAgICAgLy8g5Y+q5pyJ5Zyo5rKh5pyJ6KKr5Lit5pat55qE5oOF5Ya15LiL5omN6YeN572u54q25oCBDQogICAgICAgIGlmICh0aGlzLmFpRGlhbG9nVmlzaWJsZSkgew0KICAgICAgICAgIHRoaXMuaXNUaGlua2luZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIGRlZXBzZWVrDQogICAgYXN5bmMgZGVlcHNlZWtBaUNoYXQoKSB7DQogICAgICBpZiAodGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+WFiOmAieaLqeimgeino+ivu+eahOaWh+eroCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5pyJ5q2j5Zyo6L+b6KGM55qE6K+35rGC77yM5Lit5pat5a6DDQogICAgICBpZiAodGhpcy5pc1JlcXVlc3RpbmcpIHsNCiAgICAgICAgdGhpcy5pc0Fib3J0ZWQgPSB0cnVlOw0KICAgICAgICBpZiAodGhpcy5jdXJyZW50UmVhZGVyKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuY3VycmVudFJlYWRlci5jYW5jZWwoKTsNCiAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICBjb25zb2xlLmxvZygi5Lit5pat5LmL5YmN55qE6K+35rGC5aSx6LSlIiwgZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC8vIOetieW+heS5i+WJjeeahOivt+axgueKtuaAgea4heeQhuWujOaIkA0KICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSB0cnVlOw0KICAgICAgdGhpcy5pc0Fib3J0ZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMuYWlEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzID0gW107DQogICAgICB0aGlzLmlzVGhpbmtpbmcgPSB0cnVlOw0KDQogICAgICBjb25zdCBzZWxlY3RlZEFydGljbGVzID0gdGhpcy5BcnRpY2xlTGlzdC5maWx0ZXIoKGFydGljbGUpID0+DQogICAgICAgIHRoaXMuY2hlY2tlZENpdGllcy5pbmNsdWRlcyhhcnRpY2xlLmlkKQ0KICAgICAgKTsNCiAgICAgIGNvbnN0IHRpdGxlcyA9IHNlbGVjdGVkQXJ0aWNsZXMNCiAgICAgICAgLm1hcCgoYXJ0aWNsZSkgPT4gYOOAiiR7YXJ0aWNsZS5jblRpdGxlIHx8IGFydGljbGUudGl0bGV944CLYCkNCiAgICAgICAgLmpvaW4oIlxuIik7DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGFydGljbGVzUmVzcG9uc2UgPSBhd2FpdCBnZXRMaXN0QnlJZHMoDQogICAgICAgICAgdGhpcy5jaGVja2VkQ2l0aWVzLmpvaW4oIiwiKQ0KICAgICAgICApOw0KICAgICAgICBpZiAoIWFydGljbGVzUmVzcG9uc2UuZGF0YSB8fCAhYXJ0aWNsZXNSZXNwb25zZS5kYXRhLmxlbmd0aCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiRmFpbGVkIHRvIGdldCBhcnRpY2xlIGNvbnRlbnRzIik7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCBhcnRpY2xlc0NvbnRlbnQgPSBhcnRpY2xlc1Jlc3BvbnNlLmRhdGENCiAgICAgICAgICAubWFwKChhcnRpY2xlLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgY29uc3QgdGl0bGUgPQ0KICAgICAgICAgICAgICBzZWxlY3RlZEFydGljbGVzW2luZGV4XT8uY25UaXRsZSB8fA0KICAgICAgICAgICAgICBzZWxlY3RlZEFydGljbGVzW2luZGV4XT8udGl0bGUgfHwNCiAgICAgICAgICAgICAgIiI7DQogICAgICAgICAgICBjb25zdCBjb250ZW50ID0gYXJ0aWNsZS5jb250ZW50IHx8ICIiOw0KICAgICAgICAgICAgcmV0dXJuIGDjgJDnrKwgJHtpbmRleCArIDF9IOevh+aWh+eroOOAkeOAiiR7dGl0bGV944CLXG5cbiR7Y29udGVudH1gOw0KICAgICAgICAgIH0pDQogICAgICAgICAgLmpvaW4oIlxuXG4tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiIpOw0KDQogICAgICAgIC8vIOa3u+WKoOeUqOaIt+a2iOaBrw0KICAgICAgICB0aGlzLmNoYXRNZXNzYWdlcy5wdXNoKHsNCiAgICAgICAgICByb2xlOiAidXNlciIsDQogICAgICAgICAgY29udGVudDogYOW4ruaIkea3seW6puino+ivu+S7peS4iyR7dGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aH3nr4fmlofnq6DvvJpcbiR7dGl0bGVzfWAsDQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOWIm+W7ukFJ5raI5oGv5bm25re75Yqg5Yiw5a+56K+d5LitDQogICAgICAgIGNvbnN0IGFpTWVzc2FnZSA9IHsNCiAgICAgICAgICByb2xlOiAiYXNzaXN0YW50IiwNCiAgICAgICAgICBjb250ZW50OiAiIiwNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5jaGF0TWVzc2FnZXMucHVzaChhaU1lc3NhZ2UpOw0KICAgICAgICB0aGlzLmlzVGhpbmtpbmcgPSB0cnVlOw0KDQogICAgICAgIGNvbnN0IHByb21wdCA9DQogICAgICAgICAgdGhpcy5hcnRpY2xlQWlQcm9tcHQNCiAgICAgICAgICAgIC5yZXBsYWNlKCJhcnRpY2xlTGVuZ3RoIiwgdGhpcy5jaGVja2VkQ2l0aWVzLmxlbmd0aCkNCiAgICAgICAgICAgIC5yZXBsYWNlKC9cJmd0Oy9nLCAiPiIpICsNCiAgICAgICAgICBgXG5cbioq5Lul5LiL5piv5b6F5aSE55CG55qE5paH56ug77yaKipcblxuJHthcnRpY2xlc0NvbnRlbnR9YDsNCg0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRlZXBzZWVrQWlRYShwcm9tcHQsIHRydWUpOw0KDQogICAgICAgIGlmIChyZXNwb25zZS5vaykgew0KICAgICAgICAgIGNvbnN0IHJlYWRlciA9IHJlc3BvbnNlLmJvZHkuZ2V0UmVhZGVyKCk7DQogICAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gcmVhZGVyOyAvLyDkv53lrZjlvZPliY3nmoQgcmVhZGVyDQogICAgICAgICAgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpOw0KICAgICAgICAgIGxldCBidWZmZXIgPSAiIjsNCiAgICAgICAgICBsZXQgbGFzdFVwZGF0ZVRpbWUgPSBEYXRlLm5vdygpOw0KDQogICAgICAgICAgY29uc3QgdXBkYXRlQ29udGVudCA9IChuZXdDb250ZW50KSA9PiB7DQogICAgICAgICAgICBjb25zdCBjdXJyZW50VGltZSA9IERhdGUubm93KCk7DQogICAgICAgICAgICBpZiAoY3VycmVudFRpbWUgLSBsYXN0VXBkYXRlVGltZSA+PSA1MCkgew0KICAgICAgICAgICAgICBhaU1lc3NhZ2UuY29udGVudCA9IG5ld0NvbnRlbnQ7DQogICAgICAgICAgICAgIGxhc3RVcGRhdGVUaW1lID0gY3VycmVudFRpbWU7DQogICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgICBjb25zdCBjaGF0TWVzc2FnZXMgPSB0aGlzLiRyZWZzLmNoYXRNZXNzYWdlczsNCiAgICAgICAgICAgICAgICBpZiAoY2hhdE1lc3NhZ2VzKSB7DQogICAgICAgICAgICAgICAgICBjaGF0TWVzc2FnZXMuc2Nyb2xsVG9wID0gY2hhdE1lc3NhZ2VzLnNjcm9sbEhlaWdodDsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH07DQoNCiAgICAgICAgICB3aGlsZSAodHJ1ZSkgew0KICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5Lit5patDQogICAgICAgICAgICBpZiAodGhpcy5pc0Fib3J0ZWQpIHsNCiAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBYm9ydEVycm9yIik7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGNvbnN0IHsgZG9uZSwgdmFsdWUgfSA9IGF3YWl0IHJlYWRlci5yZWFkKCk7DQogICAgICAgICAgICBpZiAoZG9uZSkgew0KICAgICAgICAgICAgICBpZiAoYnVmZmVyLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KGJ1ZmZlcik7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpOw0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgY29uc3QgbGluZXMgPSBjaHVuay5zcGxpdCgiXG4iKTsNCg0KICAgICAgICAgICAgICBmb3IgKGNvbnN0IGxpbmUgb2YgbGluZXMpIHsNCiAgICAgICAgICAgICAgICBpZiAoIWxpbmUudHJpbSgpIHx8ICFsaW5lLnN0YXJ0c1dpdGgoImRhdGE6ICIpKSBjb250aW51ZTsNCg0KICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBsaW5lLnNsaWNlKDUpOw0KICAgICAgICAgICAgICAgIGlmIChkYXRhID09PSAiW0RPTkVdIikgYnJlYWs7DQoNCiAgICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgICAgY29uc3QganNvbkRhdGEgPSBKU09OLnBhcnNlKGRhdGEpOw0KICAgICAgICAgICAgICAgICAgaWYgKGpzb25EYXRhLmNob2ljZXM/LlswXT8uZGVsdGE/LmNvbnRlbnQpIHsNCiAgICAgICAgICAgICAgICAgICAgbGV0IGNvbnRlbnQgPSBqc29uRGF0YS5jaG9pY2VzWzBdLmRlbHRhLmNvbnRlbnQ7DQoNCiAgICAgICAgICAgICAgICAgICAgLy8g6Lez6L+H54m55q6K5a2X56ymDQogICAgICAgICAgICAgICAgICAgIGlmIChjb250ZW50ID09PSAiYGBgIiB8fCBjb250ZW50ID09PSAibWFya2Rvd24iKSB7DQogICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgICBidWZmZXIgKz0gY29udGVudDsNCiAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChidWZmZXIpOw0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHsNCiAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIkVycm9yIHBhcnNpbmcgSlNPTjoiLCBwYXJzZUVycm9yKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigiRXJyb3IgcHJvY2Vzc2luZyBjaHVuazoiLCBlKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJSZXF1ZXN0IGZhaWxlZCIpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAvLyDliKTmlq3mmK/lkKbmmK/kuK3mlq3lr7zoh7TnmoTplJnor68NCiAgICAgICAgaWYgKGVycm9yLm1lc3NhZ2UgPT09ICJBYm9ydEVycm9yIikgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCLor7fmsYLlt7LooqvkuK3mlq0iKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgY29uc29sZS5lcnJvcigiQUkgQ2hhdCBFcnJvcjoiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIkFJ6Kej6K+75aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgIGlmICh0aGlzLmNoYXRNZXNzYWdlc1sxXSkgew0KICAgICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzWzFdLmNvbnRlbnQgPSAi5oqx5q2J77yM5pyN5Yqh5Zmo57mB5b+Z77yM6K+356iN5ZCO5YaN6K+VIjsNCiAgICAgICAgfQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gbnVsbDsgLy8g5riF55CG5b2T5YmN55qEIHJlYWRlcg0KICAgICAgICAvLyDlj6rmnInlnKjmsqHmnInooqvkuK3mlq3nmoTmg4XlhrXkuIvmiY3ph43nva7nirbmgIENCiAgICAgICAgaWYgKHRoaXMuYWlEaWFsb2dWaXNpYmxlKSB7DQogICAgICAgICAgdGhpcy5pc1RoaW5raW5nID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5YWz6ZetQUnlr7nor50NCiAgICBjbG9zZUFpRGlhbG9nKCkgew0KICAgICAgdGhpcy5pc0Fib3J0ZWQgPSB0cnVlOyAvLyDorr7nva7kuK3mlq3moIflv5cNCiAgICAgIGlmICh0aGlzLmN1cnJlbnRSZWFkZXIpIHsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyLmNhbmNlbCgpOyAvLyDkuK3mlq3lvZPliY3nmoTor7vlj5YNCiAgICAgIH0NCiAgICAgIHRoaXMuYWlEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLmNoYXRNZXNzYWdlcyA9IFtdOw0KICAgICAgdGhpcy5pc1RoaW5raW5nID0gZmFsc2U7DQogICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IGZhbHNlOw0KICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gbnVsbDsNCiAgICB9LA0KICAgIGFydGljbGVBaUNoYXQoKSB7DQogICAgICBpZiAodGhpcy5haVBsYXRmb3JtID09PSAiZGlmeSIpIHsNCiAgICAgICAgdGhpcy5kaWZ5QWlDaGF0KCk7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWlQbGF0Zm9ybSA9PT0gIm9sbGFtYSIpIHsNCiAgICAgICAgdGhpcy5vbGxhbWFBaUNoYXQoKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5haVBsYXRmb3JtID09PSAiZGVlcHNlZWsiKSB7DQogICAgICAgIHRoaXMuZGVlcHNlZWtBaUNoYXQoKTsNCiAgICAgIH0NCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["MainArticle.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw6DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "MainArticle.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\r\n  <div class=\"main\" v-loading=\"loading\">\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Monitor'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    >\r\n                    </span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(getSafeSummary(item))\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\" @click=\"resultEvent('BatchGeneration')\"></i>\r\n          </p> -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-document-add\"\r\n              style=\"font-size: 24px\"\r\n              title=\"发布到每日最新热点\"\r\n              @click=\"publishHot\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\" v-if=\"$route.query.domain\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek深度解读\"\r\n              @click=\"articleAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n              >Deepseek深度解读</span\r\n            >\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          :style=\"\r\n            $route.query.domain\r\n              ? 'height: calc(100vh - 170px)'\r\n              : 'height: calc(100vh - 389px)'\r\n          \"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{\r\n                        $route.query.domain\r\n                          ? \"是\"\r\n                          : getTechnologyLabel(item.isTechnology)\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(getSafeSummary(item))\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleUpdate(item)\"\r\n                  v-hasPermi=\"['article:articleList:edit']\"\r\n                  >{{ ` 修改 ` }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  v-hasPermi=\"['article:list:remove']\"\r\n                  >{{ `删除` }}</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Special'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(getSafeSummary(item))\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'infoInter'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(getSafeSummary(item))\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n            </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Wechat'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(50vh - 200px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_kqcb\">\r\n              <div>\r\n                <div class=\"ArticlTop\" style=\"width: 55%\">\r\n                  <p style=\"line-height: 20px\">\r\n                    <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                      {{ null }} </el-checkbox\r\n                    >&nbsp;&nbsp;\r\n                    <a href=\"#\"\r\n                      ><span style=\"padding: 0 10px 0 0\"\r\n                        >{{\r\n                          (currentPage - 1) * pageSize + key + 1\r\n                        }}&nbsp;</span\r\n                      ><span\r\n                        class=\"title_Article\"\r\n                        @click=\"openNewView(item)\"\r\n                        v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                      ></span\r\n                      >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                    >\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3; cursor: pointer\"\r\n                    >\r\n                      原文链接\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n                <div class=\"info_flex\" style=\"width: auto\">\r\n                  <div class=\"ArticlBottom\">\r\n                    <div>\r\n                      类型:\r\n                      <span class=\"infomation\">\r\n                        {{ typeHandle(item.sourceType) }}\r\n                      </span>\r\n                    </div>\r\n                    <div>\r\n                      媒体来源:\r\n                      <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.author\">\r\n                      作者:\r\n                      <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                    </div>\r\n                    <div>\r\n                      发布时间:\r\n                      <span class=\"infomation\">{{\r\n                        formatPublishTime(\r\n                          item.publishTime,\r\n                          item.webstePublishTime\r\n                        )\r\n                      }}</span>\r\n                    </div>\r\n                    <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                    <!-- <p>标签A</p> -->\r\n                  </div>\r\n                  <div class=\"ArticlBottom\">\r\n                    <div v-if=\"item.industryName\">\r\n                      所属行业:\r\n                      <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.domain\">\r\n                      所属领域:\r\n                      <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(getSafeSummary(item))\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p> -->\r\n            <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            <!-- </div> -->\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'artificialIntelligence' || flag == 'networkSecurity'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div\r\n              class=\"Articl_left\"\r\n              :style=\"item.fileUrl ? 'width: 85%' : 'width: 100%'\"\r\n            >\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题:</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(getSafeSummary(item))\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\" v-if=\"item.fileUrl\">\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <!-- <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"primary\" plain @click=\"handleUpdate(item)\"\r\n                    v-hasPermi=\"['article:articleList:edit']\">{{ ` 修改 ` }}</el-button>\r\n                </p>\r\n                <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"danger\" plain @click=\"handleDelete(item)\"\r\n                    v-hasPermi=\"['article:list:remove']\">{{ `删除` }}</el-button>\r\n                </p> -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'zhikubaogao'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-upload2\"\r\n              style=\"font-size: 22px; color: #1296db\"\r\n              title=\"批量导入报告\"\r\n              @click=\"openBatchImportDialog\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载报告\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除报告\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek报告解读\"\r\n              @click=\"reportAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"reportAiChat\"\r\n              >Deepseek报告解读</span\r\n            >\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          style=\"height: calc(100vh - 310px)\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <!-- <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span> -->\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(getSafeSummary(item))\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  >删除</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"pagination\" ref=\"pagination\">\r\n      <el-pagination\r\n        :small=\"false\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"currentPage\"\r\n        :page-sizes=\"[10, 20, 30, 40, 50]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"->, total, sizes, prev, pager, next, jumper\"\r\n        :total=\"total\"\r\n      ></el-pagination>\r\n    </div>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title\r\n      :visible.sync=\"tagDialog\"\r\n      width=\"20%\"\r\n      :before-close=\"closeTag\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form\r\n        label-position=\"left\"\r\n        label-width=\"40px\"\r\n        :model=\"formLabelAlign\"\r\n        ref=\"ruleForm\"\r\n      >\r\n        <el-form-item label=\"行业\" prop=\"industry\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.industry\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            placeholder=\"请选择行业\"\r\n            :filterable=\"true\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.industryName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"领域\" prop=\"domain\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.domain\"\r\n            filterable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            clearable\r\n            placeholder=\"请选择领域\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.fieldName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"标签\" prop=\"tag\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.tag\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n            filterable\r\n            allow-create\r\n            default-first-option\r\n            placeholder=\"请添加文章标签\"\r\n          >\r\n            <!-- <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>-->\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeTag\" size=\"mini\">取 消</el-button>\r\n        <el-button @click=\"SubmitTag\" type=\"primary\" size=\"mini\"\r\n          >确 定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n    <el-drawer\r\n      @open=\"openDrawer\"\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"drawer\"\r\n      custom-class=\"drawer_box\"\r\n      direction=\"rtl\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <div\r\n        v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n        style=\"width: 100%; text-align: right; padding-right: 10px\"\r\n      >\r\n        <el-button\r\n          @click=\"documentDownload('drawer')\"\r\n          :type=\"drawerInfo.fileUrl ? 'primary' : 'info'\"\r\n          size=\"mini\"\r\n          plain\r\n          :disabled=\"!drawerInfo.fileUrl\"\r\n          >{{ \"附件下载\" }}</el-button\r\n        >\r\n        <!-- <el-button @click=\"resultEvent('drawer')\" type=\"primary\" size=\"mini\" plain>\r\n          {{ drawerInfo.snapshotUrl\r\n            ? '下载快照' : '生成快照' }}\r\n        </el-button> -->\r\n        <!-- v-if=\"translationBtnShow\" -->\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          plain\r\n          @click=\"translateEvent(drawerInfo)\"\r\n          >翻译内容</el-button\r\n        >\r\n      </div>\r\n      <template slot=\"title\">\r\n        <span class=\"drawer_Title\">{{\r\n          drawerInfo.cnTitle || drawerInfo.title\r\n        }}</span>\r\n      </template>\r\n      <div class=\"drawer_Style\">\r\n        <p class=\"title\">\r\n          {{ drawerInfo.cnTitle || drawerInfo.title }}\r\n          <!-- v-if=\"!drawerInfo.cnTitle\" -->\r\n          <i\r\n            class=\"icon-taizhangtranslate\"\r\n            @click=\"translateTitle(drawerInfo)\"\r\n          ></i>\r\n        </p>\r\n        <p style=\"text-align: center\">\r\n          <span class=\"source\">{{ drawerInfo.sourceName }}</span>\r\n          <span class=\"time\">{{ drawerInfo.publishTime }}</span>\r\n        </p>\r\n        <div\r\n          style=\"user-select: text !important; line-height: 30px\"\r\n          v-html=\"\r\n            drawerInfo.cnContent &&\r\n            drawerInfo.cnContent.replace(/<img\\b[^>]*>/gi, '')\r\n          \"\r\n        ></div>\r\n        <el-empty\r\n          description=\"当前文章暂无数据\"\r\n          v-if=\"!drawerInfo.cnContent\"\r\n        ></el-empty>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"'修改文章'\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"dialog_Box\">\r\n        <el-form\r\n          :model=\"form\"\r\n          class=\"form_Style\"\r\n          label-position=\"top\"\r\n          ref=\"form\"\r\n          :rules=\"rules\"\r\n          size=\"mini\"\r\n        >\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"文章标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"中文标题\" prop=\"cnTitle\">\r\n                <el-input v-model=\"form.cnTitle\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n                <el-select\r\n                  v-model=\"form.sourceType\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.id\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"媒体来源\" prop=\"sourceName\">\r\n                <el-select\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  filterable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeLists\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.name\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发布时间\" prop=\"publishTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.publishTime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"datetime\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  placeholder=\"选择日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"文章作者\" prop=\"author\">\r\n                <el-input\r\n                  v-model=\"form.author\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"原文链接\" prop=\"originalUrl\">\r\n            <el-input v-model=\"form.originalUrl\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"摘要\" prop=\"summary\">\r\n            <el-input\r\n              v-model=\"form.summary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文摘要\" prop=\"cnSummary\">\r\n            <el-input\r\n              v-model=\"form.cnSummary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文内容\" prop=\"cnContent\">\r\n            <editor v-model=\"form.cnContent\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <el-form-item label=\"文章内容\" prop=\"content\">\r\n            <editor v-model=\"form.content\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <!-- <el-form-item label=\"封面图片\" prop=\"cover\">\r\n            <el-upload action=\"#\" ref=\"upload\" :limit=\"3\" :on-exceed=\"exceed\" list-type=\"picture-card\"\r\n              :auto-upload=\"false\" :headers=\"vertifyUpload.headers\" :file-list=\"fileList\" :on-change=\"handleChange\"\r\n              :http-request=\"requestLoad\">\r\n              <i slot=\"default\" class=\"el-icon-plus\"></i>\r\n              <div slot=\"file\" slot-scope=\"{ file }\">\r\n                <img class=\"el-upload-list__item-thumbnail\" :src=\"file.url\" alt=\"文件缩略图加载失败\" />\r\n                <span class=\"el-upload-list__item-actions\">\r\n                  <span v-if=\"!disabled\" class=\"el-upload-list__item-delete\" @click=\"handleRemove(file)\">\r\n                    <i class=\"el-icon-delete\"></i>\r\n                  </span>\r\n                </span>\r\n              </div>\r\n            </el-upload>\r\n          </el-form-item>\r\n          <el-form-item label=\"上传附件\" prop=\"fileUrl\">\r\n            <el-upload class=\"upload-demo\" :action=\"fileUrlurl\" :before-upload=\"beforeUploadUrl\" multiple :limit=\"1\"\r\n              :http-request=\"uploadUrlRequest\" :on-success=\"uploadUrlSuccess\" :file-list=\"fileUrlList\"\r\n              :on-exceed=\"uploadUrlExceed\" :on-remove=\"uploadUrlRemove\">\r\n              <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>\r\n            </el-upload>\r\n          </el-form-item> -->\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量导入弹框 -->\r\n    <el-dialog\r\n      title=\"批量导入报告\"\r\n      :visible.sync=\"batchImportVisible\"\r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"batch-import-container\">\r\n        <!-- 文件选择区域 -->\r\n        <div class=\"file-select-area\">\r\n          <el-upload\r\n            ref=\"batchUpload\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :show-file-list=\"false\"\r\n            multiple\r\n            :on-change=\"handleFileSelect\"\r\n            accept=\".pdf,.doc,.docx,.txt\"\r\n          >\r\n            <el-button type=\"primary\" icon=\"el-icon-upload\">选择文件</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">\r\n              支持选择多个文件，格式：PDF、DOC、DOCX、TXT\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n\r\n        <!-- 文件列表表格 -->\r\n        <div class=\"file-table-area\" v-if=\"batchImportFiles.length > 0\">\r\n          <el-table\r\n            :data=\"batchImportFiles\"\r\n            border\r\n            style=\"width: 100%; margin-top: 20px\"\r\n            max-height=\"300\"\r\n          >\r\n            <el-table-column prop=\"fileName\" label=\"文件名称\" width=\"300\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.fileName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"sourceName\" label=\"数据源名称\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input\r\n                  v-model=\"scope.row.sourceName\"\r\n                  placeholder=\"请输入数据源名称\"\r\n                  size=\"small\"\r\n                ></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"removeFile(scope.$index)\"\r\n                  style=\"color: #f56c6c\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelBatchImport\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"confirmBatchImport\"\r\n          :disabled=\"batchImportFiles.length === 0\"\r\n        >\r\n          确 认\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- Deepseek报告解读弹窗 -->\r\n    <deepseek-report-dialog\r\n      :visible.sync=\"showDeepseekDialog\"\r\n      :article-data=\"currentArticle\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport request from \"@/utils/request\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { MessageBox } from \"element-ui\";\r\nimport axios from \"axios\";\r\nimport { getListClassify } from \"@/api/article/classify\";\r\nimport { saveAs } from \"file-saver\";\r\nimport { blobValidate, tansParams } from \"@/utils/ruoyi\";\r\nimport { articleListEdit, uploadCover } from \"@/api/articleCrawler/list\";\r\nimport DeepseekReportDialog from \"./DeepseekReportDialog.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\nimport { getListByIds } from \"@/api/article/articleHistory\";\r\n\r\nexport default {\r\n  props: {\r\n    downLoadShow: {\r\n      /* 下载按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    editShow: {\r\n      /* 编辑按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    copyShow: {\r\n      /* 复制按钮 */ reuqired: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    height: {\r\n      type: Number,\r\n      default: 655,\r\n    },\r\n    currentPage: {\r\n      reuqired: true,\r\n      default: 1,\r\n    },\r\n    pageSize: {\r\n      reuqired: true,\r\n      default: 50,\r\n    },\r\n    total: {\r\n      reuqired: true,\r\n      default: 0,\r\n    },\r\n    ArticleList: {\r\n      required: true,\r\n      default: [],\r\n    },\r\n    flag: {\r\n      required: true,\r\n    },\r\n    SeachData: {\r\n      required: true,\r\n    },\r\n    keywords: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    // 报告类型字段\r\n    sourceType: {\r\n      default: \"\",\r\n    },\r\n  },\r\n  components: {\r\n    DeepseekReportDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      regExpImg: /^\\n$/,\r\n      reportId: \"\",\r\n      reportOptions: [],\r\n      dialogVisible: false,\r\n      checkedCities: [] /* 多选 */,\r\n      checked: false /* 全选 */,\r\n      html: \"\",\r\n      text: \"\",\r\n      that: this,\r\n      tagShow: false,\r\n      isIndeterminate: true,\r\n      count: 0,\r\n      separate: {},\r\n      /* 标签功能 */\r\n      tagDialog: false,\r\n      formLabelAlign: {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      },\r\n      options: [],\r\n      options1: [],\r\n      tagItem: {} /* 标签对象 */,\r\n      areaList: [] /* 领域 */,\r\n      industry: [] /* 行业 */,\r\n      num: 0,\r\n      timer: null,\r\n      drawer: false,\r\n      drawerInfo: {},\r\n      AreaId: null,\r\n      translationBtnShow: null,\r\n      open: false,\r\n      sourceTypeList: [], // 数据源分类\r\n      sourceLists: [], // 数据源列表\r\n      sourceTypeLists: [],\r\n      form: {}, // 表单参数\r\n      rules: {\r\n        // 表单校验\r\n        title: [{ required: true, message: \"文章标题为必填项\" }],\r\n        content: [{ required: true, message: \"文章详情为必填项\" }],\r\n        publishTime: [{ required: true, message: \"发布时间为必填项\" }],\r\n        cnTitle: [{ required: true, message: \"中文名称为必填项\" }],\r\n        sourceType: [{ required: true, message: \"平台类型为必填项\" }],\r\n        originalUrl: [{ required: true, message: \"原文为必填项\" }],\r\n        summary: [{ required: true, message: \"请填写摘要\" }],\r\n        // cnSummary: [{ required: true, message: '请填写中文摘要' }],\r\n        sn: [{ required: true, message: \"请填写文章地址唯一识别号\" }],\r\n      },\r\n      vertifyUpload: {\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: {\r\n          Authorization: \"Bearer \" + getToken(),\r\n          ContentType: \"application/json;charset=utf-8\",\r\n        },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/article/articleList/cover\",\r\n      },\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      fileUrlurl:\r\n        process.env.VUE_APP_BASE_API + \"/article/articleList/upload/file\",\r\n      showSummary: true,\r\n      // 批量导入相关数据\r\n      batchImportVisible: false,\r\n      batchImportFiles: [],\r\n      // Deepseek报告解读弹窗\r\n      showDeepseekDialog: false,\r\n      currentArticle: {},\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n    };\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    dialogVisible: function (newVal, oldVal) {\r\n      if (newVal) {\r\n        API.getNewBuilt({ sourceType: this.sourceType }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 'formLabelAlign.industry': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options1 = this.industry\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    // 'formLabelAlign.domain': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options = this.areaList\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    \"SeachData.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        this.Refresh();\r\n      },\r\n      deep: true,\r\n    },\r\n    \"form.sourceType\": {\r\n      handler(newVal, oldVal) {\r\n        this.sourceTypeLists = this.sourceLists.filter((item) => {\r\n          return item.type == newVal;\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    if (\r\n      this.flag !== \"MonitorUse\" &&\r\n      this.flag !== \"specialSubjectUse\" &&\r\n      this.flag !== \"Wechat\"\r\n    ) {\r\n      this.openDialog();\r\n    }\r\n    if (this.flag !== \"Wechat\") {\r\n      getListClassify().then((res) => {\r\n        this.sourceTypeList = res.data;\r\n      });\r\n      API.getSourceList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.sourceLists = data.data;\r\n        }\r\n      });\r\n    }\r\n    if (this.$route.query.domain) {\r\n      getConfigKey(\"sys.ai.platform\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.aiPlatform = res.msg;\r\n        }\r\n      });\r\n      getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.articleAiPrompt = res.msg;\r\n        }\r\n      });\r\n      // 获取用户头像\r\n      this.userAvatar = this.$store.getters.avatar;\r\n    }\r\n\r\n    this.showSummary = true;\r\n  },\r\n  updated() {},\r\n  filters: {},\r\n  methods: {\r\n    // 处理科技相关字段的显示映射\r\n    getTechnologyLabel(value) {\r\n      const mapping = {\r\n        0: \"否\",\r\n        1: \"是\",\r\n        2: \"其他\",\r\n        3: \"待定\",\r\n      };\r\n      return mapping[value];\r\n    },\r\n    // 安全处理摘要内容，避免null调用replace报错\r\n    getSafeSummary(item) {\r\n      const cnSummary = item.cnSummary || '';\r\n      const summary = item.summary || '';\r\n\r\n      const processedCnSummary = cnSummary ? cnSummary.replace(\r\n        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g,\r\n        'span'\r\n      ) : '';\r\n\r\n      const processedSummary = summary ? summary.replace(\r\n        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g,\r\n        'span'\r\n      ) : '';\r\n\r\n      return processedCnSummary || processedSummary;\r\n    },\r\n    // 处理发布时间的显示\r\n    formatPublishTime(publishTime, webstePublishTime) {\r\n      // 格式化publishTime为年月日\r\n      const formattedPublishTime = this.parseTime(publishTime, \"{y}-{m}-{d}\");\r\n\r\n      // 如果webstePublishTime不存在，直接返回publishTime\r\n      if (!webstePublishTime) {\r\n        return formattedPublishTime;\r\n      }\r\n\r\n      let formattedWebsteTime = \"\";\r\n      // 处理不同格式的webstePublishTime\r\n      if (webstePublishTime) {\r\n        // 处理2025-04-12 10:09:21.971191格式（包含连字符的标准格式）\r\n        if (webstePublishTime.includes(\"-\")) {\r\n          const dateMatch = webstePublishTime.match(/(\\d{4})-(\\d{2})-(\\d{2})/);\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2];\r\n            const day = dateMatch[3];\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年04月14日 11:29:22格式（中文年月日格式，带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\") &&\r\n          webstePublishTime.includes(\"日\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})日/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年4月15格式（中文年月格式，不带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025/04/14 11:29:22格式（斜杠分隔的格式）\r\n        else if (webstePublishTime.includes(\"/\")) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})\\/(\\d{1,2})\\/(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        } else {\r\n          // 其他格式直接使用原值\r\n          formattedWebsteTime = webstePublishTime;\r\n        }\r\n      }\r\n\r\n      // 比较年月日是否相同\r\n      if (formattedPublishTime === formattedWebsteTime) {\r\n        return formattedPublishTime;\r\n      } else {\r\n        return `${formattedPublishTime} / ${webstePublishTime}`;\r\n      }\r\n    },\r\n\r\n    // 检查文本是否有实际内容（去除HTML标签后）\r\n    hasActualContent(text) {\r\n      if (!text) {\r\n        return false;\r\n      }\r\n      // 去除HTML标签\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      // 检查是否有中文、英文、数字等实际内容\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n    // 关键字替换\r\n    changeColor(str) {\r\n      let Str = str;\r\n      if (Str) {\r\n        let keywords = this.keywords.split(\",\");\r\n        keywords.map((keyitem, keyindex) => {\r\n          if (keyitem && keyitem.length > 0) {\r\n            // 匹配关键字正则\r\n            let replaceReg = new RegExp(keyitem, \"g\");\r\n            // 高亮替换v-html值\r\n            let replaceString =\r\n              '<span class=\"highlight\"' +\r\n              ' style=\"color: red;\">' +\r\n              keyitem +\r\n              \"</span>\";\r\n            Str = Str.replace(replaceReg, replaceString);\r\n          }\r\n        });\r\n      }\r\n      return Str;\r\n    },\r\n    /* 下载Excel */\r\n    async downLoadExcel() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要导出的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.flag == \"specialSubjectUse\") {\r\n        API.downLoadExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n        });\r\n      } else {\r\n        await API.downLoadExportExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n\r\n          // saveAs(blob, `source_${new Date().getTime()}.xlsx`)\r\n        });\r\n      }\r\n    },\r\n    batchDelete() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要删除的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.checkedCities.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 发布到每日最新热点 */\r\n    publishHot() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({\r\n          message: \"请选择要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.checkedCities.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 返回顶部动画 */\r\n    mainScorll() {\r\n      var scrollStep = -this.$refs.scroll.scrollTop / (800 / 15); // 计算每一步滚动的距离\r\n      var scrollInterval = setInterval(() => {\r\n        if (this.$refs.scroll.scrollTop !== 0) {\r\n          this.$refs.scroll.scrollBy(0, scrollStep); // 按照给定步长滚动窗口\r\n        } else {\r\n          clearInterval(scrollInterval); // 到达顶部时清除定时器\r\n        }\r\n      }, 15);\r\n    },\r\n    scrollChange() {\r\n      clearTimeout(this.timer);\r\n      this.timer = setTimeout(() => {\r\n        this.stopScroll();\r\n      }, 500);\r\n      // this.$refs.pagination.style.opacity = 0\r\n      // this.$refs.pagination.style.transition = '0'\r\n    } /* 滚动事件 */,\r\n    stopScroll() {\r\n      // this.$refs.pagination.style.transition = '1s'\r\n      // this.$refs.pagination.style.opacity = 1\r\n    },\r\n    /* 下载 */\r\n    downLoad() {\r\n      this.dialogVisible = true;\r\n    },\r\n    /* 每页条数变化 */\r\n    handleSizeChange(num) {\r\n      this.$emit(\"handleSizeChange\", num);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 页码变化 */\r\n    handleCurrentChange(current) {\r\n      this.$emit(\"handleCurrentChange\", current);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 收藏 */\r\n    async collect(item) {\r\n      /* 点击列表收藏 */\r\n      if (item.id) {\r\n        this.checkedCities = [item.id];\r\n      }\r\n      /* 未选择提示 */\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要收藏的文章\", type: \"info\" });\r\n        return;\r\n      }\r\n      /* 收藏 */\r\n      if (!item.favorites) {\r\n        let res = await API.collectApi([item.id]);\r\n        if (res.code) {\r\n          this.$message({\r\n            message: \"收藏成功,请前往个人中心查看\",\r\n            type: \"success\",\r\n          });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"收藏失败\", type: \"info\" });\r\n      } else {\r\n        let res = await API.cocelCollect([item.id]);\r\n        if (res.code) {\r\n          this.$message({ message: \"已取消收藏\", type: \"success\" });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"取消收藏失败\", type: \"info\" });\r\n      }\r\n    },\r\n    /* 一键复制 */\r\n    copyText(item) {\r\n      navigator.clipboard\r\n        .writeText(item.cnTitle)\r\n        .then(() => {\r\n          this.$message({ message: \"已成功复制到剪贴板\", type: \"success\" });\r\n        })\r\n        .catch(function () {\r\n          alert(\"复制失败\");\r\n        });\r\n    },\r\n    typeHandle(data) {\r\n      if (data == 1) {\r\n        return \"微信公众号\";\r\n      } else if (data == 2) {\r\n        return \"网站\";\r\n      } else if (data == 3) {\r\n        return \"手动录入\";\r\n      }\r\n    },\r\n    /* 选择事件 */\r\n    handleCheckedCitiesChange(value) {\r\n      this.checkedCities = value;\r\n    },\r\n    /* 全选 */\r\n    handleCheckAllChange(val) {\r\n      this.checkedCities = val ? this.ArticleList.map((item) => item.id) : [];\r\n      this.isIndeterminate = false;\r\n    },\r\n    /* 刷新 */\r\n    Refresh() {\r\n      this.$emit(\"Refresh\");\r\n    },\r\n    /*确定添加到报告 */\r\n    async reportSubmit() {\r\n      this.dialogVisible = false;\r\n      let keyWordList = [];\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      /* 单独添加 */\r\n      if (this.separate.id) {\r\n        // let keyword = Object.keys(this.separate.keywordCount)\r\n        keyWordList.push({\r\n          reportId: this.reportId,\r\n          listId: this.separate.id,\r\n          listSn: this.separate.sn,\r\n        });\r\n      } else {\r\n        /* 批量添加 */\r\n        if (this.checkedCities == \"\")\r\n          return this.$message({\r\n            message: \"请选择要添加的数据\",\r\n            type: \"warning\",\r\n          });\r\n        this.checkedCities.forEach((item) => {\r\n          let article = this.ArticleList.filter((value) => value.id == item);\r\n          keyWordList.push({\r\n            reportId: this.reportId,\r\n            listId: item,\r\n            listSn: article[0].sn,\r\n          });\r\n        });\r\n      }\r\n      let res = await API.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.$emit(\"Refresh\");\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.separate = {};\r\n      this.reportId = \"\";\r\n      this.checkedCities = [];\r\n      this.checked = false;\r\n    },\r\n    /* 单独添加报告 */\r\n    separateAdd(item) {\r\n      this.dialogVisible = true;\r\n      this.separate = item;\r\n    },\r\n    /* 跳转新页面 */\r\n    openNewView(item, isLink) {\r\n      if (isLink) {\r\n        if (item.originalUrl) {\r\n          window.open(item.originalUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n        return;\r\n      }\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n      // this.drawerInfo = item\r\n      // this.drawer = true\r\n    },\r\n    /* 文章打标签 */\r\n    tagHandler(item) {\r\n      this.tagDialog = true;\r\n      this.tagItem = item;\r\n      if (item.industry) {\r\n        this.formLabelAlign.industry = item.industry\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      if (item.domain) {\r\n        this.formLabelAlign.domain = item.domain\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      this.formLabelAlign.tag = item.tags ? item.tags.split(\",\") : \"\";\r\n    },\r\n    /* 获取领域和分类 */\r\n    async openDialog() {\r\n      await API.areaList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.areaList = data.data;\r\n          this.options = data.data;\r\n          API.industry().then((value) => {\r\n            this.industry = value.data;\r\n            this.options1 = value.data;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 筛选领域 */\r\n    remoteEvent(query) {\r\n      this.options = this.areaList.filter((item) => item.fieldName == query);\r\n    },\r\n    /* 筛选行业 */\r\n    remoteIndustry(query) {\r\n      this.options1 = this.industry.filter(\r\n        (item) => item.industryName == query\r\n      );\r\n    },\r\n    async SubmitTag() {\r\n      let params = {\r\n        domain: String(this.formLabelAlign.domain),\r\n        industry: String(this.formLabelAlign.industry),\r\n        tags: String(this.formLabelAlign.tag),\r\n        articleId: String(this.tagItem.id),\r\n        docId: this.tagItem.docId ? String(this.tagItem.docId) : \"\",\r\n      };\r\n      let res = await API.tagAdd(params);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"保存成功\", type: \"success\" });\r\n        setTimeout(() => {\r\n          this.Refresh();\r\n        }, 1000);\r\n      } else {\r\n        this.$message({ message: \"保存失败\", type: \"error\" });\r\n      }\r\n      this.closeTag();\r\n    },\r\n    closeTag() {\r\n      this.$refs[\"ruleForm\"].resetFields();\r\n      this.formLabelAlign = {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      };\r\n      this.tagDialog = false;\r\n    },\r\n    async hotIncrease(item) {\r\n      let isWhether = JSON.parse(item.isWhether);\r\n      let res = await API.tagAdd({\r\n        articleId: item.id,\r\n        isWhether: +!Boolean(isWhether),\r\n      });\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"操作成功\", type: \"success\" });\r\n        this.Refresh();\r\n      } else {\r\n        this.$message({ message: \"操作失败\", type: \"error\" });\r\n      }\r\n    },\r\n    async openDrawer() {\r\n      let docId = this.drawerInfo.docId;\r\n      await API.AreaInfo(this.drawerInfo.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.drawerInfo = res.data;\r\n          this.drawerInfo.docId = docId;\r\n          /* 将字符串中的\\n替换为<br> */\r\n          this.translationBtnShow = !this.drawerInfo.cnContent;\r\n          if (this.drawerInfo.cnContent || this.drawerInfo.content) {\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\\\n/g, (a, b, c) => {\r\n              return \"<br>\";\r\n            });\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\${[^}]+}/g, \"<br>\");\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(\"|xa0\", \"\");\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /* 所属行业处理 */\r\n    industryHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.industry) {\r\n        ids = item.industry.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.industry.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.industryName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    domainHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.domain) {\r\n        ids = item.domain.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.areaList.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.fieldName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    /* 快照生成 */\r\n    resultEvent(item) {\r\n      if (item == \"BatchGeneration\" && this.checkedCities.length == 0) {\r\n        this.$message.warning(\"请先选择文章\");\r\n        return;\r\n      }\r\n      let ids = null;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (item == \"drawer\") {\r\n        ids = [this.drawerInfo.id];\r\n        if (this.drawerInfo.snapshotUrl) zhuangtai = \"查看\";\r\n        url = this.drawerInfo.snapshotUrl;\r\n      } else if (item == \"BatchGeneration\") {\r\n        ids = this.checkedCities;\r\n      } else {\r\n        ids = [item.id];\r\n        if (item.snapshotUrl) zhuangtai = \"查看\";\r\n        url = item.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        if (this.flag == \"MonitorUse\") {\r\n          API.downLoadExportKe(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        } else {\r\n          API.downLoadExportZhuan(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        }\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n    /* 附件下载 */\r\n    async documentDownload(item) {\r\n      this.loading = true;\r\n      if (item.fileUrl) {\r\n        const urls = item.fileUrl.split(\",\");\r\n        for (const [index, url] of urls.entries()) {\r\n          if (url.indexOf(\"https://\") === -1) {\r\n            setTimeout(async () => {\r\n              await this.downLoadFun(url, index, item.cnTitle || item.title);\r\n            }, index * 500);\r\n          } else {\r\n            this.$message.error(\"附件还没同步到当前系统，暂时无法下载\");\r\n          }\r\n        }\r\n      }\r\n      this.loading = false;\r\n    },\r\n\r\n    async downLoadFun(url, index, title) {\r\n      let formData = new FormData();\r\n      formData.append(\"fileUrl\", url);\r\n\r\n      try {\r\n        const response = await API.downloadFile(formData);\r\n        const isBlob = blobValidate(response);\r\n\r\n        if (isBlob) {\r\n          const blob = new Blob([response]);\r\n          let list = url.split(\"/\");\r\n          let fileName = list[list.length - 1];\r\n          saveAs(blob, fileName);\r\n        } else {\r\n          const resText = await response.text();\r\n          const rspObj = JSON.parse(resText);\r\n          const errMsg =\r\n            errorCode[rspObj.code] || rspObj.msg || errorCode[\"default\"];\r\n          this.$message.error(errMsg);\r\n        }\r\n      } catch (err) {\r\n        // this.$message.error(`Error downloading file: ${err}`);\r\n      } finally {\r\n        // 确保 loading 在每次下载后都设置为 false\r\n        this.loading = false;\r\n      }\r\n\r\n      // 之前的附件下载\r\n      // if (item.annexUrl) {\r\n      //   /* 有文件地址 直接下载 */\r\n      //   let formData = new FormData()\r\n      //   formData.append('id', item.id)\r\n      //   this.loading = true\r\n      //   API.documentDownloadKe(formData).then(res => {\r\n      //     let a = document.createElement('a')\r\n      //     a.href = URL.createObjectURL(res.data)\r\n      //     a.download = res.headers['content-disposition'].split('filename=')[1]\r\n      //     a.click()\r\n      //     this.loading = false\r\n      //   })\r\n      // } else {\r\n      //   /* 没有文件格式 申请下载 */\r\n      //   API.documentDownload(id).then(response => {\r\n      //     if (response.code == 200) {\r\n      //       this.$msgbox({\r\n      //         title: '提示',\r\n      //         message: '附件正在同步中，请稍后下载',\r\n      //         showCancelButton: true,\r\n      //         confirmButtonText: '关闭',\r\n      //         cancelButtonText: '取消',\r\n      //         showCancelButton: false,\r\n      //         beforeClose: (action, instance, done) => {\r\n      //           done()\r\n      //         }\r\n      //       })\r\n      //     } else {\r\n      //       this.$message({ message: '附件下载失败', type: 'error' })\r\n      //     }\r\n      //   }).catch(err => { })\r\n      // }\r\n    },\r\n    // 翻译标题\r\n    translateTitle(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.title,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"title\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    // 翻译文章\r\n    translateEvent(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.content,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"content\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          this.translationBtnShow = false;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      API.AreaInfo(row.id).then((response) => {\r\n        this.form = response.data;\r\n        this.form.sourceType = Number(this.form.sourceType);\r\n        this.form.docId = row.docId;\r\n        // this.fileUrlList = this.form.fileUrl ? this.form.fileUrl.split(\",\").map(item => {\r\n        //   return {\r\n        //     name: item,\r\n        //     url: item\r\n        //   }\r\n        // }) : []\r\n        this.open = true;\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal\r\n        .confirm('是否确认删除该条文章？\"')\r\n        .then(() => {\r\n          return API.monitoringEsRemove({ id: row.id, docId: row.docId });\r\n        })\r\n        .then(() => {\r\n          this.Refresh();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          let queryForm = JSON.parse(JSON.stringify(this.form));\r\n          // let cover = String(this.fileList.map(item => item.path))\r\n          // queryForm.cover = cover\r\n          articleListEdit(queryForm).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.Refresh();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 自定义上传 */\r\n    async requestLoad(file) {\r\n      let data = new FormData();\r\n      data.append(\"cover\", file.file);\r\n      await uploadCover(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.fileList.map((item) => {\r\n            if (item.uid == file.file.uid) {\r\n              item.path = response.imgUrl;\r\n            }\r\n          });\r\n          this.$message({ message: \"上传成功\", type: \"success\" });\r\n        } else {\r\n          this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    /* 文件超出限制 */\r\n    exceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传三个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    /* 移除文件 */\r\n    handleRemove(file) {\r\n      this.fileList = this.fileList.filter((item) => item !== file);\r\n    },\r\n    // 文件更改\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList;\r\n      this.$refs.upload.submit();\r\n    },\r\n    // 上传附件校验\r\n    beforeUploadUrl(file) {\r\n      // 判断文件是否为excel\r\n      let fileName = file.name\r\n          .substring(file.name.lastIndexOf(\".\") + 1)\r\n          .toLowerCase(),\r\n        condition =\r\n          fileName == \"pdf\" ||\r\n          fileName == \"doc\" ||\r\n          fileName == \"xls\" ||\r\n          fileName == \"ppt\" ||\r\n          fileName == \"xlsx\" ||\r\n          fileName == \"pptx\" ||\r\n          fileName == \"docx\";\r\n      let fileSize = file.size / 1024 / 1024 < 10;\r\n      if (!condition) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件必须是pdf,doc,xls,ppt,xlsx,pptx,docx格式\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      /* 文件大小限制 */\r\n      if (!fileSize) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件的大小不能超过 10MB!\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      return condition && fileSize;\r\n    },\r\n    // 文件上传成功\r\n    uploadUrlSuccess(res, file) {\r\n      this.$message({ message: \"上传成功\", type: \"success\" });\r\n    },\r\n    // 文件上传超出限制\r\n    uploadUrlExceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传1个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    // 文件上传方法\r\n    uploadUrlRequest(file) {\r\n      if (this.form.originalUrl != null && this.form.originalUrl != \"\") {\r\n        if (\r\n          this.form.originalUrl.match(\r\n            /(http|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/\r\n          )\r\n        ) {\r\n          let data = new FormData();\r\n          data.append(\"file\", file.file);\r\n          data.append(\"originalUrl\", this.form.originalUrl);\r\n\r\n          API.uploadFile(data).then((response) => {\r\n            if (response.code == 200) {\r\n              this.$message({ message: \"上传成功\", type: \"success\" });\r\n              this.form.fileUrl = response.data;\r\n            } else {\r\n              this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n              this.form.fileUrl = \"\";\r\n            }\r\n          });\r\n        } else {\r\n          this.$message({ message: \"请填写正确的原文链接\", type: \"warning\" });\r\n          this.fileUrlList = [];\r\n        }\r\n      } else {\r\n        this.$message({ message: \"请填写原文链接\", type: \"warning\" });\r\n        this.fileUrlList = [];\r\n      }\r\n    },\r\n    // 删除附件\r\n    uploadUrlRemove() {\r\n      API.removeFile({ filePath: this.form.fileUrl }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$message({ message: \"删除成功\", type: \"success\" });\r\n          this.fileUrlList = [];\r\n          this.form.fileUrl = \"\";\r\n        } else {\r\n          this.$message({ message: \"删除失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        articleSn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceName: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: null,\r\n        deptId: null,\r\n        content: null,\r\n        cnContent: null,\r\n        fileUrl: null,\r\n        industry: null,\r\n        domain: null,\r\n        tmpUrl: null,\r\n        isFinish: null,\r\n        groupId: null,\r\n        appId: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 批量导入相关方法\r\n    // 打开批量导入弹框\r\n    openBatchImportDialog() {\r\n      this.batchImportVisible = true;\r\n      this.batchImportFiles = [];\r\n    },\r\n    // 文件选择处理\r\n    handleFileSelect(file, fileList) {\r\n      // 将新选择的文件添加到列表中\r\n      const newFiles = fileList.map((item) => ({\r\n        fileName: item.name,\r\n        file: item.raw,\r\n        sourceName: \"\",\r\n      }));\r\n      this.batchImportFiles = newFiles;\r\n    },\r\n    // 删除文件\r\n    removeFile(index) {\r\n      this.batchImportFiles.splice(index, 1);\r\n    },\r\n    // 取消批量导入\r\n    cancelBatchImport() {\r\n      this.batchImportVisible = false;\r\n      this.batchImportFiles = [];\r\n      // 清空文件选择器\r\n      this.$refs.batchUpload.clearFiles();\r\n    },\r\n    // 确认批量导入\r\n    async confirmBatchImport() {\r\n      // 验证数据源名称是否都已填写\r\n      const emptySourceNames = this.batchImportFiles.filter(\r\n        (item) => !item.sourceName.trim()\r\n      );\r\n      if (emptySourceNames.length > 0) {\r\n        this.$message({\r\n          message: \"请为所有文件填写数据源名称\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 创建FormData对象\r\n        const formData = new FormData();\r\n\r\n        // 添加文件到FormData\r\n        this.batchImportFiles.forEach((item) => {\r\n          formData.append(\"files\", item.file);\r\n        });\r\n\r\n        // 获取数据源名称数组\r\n        const sourceNames = this.batchImportFiles\r\n          .map((item) => item.sourceName)\r\n          .join(\",\");\r\n\r\n        formData.append(\"sourceNames\", sourceNames);\r\n\r\n        // 调用批量导入API，传递FormData和sourceNames参数\r\n        const response = await API.batchImportReports(formData);\r\n\r\n        if (response.code === 200) {\r\n          this.$message({\r\n            message: \"批量导入成功\",\r\n            type: \"success\",\r\n          });\r\n          this.batchImportVisible = false;\r\n          this.batchImportFiles = [];\r\n          this.$refs.batchUpload.clearFiles();\r\n          // 刷新列表\r\n          this.Refresh();\r\n        } else {\r\n          this.$message({\r\n            message: response.msg || \"批量导入失败\",\r\n            type: \"error\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"批量导入错误:\", error);\r\n        this.$message({\r\n          message: \"批量导入失败，请稍后重试\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    reportAiChat() {\r\n      this.showDeepseekDialog = true;\r\n      if (this.checkedCities.length === 0) {\r\n        this.$message({ message: \"请选择要解读的文章\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.checkedCities.length > 1) {\r\n        this.$message({ message: \"请只选择一篇文章进行解读\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      // 获取选中的文章\r\n      const selectedArticleId = this.checkedCities[0];\r\n      const selectedArticle = this.ArticleList.find(\r\n        (item) => item.id === selectedArticleId\r\n      );\r\n\r\n      if (selectedArticle) {\r\n        this.currentArticle = selectedArticle;\r\n        this.showDeepseekDialog = true;\r\n      } else {\r\n        this.$message({ message: \"未找到选中的文章\", type: \"error\" });\r\n      }\r\n    },\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.checkedCities.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* drawer样式修改 */\r\n.main ::v-deep .el-drawer.drawer_box {\r\n  width: 700px !important;\r\n}\r\n\r\n.MainArticle {\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  // margin-top: 10px;\r\n  user-select: text;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    align-items: center;\r\n    border-bottom: solid 1px #e2e2e2;\r\n  }\r\n\r\n  .leftBtnGroup {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    gap: 15px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    & > p {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .leftBtnGroup2 {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    gap: 15px;\r\n    flex: 1;\r\n    padding-left: 20px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      line-height: 36px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .scollBox {\r\n    overflow-y: auto;\r\n    height: calc(100vh - 389px);\r\n    transition: transform 5s ease;\r\n\r\n    .Articl {\r\n      display: flex;\r\n      width: 100%;\r\n      border-bottom: solid 1px #d4d4d4;\r\n\r\n      .Articl_left {\r\n        width: 85%;\r\n        padding-bottom: 16px;\r\n      }\r\n\r\n      .Articl_kqcb {\r\n        width: 100%;\r\n        padding-bottom: 16px;\r\n        & > div:first-child {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n        }\r\n      }\r\n\r\n      .ArticlTop {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding: 0 0 0 20px;\r\n        font-size: 15px;\r\n      }\r\n\r\n      .ArticlMain {\r\n        padding: 0 0 0 30px;\r\n        color: #3f3f3f;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .ArticlMain > span:hover {\r\n        color: #1889f3;\r\n        border-bottom: solid 1px #0798f8;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .info_flex {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n      }\r\n\r\n      .ArticlBottom {\r\n        padding: 0 0 0 30px;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 0 25px;\r\n        line-height: 24px;\r\n        color: #9b9b9b;\r\n        font-size: 14px;\r\n\r\n        div {\r\n          text-overflow: ellipsis;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .linkStyle:hover {\r\n          border-bottom: solid 1px #1889f3;\r\n        }\r\n\r\n        .infomation {\r\n          color: #464749;\r\n          font-size: 14px;\r\n          margin-left: 8px;\r\n        }\r\n\r\n        p {\r\n          border: solid 1px #f0a147;\r\n          width: 45px;\r\n          height: 25px;\r\n          line-height: 25px;\r\n          font-size: 14px;\r\n          color: #f0a147;\r\n          text-align: center;\r\n        }\r\n      }\r\n\r\n      .imgBox {\r\n        padding: 0 20px 0 30px;\r\n        display: flex;\r\n        gap: 15px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.pagination {\r\n  z-index: 2;\r\n  background-color: rgba(255, 255, 255, 1);\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  padding: 10px 0;\r\n}\r\n\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n  line-height: 16px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n\r\n.drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n.drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n.btnBox {\r\n  z-index: 2;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n\r\n  & > p {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 批量导入弹框样式\r\n.batch-import-container {\r\n  .file-select-area {\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    background-color: #fafafa;\r\n\r\n    &:hover {\r\n      border-color: #409eff;\r\n    }\r\n  }\r\n\r\n  .file-table-area {\r\n    margin-top: 20px;\r\n\r\n    .el-table {\r\n      border-radius: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #1296db;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #1296db;\r\n}\r\n\r\n.deepseek-text {\r\n  color: #1296db; // 使用与图标相同的颜色\r\n  margin-left: 4px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style>\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n</style>\r\n"]}]}