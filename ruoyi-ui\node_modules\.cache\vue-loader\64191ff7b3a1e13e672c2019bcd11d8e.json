{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\networkSecurity.vue?vue&type=style&index=0&id=9071903a&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\networkSecurity.vue", "mtime": 1754010111790}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudHJlZUJveCB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA5M3B4KTsNCiAgb3ZlcmZsb3cteTogc2Nyb2xsOw0KfQ0KDQoudHJlZU1haW4gew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi50cmVlUXVlcnkgew0KICA6OnYtZGVlcCAuZWwtaW5wdXQtLW1pbmkgLmVsLWlucHV0X19pbm5lciB7DQogICAgaGVpZ2h0OiAyNHB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICAgIHBhZGRpbmc6IDAgNHB4Ow0KICB9DQoNCiAgOjp2LWRlZXAgLmVsLWlucHV0X19zdWZmaXggew0KICAgIGhlaWdodDogMjBweDsNCiAgICByaWdodDogLTJweDsNCiAgICB0b3A6IDVweDsNCiAgfQ0KfQ0KDQoudG9vbEJveCB7DQogIG1pbi1oZWlnaHQ6IDEzMHB4Ow0KICBoZWlnaHQ6IGF1dG87DQogIHBhZGRpbmctYm90dG9tOiAxNXB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1LCAyNTUsIDI1NSk7DQogIGJvcmRlci1sZWZ0OiBzb2xpZCAxcHggcmdiKDIyMSwgMjE5LCAyMTkpOw0KDQogIC50aXRsZSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgaGVpZ2h0OiA3MHB4Ow0KICAgIHBhZGRpbmc6IDAgMzBweDsNCiAgICBmb250LXNpemU6IDE5cHg7DQogIH0NCg0KICAubWFpblRvb2wgew0KICAgIHBhZGRpbmc6IDAgMjhweDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgY29sb3I6IHJnYig1OCwgNTgsIDU4KTsNCiAgfQ0KDQogIC5tYWluVG9vbE9uZSB7DQogICAgbWFyZ2luLXRvcDogMTVweDsNCiAgICBoZWlnaHQ6IGF1dG87DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCiAgICAvLyBhbGlnbi1pdGVtczogY2VudGVyOw0KICB9DQoNCiAgLm1haW5Ub29sVHdvIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgaGVpZ2h0OiA0MHB4Ow0KDQogICAgcCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIGdhcDogMTBweDsNCiAgICB9DQogIH0NCg0KICAuYnRuIHsNCiAgICBtYXJnaW46IDE1cHggMCAwIDI1cHg7DQogIH0NCn0NCg0KLmtleXdvcmQgew0KICB3aWR0aDogMTAwJTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogIC5oaXN0b3J5IHsNCiAgICB3aWR0aDogNDMwcHg7DQogICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgei1pbmRleDogOTk5OTsNCiAgICBsZWZ0OiA2NXB4Ow0KICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYigyMjEsIDIxOSwgMjE5KTsNCg0KICAgIC5oaXN0b3J5SXRlbSB7DQogICAgICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQoNCiAgICAgIC5oaXN0b3J5VGV4dCB7DQogICAgICAgIHdpZHRoOiA0NTBweDsNCiAgICAgICAgaGVpZ2h0OiAzNHB4Ow0KICAgICAgICBsaW5lLWhlaWdodDogMzRweDsNCiAgICAgIH0NCg0KICAgICAgJjpudGgtbGFzdC1vZi10eXBlKDEpIHsNCiAgICAgICAgcGFkZGluZy1sZWZ0OiAwOw0KDQogICAgICAgIDo6di1kZWVwIC5lbC1idXR0b24tLXRleHQgew0KICAgICAgICAgIHBhZGRpbmc6IDEwcHggMjBweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQouaGlzdG9yeSB7DQogIHdpZHRoOiA1MzBweDsNCg0KICAuaGlzdG9yeUl0ZW0gew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIHBhZGRpbmc6IDAgMTBweDsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KDQogICAgLmhpc3RvcnlUZXh0IHsNCiAgICAgIHdpZHRoOiAzNTBweDsNCiAgICAgIGhlaWdodDogMzRweDsNCiAgICAgIGxpbmUtaGVpZ2h0OiAzNHB4Ow0KICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["networkSecurity.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4fA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "networkSecurity.vue", "sourceRoot": "src/views/domainClassification", "sourcesContent": ["<template>\r\n  <div v-if=\"funEsSeach\">\r\n    <splitpanes class=\"default-theme\">\r\n      <pane class=\"leftLink\" ref=\"leftLink\" min-size=\"20\" max-size=\"50\" size=\"25\">\r\n        <div class=\"treeMain\" style=\"width: 100%;margin:0;\">\r\n          <div style=\"display:flex;justify-content:space-between;align-items:center;gap:10px\">\r\n            <el-input placeholder=\"输入关键字进行过滤\" v-model=\"filterText\" clearable class=\"input_Fixed\">\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </div>\r\n          <div class=\"treeBox\">\r\n            <el-tree :data=\"treeData\" ref=\"tree\" show-checkbox node-key=\"id\" :default-expanded-keys=\"[1000]\"\r\n              @check-change=\"checkChange\" :expand-on-click-node=\"false\" :filter-node-method=\"filterNode\">\r\n              <template slot-scope=\"scoped\">\r\n                <div\r\n                  v-if=\"scoped.data.label != ''\"\r\n                  style=\"display: flex; align-items: center\"\r\n                >\r\n                  <div>{{ scoped.data.label }}</div>\r\n                  <div v-if=\"scoped.data.country && scoped.data.country !== '0'\" style=\"display: flex; align-items: center\">\r\n                    <div>[</div>\r\n                    <dict-tag\r\n                      :options=\"dict.type.country\"\r\n                      :value=\"scoped.data.country\"\r\n                    />\r\n                    <div>]</div>\r\n                  </div>\r\n                  <div style=\"font-weight: 600\">\r\n                    {{ `${scoped.data.count ? `(${scoped.data.count})` : \"\"}` }}\r\n                  </div>\r\n                </div>\r\n                <div v-else>\r\n                  {{ scoped.data.label }}\r\n                  <div\r\n                    style=\"position: absolute;z-index: 99;right: 10px;top: -7px;height: 35px;display: flex;align-items: center;\">\r\n                    <el-select v-model=\"treeQuery.isStability\" size=\"mini\" placeholder=\"请选择稳定源\"\r\n                      style=\"width: 60px;height: 20px;margin: 0px 10px 0 0;\" class=\"treeQuery\">\r\n                      <el-option label=\"全部\" :value=\"null\"></el-option>\r\n                      <el-option label=\"稳定源\" :value=\"1\"></el-option>\r\n                      <el-option label=\"不稳定源\" :value=\"0\"></el-option>\r\n                    </el-select>\r\n                    <el-select v-model=\"sortMode\" size=\"mini\" placeholder=\"请选择排序方式\" @change=\"treeSlot\"\r\n                      style=\"width: 60px;height: 20px;margin: 0px 10px 0 0;\" class=\"treeQuery\">\r\n                      <el-option label=\"按数量倒向排序\" :value=\"1\"></el-option>\r\n                      <el-option label=\"按权重排序\" :value=\"0\"></el-option>\r\n                    </el-select>\r\n                    <el-tooltip class=\"item\" effect=\"dark\" content=\"重置\" placement=\"top\">\r\n                      <i class=\"el-input__icon el-icon-refresh\" @click=\"treeClear\"></i>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </el-tree>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"75\">\r\n        <div class=\"rightMain\" style=\"margin-left: 0;overflow: auto;\">\r\n          <div class=\"toolBox\">\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button size=\"mini\" :type=\"SeachData.timeRange == '' ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = ''\">24小时</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\">今天</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\">昨天</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\">近7天</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\">近30天</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\">自定义</el-button>\r\n                <el-date-picker value-format=\"yyyy-MM-dd HH:mm:ss\" v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\" style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\" type=\"datetimerange\" range-separator=\"至\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\"\r\n                  unlink-panels clearable></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button size=\"mini\" :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\">24小时</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\">今天</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\">昨天</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\">近7天</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\">近30天</el-button>\r\n                <el-button size=\"mini\" :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\">自定义</el-button>\r\n                <el-date-picker value-format=\"yyyy-MM-dd HH:mm:ss\" v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\" style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\" type=\"datetimerange\" range-separator=\"至\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\"\r\n                  unlink-panels clearable></el-date-picker>\r\n              </p>\r\n              <p>\r\n                <span style=\"width:60px;display:inline-block;text-align:right;margin-right:5px\">是否与科技有关:</span>\r\n                <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                  <el-radio-button v-for=\"(dict, index) in dict.type.is_technology\" :label=\"dict.value\"\r\n                    :key=\"'is_technology' + dict.value\">{{ dict.label }}</el-radio-button>\r\n                </el-radio-group>\r\n              </p>\r\n              <div class=\"keyword\">\r\n                <span style=\"width:60px;display:inline-block;text-align:right;margin-right:5px\">关键词:</span>\r\n                <el-input ref=\"keywordRef\" placeholder=\"请输入关键词,使用逗号分割(英文)\" style=\"width:430px\" v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\" @blur=\"hideHistoryList()\">\r\n                </el-input>\r\n                <el-button type=\"primary\" size=\"mini\" @click=\"funEsSeach\" :loading=\"buttonDisabled\" style=\"margin-left: 10px; height: 36px\">搜索</el-button>\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div class=\"historyItem\" v-for=\"(history, index) in historyList\" :key=\"index\"\r\n                    v-loading=\"historyLoading\">\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">{{ history.keyword }}</div>\r\n                    <el-button type=\"text\" @click=\"removeHistory(history, 1)\">删除</el-button>\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\">更多</el-button>\r\n                    <el-button type=\"text\" @click=\"clearHistory()\">清空</el-button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n            </div>\r\n          </div>\r\n          <MainArticle v-loading=\"buttonDisabled\" :flag=\"'artificialIntelligence'\" :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\" :total=\"total\" :ArticleList=\"ArticleList\" :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\" @handleSizeChange=\"handleSizeChange\" @Refresh=\"funEsSeach\"\r\n            :SeachData=\"SeachData\"></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog title=\"关键词历史\" :visible.sync=\"dialogVisible1\" width=\"570px\" :close-on-click-modal=\"false\">\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div class=\"historyItem\" v-for=\"(history, index) in historyList1\" :key=\"index\">\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">{{ history.keyword }}</div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\">删除</el-button>\r\n        </div>\r\n      </div>\r\n      <pagination v-show=\"total1 > 0\" :total=\"total1\" :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\" :background=\"false\" @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\" />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from '@/api/ScienceApi/index.js'\r\nimport MainArticle from '../components/MainArticle.vue'\r\nimport { listArticleHistory, delArticleHistory, addArticleHistory, cleanArticleHistory } from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from 'splitpanes'\r\nimport 'splitpanes/dist/splitpanes.css'\r\n\r\nexport default {\r\n  components: { MainArticle, Splitpanes, Pane },\r\n  dicts: ['is_technology', 'country'],\r\n  data() {\r\n    return {\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      /* 左侧tree数据 */\r\n      filterText: '',\r\n      treeData: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: '' /* 匹配模式 */,\r\n        keyword: '' /* 关键词 */,\r\n        sortMode: '0' /* 排序模式 */,\r\n        timeRange: '4' /* 时间范围 */,\r\n        customDay: '' /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: '' /* 自定义天 */,\r\n        isTechnology: 1,\r\n      } /* 搜索条件 */,\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      funEsSeach: false,\r\n      treeQuery: {\r\n        isStability: 1,\r\n        industry: null,\r\n        domain: null\r\n      },\r\n      countBySourceName: null,\r\n      sortMode: 1,\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n    }\r\n  },\r\n  async created() {\r\n    this.getArticleHistory()\r\n    this.getTree()\r\n    this.funEsSeach = this.debounce(this.EsSeach, 200)\r\n  },\r\n  watch: {\r\n    filterText(val) {\r\n      this.$refs.tree.filter(val)\r\n    },\r\n    'treeQuery': {\r\n      handler(newValue, oldValue) {\r\n        this.getTree();\r\n      },\r\n      deep: true // 开启深度监听\r\n    },\r\n    \"SeachData.timeRange\"(newVal, oldVal) {\r\n      this.SeachData.customDay = [];\r\n      if (newVal !== 6) {\r\n        this.funEsSeach();\r\n      }\r\n    },\r\n    \"SeachData.customDay\"(newVal, oldVal) {\r\n      if (newVal.length == 0) {\r\n        return;\r\n      }\r\n      this.funEsSeach();\r\n    },\r\n    \"SeachData.collectionDateType\"(newVal, oldVal) {\r\n      this.SeachData.collectionTime = [];\r\n      if (newVal !== 6) {\r\n        this.funEsSeach();\r\n      }\r\n    },\r\n    \"SeachData.collectionTime\"(newVal, oldVal) {\r\n      if (newVal.length == 0) {\r\n        return;\r\n      }\r\n      this.funEsSeach();\r\n    },\r\n    \"SeachData.isTechnology\"(newVal, oldVal) {\r\n      this.funEsSeach();\r\n    },\r\n  },\r\n  methods: {\r\n    EsSeach(flag) {\r\n      this.buttonDisabled = true\r\n      var regex = /\\d+/g, regex1 = /\\d/ // \\d 表示匹配数字\r\n      let data = this.checkList.map(item => {\r\n        if (regex1.test(item.label)) {\r\n          return item.label.slice(0, item.nameLength)\r\n        } else {\r\n          return item.label\r\n        }\r\n      })\r\n      let sourceSn = data.map(item => {\r\n        const foundItem = this.treeDataTransfer.find(row => row.label === item);\r\n        return foundItem ? foundItem.sourceSn : null;\r\n      }).filter(sn => sn !== null);\r\n      let params = {\r\n        m: 1,\r\n        pageNum: this.currentPage,\r\n        pageSize: this.pageSize,\r\n        id: 1,\r\n        weChatName: String(data),\r\n        sourceSn: String(sourceSn),\r\n        isSort: this.SeachData.sortMode,\r\n        dateType: this.SeachData.timeRange != 6 ? this.SeachData.timeRange : '',\r\n        startTime: this.SeachData.customDay[0],\r\n        endTime: this.SeachData.customDay[1],\r\n        collectionDateType: this.SeachData.collectionDateType != 6 ? this.SeachData.collectionDateType : '',\r\n        collectionStartTime: this.SeachData.collectionTime[0],\r\n        collectionEndTime: this.SeachData.collectionTime[1],\r\n        keywords: this.SeachData.keyword,\r\n        isTechnology: this.SeachData.isTechnology\r\n      }\r\n      if (params.keywords) {\r\n        addArticleHistory({ keyword: params.keywords, type: 2 }).then(response => {\r\n          this.getArticleHistory()\r\n        });\r\n      }\r\n      api.KeIntegration({ ...params, ...this.treeQuery, menuType: 2, }).then(Data => {\r\n        if (Data.code == 200) {\r\n          this.ArticleList = Data.data.list\r\n          this.total = Data.data.total ? Data.data.total : 0\r\n          if (this.ArticleList.length == 0 && this.pageSize * (this.currentPage - 1) >= this.total && this.total != 0) {\r\n            this.currentPage = Math.trunc(this.total / this.pageSize) + 1\r\n            this.EsSeach('source')\r\n          }\r\n        }\r\n        if (flag == 'source') {\r\n          this.buttonDisabled = false\r\n        }\r\n      }).catch(err => {\r\n        this.buttonDisabled = false\r\n      })\r\n      if (flag != 'source') {\r\n        let data = JSON.parse(JSON.stringify(params))\r\n        delete data.weChatName\r\n        api.wechatCountSourceName({ ...data, ...this.treeQuery, menuType: 2, platformType: 1, id: 1, }).then(res => {\r\n          if (res.code == 200) {\r\n            this.countBySourceName = res.data\r\n            this.treeListChange()\r\n          }\r\n          this.buttonDisabled = false\r\n        }).catch(err => {\r\n          this.buttonDisabled = false\r\n        })\r\n        return\r\n      }\r\n    },\r\n    treeListChange(data) {\r\n      const handleTreeData = (sourceList, targetList) => {\r\n        sourceList.forEach(sourceItem => {\r\n          const targetItem = targetList.find(target => target.label === sourceItem);\r\n          if (targetItem) {\r\n            this.$set(targetItem, 'count', this.countBySourceName[sourceItem])\r\n          }\r\n        });\r\n      };\r\n      const selectTheSelectedData = () => {\r\n        this.$refs.tree.setCheckedKeys([])\r\n        const checkList = [...this.checkList];\r\n        const ids = checkList.map(item =>\r\n          this.treeData[0].children.find(row => row.label === item.label)?.id\r\n        )\r\n        setTimeout(() => {\r\n          this.$refs.tree.setCheckedKeys(ids.filter(id => id !== undefined))\r\n        }, 100);\r\n      };\r\n\r\n      if (this.countBySourceName) {\r\n        if (this.checkList.length) {\r\n          const list = JSON.parse(JSON.stringify(this.treeData[0].children))\r\n          list.forEach((row, index) => {\r\n            row.count = 0;\r\n            this.$set(this.treeData[0].children, index, row);\r\n          });\r\n          handleTreeData(Object.keys(this.countBySourceName), list)\r\n          this.$set(this.treeData[0], 'children', list);\r\n          selectTheSelectedData()\r\n        } else {\r\n          const list = JSON.parse(JSON.stringify(this.treeDataTransfer))\r\n          handleTreeData(Object.keys(this.countBySourceName), list);\r\n          this.$set(this.treeData[0], 'children', list);\r\n        }\r\n      } else {\r\n        const list = JSON.parse(JSON.stringify(this.treeDataTransfer))\r\n        this.$set(this.treeData[0], 'children', list);\r\n        selectTheSelectedData()\r\n      }\r\n      this.treeSlot()\r\n    },\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current\r\n      this.funEsSeach()\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize\r\n      this.funEsSeach()\r\n    },\r\n    async getTree() {\r\n      await api.monitoringMedium({ platformType: 1, id: 1, ...this.treeQuery, menuType: 2, }).then(item => {\r\n        if (item.code === 200) {\r\n          const mapData = data => data.map((ITEM, index) => ({\r\n            id: index + 1,\r\n            label: ITEM.cnName,\r\n            count: 0,\r\n            orderNum: ITEM.orderNum,\r\n            country: ITEM.countryOfOrigin,\r\n            sourceSn: ITEM.sourceSn\r\n          }));\r\n\r\n          this.treeData = [\r\n            {\r\n              id: 1000,\r\n              label: '',\r\n              children: mapData(item.data),\r\n            }\r\n          ];\r\n          this.treeDataTransfer = mapData(item.data);\r\n        }\r\n        this.funEsSeach()\r\n      })\r\n    },\r\n    checkChange(item, isCheck, sonCheck) {\r\n      if (isCheck) {\r\n        if (item.label !== '') {\r\n          this.checkList.push(item)\r\n        }\r\n      } else {\r\n        this.checkList.splice(this.checkList.findIndex(row => row.label == item.label), 1)\r\n      }\r\n      this.funEsSeach('source')\r\n    },\r\n    // 防抖\r\n    debounce(fn, delay) {\r\n      let timer;\r\n      return function () {\r\n        let context = this;\r\n        let args = arguments;\r\n        clearTimeout(timer);\r\n        timer = setTimeout(() => {\r\n          fn.apply(context, args);\r\n        }, delay);\r\n      }\r\n    },\r\n    filterNode(value, data) {\r\n      if (!value) return true\r\n      return data.label.indexOf(value) !== -1\r\n    },\r\n    // 左侧列表重置\r\n    treeClear() {\r\n      this.$refs.tree.setCheckedKeys([]);\r\n    },\r\n    // 左侧树排序\r\n    treeSlot(type) {\r\n      let checkList = JSON.parse(JSON.stringify(this.checkList))\r\n      let list = JSON.parse(JSON.stringify(this.treeData[0].children))\r\n      let list1 = list.sort((a, b) => {\r\n        if (this.sortMode == 1) {\r\n          return b.count - a.count\r\n        } else if (this.sortMode == 2) {\r\n          return a.count - b.count\r\n        } else {\r\n          return b.orderNum - a.orderNum\r\n        }\r\n      }).map((item, index) => {\r\n        item.id = index + 1\r\n        return item\r\n      })\r\n      this.$set(this.treeData[0], 'children', list1)\r\n      let ids = checkList.map(item => {\r\n        return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id\r\n      })\r\n      setTimeout(res => {\r\n        this.$refs.tree.setCheckedKeys(ids);\r\n      }, 100)\r\n    },\r\n    resetting() {\r\n      this.SeachData = {\r\n        metaMode: '' /* 匹配模式 */,\r\n        keyword: '' /* 关键词 */,\r\n        sortMode: '0' /* 排序模式 */,\r\n        timeRange: '' /* 时间范围 */,\r\n        customDay: '' /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: '' /* 自定义天 */,\r\n        isTechnology: 1,\r\n      }\r\n      this.funEsSeach()\r\n    },\r\n    async removeHistory(item, type) {\r\n      clearTimeout(this.historyTimeout);\r\n      await delArticleHistory([item.id])\r\n      if (type == 1) {\r\n        this.$refs['keywordRef'].focus();\r\n        await this.getArticleHistory()\r\n      } else {\r\n        await this.getArticleHistory()\r\n        await this.getArticleHistory1()\r\n      }\r\n    },\r\n    showHistoryList() {\r\n      this.showHistory = true;\r\n    },\r\n    hideHistoryList() {\r\n      this.historyTimeout = setTimeout(() => {\r\n        this.showHistory = false;\r\n      }, 500);\r\n    },\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword\r\n      this.dialogVisible1 = false\r\n    },\r\n    getArticleHistory() {\r\n      this.historyLoading = true\r\n      listArticleHistory({ pageNum: 1, pageSize: 5, type: 2 }).then(response => {\r\n        this.historyList = response.rows;\r\n        this.historyLoading = false\r\n      });\r\n    },\r\n    async clearHistory() {\r\n      clearTimeout(this.historyTimeout)\r\n      this.$refs['keywordRef'].focus();\r\n      await cleanArticleHistory(2)\r\n      this.getArticleHistory()\r\n    },\r\n    moreHistory() {\r\n      this.historyLoading = true\r\n      this.getArticleHistory1()\r\n      this.dialogVisible1 = true\r\n    },\r\n    getArticleHistory1() {\r\n      this.historyLoading = true\r\n      listArticleHistory({ ...this.queryParams1, type: 2 }).then(response => {\r\n        this.historyList1 = response.rows;\r\n        this.total1 = response.total;\r\n        this.historyLoading = false\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 93px);\r\n  overflow-y: scroll;\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    height: 20px;\r\n    right: -2px;\r\n    top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}