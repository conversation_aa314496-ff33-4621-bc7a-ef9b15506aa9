<template>
  <div v-if="funEsSeach">
    <splitpanes class="default-theme">
      <pane class="leftLink" ref="leftLink" min-size="20" max-size="50" size="25">
        <div class="treeMain" style="width: 100%;margin:0;">
          <div style="display:flex;justify-content:space-between;align-items:center;gap:10px">
            <el-input placeholder="输入关键字进行过滤" v-model="filterText" clearable class="input_Fixed">
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
          <div class="treeBox">
            <el-tree :data="treeData" ref="tree" show-checkbox node-key="id" :default-expanded-keys="[1000]"
              @check-change="checkChange" :expand-on-click-node="false" :filter-node-method="filterNode">
              <template slot-scope="scoped">
                <div
                  v-if="scoped.data.label != ''"
                  style="display: flex; align-items: center"
                >
                  <div>{{ scoped.data.label }}</div>
                  <div v-if="scoped.data.country && scoped.data.country !== '0'" style="display: flex; align-items: center">
                    <div>[</div>
                    <dict-tag
                      :options="dict.type.country"
                      :value="scoped.data.country"
                    />
                    <div>]</div>
                  </div>
                  <div style="font-weight: 600">
                    {{ `${scoped.data.count ? `(${scoped.data.count})` : ""}` }}
                  </div>
                </div>
                <div v-else>
                  {{ scoped.data.label }}
                  <div
                    style="position: absolute;z-index: 99;right: 10px;top: -7px;height: 35px;display: flex;align-items: center;">
                    <el-select v-model="treeQuery.isStability" size="mini" placeholder="请选择稳定源"
                      style="width: 60px;height: 20px;margin: 0px 10px 0 0;" class="treeQuery">
                      <el-option label="全部" :value="null"></el-option>
                      <el-option label="稳定源" :value="1"></el-option>
                      <el-option label="不稳定源" :value="0"></el-option>
                    </el-select>
                    <el-select v-model="sortMode" size="mini" placeholder="请选择排序方式" @change="treeSlot"
                      style="width: 60px;height: 20px;margin: 0px 10px 0 0;" class="treeQuery">
                      <el-option label="按数量倒向排序" :value="1"></el-option>
                      <el-option label="按权重排序" :value="0"></el-option>
                    </el-select>
                    <el-tooltip class="item" effect="dark" content="重置" placement="top">
                      <i class="el-input__icon el-icon-refresh" @click="treeClear"></i>
                    </el-tooltip>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </pane>
      <pane min-size="50" max-size="80" size="75">
        <div class="rightMain" style="margin-left: 0;overflow: auto;">
          <div class="toolBox">
            <div class="mainTool">
              <p>
                发布日期:
                <el-button size="mini" :type="SeachData.timeRange == '' ? 'primary' : ''"
                  @click="SeachData.timeRange = ''">24小时</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 1 ? 'primary' : ''"
                  @click="SeachData.timeRange = 1">今天</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 2 ? 'primary' : ''"
                  @click="SeachData.timeRange = 2">昨天</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 4 ? 'primary' : ''"
                  @click="SeachData.timeRange = 4">近7天</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 5 ? 'primary' : ''"
                  @click="SeachData.timeRange = 5">近30天</el-button>
                <el-button size="mini" :type="SeachData.timeRange == 6 ? 'primary' : ''"
                  @click="SeachData.timeRange = 6">自定义</el-button>
                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="SeachData.customDay"
                  v-if="SeachData.timeRange == 6" style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  unlink-panels clearable></el-date-picker>
              </p>
              <p>
                采集日期:
                <el-button size="mini" :type="SeachData.collectionDateType == 0 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 0">24小时</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 1 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 1">今天</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 2 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 2">昨天</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 4 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 4">近7天</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 5 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 5">近30天</el-button>
                <el-button size="mini" :type="SeachData.collectionDateType == 6 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 6">自定义</el-button>
                <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="SeachData.collectionTime"
                  v-if="SeachData.collectionDateType == 6" style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  unlink-panels clearable></el-date-picker>
              </p>
              <p>
                <span style="width:60px;display:inline-block;text-align:right;margin-right:5px">是否与科技有关:</span>
                <el-radio-group v-model="SeachData.isTechnology" size="small">
                  <el-radio-button v-for="(dict, index) in dict.type.is_technology" :label="dict.value"
                    :key="'is_technology' + dict.value">{{ dict.label }}</el-radio-button>
                </el-radio-group>
              </p>
              <div class="keyword">
                <span style="width:60px;display:inline-block;text-align:right;margin-right:5px">关键词:</span>
                <el-input ref="keywordRef" placeholder="请输入关键词,使用逗号分割(英文)" style="width:430px" v-model="SeachData.keyword"
                  @focus="showHistoryList()" @blur="hideHistoryList()">
                </el-input>
                <el-button type="primary" size="mini" @click="funEsSeach" :loading="buttonDisabled" style="margin-left: 10px; height: 36px">搜索</el-button>
                <div class="history" v-show="showHistory">
                  <div class="historyItem" v-for="(history, index) in historyList" :key="index"
                    v-loading="historyLoading">
                    <div @click="keywordsChange(history)" class="historyText">{{ history.keyword }}</div>
                    <el-button type="text" @click="removeHistory(history, 1)">删除</el-button>
                  </div>
                  <div class="historyItem">
                    <el-button type="text" @click="moreHistory()">更多</el-button>
                    <el-button type="text" @click="clearHistory()">清空</el-button>
                  </div>
                </div>
              </div>
              
            </div>
          </div>
          <MainArticle v-loading="buttonDisabled" :flag="'artificialIntelligence'" :currentPage="currentPage"
            :pageSize="pageSize" :total="total" :ArticleList="ArticleList" :keywords="SeachData.keyword"
            @handleCurrentChange="handleCurrentChange" @handleSizeChange="handleSizeChange" @Refresh="funEsSeach"
            :SeachData="SeachData"></MainArticle>
        </div>
      </pane>
    </splitpanes>

    <el-dialog title="关键词历史" :visible.sync="dialogVisible1" width="570px" :close-on-click-modal="false">
      <div class="history" v-loading="historyLoading">
        <div class="historyItem" v-for="(history, index) in historyList1" :key="index">
          <div @click="keywordsChange(history)" class="historyText">{{ history.keyword }}</div>
          <el-button type="text" @click="removeHistory(history, 2)">删除</el-button>
        </div>
      </div>
      <pagination v-show="total1 > 0" :total="total1" :page.sync="queryParams1.pageNum"
        :limit.sync="queryParams1.pageSize" :background="false" @pagination="getArticleHistory1"
        :layout="'total, prev, pager, next'" />
    </el-dialog>
  </div>
</template>

<script>
import api from '@/api/ScienceApi/index.js'
import MainArticle from '../components/MainArticle.vue'
import { listArticleHistory, delArticleHistory, addArticleHistory, cleanArticleHistory } from "@/api/article/articleHistory";
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

export default {
  components: { MainArticle, Splitpanes, Pane },
  dicts: ['is_technology', 'country'],
  data() {
    return {
      /* 文章主体组件数据 */
      currentPage: 1,
      pageSize: 50,
      total: 0,
      ArticleList: [],
      /* 左侧tree数据 */
      filterText: '',
      treeData: [],
      treeDataTransfer: [],
      checkList: [],
      /* 搜索组件数据 */
      SeachData: {
        metaMode: '' /* 匹配模式 */,
        keyword: '' /* 关键词 */,
        sortMode: '0' /* 排序模式 */,
        timeRange: '4' /* 时间范围 */,
        customDay: '' /* 自定义天 */,
        collectionDateType: null /* 时间范围 */,
        collectionTime: '' /* 自定义天 */,
        isTechnology: 1,
      } /* 搜索条件 */,
      buttonDisabled: false /* 按钮防抖 */,
      funEsSeach: false,
      treeQuery: {
        isStability: 1,
        industry: null,
        domain: null
      },
      countBySourceName: null,
      sortMode: 1,
      showHistory: false,
      historyList: [],
      historyTimeout: null,
      dialogVisible1: false,
      historyLoading: false,
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
      },
      total1: 0,
      historyList1: [],
    }
  },
  async created() {
    this.getArticleHistory()
    this.getTree()
    this.funEsSeach = this.debounce(this.EsSeach, 200)
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    'treeQuery': {
      handler(newValue, oldValue) {
        this.getTree();
      },
      deep: true // 开启深度监听
    },
    "SeachData.timeRange"(newVal, oldVal) {
      this.SeachData.customDay = [];
      if (newVal !== 6) {
        this.funEsSeach();
      }
    },
    "SeachData.customDay"(newVal, oldVal) {
      if (newVal.length == 0) {
        return;
      }
      this.funEsSeach();
    },
    "SeachData.collectionDateType"(newVal, oldVal) {
      this.SeachData.collectionTime = [];
      if (newVal !== 6) {
        this.funEsSeach();
      }
    },
    "SeachData.collectionTime"(newVal, oldVal) {
      if (newVal.length == 0) {
        return;
      }
      this.funEsSeach();
    },
    "SeachData.isTechnology"(newVal, oldVal) {
      this.funEsSeach();
    },
  },
  methods: {
    EsSeach(flag) {
      this.buttonDisabled = true
      var regex = /\d+/g, regex1 = /\d/ // \d 表示匹配数字
      let data = this.checkList.map(item => {
        if (regex1.test(item.label)) {
          return item.label.slice(0, item.nameLength)
        } else {
          return item.label
        }
      })
      let sourceSn = data.map(item => {
        const foundItem = this.treeDataTransfer.find(row => row.label === item);
        return foundItem ? foundItem.sourceSn : null;
      }).filter(sn => sn !== null);
      let params = {
        m: 1,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        id: 1,
        weChatName: String(data),
        sourceSn: String(sourceSn),
        isSort: this.SeachData.sortMode,
        dateType: this.SeachData.timeRange != 6 ? this.SeachData.timeRange : '',
        startTime: this.SeachData.customDay[0],
        endTime: this.SeachData.customDay[1],
        collectionDateType: this.SeachData.collectionDateType != 6 ? this.SeachData.collectionDateType : '',
        collectionStartTime: this.SeachData.collectionTime[0],
        collectionEndTime: this.SeachData.collectionTime[1],
        keywords: this.SeachData.keyword,
        isTechnology: this.SeachData.isTechnology
      }
      if (params.keywords) {
        addArticleHistory({ keyword: params.keywords, type: 2 }).then(response => {
          this.getArticleHistory()
        });
      }
      api.KeIntegration({ ...params, ...this.treeQuery, menuType: 2, }).then(Data => {
        if (Data.code == 200) {
          this.ArticleList = Data.data.list
          this.total = Data.data.total ? Data.data.total : 0
          if (this.ArticleList.length == 0 && this.pageSize * (this.currentPage - 1) >= this.total && this.total != 0) {
            this.currentPage = Math.trunc(this.total / this.pageSize) + 1
            this.EsSeach('source')
          }
        }
        if (flag == 'source') {
          this.buttonDisabled = false
        }
      }).catch(err => {
        this.buttonDisabled = false
      })
      if (flag != 'source') {
        let data = JSON.parse(JSON.stringify(params))
        delete data.weChatName
        api.wechatCountSourceName({ ...data, ...this.treeQuery, menuType: 2, platformType: 1, id: 1, }).then(res => {
          if (res.code == 200) {
            this.countBySourceName = res.data
            this.treeListChange()
          }
          this.buttonDisabled = false
        }).catch(err => {
          this.buttonDisabled = false
        })
        return
      }
    },
    treeListChange(data) {
      const handleTreeData = (sourceList, targetList) => {
        sourceList.forEach(sourceItem => {
          const targetItem = targetList.find(target => target.label === sourceItem);
          if (targetItem) {
            this.$set(targetItem, 'count', this.countBySourceName[sourceItem])
          }
        });
      };
      const selectTheSelectedData = () => {
        this.$refs.tree.setCheckedKeys([])
        const checkList = [...this.checkList];
        const ids = checkList.map(item =>
          this.treeData[0].children.find(row => row.label === item.label)?.id
        )
        setTimeout(() => {
          this.$refs.tree.setCheckedKeys(ids.filter(id => id !== undefined))
        }, 100);
      };

      if (this.countBySourceName) {
        if (this.checkList.length) {
          const list = JSON.parse(JSON.stringify(this.treeData[0].children))
          list.forEach((row, index) => {
            row.count = 0;
            this.$set(this.treeData[0].children, index, row);
          });
          handleTreeData(Object.keys(this.countBySourceName), list)
          this.$set(this.treeData[0], 'children', list);
          selectTheSelectedData()
        } else {
          const list = JSON.parse(JSON.stringify(this.treeDataTransfer))
          handleTreeData(Object.keys(this.countBySourceName), list);
          this.$set(this.treeData[0], 'children', list);
        }
      } else {
        const list = JSON.parse(JSON.stringify(this.treeDataTransfer))
        this.$set(this.treeData[0], 'children', list);
        selectTheSelectedData()
      }
      this.treeSlot()
    },
    handleCurrentChange(current) {
      this.currentPage = current
      this.funEsSeach()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.funEsSeach()
    },
    async getTree() {
      await api.monitoringMedium({ platformType: 1, id: 1, ...this.treeQuery, menuType: 2, }).then(item => {
        if (item.code === 200) {
          const mapData = data => data.map((ITEM, index) => ({
            id: index + 1,
            label: ITEM.cnName,
            count: 0,
            orderNum: ITEM.orderNum,
            country: ITEM.countryOfOrigin,
            sourceSn: ITEM.sourceSn
          }));

          this.treeData = [
            {
              id: 1000,
              label: '',
              children: mapData(item.data),
            }
          ];
          this.treeDataTransfer = mapData(item.data);
        }
        this.funEsSeach()
      })
    },
    checkChange(item, isCheck, sonCheck) {
      if (isCheck) {
        if (item.label !== '') {
          this.checkList.push(item)
        }
      } else {
        this.checkList.splice(this.checkList.findIndex(row => row.label == item.label), 1)
      }
      this.funEsSeach('source')
    },
    // 防抖
    debounce(fn, delay) {
      let timer;
      return function () {
        let context = this;
        let args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 左侧列表重置
    treeClear() {
      this.$refs.tree.setCheckedKeys([]);
    },
    // 左侧树排序
    treeSlot(type) {
      let checkList = JSON.parse(JSON.stringify(this.checkList))
      let list = JSON.parse(JSON.stringify(this.treeData[0].children))
      let list1 = list.sort((a, b) => {
        if (this.sortMode == 1) {
          return b.count - a.count
        } else if (this.sortMode == 2) {
          return a.count - b.count
        } else {
          return b.orderNum - a.orderNum
        }
      }).map((item, index) => {
        item.id = index + 1
        return item
      })
      this.$set(this.treeData[0], 'children', list1)
      let ids = checkList.map(item => {
        return this.treeData[0].children.filter(row => { return row.label == item.label })[0].id
      })
      setTimeout(res => {
        this.$refs.tree.setCheckedKeys(ids);
      }, 100)
    },
    resetting() {
      this.SeachData = {
        metaMode: '' /* 匹配模式 */,
        keyword: '' /* 关键词 */,
        sortMode: '0' /* 排序模式 */,
        timeRange: '' /* 时间范围 */,
        customDay: '' /* 自定义天 */,
        collectionDateType: null /* 时间范围 */,
        collectionTime: '' /* 自定义天 */,
        isTechnology: 1,
      }
      this.funEsSeach()
    },
    async removeHistory(item, type) {
      clearTimeout(this.historyTimeout);
      await delArticleHistory([item.id])
      if (type == 1) {
        this.$refs['keywordRef'].focus();
        await this.getArticleHistory()
      } else {
        await this.getArticleHistory()
        await this.getArticleHistory1()
      }
    },
    showHistoryList() {
      this.showHistory = true;
    },
    hideHistoryList() {
      this.historyTimeout = setTimeout(() => {
        this.showHistory = false;
      }, 500);
    },
    keywordsChange(item) {
      this.SeachData.keyword = item.keyword
      this.dialogVisible1 = false
    },
    getArticleHistory() {
      this.historyLoading = true
      listArticleHistory({ pageNum: 1, pageSize: 5, type: 2 }).then(response => {
        this.historyList = response.rows;
        this.historyLoading = false
      });
    },
    async clearHistory() {
      clearTimeout(this.historyTimeout)
      this.$refs['keywordRef'].focus();
      await cleanArticleHistory(2)
      this.getArticleHistory()
    },
    moreHistory() {
      this.historyLoading = true
      this.getArticleHistory1()
      this.dialogVisible1 = true
    },
    getArticleHistory1() {
      this.historyLoading = true
      listArticleHistory({ ...this.queryParams1, type: 2 }).then(response => {
        this.historyList1 = response.rows;
        this.total1 = response.total;
        this.historyLoading = false
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.treeBox {
  width: 100%;
  height: calc(100vh - 93px);
  overflow-y: scroll;
}

.treeMain {
  position: relative;
}

.treeQuery {
  ::v-deep .el-input--mini .el-input__inner {
    height: 24px;
    line-height: 24px;
    padding: 0 4px;
  }

  ::v-deep .el-input__suffix {
    height: 20px;
    right: -2px;
    top: 5px;
  }
}

.toolBox {
  min-height: 130px;
  height: auto;
  padding-bottom: 15px;
  background-color: rgb(255, 255, 255);
  border-left: solid 1px rgb(221, 219, 219);

  .title {
    display: flex;
    justify-content: space-between;
    height: 70px;
    padding: 0 30px;
    font-size: 19px;
  }

  .mainTool {
    padding: 0 28px;
    font-size: 14px;
    color: rgb(58, 58, 58);
  }

  .mainToolOne {
    margin-top: 15px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    // align-items: center;
  }

  .mainToolTwo {
    display: flex;
    align-items: center;
    height: 40px;

    p {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .btn {
    margin: 15px 0 0 25px;
  }
}

.keyword {
  width: 100%;
  position: relative;

  .history {
    width: 430px;
    position: absolute;
    background: #fff;
    z-index: 9999;
    left: 65px;
    border: 1px solid rgb(221, 219, 219);

    .historyItem {
      padding-left: 20px;

      .historyText {
        width: 450px;
        height: 34px;
        line-height: 34px;
      }

      &:nth-last-of-type(1) {
        padding-left: 0;

        ::v-deep .el-button--text {
          padding: 10px 20px;
        }
      }
    }
  }
}

.history {
  width: 530px;

  .historyItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 0 10px;
    overflow: hidden;

    .historyText {
      width: 350px;
      height: 34px;
      line-height: 34px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
}
</style>
