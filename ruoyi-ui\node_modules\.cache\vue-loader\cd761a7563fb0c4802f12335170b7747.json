{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\artificialIntelligence.vue?vue&type=template&id=7e518ed4&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\artificialIntelligence.vue", "mtime": 1754010096859}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}