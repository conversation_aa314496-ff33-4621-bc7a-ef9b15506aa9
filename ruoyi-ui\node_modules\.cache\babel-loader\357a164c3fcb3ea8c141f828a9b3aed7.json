{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue", "mtime": 1754108735728}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_request", "_auth", "_elementUi", "_axios", "_classify", "_fileSaver", "_ruoyi", "_list", "_DeepseekReportDialog", "_ai", "_marked", "_config", "_articleHistory", "props", "downLoadShow", "required", "type", "Boolean", "default", "editShow", "copyShow", "<PERSON>u<PERSON><PERSON>", "height", "Number", "currentPage", "pageSize", "total", "ArticleList", "flag", "SeachData", "keywords", "String", "sourceType", "components", "DeepseekReportDialog", "data", "loading", "regExpImg", "reportId", "reportOptions", "dialogVisible", "checkedCities", "checked", "html", "text", "that", "tagShow", "isIndeterminate", "count", "separate", "tagDialog", "formLabelAlign", "tag", "industry", "domain", "options", "options1", "tagItem", "areaList", "num", "timer", "drawer", "drawerInfo", "AreaId", "translationBtnShow", "open", "sourceTypeList", "sourceLists", "sourceTypeLists", "form", "rules", "title", "message", "content", "publishTime", "cnTitle", "originalUrl", "summary", "sn", "vertifyUpload", "isUploading", "headers", "Authorization", "getToken", "ContentType", "url", "process", "env", "VUE_APP_BASE_API", "fileList", "fileUrlList", "fileUrlurl", "showSummary", "batchImportVisible", "batchImportFiles", "showDeepseekDialog", "currentArticle", "aiDialogVisible", "chatMessages", "isThinking", "userAvatar", "streamingMessage", "markdownOptions", "gfm", "breaks", "headerIds", "mangle", "headerPrefix", "pedantic", "sanitize", "smartLists", "smartypants", "xhtml", "isRequesting", "isAborted", "currentReader", "aiPlatform", "articleAiPrompt", "computed", "watch", "newVal", "oldVal", "_this", "API", "getNewBuilt", "then", "code", "$message", "handler", "Refresh", "deep", "filter", "item", "mounted", "created", "_this2", "openDialog", "getListClassify", "res", "getSourceList", "$route", "query", "getConfigKey", "msg", "$store", "getters", "avatar", "updated", "filters", "methods", "getTechnologyLabel", "value", "mapping", "getSafeSummary", "cnSummary", "processedCnSummary", "replace", "trim", "processedSummary", "formatPublishTime", "webstePublishTime", "formattedPublishTime", "parseTime", "formattedWebsteTime", "includes", "dateMatch", "match", "year", "month", "day", "concat", "padStart", "has<PERSON><PERSON>ual<PERSON><PERSON>nt", "contentWithoutTags", "test", "changeColor", "str", "Str", "split", "map", "keyitem", "keyindex", "length", "replaceReg", "RegExp", "replaceString", "downLoadExcel", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "response", "a", "document", "createElement", "href", "window", "URL", "createObjectURL", "download", "Date", "getTime", "click", "downLoadExportExcel", "stop", "batchDelete", "_this4", "$confirm", "batchRemove", "join", "$emit", "catch", "publishHot", "_this5", "publishEverydayHot", "mainScorll", "_this6", "scrollStep", "$refs", "scroll", "scrollTop", "scrollInterval", "setInterval", "scrollBy", "clearInterval", "scrollChange", "_this7", "clearTimeout", "setTimeout", "stopScroll", "downLoad", "handleSizeChange", "handleCurrentChange", "current", "collect", "_this8", "_callee2", "_res", "_callee2$", "_context2", "id", "favorites", "collectApi", "sent", "cocelCollect", "copyText", "_this9", "navigator", "clipboard", "writeText", "alert", "typeHandle", "handleCheckedCitiesChange", "handleCheckAllChange", "val", "reportSubmit", "_this10", "_callee3", "keyWordList", "_callee3$", "_context3", "push", "listId", "listSn", "for<PERSON>ach", "article", "AddReport", "separateAdd", "openNewView", "isLink", "docId", "<PERSON><PERSON><PERSON><PERSON>", "tags", "_this11", "_callee4", "_callee4$", "_context4", "remoteEvent", "fieldName", "remoteIndustry", "industryName", "SubmitTag", "_this12", "_callee5", "params", "_callee5$", "_context5", "articleId", "tagAdd", "closeTag", "resetFields", "hotIncrease", "_this13", "_callee6", "is<PERSON><PERSON>ther", "_callee6$", "_context6", "JSON", "parse", "openDrawer", "_this14", "_callee7", "_callee7$", "_context7", "AreaInfo", "cnC<PERSON>nt", "b", "c", "industryHandle", "_this15", "ids", "ele", "undefined", "domainHandle", "_this16", "resultEvent", "_this17", "warning", "zhuangtai", "snapshotUrl", "downLoadExportKe", "$msgbox", "_defineProperty2", "showCancelButton", "confirmButtonText", "cancelButtonText", "beforeClose", "action", "instance", "done", "err", "downLoadExportZhuan", "location", "origin", "documentDownload", "_this18", "_callee9", "urls", "_iterator", "_step", "_loop", "_callee9$", "_context10", "fileUrl", "_createForOfIteratorHelper2", "entries", "_step$value", "index", "_loop$", "_context9", "_slicedToArray2", "indexOf", "_callee8", "_callee8$", "_context8", "downLoadFun", "error", "s", "n", "<PERSON><PERSON><PERSON>", "t1", "e", "f", "finish", "_this19", "_callee10", "formData", "isBlob", "blob", "list", "fileName", "resText", "rspObj", "errMsg", "_callee10$", "_context11", "FormData", "append", "downloadFile", "blobValidate", "Blob", "saveAs", "errorCode", "t0", "translateTitle", "row", "_this20", "$loading", "lock", "spinner", "background", "translationTitle", "originalText", "translationField", "translationType", "findIndex", "isTranslated", "close", "translateEvent", "_this21", "handleUpdate", "_this22", "reset", "handleDelete", "_this23", "$modal", "confirm", "monitoringEsRemove", "msgSuccess", "submitForm", "_this24", "validate", "valid", "queryForm", "stringify", "articleListEdit", "requestLoad", "file", "_this25", "_callee11", "_callee11$", "_context12", "uploadCover", "uid", "path", "imgUrl", "exceed", "handleRemove", "handleChange", "upload", "submit", "beforeUploadUrl", "name", "substring", "lastIndexOf", "toLowerCase", "condition", "fileSize", "size", "$notify", "uploadUrlSuccess", "uploadUrlExceed", "uploadUrlRequest", "_this26", "uploadFile", "uploadUrlRemove", "_this27", "removeFile", "filePath", "cancel", "articleSn", "sourceName", "sourceSn", "shortUrl", "author", "description", "cover", "publishType", "publishCode", "publishArea", "numberLikes", "numberReads", "numberCollects", "numberShares", "numberComments", "emotion", "status", "remark", "createBy", "createTime", "updateBy", "updateTime", "userId", "deptId", "tmpUrl", "is<PERSON><PERSON><PERSON>", "groupId", "appId", "resetForm", "openBatchImportDialog", "handleFileSelect", "newFiles", "raw", "splice", "cancelBatchImport", "batchUpload", "clearFiles", "confirmBatchImport", "_this28", "_callee12", "emptySourceNames", "sourceNames", "_callee12$", "_context13", "batchImportReports", "console", "reportAiChat", "selectedArticleId", "selectedArticle", "find", "difyAiChat", "_this29", "_callee13", "_articlesResponse$dat", "selectedArticles", "titles", "articlesResponse", "articlesContent", "aiMessage", "prompt", "reader", "decoder", "buffer", "<PERSON><PERSON><PERSON><PERSON>", "isInThinkTag", "decodeUnicode", "updateContent", "_yield$reader$read", "lastData", "decodedAnswer", "chunk", "newlineIndex", "line", "jsonData", "answer", "_callee13$", "_context14", "log", "Promise", "resolve", "getListByIds", "Error", "_selectedArticles$ind", "_selectedArticles$ind2", "role", "difyAiQa", "ok", "body", "<PERSON><PERSON><PERSON><PERSON>", "TextDecoder", "fromCharCode", "parseInt", "newContent", "renderedContent", "marked", "$nextTick", "scrollHeight", "read", "warn", "decode", "slice", "startsWith", "t2", "ollamaAiChat", "_this30", "_callee15", "_articlesResponse$dat2", "_aiMessage", "lastUpdateTime", "isThinkContent", "temp<PERSON><PERSON><PERSON>", "processStream", "_callee15$", "_context16", "_selectedArticles$ind3", "_selectedArticles$ind4", "ollamaAiQa", "now", "currentTime", "_ref2", "_callee14", "_yield$reader$read2", "lines", "_iterator2", "_step2", "_response", "thinkStartIndex", "thinkEndIndex", "_callee14$", "_context15", "apply", "arguments", "deepseekAiChat", "_this31", "_callee16", "_aiMessage2", "_lastUpdateTime", "_yield$reader$read3", "_iterator3", "_step3", "_jsonData$choices", "_callee16$", "_context17", "_selectedArticles$ind5", "_selectedArticles$ind6", "deepseekAiQa", "choices", "delta", "t3", "t4", "closeAiDialog", "articleAiChat"], "sources": ["src/views/components/MainArticle.vue"], "sourcesContent": ["<template>\r\n  <div class=\"main\" v-loading=\"loading\">\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Monitor'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    >\r\n                    </span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\" @click=\"resultEvent('BatchGeneration')\"></i>\r\n          </p> -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-document-add\"\r\n              style=\"font-size: 24px\"\r\n              title=\"发布到每日最新热点\"\r\n              @click=\"publishHot\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\" v-if=\"$route.query.domain\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek深度解读\"\r\n              @click=\"articleAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n              >Deepseek深度解读</span\r\n            >\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          :style=\"\r\n            $route.query.domain\r\n              ? 'height: calc(100vh - 170px)'\r\n              : 'height: calc(100vh - 389px)'\r\n          \"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{\r\n                        $route.query.domain\r\n                          ? \"是\"\r\n                          : getTechnologyLabel(item.isTechnology)\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleUpdate(item)\"\r\n                  v-hasPermi=\"['article:articleList:edit']\"\r\n                  >{{ ` 修改 ` }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  v-hasPermi=\"['article:list:remove']\"\r\n                  >{{ `删除` }}</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Special'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'infoInter'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n            </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Wechat'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(50vh - 200px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_kqcb\">\r\n              <div>\r\n                <div class=\"ArticlTop\" style=\"width: 55%\">\r\n                  <p style=\"line-height: 20px\">\r\n                    <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                      {{ null }} </el-checkbox\r\n                    >&nbsp;&nbsp;\r\n                    <a href=\"#\"\r\n                      ><span style=\"padding: 0 10px 0 0\"\r\n                        >{{\r\n                          (currentPage - 1) * pageSize + key + 1\r\n                        }}&nbsp;</span\r\n                      ><span\r\n                        class=\"title_Article\"\r\n                        @click=\"openNewView(item)\"\r\n                        v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                      ></span\r\n                      >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                    >\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3; cursor: pointer\"\r\n                    >\r\n                      原文链接\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n                <div class=\"info_flex\" style=\"width: auto\">\r\n                  <div class=\"ArticlBottom\">\r\n                    <div>\r\n                      类型:\r\n                      <span class=\"infomation\">\r\n                        {{ typeHandle(item.sourceType) }}\r\n                      </span>\r\n                    </div>\r\n                    <div>\r\n                      媒体来源:\r\n                      <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.author\">\r\n                      作者:\r\n                      <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                    </div>\r\n                    <div>\r\n                      发布时间:\r\n                      <span class=\"infomation\">{{\r\n                        formatPublishTime(\r\n                          item.publishTime,\r\n                          item.webstePublishTime\r\n                        )\r\n                      }}</span>\r\n                    </div>\r\n                    <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                    <!-- <p>标签A</p> -->\r\n                  </div>\r\n                  <div class=\"ArticlBottom\">\r\n                    <div v-if=\"item.industryName\">\r\n                      所属行业:\r\n                      <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.domain\">\r\n                      所属领域:\r\n                      <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p> -->\r\n            <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            <!-- </div> -->\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'artificialIntelligence' || flag == 'networkSecurity'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div\r\n              class=\"Articl_left\"\r\n              :style=\"item.fileUrl ? 'width: 85%' : 'width: 100%'\"\r\n            >\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题:</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\" v-if=\"item.fileUrl\">\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <!-- <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"primary\" plain @click=\"handleUpdate(item)\"\r\n                    v-hasPermi=\"['article:articleList:edit']\">{{ ` 修改 ` }}</el-button>\r\n                </p>\r\n                <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"danger\" plain @click=\"handleDelete(item)\"\r\n                    v-hasPermi=\"['article:list:remove']\">{{ `删除` }}</el-button>\r\n                </p> -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'zhikubaogao'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-upload2\"\r\n              style=\"font-size: 22px; color: #1296db\"\r\n              title=\"批量导入报告\"\r\n              @click=\"openBatchImportDialog\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载报告\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除报告\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek报告解读\"\r\n              @click=\"reportAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"reportAiChat\"\r\n              >Deepseek报告解读</span\r\n            >\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          style=\"height: calc(100vh - 310px)\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <!-- <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span> -->\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ getTechnologyLabel(item.isTechnology) }}\r\n                    </span>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"changeColor(getSafeSummary(item))\"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  >删除</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"pagination\" ref=\"pagination\">\r\n      <el-pagination\r\n        :small=\"false\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"currentPage\"\r\n        :page-sizes=\"[10, 20, 30, 40, 50]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"->, total, sizes, prev, pager, next, jumper\"\r\n        :total=\"total\"\r\n      ></el-pagination>\r\n    </div>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title\r\n      :visible.sync=\"tagDialog\"\r\n      width=\"20%\"\r\n      :before-close=\"closeTag\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form\r\n        label-position=\"left\"\r\n        label-width=\"40px\"\r\n        :model=\"formLabelAlign\"\r\n        ref=\"ruleForm\"\r\n      >\r\n        <el-form-item label=\"行业\" prop=\"industry\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.industry\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            placeholder=\"请选择行业\"\r\n            :filterable=\"true\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.industryName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"领域\" prop=\"domain\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.domain\"\r\n            filterable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            clearable\r\n            placeholder=\"请选择领域\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.fieldName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"标签\" prop=\"tag\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.tag\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n            filterable\r\n            allow-create\r\n            default-first-option\r\n            placeholder=\"请添加文章标签\"\r\n          >\r\n            <!-- <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>-->\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeTag\" size=\"mini\">取 消</el-button>\r\n        <el-button @click=\"SubmitTag\" type=\"primary\" size=\"mini\"\r\n          >确 定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n    <el-drawer\r\n      @open=\"openDrawer\"\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"drawer\"\r\n      custom-class=\"drawer_box\"\r\n      direction=\"rtl\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <div\r\n        v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n        style=\"width: 100%; text-align: right; padding-right: 10px\"\r\n      >\r\n        <el-button\r\n          @click=\"documentDownload('drawer')\"\r\n          :type=\"drawerInfo.fileUrl ? 'primary' : 'info'\"\r\n          size=\"mini\"\r\n          plain\r\n          :disabled=\"!drawerInfo.fileUrl\"\r\n          >{{ \"附件下载\" }}</el-button\r\n        >\r\n        <!-- <el-button @click=\"resultEvent('drawer')\" type=\"primary\" size=\"mini\" plain>\r\n          {{ drawerInfo.snapshotUrl\r\n            ? '下载快照' : '生成快照' }}\r\n        </el-button> -->\r\n        <!-- v-if=\"translationBtnShow\" -->\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          plain\r\n          @click=\"translateEvent(drawerInfo)\"\r\n          >翻译内容</el-button\r\n        >\r\n      </div>\r\n      <template slot=\"title\">\r\n        <span class=\"drawer_Title\">{{\r\n          drawerInfo.cnTitle || drawerInfo.title\r\n        }}</span>\r\n      </template>\r\n      <div class=\"drawer_Style\">\r\n        <p class=\"title\">\r\n          {{ drawerInfo.cnTitle || drawerInfo.title }}\r\n          <!-- v-if=\"!drawerInfo.cnTitle\" -->\r\n          <i\r\n            class=\"icon-taizhangtranslate\"\r\n            @click=\"translateTitle(drawerInfo)\"\r\n          ></i>\r\n        </p>\r\n        <p style=\"text-align: center\">\r\n          <span class=\"source\">{{ drawerInfo.sourceName }}</span>\r\n          <span class=\"time\">{{ drawerInfo.publishTime }}</span>\r\n        </p>\r\n        <div\r\n          style=\"user-select: text !important; line-height: 30px\"\r\n          v-html=\"\r\n            drawerInfo.cnContent &&\r\n            drawerInfo.cnContent.replace(/<img\\b[^>]*>/gi, '')\r\n          \"\r\n        ></div>\r\n        <el-empty\r\n          description=\"当前文章暂无数据\"\r\n          v-if=\"!drawerInfo.cnContent\"\r\n        ></el-empty>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"'修改文章'\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"dialog_Box\">\r\n        <el-form\r\n          :model=\"form\"\r\n          class=\"form_Style\"\r\n          label-position=\"top\"\r\n          ref=\"form\"\r\n          :rules=\"rules\"\r\n          size=\"mini\"\r\n        >\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"文章标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"中文标题\" prop=\"cnTitle\">\r\n                <el-input v-model=\"form.cnTitle\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n                <el-select\r\n                  v-model=\"form.sourceType\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.id\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"媒体来源\" prop=\"sourceName\">\r\n                <el-select\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  filterable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeLists\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.name\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发布时间\" prop=\"publishTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.publishTime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"datetime\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  placeholder=\"选择日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"文章作者\" prop=\"author\">\r\n                <el-input\r\n                  v-model=\"form.author\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"原文链接\" prop=\"originalUrl\">\r\n            <el-input v-model=\"form.originalUrl\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"摘要\" prop=\"summary\">\r\n            <el-input\r\n              v-model=\"form.summary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文摘要\" prop=\"cnSummary\">\r\n            <el-input\r\n              v-model=\"form.cnSummary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文内容\" prop=\"cnContent\">\r\n            <editor v-model=\"form.cnContent\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <el-form-item label=\"文章内容\" prop=\"content\">\r\n            <editor v-model=\"form.content\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <!-- <el-form-item label=\"封面图片\" prop=\"cover\">\r\n            <el-upload action=\"#\" ref=\"upload\" :limit=\"3\" :on-exceed=\"exceed\" list-type=\"picture-card\"\r\n              :auto-upload=\"false\" :headers=\"vertifyUpload.headers\" :file-list=\"fileList\" :on-change=\"handleChange\"\r\n              :http-request=\"requestLoad\">\r\n              <i slot=\"default\" class=\"el-icon-plus\"></i>\r\n              <div slot=\"file\" slot-scope=\"{ file }\">\r\n                <img class=\"el-upload-list__item-thumbnail\" :src=\"file.url\" alt=\"文件缩略图加载失败\" />\r\n                <span class=\"el-upload-list__item-actions\">\r\n                  <span v-if=\"!disabled\" class=\"el-upload-list__item-delete\" @click=\"handleRemove(file)\">\r\n                    <i class=\"el-icon-delete\"></i>\r\n                  </span>\r\n                </span>\r\n              </div>\r\n            </el-upload>\r\n          </el-form-item>\r\n          <el-form-item label=\"上传附件\" prop=\"fileUrl\">\r\n            <el-upload class=\"upload-demo\" :action=\"fileUrlurl\" :before-upload=\"beforeUploadUrl\" multiple :limit=\"1\"\r\n              :http-request=\"uploadUrlRequest\" :on-success=\"uploadUrlSuccess\" :file-list=\"fileUrlList\"\r\n              :on-exceed=\"uploadUrlExceed\" :on-remove=\"uploadUrlRemove\">\r\n              <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>\r\n            </el-upload>\r\n          </el-form-item> -->\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量导入弹框 -->\r\n    <el-dialog\r\n      title=\"批量导入报告\"\r\n      :visible.sync=\"batchImportVisible\"\r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"batch-import-container\">\r\n        <!-- 文件选择区域 -->\r\n        <div class=\"file-select-area\">\r\n          <el-upload\r\n            ref=\"batchUpload\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :show-file-list=\"false\"\r\n            multiple\r\n            :on-change=\"handleFileSelect\"\r\n            accept=\".pdf,.doc,.docx,.txt\"\r\n          >\r\n            <el-button type=\"primary\" icon=\"el-icon-upload\">选择文件</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">\r\n              支持选择多个文件，格式：PDF、DOC、DOCX、TXT\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n\r\n        <!-- 文件列表表格 -->\r\n        <div class=\"file-table-area\" v-if=\"batchImportFiles.length > 0\">\r\n          <el-table\r\n            :data=\"batchImportFiles\"\r\n            border\r\n            style=\"width: 100%; margin-top: 20px\"\r\n            max-height=\"300\"\r\n          >\r\n            <el-table-column prop=\"fileName\" label=\"文件名称\" width=\"300\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.fileName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"sourceName\" label=\"数据源名称\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input\r\n                  v-model=\"scope.row.sourceName\"\r\n                  placeholder=\"请输入数据源名称\"\r\n                  size=\"small\"\r\n                ></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"removeFile(scope.$index)\"\r\n                  style=\"color: #f56c6c\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelBatchImport\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"confirmBatchImport\"\r\n          :disabled=\"batchImportFiles.length === 0\"\r\n        >\r\n          确 认\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- Deepseek报告解读弹窗 -->\r\n    <deepseek-report-dialog\r\n      :visible.sync=\"showDeepseekDialog\"\r\n      :article-data=\"currentArticle\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport request from \"@/utils/request\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { MessageBox } from \"element-ui\";\r\nimport axios from \"axios\";\r\nimport { getListClassify } from \"@/api/article/classify\";\r\nimport { saveAs } from \"file-saver\";\r\nimport { blobValidate, tansParams } from \"@/utils/ruoyi\";\r\nimport { articleListEdit, uploadCover } from \"@/api/articleCrawler/list\";\r\nimport DeepseekReportDialog from \"./DeepseekReportDialog.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\nimport { getListByIds } from \"@/api/article/articleHistory\";\r\n\r\nexport default {\r\n  props: {\r\n    downLoadShow: {\r\n      /* 下载按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    editShow: {\r\n      /* 编辑按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    copyShow: {\r\n      /* 复制按钮 */ reuqired: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    height: {\r\n      type: Number,\r\n      default: 655,\r\n    },\r\n    currentPage: {\r\n      reuqired: true,\r\n      default: 1,\r\n    },\r\n    pageSize: {\r\n      reuqired: true,\r\n      default: 50,\r\n    },\r\n    total: {\r\n      reuqired: true,\r\n      default: 0,\r\n    },\r\n    ArticleList: {\r\n      required: true,\r\n      default: [],\r\n    },\r\n    flag: {\r\n      required: true,\r\n    },\r\n    SeachData: {\r\n      required: true,\r\n    },\r\n    keywords: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    // 报告类型字段\r\n    sourceType: {\r\n      default: \"\",\r\n    },\r\n  },\r\n  components: {\r\n    DeepseekReportDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      regExpImg: /^\\n$/,\r\n      reportId: \"\",\r\n      reportOptions: [],\r\n      dialogVisible: false,\r\n      checkedCities: [] /* 多选 */,\r\n      checked: false /* 全选 */,\r\n      html: \"\",\r\n      text: \"\",\r\n      that: this,\r\n      tagShow: false,\r\n      isIndeterminate: true,\r\n      count: 0,\r\n      separate: {},\r\n      /* 标签功能 */\r\n      tagDialog: false,\r\n      formLabelAlign: {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      },\r\n      options: [],\r\n      options1: [],\r\n      tagItem: {} /* 标签对象 */,\r\n      areaList: [] /* 领域 */,\r\n      industry: [] /* 行业 */,\r\n      num: 0,\r\n      timer: null,\r\n      drawer: false,\r\n      drawerInfo: {},\r\n      AreaId: null,\r\n      translationBtnShow: null,\r\n      open: false,\r\n      sourceTypeList: [], // 数据源分类\r\n      sourceLists: [], // 数据源列表\r\n      sourceTypeLists: [],\r\n      form: {}, // 表单参数\r\n      rules: {\r\n        // 表单校验\r\n        title: [{ required: true, message: \"文章标题为必填项\" }],\r\n        content: [{ required: true, message: \"文章详情为必填项\" }],\r\n        publishTime: [{ required: true, message: \"发布时间为必填项\" }],\r\n        cnTitle: [{ required: true, message: \"中文名称为必填项\" }],\r\n        sourceType: [{ required: true, message: \"平台类型为必填项\" }],\r\n        originalUrl: [{ required: true, message: \"原文为必填项\" }],\r\n        summary: [{ required: true, message: \"请填写摘要\" }],\r\n        // cnSummary: [{ required: true, message: '请填写中文摘要' }],\r\n        sn: [{ required: true, message: \"请填写文章地址唯一识别号\" }],\r\n      },\r\n      vertifyUpload: {\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: {\r\n          Authorization: \"Bearer \" + getToken(),\r\n          ContentType: \"application/json;charset=utf-8\",\r\n        },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/article/articleList/cover\",\r\n      },\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      fileUrlurl:\r\n        process.env.VUE_APP_BASE_API + \"/article/articleList/upload/file\",\r\n      showSummary: true,\r\n      // 批量导入相关数据\r\n      batchImportVisible: false,\r\n      batchImportFiles: [],\r\n      // Deepseek报告解读弹窗\r\n      showDeepseekDialog: false,\r\n      currentArticle: {},\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n    };\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    dialogVisible: function (newVal, oldVal) {\r\n      if (newVal) {\r\n        API.getNewBuilt({ sourceType: this.sourceType }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 'formLabelAlign.industry': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options1 = this.industry\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    // 'formLabelAlign.domain': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options = this.areaList\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    \"SeachData.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        this.Refresh();\r\n      },\r\n      deep: true,\r\n    },\r\n    \"form.sourceType\": {\r\n      handler(newVal, oldVal) {\r\n        this.sourceTypeLists = this.sourceLists.filter((item) => {\r\n          return item.type == newVal;\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    if (\r\n      this.flag !== \"MonitorUse\" &&\r\n      this.flag !== \"specialSubjectUse\" &&\r\n      this.flag !== \"Wechat\"\r\n    ) {\r\n      this.openDialog();\r\n    }\r\n    if (this.flag !== \"Wechat\") {\r\n      getListClassify().then((res) => {\r\n        this.sourceTypeList = res.data;\r\n      });\r\n      API.getSourceList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.sourceLists = data.data;\r\n        }\r\n      });\r\n    }\r\n    if (this.$route.query.domain) {\r\n      getConfigKey(\"sys.ai.platform\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.aiPlatform = res.msg;\r\n        }\r\n      });\r\n      getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.articleAiPrompt = res.msg;\r\n        }\r\n      });\r\n      // 获取用户头像\r\n      this.userAvatar = this.$store.getters.avatar;\r\n    }\r\n\r\n    this.showSummary = true;\r\n  },\r\n  updated() {},\r\n  filters: {},\r\n  methods: {\r\n    // 处理科技相关字段的显示映射\r\n    getTechnologyLabel(value) {\r\n      const mapping = {\r\n        0: \"否\",\r\n        1: \"是\",\r\n        2: \"其他\",\r\n        3: \"待定\",\r\n      };\r\n      return mapping[value];\r\n    },\r\n    // 安全处理摘要内容，避免null调用replace报错\r\n    getSafeSummary(item) {\r\n      const cnSummary = item.cnSummary || \"\";\r\n      const summary = item.summary || \"\";\r\n\r\n      const processedCnSummary = cnSummary\r\n        ? cnSummary\r\n            .replace(/p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g, \"span\")\r\n            .replace(/<img[^>]*>/g, \"\") // 移除img标签\r\n            .replace(/\\\\n|\\\\\\\\n|\\\\\\\\\\\\n|\\n/g, \" \") // 移除各种换行符\r\n            .replace(/\\s+/g, \" \") // 合并多个空格为一个\r\n            .trim()\r\n        : \"\";\r\n\r\n      const processedSummary = summary\r\n        ? summary\r\n            .replace(/p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6|ul|li|a/g, \"span\")\r\n            .replace(/<img[^>]*>/g, \"\") // 移除img标签\r\n            .replace(/\\\\n|\\\\\\\\n|\\\\\\\\\\\\n|\\n/g, \" \") // 移除各种换行符\r\n            .replace(/\\s+/g, \" \") // 合并多个空格为一个\r\n            .trim()\r\n        : \"\";\r\n\r\n      return processedCnSummary || processedSummary;\r\n    },\r\n    // 处理发布时间的显示\r\n    formatPublishTime(publishTime, webstePublishTime) {\r\n      // 格式化publishTime为年月日\r\n      const formattedPublishTime = this.parseTime(publishTime, \"{y}-{m}-{d}\");\r\n\r\n      // 如果webstePublishTime不存在，直接返回publishTime\r\n      if (!webstePublishTime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      }\r\n\r\n      let formattedWebsteTime = \"\";\r\n      // 处理不同格式的webstePublishTime\r\n      if (webstePublishTime) {\r\n        // 处理2025-04-12 10:09:21.971191格式（包含连字符的标准格式）\r\n        if (webstePublishTime.includes(\"-\")) {\r\n          const dateMatch = webstePublishTime.match(/(\\d{4})-(\\d{2})-(\\d{2})/);\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2];\r\n            const day = dateMatch[3];\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年04月14日 11:29:22格式（中文年月日格式，带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\") &&\r\n          webstePublishTime.includes(\"日\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})日/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年4月15格式（中文年月格式，不带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025/04/14 11:29:22格式（斜杠分隔的格式）\r\n        else if (webstePublishTime.includes(\"/\")) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})\\/(\\d{1,2})\\/(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        } else {\r\n          // 其他格式直接使用原值\r\n          formattedWebsteTime = webstePublishTime;\r\n        }\r\n      }\r\n\r\n      // 比较年月日是否相同\r\n      if (formattedPublishTime === formattedWebsteTime) {\r\n        return \"[北京]\" + formattedPublishTime;\r\n      } else {\r\n        return `[北京]${formattedPublishTime} / [当地]${webstePublishTime}`;\r\n      }\r\n    },\r\n\r\n    // 检查文本是否有实际内容（去除HTML标签后）\r\n    hasActualContent(text) {\r\n      if (!text) {\r\n        return false;\r\n      }\r\n      // 去除HTML标签\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      // 检查是否有中文、英文、数字等实际内容\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n    // 关键字替换\r\n    changeColor(str) {\r\n      let Str = str;\r\n      if (Str) {\r\n        let keywords = this.keywords.split(\",\");\r\n        keywords.map((keyitem, keyindex) => {\r\n          if (keyitem && keyitem.length > 0) {\r\n            // 匹配关键字正则\r\n            let replaceReg = new RegExp(keyitem, \"g\");\r\n            // 高亮替换v-html值\r\n            let replaceString =\r\n              '<span class=\"highlight\"' +\r\n              ' style=\"color: red;\">' +\r\n              keyitem +\r\n              \"</span>\";\r\n            Str = Str.replace(replaceReg, replaceString);\r\n          }\r\n        });\r\n      }\r\n      return Str;\r\n    },\r\n    /* 下载Excel */\r\n    async downLoadExcel() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要导出的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.flag == \"specialSubjectUse\") {\r\n        API.downLoadExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n        });\r\n      } else {\r\n        await API.downLoadExportExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n\r\n          // saveAs(blob, `source_${new Date().getTime()}.xlsx`)\r\n        });\r\n      }\r\n    },\r\n    batchDelete() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要删除的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.checkedCities.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 发布到每日最新热点 */\r\n    publishHot() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({\r\n          message: \"请选择要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.checkedCities.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 返回顶部动画 */\r\n    mainScorll() {\r\n      var scrollStep = -this.$refs.scroll.scrollTop / (800 / 15); // 计算每一步滚动的距离\r\n      var scrollInterval = setInterval(() => {\r\n        if (this.$refs.scroll.scrollTop !== 0) {\r\n          this.$refs.scroll.scrollBy(0, scrollStep); // 按照给定步长滚动窗口\r\n        } else {\r\n          clearInterval(scrollInterval); // 到达顶部时清除定时器\r\n        }\r\n      }, 15);\r\n    },\r\n    scrollChange() {\r\n      clearTimeout(this.timer);\r\n      this.timer = setTimeout(() => {\r\n        this.stopScroll();\r\n      }, 500);\r\n      // this.$refs.pagination.style.opacity = 0\r\n      // this.$refs.pagination.style.transition = '0'\r\n    } /* 滚动事件 */,\r\n    stopScroll() {\r\n      // this.$refs.pagination.style.transition = '1s'\r\n      // this.$refs.pagination.style.opacity = 1\r\n    },\r\n    /* 下载 */\r\n    downLoad() {\r\n      this.dialogVisible = true;\r\n    },\r\n    /* 每页条数变化 */\r\n    handleSizeChange(num) {\r\n      this.$emit(\"handleSizeChange\", num);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 页码变化 */\r\n    handleCurrentChange(current) {\r\n      this.$emit(\"handleCurrentChange\", current);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 收藏 */\r\n    async collect(item) {\r\n      /* 点击列表收藏 */\r\n      if (item.id) {\r\n        this.checkedCities = [item.id];\r\n      }\r\n      /* 未选择提示 */\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要收藏的文章\", type: \"info\" });\r\n        return;\r\n      }\r\n      /* 收藏 */\r\n      if (!item.favorites) {\r\n        let res = await API.collectApi([item.id]);\r\n        if (res.code) {\r\n          this.$message({\r\n            message: \"收藏成功,请前往个人中心查看\",\r\n            type: \"success\",\r\n          });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"收藏失败\", type: \"info\" });\r\n      } else {\r\n        let res = await API.cocelCollect([item.id]);\r\n        if (res.code) {\r\n          this.$message({ message: \"已取消收藏\", type: \"success\" });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"取消收藏失败\", type: \"info\" });\r\n      }\r\n    },\r\n    /* 一键复制 */\r\n    copyText(item) {\r\n      navigator.clipboard\r\n        .writeText(item.cnTitle)\r\n        .then(() => {\r\n          this.$message({ message: \"已成功复制到剪贴板\", type: \"success\" });\r\n        })\r\n        .catch(function () {\r\n          alert(\"复制失败\");\r\n        });\r\n    },\r\n    typeHandle(data) {\r\n      if (data == 1) {\r\n        return \"微信公众号\";\r\n      } else if (data == 2) {\r\n        return \"网站\";\r\n      } else if (data == 3) {\r\n        return \"手动录入\";\r\n      }\r\n    },\r\n    /* 选择事件 */\r\n    handleCheckedCitiesChange(value) {\r\n      this.checkedCities = value;\r\n    },\r\n    /* 全选 */\r\n    handleCheckAllChange(val) {\r\n      this.checkedCities = val ? this.ArticleList.map((item) => item.id) : [];\r\n      this.isIndeterminate = false;\r\n    },\r\n    /* 刷新 */\r\n    Refresh() {\r\n      this.$emit(\"Refresh\");\r\n    },\r\n    /*确定添加到报告 */\r\n    async reportSubmit() {\r\n      this.dialogVisible = false;\r\n      let keyWordList = [];\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      /* 单独添加 */\r\n      if (this.separate.id) {\r\n        // let keyword = Object.keys(this.separate.keywordCount)\r\n        keyWordList.push({\r\n          reportId: this.reportId,\r\n          listId: this.separate.id,\r\n          listSn: this.separate.sn,\r\n        });\r\n      } else {\r\n        /* 批量添加 */\r\n        if (this.checkedCities == \"\")\r\n          return this.$message({\r\n            message: \"请选择要添加的数据\",\r\n            type: \"warning\",\r\n          });\r\n        this.checkedCities.forEach((item) => {\r\n          let article = this.ArticleList.filter((value) => value.id == item);\r\n          keyWordList.push({\r\n            reportId: this.reportId,\r\n            listId: item,\r\n            listSn: article[0].sn,\r\n          });\r\n        });\r\n      }\r\n      let res = await API.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.$emit(\"Refresh\");\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.separate = {};\r\n      this.reportId = \"\";\r\n      this.checkedCities = [];\r\n      this.checked = false;\r\n    },\r\n    /* 单独添加报告 */\r\n    separateAdd(item) {\r\n      this.dialogVisible = true;\r\n      this.separate = item;\r\n    },\r\n    /* 跳转新页面 */\r\n    openNewView(item, isLink) {\r\n      if (isLink) {\r\n        if (item.originalUrl) {\r\n          window.open(item.originalUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n        return;\r\n      }\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n      // this.drawerInfo = item\r\n      // this.drawer = true\r\n    },\r\n    /* 文章打标签 */\r\n    tagHandler(item) {\r\n      this.tagDialog = true;\r\n      this.tagItem = item;\r\n      if (item.industry) {\r\n        this.formLabelAlign.industry = item.industry\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      if (item.domain) {\r\n        this.formLabelAlign.domain = item.domain\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      this.formLabelAlign.tag = item.tags ? item.tags.split(\",\") : \"\";\r\n    },\r\n    /* 获取领域和分类 */\r\n    async openDialog() {\r\n      await API.areaList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.areaList = data.data;\r\n          this.options = data.data;\r\n          API.industry().then((value) => {\r\n            this.industry = value.data;\r\n            this.options1 = value.data;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 筛选领域 */\r\n    remoteEvent(query) {\r\n      this.options = this.areaList.filter((item) => item.fieldName == query);\r\n    },\r\n    /* 筛选行业 */\r\n    remoteIndustry(query) {\r\n      this.options1 = this.industry.filter(\r\n        (item) => item.industryName == query\r\n      );\r\n    },\r\n    async SubmitTag() {\r\n      let params = {\r\n        domain: String(this.formLabelAlign.domain),\r\n        industry: String(this.formLabelAlign.industry),\r\n        tags: String(this.formLabelAlign.tag),\r\n        articleId: String(this.tagItem.id),\r\n        docId: this.tagItem.docId ? String(this.tagItem.docId) : \"\",\r\n      };\r\n      let res = await API.tagAdd(params);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"保存成功\", type: \"success\" });\r\n        setTimeout(() => {\r\n          this.Refresh();\r\n        }, 1000);\r\n      } else {\r\n        this.$message({ message: \"保存失败\", type: \"error\" });\r\n      }\r\n      this.closeTag();\r\n    },\r\n    closeTag() {\r\n      this.$refs[\"ruleForm\"].resetFields();\r\n      this.formLabelAlign = {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      };\r\n      this.tagDialog = false;\r\n    },\r\n    async hotIncrease(item) {\r\n      let isWhether = JSON.parse(item.isWhether);\r\n      let res = await API.tagAdd({\r\n        articleId: item.id,\r\n        isWhether: +!Boolean(isWhether),\r\n      });\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"操作成功\", type: \"success\" });\r\n        this.Refresh();\r\n      } else {\r\n        this.$message({ message: \"操作失败\", type: \"error\" });\r\n      }\r\n    },\r\n    async openDrawer() {\r\n      let docId = this.drawerInfo.docId;\r\n      await API.AreaInfo(this.drawerInfo.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.drawerInfo = res.data;\r\n          this.drawerInfo.docId = docId;\r\n          /* 将字符串中的\\n替换为<br> */\r\n          this.translationBtnShow = !this.drawerInfo.cnContent;\r\n          if (this.drawerInfo.cnContent || this.drawerInfo.content) {\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\\\n/g, (a, b, c) => {\r\n              return \"<br>\";\r\n            });\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\${[^}]+}/g, \"<br>\");\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(\"|xa0\", \"\");\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /* 所属行业处理 */\r\n    industryHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.industry) {\r\n        ids = item.industry.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.industry.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.industryName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    domainHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.domain) {\r\n        ids = item.domain.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.areaList.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.fieldName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    /* 快照生成 */\r\n    resultEvent(item) {\r\n      if (item == \"BatchGeneration\" && this.checkedCities.length == 0) {\r\n        this.$message.warning(\"请先选择文章\");\r\n        return;\r\n      }\r\n      let ids = null;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (item == \"drawer\") {\r\n        ids = [this.drawerInfo.id];\r\n        if (this.drawerInfo.snapshotUrl) zhuangtai = \"查看\";\r\n        url = this.drawerInfo.snapshotUrl;\r\n      } else if (item == \"BatchGeneration\") {\r\n        ids = this.checkedCities;\r\n      } else {\r\n        ids = [item.id];\r\n        if (item.snapshotUrl) zhuangtai = \"查看\";\r\n        url = item.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        if (this.flag == \"MonitorUse\") {\r\n          API.downLoadExportKe(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        } else {\r\n          API.downLoadExportZhuan(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        }\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n    /* 附件下载 */\r\n    async documentDownload(item) {\r\n      this.loading = true;\r\n      if (item.fileUrl) {\r\n        const urls = item.fileUrl.split(\",\");\r\n        for (const [index, url] of urls.entries()) {\r\n          if (url.indexOf(\"https://\") === -1) {\r\n            setTimeout(async () => {\r\n              await this.downLoadFun(url, index, item.cnTitle || item.title);\r\n            }, index * 500);\r\n          } else {\r\n            this.$message.error(\"附件还没同步到当前系统，暂时无法下载\");\r\n          }\r\n        }\r\n      }\r\n      this.loading = false;\r\n    },\r\n\r\n    async downLoadFun(url, index, title) {\r\n      let formData = new FormData();\r\n      formData.append(\"fileUrl\", url);\r\n\r\n      try {\r\n        const response = await API.downloadFile(formData);\r\n        const isBlob = blobValidate(response);\r\n\r\n        if (isBlob) {\r\n          const blob = new Blob([response]);\r\n          let list = url.split(\"/\");\r\n          let fileName = list[list.length - 1];\r\n          saveAs(blob, fileName);\r\n        } else {\r\n          const resText = await response.text();\r\n          const rspObj = JSON.parse(resText);\r\n          const errMsg =\r\n            errorCode[rspObj.code] || rspObj.msg || errorCode[\"default\"];\r\n          this.$message.error(errMsg);\r\n        }\r\n      } catch (err) {\r\n        // this.$message.error(`Error downloading file: ${err}`);\r\n      } finally {\r\n        // 确保 loading 在每次下载后都设置为 false\r\n        this.loading = false;\r\n      }\r\n\r\n      // 之前的附件下载\r\n      // if (item.annexUrl) {\r\n      //   /* 有文件地址 直接下载 */\r\n      //   let formData = new FormData()\r\n      //   formData.append('id', item.id)\r\n      //   this.loading = true\r\n      //   API.documentDownloadKe(formData).then(res => {\r\n      //     let a = document.createElement('a')\r\n      //     a.href = URL.createObjectURL(res.data)\r\n      //     a.download = res.headers['content-disposition'].split('filename=')[1]\r\n      //     a.click()\r\n      //     this.loading = false\r\n      //   })\r\n      // } else {\r\n      //   /* 没有文件格式 申请下载 */\r\n      //   API.documentDownload(id).then(response => {\r\n      //     if (response.code == 200) {\r\n      //       this.$msgbox({\r\n      //         title: '提示',\r\n      //         message: '附件正在同步中，请稍后下载',\r\n      //         showCancelButton: true,\r\n      //         confirmButtonText: '关闭',\r\n      //         cancelButtonText: '取消',\r\n      //         showCancelButton: false,\r\n      //         beforeClose: (action, instance, done) => {\r\n      //           done()\r\n      //         }\r\n      //       })\r\n      //     } else {\r\n      //       this.$message({ message: '附件下载失败', type: 'error' })\r\n      //     }\r\n      //   }).catch(err => { })\r\n      // }\r\n    },\r\n    // 翻译标题\r\n    translateTitle(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.title,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"title\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    // 翻译文章\r\n    translateEvent(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.content,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"content\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          this.translationBtnShow = false;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      API.AreaInfo(row.id).then((response) => {\r\n        this.form = response.data;\r\n        this.form.sourceType = Number(this.form.sourceType);\r\n        this.form.docId = row.docId;\r\n        // this.fileUrlList = this.form.fileUrl ? this.form.fileUrl.split(\",\").map(item => {\r\n        //   return {\r\n        //     name: item,\r\n        //     url: item\r\n        //   }\r\n        // }) : []\r\n        this.open = true;\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal\r\n        .confirm('是否确认删除该条文章？\"')\r\n        .then(() => {\r\n          return API.monitoringEsRemove({ id: row.id, docId: row.docId });\r\n        })\r\n        .then(() => {\r\n          this.Refresh();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          let queryForm = JSON.parse(JSON.stringify(this.form));\r\n          // let cover = String(this.fileList.map(item => item.path))\r\n          // queryForm.cover = cover\r\n          articleListEdit(queryForm).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.Refresh();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 自定义上传 */\r\n    async requestLoad(file) {\r\n      let data = new FormData();\r\n      data.append(\"cover\", file.file);\r\n      await uploadCover(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.fileList.map((item) => {\r\n            if (item.uid == file.file.uid) {\r\n              item.path = response.imgUrl;\r\n            }\r\n          });\r\n          this.$message({ message: \"上传成功\", type: \"success\" });\r\n        } else {\r\n          this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    /* 文件超出限制 */\r\n    exceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传三个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    /* 移除文件 */\r\n    handleRemove(file) {\r\n      this.fileList = this.fileList.filter((item) => item !== file);\r\n    },\r\n    // 文件更改\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList;\r\n      this.$refs.upload.submit();\r\n    },\r\n    // 上传附件校验\r\n    beforeUploadUrl(file) {\r\n      // 判断文件是否为excel\r\n      let fileName = file.name\r\n          .substring(file.name.lastIndexOf(\".\") + 1)\r\n          .toLowerCase(),\r\n        condition =\r\n          fileName == \"pdf\" ||\r\n          fileName == \"doc\" ||\r\n          fileName == \"xls\" ||\r\n          fileName == \"ppt\" ||\r\n          fileName == \"xlsx\" ||\r\n          fileName == \"pptx\" ||\r\n          fileName == \"docx\";\r\n      let fileSize = file.size / 1024 / 1024 < 10;\r\n      if (!condition) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件必须是pdf,doc,xls,ppt,xlsx,pptx,docx格式\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      /* 文件大小限制 */\r\n      if (!fileSize) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件的大小不能超过 10MB!\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      return condition && fileSize;\r\n    },\r\n    // 文件上传成功\r\n    uploadUrlSuccess(res, file) {\r\n      this.$message({ message: \"上传成功\", type: \"success\" });\r\n    },\r\n    // 文件上传超出限制\r\n    uploadUrlExceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传1个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    // 文件上传方法\r\n    uploadUrlRequest(file) {\r\n      if (this.form.originalUrl != null && this.form.originalUrl != \"\") {\r\n        if (\r\n          this.form.originalUrl.match(\r\n            /(http|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/\r\n          )\r\n        ) {\r\n          let data = new FormData();\r\n          data.append(\"file\", file.file);\r\n          data.append(\"originalUrl\", this.form.originalUrl);\r\n\r\n          API.uploadFile(data).then((response) => {\r\n            if (response.code == 200) {\r\n              this.$message({ message: \"上传成功\", type: \"success\" });\r\n              this.form.fileUrl = response.data;\r\n            } else {\r\n              this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n              this.form.fileUrl = \"\";\r\n            }\r\n          });\r\n        } else {\r\n          this.$message({ message: \"请填写正确的原文链接\", type: \"warning\" });\r\n          this.fileUrlList = [];\r\n        }\r\n      } else {\r\n        this.$message({ message: \"请填写原文链接\", type: \"warning\" });\r\n        this.fileUrlList = [];\r\n      }\r\n    },\r\n    // 删除附件\r\n    uploadUrlRemove() {\r\n      API.removeFile({ filePath: this.form.fileUrl }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$message({ message: \"删除成功\", type: \"success\" });\r\n          this.fileUrlList = [];\r\n          this.form.fileUrl = \"\";\r\n        } else {\r\n          this.$message({ message: \"删除失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        articleSn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceName: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: null,\r\n        deptId: null,\r\n        content: null,\r\n        cnContent: null,\r\n        fileUrl: null,\r\n        industry: null,\r\n        domain: null,\r\n        tmpUrl: null,\r\n        isFinish: null,\r\n        groupId: null,\r\n        appId: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 批量导入相关方法\r\n    // 打开批量导入弹框\r\n    openBatchImportDialog() {\r\n      this.batchImportVisible = true;\r\n      this.batchImportFiles = [];\r\n    },\r\n    // 文件选择处理\r\n    handleFileSelect(file, fileList) {\r\n      // 将新选择的文件添加到列表中\r\n      const newFiles = fileList.map((item) => ({\r\n        fileName: item.name,\r\n        file: item.raw,\r\n        sourceName: \"\",\r\n      }));\r\n      this.batchImportFiles = newFiles;\r\n    },\r\n    // 删除文件\r\n    removeFile(index) {\r\n      this.batchImportFiles.splice(index, 1);\r\n    },\r\n    // 取消批量导入\r\n    cancelBatchImport() {\r\n      this.batchImportVisible = false;\r\n      this.batchImportFiles = [];\r\n      // 清空文件选择器\r\n      this.$refs.batchUpload.clearFiles();\r\n    },\r\n    // 确认批量导入\r\n    async confirmBatchImport() {\r\n      // 验证数据源名称是否都已填写\r\n      const emptySourceNames = this.batchImportFiles.filter(\r\n        (item) => !item.sourceName.trim()\r\n      );\r\n      if (emptySourceNames.length > 0) {\r\n        this.$message({\r\n          message: \"请为所有文件填写数据源名称\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 创建FormData对象\r\n        const formData = new FormData();\r\n\r\n        // 添加文件到FormData\r\n        this.batchImportFiles.forEach((item) => {\r\n          formData.append(\"files\", item.file);\r\n        });\r\n\r\n        // 获取数据源名称数组\r\n        const sourceNames = this.batchImportFiles\r\n          .map((item) => item.sourceName)\r\n          .join(\",\");\r\n\r\n        formData.append(\"sourceNames\", sourceNames);\r\n\r\n        // 调用批量导入API，传递FormData和sourceNames参数\r\n        const response = await API.batchImportReports(formData);\r\n\r\n        if (response.code === 200) {\r\n          this.$message({\r\n            message: \"批量导入成功\",\r\n            type: \"success\",\r\n          });\r\n          this.batchImportVisible = false;\r\n          this.batchImportFiles = [];\r\n          this.$refs.batchUpload.clearFiles();\r\n          // 刷新列表\r\n          this.Refresh();\r\n        } else {\r\n          this.$message({\r\n            message: response.msg || \"批量导入失败\",\r\n            type: \"error\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"批量导入错误:\", error);\r\n        this.$message({\r\n          message: \"批量导入失败，请稍后重试\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    reportAiChat() {\r\n      this.showDeepseekDialog = true;\r\n      if (this.checkedCities.length === 0) {\r\n        this.$message({ message: \"请选择要解读的文章\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.checkedCities.length > 1) {\r\n        this.$message({ message: \"请只选择一篇文章进行解读\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      // 获取选中的文章\r\n      const selectedArticleId = this.checkedCities[0];\r\n      const selectedArticle = this.ArticleList.find(\r\n        (item) => item.id === selectedArticleId\r\n      );\r\n\r\n      if (selectedArticle) {\r\n        this.currentArticle = selectedArticle;\r\n        this.showDeepseekDialog = true;\r\n      } else {\r\n        this.$message({ message: \"未找到选中的文章\", type: \"error\" });\r\n      }\r\n    },\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.checkedCities.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* drawer样式修改 */\r\n.main ::v-deep .el-drawer.drawer_box {\r\n  width: 700px !important;\r\n}\r\n\r\n.MainArticle {\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  // margin-top: 10px;\r\n  user-select: text;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    align-items: center;\r\n    border-bottom: solid 1px #e2e2e2;\r\n  }\r\n\r\n  .leftBtnGroup {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    gap: 15px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    & > p {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .leftBtnGroup2 {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    gap: 15px;\r\n    flex: 1;\r\n    padding-left: 20px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      line-height: 36px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .scollBox {\r\n    overflow-y: auto;\r\n    height: calc(100vh - 389px);\r\n    transition: transform 5s ease;\r\n\r\n    .Articl {\r\n      display: flex;\r\n      width: 100%;\r\n      border-bottom: solid 1px #d4d4d4;\r\n\r\n      .Articl_left {\r\n        width: 85%;\r\n        padding-bottom: 16px;\r\n      }\r\n\r\n      .Articl_kqcb {\r\n        width: 100%;\r\n        padding-bottom: 16px;\r\n        & > div:first-child {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n        }\r\n      }\r\n\r\n      .ArticlTop {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding: 0 0 0 20px;\r\n        font-size: 15px;\r\n      }\r\n\r\n      .ArticlMain {\r\n        padding: 0 0 0 30px;\r\n        color: #3f3f3f;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .ArticlMain > span:hover {\r\n        color: #1889f3;\r\n        border-bottom: solid 1px #0798f8;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .info_flex {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n      }\r\n\r\n      .ArticlBottom {\r\n        padding: 0 0 0 30px;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 0 25px;\r\n        line-height: 24px;\r\n        color: #9b9b9b;\r\n        font-size: 14px;\r\n\r\n        div {\r\n          text-overflow: ellipsis;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .linkStyle:hover {\r\n          border-bottom: solid 1px #1889f3;\r\n        }\r\n\r\n        .infomation {\r\n          color: #464749;\r\n          font-size: 14px;\r\n          margin-left: 8px;\r\n        }\r\n\r\n        p {\r\n          border: solid 1px #f0a147;\r\n          width: 45px;\r\n          height: 25px;\r\n          line-height: 25px;\r\n          font-size: 14px;\r\n          color: #f0a147;\r\n          text-align: center;\r\n        }\r\n      }\r\n\r\n      .imgBox {\r\n        padding: 0 20px 0 30px;\r\n        display: flex;\r\n        gap: 15px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.pagination {\r\n  z-index: 2;\r\n  background-color: rgba(255, 255, 255, 1);\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  padding: 10px 0;\r\n}\r\n\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n  line-height: 16px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n\r\n.drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n.drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n.btnBox {\r\n  z-index: 2;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n\r\n  & > p {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 批量导入弹框样式\r\n.batch-import-container {\r\n  .file-select-area {\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    background-color: #fafafa;\r\n\r\n    &:hover {\r\n      border-color: #409eff;\r\n    }\r\n  }\r\n\r\n  .file-table-area {\r\n    margin-top: 20px;\r\n\r\n    .el-table {\r\n      border-radius: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #1296db;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #1296db;\r\n}\r\n\r\n.deepseek-text {\r\n  color: #1296db; // 使用与图标相同的颜色\r\n  margin-left: 4px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style>\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA05DA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,UAAA,GAAAN,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;AACA,IAAAS,qBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,GAAA,GAAAV,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,eAAA,GAAAb,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAc,KAAA;IACAC,YAAA;MACA,UAAAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACA,UAAAJ,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAE,QAAA;MACA,UAAAC,QAAA;MACAL,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAI,MAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACAM,WAAA;MACAH,QAAA;MACAH,OAAA;IACA;IACAO,QAAA;MACAJ,QAAA;MACAH,OAAA;IACA;IACAQ,KAAA;MACAL,QAAA;MACAH,OAAA;IACA;IACAS,WAAA;MACAZ,QAAA;MACAG,OAAA;IACA;IACAU,IAAA;MACAb,QAAA;IACA;IACAc,SAAA;MACAd,QAAA;IACA;IACAe,QAAA;MACAd,IAAA,EAAAe,MAAA;MACAb,OAAA;IACA;IACA;IACAc,UAAA;MACAd,OAAA;IACA;EACA;EACAe,UAAA;IACAC,oBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;MACAC,aAAA;MACAC,OAAA;MACAC,IAAA;MACAC,IAAA;MACAC,IAAA;MACAC,OAAA;MACAC,eAAA;MACAC,KAAA;MACAC,QAAA;MACA;MACAC,SAAA;MACAC,cAAA;QACAC,GAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;MACAC,QAAA;MACAL,QAAA;MACAM,GAAA;MACAC,KAAA;MACAC,MAAA;MACAC,UAAA;MACAC,MAAA;MACAC,kBAAA;MACAC,IAAA;MACAC,cAAA;MAAA;MACAC,WAAA;MAAA;MACAC,eAAA;MACAC,IAAA;MAAA;MACAC,KAAA;QACA;QACAC,KAAA;UAAAxD,QAAA;UAAAyD,OAAA;QAAA;QACAC,OAAA;UAAA1D,QAAA;UAAAyD,OAAA;QAAA;QACAE,WAAA;UAAA3D,QAAA;UAAAyD,OAAA;QAAA;QACAG,OAAA;UAAA5D,QAAA;UAAAyD,OAAA;QAAA;QACAxC,UAAA;UAAAjB,QAAA;UAAAyD,OAAA;QAAA;QACAI,WAAA;UAAA7D,QAAA;UAAAyD,OAAA;QAAA;QACAK,OAAA;UAAA9D,QAAA;UAAAyD,OAAA;QAAA;QACA;QACAM,EAAA;UAAA/D,QAAA;UAAAyD,OAAA;QAAA;MACA;MACAO,aAAA;QACAC,WAAA;QACA;QACAC,OAAA;UACAC,aAAA,kBAAAC,cAAA;UACAC,WAAA;QACA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,UAAA,EACAL,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAI,WAAA;MACA;MACAC,kBAAA;MACAC,gBAAA;MACA;MACAC,kBAAA;MACAC,cAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,UAAA;MACAC,UAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;QACAC,GAAA;QACAC,MAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA;MACAC,YAAA;MAAA;MACAC,SAAA;MAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;EACAC,KAAA;IACA/E,aAAA,WAAAA,cAAAgF,MAAA,EAAAC,MAAA;MAAA,IAAAC,KAAA;MACA,IAAAF,MAAA;QACAG,cAAA,CAAAC,WAAA;UAAA5F,UAAA,OAAAA;QAAA,GAAA6F,IAAA,WAAA1F,IAAA;UACA,IAAAA,IAAA,CAAA2F,IAAA;YACAJ,KAAA,CAAAnF,aAAA,GAAAJ,IAAA,CAAAA,IAAA;UACA;YACAuF,KAAA,CAAAK,QAAA;cAAAvD,OAAA;cAAAxD,IAAA;YAAA;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAgH,OAAA,WAAAA,QAAAR,MAAA,EAAAC,MAAA;QACA,KAAAQ,OAAA;MACA;MACAC,IAAA;IACA;IACA;MACAF,OAAA,WAAAA,QAAAR,MAAA,EAAAC,MAAA;QACA,KAAArD,eAAA,QAAAD,WAAA,CAAAgE,MAAA,WAAAC,IAAA;UACA,OAAAA,IAAA,CAAApH,IAAA,IAAAwG,MAAA;QACA;MACA;MACAU,IAAA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,IACA,KAAA3G,IAAA,qBACA,KAAAA,IAAA,4BACA,KAAAA,IAAA,eACA;MACA,KAAA4G,UAAA;IACA;IACA,SAAA5G,IAAA;MACA,IAAA6G,yBAAA,IAAAZ,IAAA,WAAAa,GAAA;QACAH,MAAA,CAAArE,cAAA,GAAAwE,GAAA,CAAAvG,IAAA;MACA;MACAwF,cAAA,CAAAgB,aAAA,GAAAd,IAAA,WAAA1F,IAAA;QACA,IAAAA,IAAA,CAAA2F,IAAA;UACAS,MAAA,CAAApE,WAAA,GAAAhC,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACA,SAAAyG,MAAA,CAAAC,KAAA,CAAAvF,MAAA;MACA,IAAAwF,oBAAA,qBAAAjB,IAAA,WAAAa,GAAA;QACA,IAAAA,GAAA,CAAAZ,IAAA;UACAS,MAAA,CAAAnB,UAAA,GAAAsB,GAAA,CAAAK,GAAA;QACA;MACA;MACA,IAAAD,oBAAA,6BAAAjB,IAAA,WAAAa,GAAA;QACA,IAAAA,GAAA,CAAAZ,IAAA;UACAS,MAAA,CAAAlB,eAAA,GAAAqB,GAAA,CAAAK,GAAA;QACA;MACA;MACA;MACA,KAAA3C,UAAA,QAAA4C,MAAA,CAAAC,OAAA,CAAAC,MAAA;IACA;IAEA,KAAAtD,WAAA;EACA;EACAuD,OAAA,WAAAA,QAAA;EACAC,OAAA;EACAC,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,KAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,KAAA;IACA;IACA;IACAE,cAAA,WAAAA,eAAArB,IAAA;MACA,IAAAsB,SAAA,GAAAtB,IAAA,CAAAsB,SAAA;MACA,IAAA7E,OAAA,GAAAuD,IAAA,CAAAvD,OAAA;MAEA,IAAA8E,kBAAA,GAAAD,SAAA,GACAA,SAAA,CACAE,OAAA,6DACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAC,IAAA,KACA;MAEA,IAAAC,gBAAA,GAAAjF,OAAA,GACAA,OAAA,CACA+E,OAAA,6DACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAC,IAAA,KACA;MAEA,OAAAF,kBAAA,IAAAG,gBAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAArF,WAAA,EAAAsF,iBAAA;MACA;MACA,IAAAC,oBAAA,QAAAC,SAAA,CAAAxF,WAAA;;MAEA;MACA,KAAAsF,iBAAA;QACA,gBAAAC,oBAAA;MACA;MAEA,IAAAE,mBAAA;MACA;MACA,IAAAH,iBAAA;QACA;QACA,IAAAA,iBAAA,CAAAI,QAAA;UACA,IAAAC,SAAA,GAAAL,iBAAA,CAAAM,KAAA;UACA,IAAAD,SAAA;YACA,IAAAE,IAAA,GAAAF,SAAA;YACA,IAAAG,KAAA,GAAAH,SAAA;YACA,IAAAI,GAAA,GAAAJ,SAAA;YACAF,mBAAA,MAAAO,MAAA,CAAAH,IAAA,OAAAG,MAAA,CAAAF,KAAA,OAAAE,MAAA,CAAAD,GAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;QACA;QAAA,KACA,IACAA,iBAAA,CAAAI,QAAA,SACAJ,iBAAA,CAAAI,QAAA,SACAJ,iBAAA,CAAAI,QAAA,OACA;UACA,IAAAC,UAAA,GAAAL,iBAAA,CAAAM,KAAA,CACA,8BACA;UACA,IAAAD,UAAA;YACA,IAAAE,KAAA,GAAAF,UAAA;YACA,IAAAG,MAAA,GAAAH,UAAA,IAAAM,QAAA;YACA,IAAAF,IAAA,GAAAJ,UAAA,IAAAM,QAAA;YACAR,mBAAA,MAAAO,MAAA,CAAAH,KAAA,OAAAG,MAAA,CAAAF,MAAA,OAAAE,MAAA,CAAAD,IAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;QACA;QAAA,KACA,IACAA,iBAAA,CAAAI,QAAA,SACAJ,iBAAA,CAAAI,QAAA,OACA;UACA,IAAAC,WAAA,GAAAL,iBAAA,CAAAM,KAAA,CACA,6BACA;UACA,IAAAD,WAAA;YACA,IAAAE,MAAA,GAAAF,WAAA;YACA,IAAAG,OAAA,GAAAH,WAAA,IAAAM,QAAA;YACA,IAAAF,KAAA,GAAAJ,WAAA,IAAAM,QAAA;YACAR,mBAAA,MAAAO,MAAA,CAAAH,MAAA,OAAAG,MAAA,CAAAF,OAAA,OAAAE,MAAA,CAAAD,KAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;QACA;QAAA,KACA,IAAAA,iBAAA,CAAAI,QAAA;UACA,IAAAC,WAAA,GAAAL,iBAAA,CAAAM,KAAA,CACA,+BACA;UACA,IAAAD,WAAA;YACA,IAAAE,MAAA,GAAAF,WAAA;YACA,IAAAG,OAAA,GAAAH,WAAA,IAAAM,QAAA;YACA,IAAAF,KAAA,GAAAJ,WAAA,IAAAM,QAAA;YACAR,mBAAA,MAAAO,MAAA,CAAAH,MAAA,OAAAG,MAAA,CAAAF,OAAA,OAAAE,MAAA,CAAAD,KAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;UACA;UACAG,mBAAA,GAAAH,iBAAA;QACA;MACA;;MAEA;MACA,IAAAC,oBAAA,KAAAE,mBAAA;QACA,gBAAAF,oBAAA;MACA;QACA,wBAAAS,MAAA,CAAAT,oBAAA,uBAAAS,MAAA,CAAAV,iBAAA;MACA;IACA;IAEA;IACAY,gBAAA,WAAAA,iBAAAhI,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA;MACA,IAAAiI,kBAAA,GAAAjI,IAAA,CAAAgH,OAAA;MACA;MACA,kCAAAkB,IAAA,CAAAD,kBAAA;IACA;IACA;IACAE,WAAA,WAAAA,YAAAC,GAAA;MACA,IAAAC,GAAA,GAAAD,GAAA;MACA,IAAAC,GAAA;QACA,IAAAnJ,QAAA,QAAAA,QAAA,CAAAoJ,KAAA;QACApJ,QAAA,CAAAqJ,GAAA,WAAAC,OAAA,EAAAC,QAAA;UACA,IAAAD,OAAA,IAAAA,OAAA,CAAAE,MAAA;YACA;YACA,IAAAC,UAAA,OAAAC,MAAA,CAAAJ,OAAA;YACA;YACA,IAAAK,aAAA,GACA,4BACA,0BACAL,OAAA,GACA;YACAH,GAAA,GAAAA,GAAA,CAAArB,OAAA,CAAA2B,UAAA,EAAAE,aAAA;UACA;QACA;MACA;MACA,OAAAR,GAAA;IACA;IACA,aACAS,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,MACAT,MAAA,CAAAlJ,aAAA,CAAA6I,MAAA;gBAAAY,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAT,MAAA,CAAA5D,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cAAA,OAAAkL,QAAA,CAAAG,MAAA;YAAA;cAAA,MAIAV,MAAA,CAAA/J,IAAA;gBAAAsK,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAzE,cAAA,CAAA+D,aAAA,CAAAC,MAAA,CAAAlJ,aAAA,EAAAoF,IAAA,WAAAyE,QAAA;gBACA,IAAAC,CAAA,GAAAC,QAAA,CAAAC,aAAA;gBACAF,CAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAP,QAAA;gBACAC,CAAA,CAAAO,QAAA,aAAApC,MAAA,KAAAqC,IAAA,GAAAC,OAAA;gBACAT,CAAA,CAAAU,KAAA;cACA;cAAAf,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAEAzE,cAAA,CAAAuF,mBAAA,CAAAvB,MAAA,CAAAlJ,aAAA,EAAAoF,IAAA,WAAAyE,QAAA;gBACA,IAAAC,CAAA,GAAAC,QAAA,CAAAC,aAAA;gBACAF,CAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAP,QAAA;gBACAC,CAAA,CAAAO,QAAA,aAAApC,MAAA,KAAAqC,IAAA,GAAAC,OAAA;gBACAT,CAAA,CAAAU,KAAA;;gBAEA;cACA;YAAA;YAAA;cAAA,OAAAf,QAAA,CAAAiB,IAAA;UAAA;QAAA,GAAApB,OAAA;MAAA;IAEA;IACAqB,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAA5K,aAAA,CAAA6I,MAAA;QACA,KAAAvD,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA;MACA;MACA,KAAAsM,QAAA,mBACAzF,IAAA;QACAF,cAAA,CAAA4F,WAAA,CAAAF,MAAA,CAAA5K,aAAA,CAAA+K,IAAA,OAAA3F,IAAA,WAAAyE,QAAA;UACAe,MAAA,CAAAtF,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;UACAqM,MAAA,CAAAI,KAAA;UACAJ,MAAA,CAAA5K,aAAA;QACA;MACA,GACAiL,KAAA;IACA;IACA,eACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAAnL,aAAA,CAAA6I,MAAA;QACA,KAAAvD,QAAA;UACAvD,OAAA;UACAxD,IAAA;QACA;QACA;MACA;MACA,KAAAsM,QAAA,0BACAzF,IAAA;QACAF,cAAA,CAAAkG,kBAAA,CAAAD,MAAA,CAAAnL,aAAA,CAAA+K,IAAA,OAAA3F,IAAA;UACA+F,MAAA,CAAA7F,QAAA;YAAA/G,IAAA;YAAAwD,OAAA;UAAA;UACAoJ,MAAA,CAAAH,KAAA;UACAG,MAAA,CAAAnL,aAAA;QACA;MACA,GACAiL,KAAA;IACA;IACA,YACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,UAAA,SAAAC,KAAA,CAAAC,MAAA,CAAAC,SAAA;MACA,IAAAC,cAAA,GAAAC,WAAA;QACA,IAAAN,MAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAC,SAAA;UACAJ,MAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAI,QAAA,IAAAN,UAAA;QACA;UACAO,aAAA,CAAAH,cAAA;QACA;MACA;IACA;IACAI,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACAC,YAAA,MAAA9K,KAAA;MACA,KAAAA,KAAA,GAAA+K,UAAA;QACAF,MAAA,CAAAG,UAAA;MACA;MACA;MACA;IACA;IACAA,UAAA,WAAAA,WAAA;MACA;MACA;IAAA,CACA;IACA,QACAC,QAAA,WAAAA,SAAA;MACA,KAAArM,aAAA;IACA;IACA,YACAsM,gBAAA,WAAAA,iBAAAnL,GAAA;MACA,KAAA8J,KAAA,qBAAA9J,GAAA;MACA,KAAAmK,UAAA;MACA,KAAApL,OAAA;IACA;IACA,UACAqM,mBAAA,WAAAA,oBAAAC,OAAA;MACA,KAAAvB,KAAA,wBAAAuB,OAAA;MACA,KAAAlB,UAAA;MACA,KAAApL,OAAA;IACA;IACA,QACAuM,OAAA,WAAAA,QAAA7G,IAAA;MAAA,IAAA8G,MAAA;MAAA,WAAAtD,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAqD,SAAA;QAAA,IAAAzG,GAAA,EAAA0G,IAAA;QAAA,WAAAvD,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAqD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnD,IAAA,GAAAmD,SAAA,CAAAlD,IAAA;YAAA;cACA;cACA,IAAAhE,IAAA,CAAAmH,EAAA;gBACAL,MAAA,CAAAzM,aAAA,IAAA2F,IAAA,CAAAmH,EAAA;cACA;cACA;cAAA,MACAL,MAAA,CAAAzM,aAAA,CAAA6I,MAAA;gBAAAgE,SAAA,CAAAlD,IAAA;gBAAA;cAAA;cACA8C,MAAA,CAAAnH,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cAAA,OAAAsO,SAAA,CAAAjD,MAAA;YAAA;cAAA,IAIAjE,IAAA,CAAAoH,SAAA;gBAAAF,SAAA,CAAAlD,IAAA;gBAAA;cAAA;cAAAkD,SAAA,CAAAlD,IAAA;cAAA,OACAzE,cAAA,CAAA8H,UAAA,EAAArH,IAAA,CAAAmH,EAAA;YAAA;cAAA7G,GAAA,GAAA4G,SAAA,CAAAI,IAAA;cAAA,KACAhH,GAAA,CAAAZ,IAAA;gBAAAwH,SAAA,CAAAlD,IAAA;gBAAA;cAAA;cACA8C,MAAA,CAAAnH,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;cACAkO,MAAA,CAAAzB,KAAA;cACAyB,MAAA,CAAAzM,aAAA;cAAA,OAAA6M,SAAA,CAAAjD,MAAA;YAAA;cAGA6C,MAAA,CAAAnH,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cAAAsO,SAAA,CAAAlD,IAAA;cAAA;YAAA;cAAAkD,SAAA,CAAAlD,IAAA;cAAA,OAEAzE,cAAA,CAAAgI,YAAA,EAAAvH,IAAA,CAAAmH,EAAA;YAAA;cAAA7G,IAAA,GAAA4G,SAAA,CAAAI,IAAA;cAAA,KACAhH,IAAA,CAAAZ,IAAA;gBAAAwH,SAAA,CAAAlD,IAAA;gBAAA;cAAA;cACA8C,MAAA,CAAAnH,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cACAkO,MAAA,CAAAzB,KAAA;cACAyB,MAAA,CAAAzM,aAAA;cAAA,OAAA6M,SAAA,CAAAjD,MAAA;YAAA;cAGA6C,MAAA,CAAAnH,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;YAAA;YAAA;cAAA,OAAAsO,SAAA,CAAAnC,IAAA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IAEA;IACA,UACAS,QAAA,WAAAA,SAAAxH,IAAA;MAAA,IAAAyH,MAAA;MACAC,SAAA,CAAAC,SAAA,CACAC,SAAA,CAAA5H,IAAA,CAAAzD,OAAA,EACAkD,IAAA;QACAgI,MAAA,CAAA9H,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;MACA,GACA0M,KAAA;QACAuC,KAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA/N,IAAA;MACA,IAAAA,IAAA;QACA;MACA,WAAAA,IAAA;QACA;MACA,WAAAA,IAAA;QACA;MACA;IACA;IACA,UACAgO,yBAAA,WAAAA,0BAAA5G,KAAA;MACA,KAAA9G,aAAA,GAAA8G,KAAA;IACA;IACA,QACA6G,oBAAA,WAAAA,qBAAAC,GAAA;MACA,KAAA5N,aAAA,GAAA4N,GAAA,QAAA1O,WAAA,CAAAwJ,GAAA,WAAA/C,IAAA;QAAA,OAAAA,IAAA,CAAAmH,EAAA;MAAA;MACA,KAAAxM,eAAA;IACA;IACA,QACAkF,OAAA,WAAAA,QAAA;MACA,KAAAwF,KAAA;IACA;IACA,YACA6C,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA3E,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAA0E,SAAA;QAAA,IAAAC,WAAA,EAAA/H,GAAA;QAAA,WAAAmD,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAA0E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxE,IAAA,GAAAwE,SAAA,CAAAvE,IAAA;YAAA;cACAmE,OAAA,CAAA/N,aAAA;cACAiO,WAAA;cAAA,IACAF,OAAA,CAAAjO,QAAA;gBAAAqO,SAAA,CAAAvE,IAAA;gBAAA;cAAA;cAAA,OAAAuE,SAAA,CAAAtE,MAAA,WACAkE,OAAA,CAAAxI,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAEAuP,OAAA,CAAAtN,QAAA,CAAAsM,EAAA;gBAAAoB,SAAA,CAAAvE,IAAA;gBAAA;cAAA;cACA;cACAqE,WAAA,CAAAG,IAAA;gBACAtO,QAAA,EAAAiO,OAAA,CAAAjO,QAAA;gBACAuO,MAAA,EAAAN,OAAA,CAAAtN,QAAA,CAAAsM,EAAA;gBACAuB,MAAA,EAAAP,OAAA,CAAAtN,QAAA,CAAA6B;cACA;cAAA6L,SAAA,CAAAvE,IAAA;cAAA;YAAA;cAAA,MAGAmE,OAAA,CAAA9N,aAAA;gBAAAkO,SAAA,CAAAvE,IAAA;gBAAA;cAAA;cAAA,OAAAuE,SAAA,CAAAtE,MAAA,WACAkE,OAAA,CAAAxI,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cACAuP,OAAA,CAAA9N,aAAA,CAAAsO,OAAA,WAAA3I,IAAA;gBACA,IAAA4I,OAAA,GAAAT,OAAA,CAAA5O,WAAA,CAAAwG,MAAA,WAAAoB,KAAA;kBAAA,OAAAA,KAAA,CAAAgG,EAAA,IAAAnH,IAAA;gBAAA;gBACAqI,WAAA,CAAAG,IAAA;kBACAtO,QAAA,EAAAiO,OAAA,CAAAjO,QAAA;kBACAuO,MAAA,EAAAzI,IAAA;kBACA0I,MAAA,EAAAE,OAAA,IAAAlM;gBACA;cACA;YAAA;cAAA6L,SAAA,CAAAvE,IAAA;cAAA,OAEAzE,cAAA,CAAAsJ,SAAA,CAAAR,WAAA;YAAA;cAAA/H,GAAA,GAAAiI,SAAA,CAAAjB,IAAA;cACA,IAAAhH,GAAA,CAAAZ,IAAA;gBACAyI,OAAA,CAAAxI,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;gBACAuP,OAAA,CAAA9C,KAAA;cACA;gBACA8C,OAAA,CAAAxI,QAAA;kBACAvD,OAAA;kBACAxD,IAAA;gBACA;cACA;cACAuP,OAAA,CAAAtN,QAAA;cACAsN,OAAA,CAAAjO,QAAA;cACAiO,OAAA,CAAA9N,aAAA;cACA8N,OAAA,CAAA7N,OAAA;YAAA;YAAA;cAAA,OAAAiO,SAAA,CAAAxD,IAAA;UAAA;QAAA,GAAAqD,QAAA;MAAA;IACA;IACA,YACAU,WAAA,WAAAA,YAAA9I,IAAA;MACA,KAAA5F,aAAA;MACA,KAAAS,QAAA,GAAAmF,IAAA;IACA;IACA,WACA+I,WAAA,WAAAA,YAAA/I,IAAA,EAAAgJ,MAAA;MACA,IAAAA,MAAA;QACA,IAAAhJ,IAAA,CAAAxD,WAAA;UACA+H,MAAA,CAAA1I,IAAA,CAAAmE,IAAA,CAAAxD,WAAA;UACA;QACA;QACA,KAAAmD,QAAA;UAAAvD,OAAA;QAAA;QACA;MACA;MACAmI,MAAA,CAAA1I,IAAA,uBAAAyG,MAAA,CACAtC,IAAA,CAAAmH,EAAA,aAAA7E,MAAA,CAAAtC,IAAA,CAAAiJ,KAAA,kBAAA3G,MAAA,CAAAtC,IAAA,CAAApG,UAAA,GACA,QACA;MACA;MACA;IACA;IACA,WACAsP,UAAA,WAAAA,WAAAlJ,IAAA;MACA,KAAAlF,SAAA;MACA,KAAAO,OAAA,GAAA2E,IAAA;MACA,IAAAA,IAAA,CAAA/E,QAAA;QACA,KAAAF,cAAA,CAAAE,QAAA,GAAA+E,IAAA,CAAA/E,QAAA,CACA6H,KAAA,MACAC,GAAA,WAAAhJ,IAAA;UAAA,OAAAZ,MAAA,CAAAY,IAAA;QAAA;MACA;MACA,IAAAiG,IAAA,CAAA9E,MAAA;QACA,KAAAH,cAAA,CAAAG,MAAA,GAAA8E,IAAA,CAAA9E,MAAA,CACA4H,KAAA,MACAC,GAAA,WAAAhJ,IAAA;UAAA,OAAAZ,MAAA,CAAAY,IAAA;QAAA;MACA;MACA,KAAAgB,cAAA,CAAAC,GAAA,GAAAgF,IAAA,CAAAmJ,IAAA,GAAAnJ,IAAA,CAAAmJ,IAAA,CAAArG,KAAA;IACA;IACA,aACA1C,UAAA,WAAAA,WAAA;MAAA,IAAAgJ,OAAA;MAAA,WAAA5F,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAA2F,SAAA;QAAA,WAAA5F,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAA0F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxF,IAAA,GAAAwF,SAAA,CAAAvF,IAAA;YAAA;cAAAuF,SAAA,CAAAvF,IAAA;cAAA,OACAzE,cAAA,CAAAjE,QAAA,GAAAmE,IAAA,WAAA1F,IAAA;gBACA,IAAAA,IAAA,CAAA2F,IAAA;kBACA0J,OAAA,CAAA9N,QAAA,GAAAvB,IAAA,CAAAA,IAAA;kBACAqP,OAAA,CAAAjO,OAAA,GAAApB,IAAA,CAAAA,IAAA;kBACAwF,cAAA,CAAAtE,QAAA,GAAAwE,IAAA,WAAA0B,KAAA;oBACAiI,OAAA,CAAAnO,QAAA,GAAAkG,KAAA,CAAApH,IAAA;oBACAqP,OAAA,CAAAhO,QAAA,GAAA+F,KAAA,CAAApH,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAwP,SAAA,CAAAxE,IAAA;UAAA;QAAA,GAAAsE,QAAA;MAAA;IACA;IACA,UACAG,WAAA,WAAAA,YAAA/I,KAAA;MACA,KAAAtF,OAAA,QAAAG,QAAA,CAAAyE,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAyJ,SAAA,IAAAhJ,KAAA;MAAA;IACA;IACA,UACAiJ,cAAA,WAAAA,eAAAjJ,KAAA;MACA,KAAArF,QAAA,QAAAH,QAAA,CAAA8E,MAAA,CACA,UAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA2J,YAAA,IAAAlJ,KAAA;MAAA,CACA;IACA;IACAmJ,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MAAA,WAAArG,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAoG,SAAA;QAAA,IAAAC,MAAA,EAAAzJ,GAAA;QAAA,WAAAmD,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAoG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlG,IAAA,GAAAkG,SAAA,CAAAjG,IAAA;YAAA;cACA+F,MAAA;gBACA7O,MAAA,EAAAvB,MAAA,CAAAkQ,OAAA,CAAA9O,cAAA,CAAAG,MAAA;gBACAD,QAAA,EAAAtB,MAAA,CAAAkQ,OAAA,CAAA9O,cAAA,CAAAE,QAAA;gBACAkO,IAAA,EAAAxP,MAAA,CAAAkQ,OAAA,CAAA9O,cAAA,CAAAC,GAAA;gBACAkP,SAAA,EAAAvQ,MAAA,CAAAkQ,OAAA,CAAAxO,OAAA,CAAA8L,EAAA;gBACA8B,KAAA,EAAAY,OAAA,CAAAxO,OAAA,CAAA4N,KAAA,GAAAtP,MAAA,CAAAkQ,OAAA,CAAAxO,OAAA,CAAA4N,KAAA;cACA;cAAAgB,SAAA,CAAAjG,IAAA;cAAA,OACAzE,cAAA,CAAA4K,MAAA,CAAAJ,MAAA;YAAA;cAAAzJ,GAAA,GAAA2J,SAAA,CAAA3C,IAAA;cACA,IAAAhH,GAAA,CAAAZ,IAAA;gBACAmK,OAAA,CAAAlK,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;gBACA2N,UAAA;kBACAsD,OAAA,CAAAhK,OAAA;gBACA;cACA;gBACAgK,OAAA,CAAAlK,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;cACA;cACAiR,OAAA,CAAAO,QAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAlF,IAAA;UAAA;QAAA,GAAA+E,QAAA;MAAA;IACA;IACAM,QAAA,WAAAA,SAAA;MACA,KAAAvE,KAAA,aAAAwE,WAAA;MACA,KAAAtP,cAAA;QACAC,GAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA,KAAAJ,SAAA;IACA;IACAwP,WAAA,WAAAA,YAAAtK,IAAA;MAAA,IAAAuK,OAAA;MAAA,WAAA/G,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAA8G,SAAA;QAAA,IAAAC,SAAA,EAAAnK,GAAA;QAAA,WAAAmD,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAA8G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,IAAA,GAAA4G,SAAA,CAAA3G,IAAA;YAAA;cACAyG,SAAA,GAAAG,IAAA,CAAAC,KAAA,CAAA7K,IAAA,CAAAyK,SAAA;cAAAE,SAAA,CAAA3G,IAAA;cAAA,OACAzE,cAAA,CAAA4K,MAAA;gBACAD,SAAA,EAAAlK,IAAA,CAAAmH,EAAA;gBACAsD,SAAA,IAAA5R,OAAA,CAAA4R,SAAA;cACA;YAAA;cAHAnK,GAAA,GAAAqK,SAAA,CAAArD,IAAA;cAIA,IAAAhH,GAAA,CAAAZ,IAAA;gBACA6K,OAAA,CAAA5K,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;gBACA2R,OAAA,CAAA1K,OAAA;cACA;gBACA0K,OAAA,CAAA5K,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;cACA;YAAA;YAAA;cAAA,OAAA+R,SAAA,CAAA5F,IAAA;UAAA;QAAA,GAAAyF,QAAA;MAAA;IACA;IACAM,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAAvH,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAsH,SAAA;QAAA,IAAA/B,KAAA;QAAA,WAAAxF,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAqH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnH,IAAA,GAAAmH,SAAA,CAAAlH,IAAA;YAAA;cACAiF,KAAA,GAAA8B,OAAA,CAAArP,UAAA,CAAAuN,KAAA;cAAAiC,SAAA,CAAAlH,IAAA;cAAA,OACAzE,cAAA,CAAA4L,QAAA,CAAAJ,OAAA,CAAArP,UAAA,CAAAyL,EAAA,EAAA1H,IAAA,WAAAa,GAAA;gBACA,IAAAA,GAAA,CAAAZ,IAAA;kBACAqL,OAAA,CAAArP,UAAA,GAAA4E,GAAA,CAAAvG,IAAA;kBACAgR,OAAA,CAAArP,UAAA,CAAAuN,KAAA,GAAAA,KAAA;kBACA;kBACA8B,OAAA,CAAAnP,kBAAA,IAAAmP,OAAA,CAAArP,UAAA,CAAA0P,SAAA;kBACA,IAAAL,OAAA,CAAArP,UAAA,CAAA0P,SAAA,IAAAL,OAAA,CAAArP,UAAA,CAAAW,OAAA;oBACA0O,OAAA,CAAArP,UAAA,CAAA0P,SAAA,IACAL,OAAA,CAAArP,UAAA,CAAA0P,SAAA,IAAAL,OAAA,CAAArP,UAAA,CAAAW,OAAA,EACAmF,OAAA,mBAAA2C,CAAA,EAAAkH,CAAA,EAAAC,CAAA;sBACA;oBACA;oBACAP,OAAA,CAAArP,UAAA,CAAA0P,SAAA,IACAL,OAAA,CAAArP,UAAA,CAAA0P,SAAA,IAAAL,OAAA,CAAArP,UAAA,CAAAW,OAAA,EACAmF,OAAA;oBACAuJ,OAAA,CAAArP,UAAA,CAAA0P,SAAA,IACAL,OAAA,CAAArP,UAAA,CAAA0P,SAAA,IAAAL,OAAA,CAAArP,UAAA,CAAAW,OAAA,EACAmF,OAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA0J,SAAA,CAAAnG,IAAA;UAAA;QAAA,GAAAiG,QAAA;MAAA;IACA;IACA,YACAO,cAAA,WAAAA,eAAAvL,IAAA;MAAA,IAAAwL,OAAA;MACA,IAAAC,GAAA;QACA7I,GAAA;MACA,IAAA5C,IAAA,CAAA/E,QAAA;QACAwQ,GAAA,GAAAzL,IAAA,CAAA/E,QAAA,CAAA6H,KAAA;MACA;MACA2I,GAAA,CAAA9C,OAAA,WAAA5O,IAAA;QACAyR,OAAA,CAAAvQ,QAAA,CAAA8H,GAAA,WAAA2I,GAAA;UACA,IAAAA,GAAA,CAAAvE,EAAA,IAAApN,IAAA;YACA,IAAA6I,GAAA,IAAA+I,SAAA;cACA/I,GAAA;YACA;YACAA,GAAA,IAAA8I,GAAA,CAAA/B,YAAA;UACA;QACA;MACA;MACA,OAAA/G,GAAA;IACA;IACAgJ,YAAA,WAAAA,aAAA5L,IAAA;MAAA,IAAA6L,OAAA;MACA,IAAAJ,GAAA;QACA7I,GAAA;MACA,IAAA5C,IAAA,CAAA9E,MAAA;QACAuQ,GAAA,GAAAzL,IAAA,CAAA9E,MAAA,CAAA4H,KAAA;MACA;MACA2I,GAAA,CAAA9C,OAAA,WAAA5O,IAAA;QACA8R,OAAA,CAAAvQ,QAAA,CAAAyH,GAAA,WAAA2I,GAAA;UACA,IAAAA,GAAA,CAAAvE,EAAA,IAAApN,IAAA;YACA,IAAA6I,GAAA,IAAA+I,SAAA;cACA/I,GAAA;YACA;YACAA,GAAA,IAAA8I,GAAA,CAAAjC,SAAA;UACA;QACA;MACA;MACA,OAAA7G,GAAA;IACA;IACA,UACAkJ,WAAA,WAAAA,YAAA9L,IAAA;MAAA,IAAA+L,OAAA;MACA,IAAA/L,IAAA,8BAAA3F,aAAA,CAAA6I,MAAA;QACA,KAAAvD,QAAA,CAAAqM,OAAA;QACA;MACA;MACA,IAAAP,GAAA;MACA,IAAAQ,SAAA;MACA,IAAAhP,GAAA;MACA,IAAA+C,IAAA;QACAyL,GAAA,SAAA/P,UAAA,CAAAyL,EAAA;QACA,SAAAzL,UAAA,CAAAwQ,WAAA,EAAAD,SAAA;QACAhP,GAAA,QAAAvB,UAAA,CAAAwQ,WAAA;MACA,WAAAlM,IAAA;QACAyL,GAAA,QAAApR,aAAA;MACA;QACAoR,GAAA,IAAAzL,IAAA,CAAAmH,EAAA;QACA,IAAAnH,IAAA,CAAAkM,WAAA,EAAAD,SAAA;QACAhP,GAAA,GAAA+C,IAAA,CAAAkM,WAAA;MACA;MACA,IAAAD,SAAA;QACA,SAAAzS,IAAA;UACA+F,cAAA,CAAA4M,gBAAA,CAAAV,GAAA,EACAhM,IAAA,WAAAyE,QAAA;YACA,IAAAA,QAAA,CAAAxE,IAAA;cACAqM,OAAA,CAAAK,OAAA,KAAAC,gBAAA,CAAAvT,OAAA,MAAAuT,gBAAA,CAAAvT,OAAA;gBACAqD,KAAA;gBACAC,OAAA;gBACAkQ,gBAAA;gBACAC,iBAAA;gBACAC,gBAAA;cAAA,uBACA,uBACA,SAAAC,YAAAC,MAAA,EAAAC,QAAA,EAAAC,IAAA;gBACAA,IAAA;cACA,EACA;YACA;cACAb,OAAA,CAAApM,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YACA;UACA,GACA0M,KAAA,WAAAuH,GAAA;QACA;UACAtN,cAAA,CAAAuN,mBAAA,CAAArB,GAAA,EACAhM,IAAA,WAAAyE,QAAA;YACA,IAAAA,QAAA,CAAAxE,IAAA;cACAqM,OAAA,CAAAK,OAAA,KAAAC,gBAAA,CAAAvT,OAAA,MAAAuT,gBAAA,CAAAvT,OAAA;gBACAqD,KAAA;gBACAC,OAAA;gBACAkQ,gBAAA;gBACAC,iBAAA;gBACAC,gBAAA;cAAA,uBACA,uBACA,SAAAC,YAAAC,MAAA,EAAAC,QAAA,EAAAC,IAAA;gBACAA,IAAA;cACA,EACA;YACA;cACAb,OAAA,CAAApM,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YACA;UACA,GACA0M,KAAA,WAAAuH,GAAA;QACA;MACA;QACA5P,GAAA,GAAAA,GAAA,CAAAuE,OAAA,KAAA4B,MAAA;QACAnG,GAAA,GAAAA,GAAA,CAAAuE,OAAA,KAAA4B,MAAA;QACAmB,MAAA,CAAA1I,IAAA,CAAA0I,MAAA,CAAAwI,QAAA,CAAAC,MAAA,GAAA/P,GAAA;MACA;IACA;IACA,UACAgQ,gBAAA,WAAAA,iBAAAjN,IAAA;MAAA,IAAAkN,OAAA;MAAA,WAAA1J,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAyJ,SAAA;QAAA,IAAAC,IAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA;QAAA,WAAA9J,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAA4J,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1J,IAAA,GAAA0J,UAAA,CAAAzJ,IAAA;YAAA;cACAkJ,OAAA,CAAAlT,OAAA;cAAA,KACAgG,IAAA,CAAA0N,OAAA;gBAAAD,UAAA,CAAAzJ,IAAA;gBAAA;cAAA;cACAoJ,IAAA,GAAApN,IAAA,CAAA0N,OAAA,CAAA5K,KAAA;cAAAuK,SAAA,OAAAM,2BAAA,CAAA7U,OAAA,EACAsU,IAAA,CAAAQ,OAAA;cAAAH,UAAA,CAAA1J,IAAA;cAAAwJ,KAAA,oBAAA9J,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAA6J,MAAA;gBAAA,IAAAM,WAAA,EAAAC,KAAA,EAAA7Q,GAAA;gBAAA,WAAAwG,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAmK,OAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAjK,IAAA,GAAAiK,SAAA,CAAAhK,IAAA;oBAAA;sBAAA6J,WAAA,OAAAI,eAAA,CAAAnV,OAAA,EAAAwU,KAAA,CAAAnM,KAAA,MAAA2M,KAAA,GAAAD,WAAA,KAAA5Q,GAAA,GAAA4Q,WAAA;sBACA,IAAA5Q,GAAA,CAAAiR,OAAA;wBACA3H,UAAA,kBAAA/C,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAyK,SAAA;0BAAA,WAAA1K,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAwK,UAAAC,SAAA;4BAAA,kBAAAA,SAAA,CAAAtK,IAAA,GAAAsK,SAAA,CAAArK,IAAA;8BAAA;gCAAAqK,SAAA,CAAArK,IAAA;gCAAA,OACAkJ,OAAA,CAAAoB,WAAA,CAAArR,GAAA,EAAA6Q,KAAA,EAAA9N,IAAA,CAAAzD,OAAA,IAAAyD,IAAA,CAAA7D,KAAA;8BAAA;8BAAA;gCAAA,OAAAkS,SAAA,CAAAtJ,IAAA;4BAAA;0BAAA,GAAAoJ,QAAA;wBAAA,CACA,IAAAL,KAAA;sBACA;wBACAZ,OAAA,CAAAvN,QAAA,CAAA4O,KAAA;sBACA;oBAAA;oBAAA;sBAAA,OAAAP,SAAA,CAAAjJ,IAAA;kBAAA;gBAAA,GAAAwI,KAAA;cAAA;cAAAF,SAAA,CAAAmB,CAAA;YAAA;cAAA,KAAAlB,KAAA,GAAAD,SAAA,CAAAoB,CAAA,IAAA7B,IAAA;gBAAAa,UAAA,CAAAzJ,IAAA;gBAAA;cAAA;cAAA,OAAAyJ,UAAA,CAAAiB,aAAA,CAAAnB,KAAA;YAAA;cAAAE,UAAA,CAAAzJ,IAAA;cAAA;YAAA;cAAAyJ,UAAA,CAAAzJ,IAAA;cAAA;YAAA;cAAAyJ,UAAA,CAAA1J,IAAA;cAAA0J,UAAA,CAAAkB,EAAA,GAAAlB,UAAA;cAAAJ,SAAA,CAAAuB,CAAA,CAAAnB,UAAA,CAAAkB,EAAA;YAAA;cAAAlB,UAAA,CAAA1J,IAAA;cAAAsJ,SAAA,CAAAwB,CAAA;cAAA,OAAApB,UAAA,CAAAqB,MAAA;YAAA;cAGA5B,OAAA,CAAAlT,OAAA;YAAA;YAAA;cAAA,OAAAyT,UAAA,CAAA1I,IAAA;UAAA;QAAA,GAAAoI,QAAA;MAAA;IACA;IAEAmB,WAAA,WAAAA,YAAArR,GAAA,EAAA6Q,KAAA,EAAA3R,KAAA;MAAA,IAAA4S,OAAA;MAAA,WAAAvL,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAsL,UAAA;QAAA,IAAAC,QAAA,EAAA/K,QAAA,EAAAgL,MAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,MAAA;QAAA,WAAA/L,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAA6L,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3L,IAAA,GAAA2L,UAAA,CAAA1L,IAAA;YAAA;cACAiL,QAAA,OAAAU,QAAA;cACAV,QAAA,CAAAW,MAAA,YAAA3S,GAAA;cAAAyS,UAAA,CAAA3L,IAAA;cAAA2L,UAAA,CAAA1L,IAAA;cAAA,OAGAzE,cAAA,CAAAsQ,YAAA,CAAAZ,QAAA;YAAA;cAAA/K,QAAA,GAAAwL,UAAA,CAAApI,IAAA;cACA4H,MAAA,OAAAY,mBAAA,EAAA5L,QAAA;cAAA,KAEAgL,MAAA;gBAAAQ,UAAA,CAAA1L,IAAA;gBAAA;cAAA;cACAmL,IAAA,OAAAY,IAAA,EAAA7L,QAAA;cACAkL,IAAA,GAAAnS,GAAA,CAAA6F,KAAA;cACAuM,QAAA,GAAAD,IAAA,CAAAA,IAAA,CAAAlM,MAAA;cACA,IAAA8M,iBAAA,EAAAb,IAAA,EAAAE,QAAA;cAAAK,UAAA,CAAA1L,IAAA;cAAA;YAAA;cAAA0L,UAAA,CAAA1L,IAAA;cAAA,OAEAE,QAAA,CAAA1J,IAAA;YAAA;cAAA8U,OAAA,GAAAI,UAAA,CAAApI,IAAA;cACAiI,MAAA,GAAA3E,IAAA,CAAAC,KAAA,CAAAyE,OAAA;cACAE,MAAA,GACAS,SAAA,CAAAV,MAAA,CAAA7P,IAAA,KAAA6P,MAAA,CAAA5O,GAAA,IAAAsP,SAAA;cACAlB,OAAA,CAAApP,QAAA,CAAA4O,KAAA,CAAAiB,MAAA;YAAA;cAAAE,UAAA,CAAA1L,IAAA;cAAA;YAAA;cAAA0L,UAAA,CAAA3L,IAAA;cAAA2L,UAAA,CAAAQ,EAAA,GAAAR,UAAA;YAAA;cAAAA,UAAA,CAAA3L,IAAA;cAKA;cACAgL,OAAA,CAAA/U,OAAA;cAAA,OAAA0V,UAAA,CAAAZ,MAAA;YAAA;YAAA;cAAA,OAAAY,UAAA,CAAA3K,IAAA;UAAA;QAAA,GAAAiK,SAAA;MAAA;IAoCA;IACA;IACAmB,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,OAAA;MACA,IAAArW,OAAA,QAAAsW,QAAA;QACAC,IAAA;QACA/V,IAAA;QACAgW,OAAA;QACAC,UAAA;MACA;MACAlR,cAAA,CAAAmR,gBAAA;QACAC,YAAA,EAAAP,GAAA,CAAAjU,KAAA;QACA8M,KAAA,EAAAmH,GAAA,CAAAnH,KAAA;QACA9B,EAAA,EAAAiJ,GAAA,CAAAjJ,EAAA;QACAyJ,gBAAA;QACAC,eAAA;MACA,GACApR,IAAA,WAAAa,GAAA;QACA+P,OAAA,CAAA3U,UAAA,CAAAa,OAAA,GAAA+D,GAAA,CAAAvG,IAAA;QACAsW,OAAA,CAAA9W,WAAA,CACA8W,OAAA,CAAA9W,WAAA,CAAAuX,SAAA,WAAA3P,KAAA;UAAA,OAAAA,KAAA,CAAAgG,EAAA,IAAAiJ,GAAA,CAAAjJ,EAAA;QAAA,GACA,CAAA5K,OAAA,GAAA+D,GAAA,CAAAvG,IAAA;QACAsW,OAAA,CAAA9W,WAAA,CACA8W,OAAA,CAAA9W,WAAA,CAAAuX,SAAA,WAAA3P,KAAA;UAAA,OAAAA,KAAA,CAAAgG,EAAA,IAAAiJ,GAAA,CAAAjJ,EAAA;QAAA,GACA,CAAA4J,YAAA;QACA/W,OAAA,CAAAgX,KAAA;MACA,GACA1L,KAAA,WAAAuH,GAAA;QACA7S,OAAA,CAAAgX,KAAA;MACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAb,GAAA;MAAA,IAAAc,OAAA;MACA,IAAAlX,OAAA,QAAAsW,QAAA;QACAC,IAAA;QACA/V,IAAA;QACAgW,OAAA;QACAC,UAAA;MACA;MACAlR,cAAA,CAAAmR,gBAAA;QACAC,YAAA,EAAAP,GAAA,CAAA/T,OAAA;QACA4M,KAAA,EAAAmH,GAAA,CAAAnH,KAAA;QACA9B,EAAA,EAAAiJ,GAAA,CAAAjJ,EAAA;QACAyJ,gBAAA;QACAC,eAAA;MACA,GACApR,IAAA,WAAAa,GAAA;QACA4Q,OAAA,CAAAxV,UAAA,CAAA0P,SAAA,GAAA9K,GAAA,CAAAvG,IAAA;QACAmX,OAAA,CAAA3X,WAAA,CACA2X,OAAA,CAAA3X,WAAA,CAAAuX,SAAA,WAAA3P,KAAA;UAAA,OAAAA,KAAA,CAAAgG,EAAA,IAAAiJ,GAAA,CAAAjJ,EAAA;QAAA,GACA,CAAAiE,SAAA,GAAA9K,GAAA,CAAAvG,IAAA;QACAmX,OAAA,CAAA3X,WAAA,CACA2X,OAAA,CAAA3X,WAAA,CAAAuX,SAAA,WAAA3P,KAAA;UAAA,OAAAA,KAAA,CAAAgG,EAAA,IAAAiJ,GAAA,CAAAjJ,EAAA;QAAA,GACA,CAAA4J,YAAA;QACAG,OAAA,CAAAtV,kBAAA;QACA5B,OAAA,CAAAgX,KAAA;MACA,GACA1L,KAAA,WAAAuH,GAAA;QACA7S,OAAA,CAAAgX,KAAA;MACA;IACA;IACA,aACAG,YAAA,WAAAA,aAAAf,GAAA;MAAA,IAAAgB,OAAA;MACA,KAAAC,KAAA;MACA9R,cAAA,CAAA4L,QAAA,CAAAiF,GAAA,CAAAjJ,EAAA,EAAA1H,IAAA,WAAAyE,QAAA;QACAkN,OAAA,CAAAnV,IAAA,GAAAiI,QAAA,CAAAnK,IAAA;QACAqX,OAAA,CAAAnV,IAAA,CAAArC,UAAA,GAAAT,MAAA,CAAAiY,OAAA,CAAAnV,IAAA,CAAArC,UAAA;QACAwX,OAAA,CAAAnV,IAAA,CAAAgN,KAAA,GAAAmH,GAAA,CAAAnH,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACAmI,OAAA,CAAAvV,IAAA;MACA;IACA;IACA,aACAyV,YAAA,WAAAA,aAAAlB,GAAA;MAAA,IAAAmB,OAAA;MACA,KAAAC,MAAA,CACAC,OAAA,iBACAhS,IAAA;QACA,OAAAF,cAAA,CAAAmS,kBAAA;UAAAvK,EAAA,EAAAiJ,GAAA,CAAAjJ,EAAA;UAAA8B,KAAA,EAAAmH,GAAA,CAAAnH;QAAA;MACA,GACAxJ,IAAA;QACA8R,OAAA,CAAA1R,OAAA;QACA0R,OAAA,CAAAC,MAAA,CAAAG,UAAA;MACA,GACArM,KAAA;IACA;IACA,WACAsM,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAhM,KAAA,SAAAiM,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,SAAA,GAAApH,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAqH,SAAA,CAAAJ,OAAA,CAAA5V,IAAA;UACA;UACA;UACA,IAAAiW,qBAAA,EAAAF,SAAA,EAAAvS,IAAA,WAAAyE,QAAA;YACA2N,OAAA,CAAAL,MAAA,CAAAG,UAAA;YACAE,OAAA,CAAAhW,IAAA;YACAgW,OAAA,CAAAhS,OAAA;UACA;QACA;MACA;IACA;IACA,WACAsS,WAAA,WAAAA,YAAAC,IAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7O,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAA4O,UAAA;QAAA,IAAAvY,IAAA;QAAA,WAAA0J,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAA2O,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAzO,IAAA,GAAAyO,UAAA,CAAAxO,IAAA;YAAA;cACAjK,IAAA,OAAA4V,QAAA;cACA5V,IAAA,CAAA6V,MAAA,UAAAwC,IAAA,CAAAA,IAAA;cAAAI,UAAA,CAAAxO,IAAA;cAAA,OACA,IAAAyO,iBAAA,EAAA1Y,IAAA,EAAA0F,IAAA,WAAAyE,QAAA;gBACA,IAAAA,QAAA,CAAAxE,IAAA;kBACA2S,OAAA,CAAAhV,QAAA,CAAA0F,GAAA,WAAA/C,IAAA;oBACA,IAAAA,IAAA,CAAA0S,GAAA,IAAAN,IAAA,CAAAA,IAAA,CAAAM,GAAA;sBACA1S,IAAA,CAAA2S,IAAA,GAAAzO,QAAA,CAAA0O,MAAA;oBACA;kBACA;kBACAP,OAAA,CAAA1S,QAAA;oBAAAvD,OAAA;oBAAAxD,IAAA;kBAAA;gBACA;kBACAyZ,OAAA,CAAA1S,QAAA;oBAAAvD,OAAA;oBAAAxD,IAAA;kBAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA4Z,UAAA,CAAAzN,IAAA;UAAA;QAAA,GAAAuN,SAAA;MAAA;IACA;IACA,YACAO,MAAA,WAAAA,OAAA;MACA,KAAAlT,QAAA;QACAvD,OAAA;QACAxD,IAAA;MACA;IACA;IACA,UACAka,YAAA,WAAAA,aAAAV,IAAA;MACA,KAAA/U,QAAA,QAAAA,QAAA,CAAA0C,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,KAAAoS,IAAA;MAAA;IACA;IACA;IACAW,YAAA,WAAAA,aAAAX,IAAA,EAAA/U,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAwI,KAAA,CAAAmN,MAAA,CAAAC,MAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAd,IAAA;MACA;MACA,IAAA/C,QAAA,GAAA+C,IAAA,CAAAe,IAAA,CACAC,SAAA,CAAAhB,IAAA,CAAAe,IAAA,CAAAE,WAAA,WACAC,WAAA;QACAC,SAAA,GACAlE,QAAA,aACAA,QAAA,aACAA,QAAA,aACAA,QAAA,aACAA,QAAA,cACAA,QAAA,cACAA,QAAA;MACA,IAAAmE,QAAA,GAAApB,IAAA,CAAAqB,IAAA;MACA,KAAAF,SAAA;QACA,KAAAG,OAAA;UACAvX,KAAA;UACAC,OAAA;UACAxD,IAAA;QACA;MACA;MACA;MACA,KAAA4a,QAAA;QACA,KAAAE,OAAA;UACAvX,KAAA;UACAC,OAAA;UACAxD,IAAA;QACA;MACA;MACA,OAAA2a,SAAA,IAAAC,QAAA;IACA;IACA;IACAG,gBAAA,WAAAA,iBAAArT,GAAA,EAAA8R,IAAA;MACA,KAAAzS,QAAA;QAAAvD,OAAA;QAAAxD,IAAA;MAAA;IACA;IACA;IACAgb,eAAA,WAAAA,gBAAA;MACA,KAAAjU,QAAA;QACAvD,OAAA;QACAxD,IAAA;MACA;IACA;IACA;IACAib,gBAAA,WAAAA,iBAAAzB,IAAA;MAAA,IAAA0B,OAAA;MACA,SAAA7X,IAAA,CAAAO,WAAA,iBAAAP,IAAA,CAAAO,WAAA;QACA,IACA,KAAAP,IAAA,CAAAO,WAAA,CAAA0F,KAAA,CACA,mFACA,GACA;UACA,IAAAnI,IAAA,OAAA4V,QAAA;UACA5V,IAAA,CAAA6V,MAAA,SAAAwC,IAAA,CAAAA,IAAA;UACArY,IAAA,CAAA6V,MAAA,qBAAA3T,IAAA,CAAAO,WAAA;UAEA+C,cAAA,CAAAwU,UAAA,CAAAha,IAAA,EAAA0F,IAAA,WAAAyE,QAAA;YACA,IAAAA,QAAA,CAAAxE,IAAA;cACAoU,OAAA,CAAAnU,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cACAkb,OAAA,CAAA7X,IAAA,CAAAyR,OAAA,GAAAxJ,QAAA,CAAAnK,IAAA;YACA;cACA+Z,OAAA,CAAAnU,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cACAkb,OAAA,CAAA7X,IAAA,CAAAyR,OAAA;YACA;UACA;QACA;UACA,KAAA/N,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;UACA,KAAA0E,WAAA;QACA;MACA;QACA,KAAAqC,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA,KAAA0E,WAAA;MACA;IACA;IACA;IACA0W,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA1U,cAAA,CAAA2U,UAAA;QAAAC,QAAA,OAAAlY,IAAA,CAAAyR;MAAA,GAAAjO,IAAA,WAAAyE,QAAA;QACA,IAAAA,QAAA,CAAAxE,IAAA;UACAuU,OAAA,CAAAtU,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;UACAqb,OAAA,CAAA3W,WAAA;UACA2W,OAAA,CAAAhY,IAAA,CAAAyR,OAAA;QACA;UACAuG,OAAA,CAAAtU,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;QACA;MACA;IACA;IACA;IACAwb,MAAA,WAAAA,OAAA;MACA,KAAAvY,IAAA;MACA,KAAAwV,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAApV,IAAA;QACAkL,EAAA;QACAkN,SAAA;QACAlY,KAAA;QACAI,OAAA;QACA3C,UAAA;QACA0a,UAAA;QACAC,QAAA;QACA/X,WAAA;QACAgY,QAAA;QACAC,MAAA;QACAC,WAAA;QACAjY,OAAA;QACA6E,SAAA;QACAqT,KAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAxY,WAAA;QACAyY,WAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,cAAA;QACAC,OAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAvZ,OAAA;QACA+O,SAAA;QACAsC,OAAA;QACAzS,QAAA;QACAC,MAAA;QACA2a,MAAA;QACAC,QAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA,KAAAzY,kBAAA;MACA,KAAAC,gBAAA;IACA;IACA;IACAyY,gBAAA,WAAAA,iBAAA/D,IAAA,EAAA/U,QAAA;MACA;MACA,IAAA+Y,QAAA,GAAA/Y,QAAA,CAAA0F,GAAA,WAAA/C,IAAA;QAAA;UACAqP,QAAA,EAAArP,IAAA,CAAAmT,IAAA;UACAf,IAAA,EAAApS,IAAA,CAAAqW,GAAA;UACA/B,UAAA;QACA;MAAA;MACA,KAAA5W,gBAAA,GAAA0Y,QAAA;IACA;IACA;IACAlC,UAAA,WAAAA,WAAApG,KAAA;MACA,KAAApQ,gBAAA,CAAA4Y,MAAA,CAAAxI,KAAA;IACA;IACA;IACAyI,iBAAA,WAAAA,kBAAA;MACA,KAAA9Y,kBAAA;MACA,KAAAC,gBAAA;MACA;MACA,KAAAmI,KAAA,CAAA2Q,WAAA,CAAAC,UAAA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAnT,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAkT,UAAA;QAAA,IAAAC,gBAAA,EAAA5H,QAAA,EAAA6H,WAAA,EAAA5S,QAAA;QAAA,WAAAT,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAmT,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAjT,IAAA,GAAAiT,UAAA,CAAAhT,IAAA;YAAA;cACA;cACA6S,gBAAA,GAAAF,OAAA,CAAAjZ,gBAAA,CAAAqC,MAAA,CACA,UAAAC,IAAA;gBAAA,QAAAA,IAAA,CAAAsU,UAAA,CAAA7S,IAAA;cAAA,CACA;cAAA,MACAoV,gBAAA,CAAA3T,MAAA;gBAAA8T,UAAA,CAAAhT,IAAA;gBAAA;cAAA;cACA2S,OAAA,CAAAhX,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;cAAA,OAAAoe,UAAA,CAAA/S,MAAA;YAAA;cAAA+S,UAAA,CAAAjT,IAAA;cAKA4S,OAAA,CAAA3c,OAAA;;cAEA;cACAiV,QAAA,OAAAU,QAAA,IAEA;cACAgH,OAAA,CAAAjZ,gBAAA,CAAAiL,OAAA,WAAA3I,IAAA;gBACAiP,QAAA,CAAAW,MAAA,UAAA5P,IAAA,CAAAoS,IAAA;cACA;;cAEA;cACA0E,WAAA,GAAAH,OAAA,CAAAjZ,gBAAA,CACAqF,GAAA,WAAA/C,IAAA;gBAAA,OAAAA,IAAA,CAAAsU,UAAA;cAAA,GACAlP,IAAA;cAEA6J,QAAA,CAAAW,MAAA,gBAAAkH,WAAA;;cAEA;cAAAE,UAAA,CAAAhT,IAAA;cAAA,OACAzE,cAAA,CAAA0X,kBAAA,CAAAhI,QAAA;YAAA;cAAA/K,QAAA,GAAA8S,UAAA,CAAA1P,IAAA;cAEA,IAAApD,QAAA,CAAAxE,IAAA;gBACAiX,OAAA,CAAAhX,QAAA;kBACAvD,OAAA;kBACAxD,IAAA;gBACA;gBACA+d,OAAA,CAAAlZ,kBAAA;gBACAkZ,OAAA,CAAAjZ,gBAAA;gBACAiZ,OAAA,CAAA9Q,KAAA,CAAA2Q,WAAA,CAAAC,UAAA;gBACA;gBACAE,OAAA,CAAA9W,OAAA;cACA;gBACA8W,OAAA,CAAAhX,QAAA;kBACAvD,OAAA,EAAA8H,QAAA,CAAAvD,GAAA;kBACA/H,IAAA;gBACA;cACA;cAAAoe,UAAA,CAAAhT,IAAA;cAAA;YAAA;cAAAgT,UAAA,CAAAjT,IAAA;cAAAiT,UAAA,CAAA9G,EAAA,GAAA8G,UAAA;cAEAE,OAAA,CAAA3I,KAAA,YAAAyI,UAAA,CAAA9G,EAAA;cACAyG,OAAA,CAAAhX,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAAoe,UAAA,CAAAjT,IAAA;cAEA4S,OAAA,CAAA3c,OAAA;cAAA,OAAAgd,UAAA,CAAAlI,MAAA;YAAA;YAAA;cAAA,OAAAkI,UAAA,CAAAjS,IAAA;UAAA;QAAA,GAAA6R,SAAA;MAAA;IAEA;IACAO,YAAA,WAAAA,aAAA;MACA,KAAAxZ,kBAAA;MACA,SAAAtD,aAAA,CAAA6I,MAAA;QACA,KAAAvD,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA;MACA;MAEA,SAAAyB,aAAA,CAAA6I,MAAA;QACA,KAAAvD,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA;MACA;;MAEA;MACA,IAAAwe,iBAAA,QAAA/c,aAAA;MACA,IAAAgd,eAAA,QAAA9d,WAAA,CAAA+d,IAAA,CACA,UAAAtX,IAAA;QAAA,OAAAA,IAAA,CAAAmH,EAAA,KAAAiQ,iBAAA;MAAA,CACA;MAEA,IAAAC,eAAA;QACA,KAAAzZ,cAAA,GAAAyZ,eAAA;QACA,KAAA1Z,kBAAA;MACA;QACA,KAAAgC,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;MACA;IACA;IACA;IACA;IACA2e,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhU,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAA+T,UAAA;QAAA,IAAAC,qBAAA,EAAAC,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,MAAA,EAAA9T,QAAA,EAAA+T,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,kBAAA,EAAA5L,IAAA,EAAAzL,KAAA,EAAAsX,QAAA,EAAAC,aAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,IAAA,EAAA9e,IAAA,EAAA+e,QAAA,EAAAC,MAAA;QAAA,WAAAtV,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAoV,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlV,IAAA,GAAAkV,UAAA,CAAAjV,IAAA;YAAA;cAAA,MACAwT,OAAA,CAAAnd,aAAA,CAAA6I,MAAA;gBAAA+V,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAA,OAAAiV,UAAA,CAAAhV,MAAA,WACAuT,OAAA,CAAA7X,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAIA4e,OAAA,CAAA3Y,YAAA;gBAAAoa,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cACAwT,OAAA,CAAA1Y,SAAA;cAAA,KACA0Y,OAAA,CAAAzY,aAAA;gBAAAka,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAAiV,UAAA,CAAAlV,IAAA;cAAAkV,UAAA,CAAAjV,IAAA;cAAA,OAEAwT,OAAA,CAAAzY,aAAA,CAAAqV,MAAA;YAAA;cAAA6E,UAAA,CAAAjV,IAAA;cAAA;YAAA;cAAAiV,UAAA,CAAAlV,IAAA;cAAAkV,UAAA,CAAA/I,EAAA,GAAA+I,UAAA;cAEA/B,OAAA,CAAAgC,GAAA,sOAAAD,UAAA,CAAA/I,EAAA;YAAA;cAAA+I,UAAA,CAAAjV,IAAA;cAAA,OAGA,IAAAmV,OAAA,WAAAC,OAAA;gBAAA,OAAA7S,UAAA,CAAA6S,OAAA;cAAA;YAAA;cAGA5B,OAAA,CAAA3Y,YAAA;cACA2Y,OAAA,CAAA1Y,SAAA;cACA0Y,OAAA,CAAA3Z,eAAA;cACA2Z,OAAA,CAAA1Z,YAAA;cACA0Z,OAAA,CAAAzZ,UAAA;cAAAkb,UAAA,CAAAlV,IAAA;cAGA;cACA4T,gBAAA,GAAAH,OAAA,CAAAje,WAAA,CAAAwG,MAAA,WAAA6I,OAAA;gBAAA,OACA4O,OAAA,CAAAnd,aAAA,CAAA2H,QAAA,CAAA4G,OAAA,CAAAzB,EAAA;cAAA,CACA;cACAyQ,MAAA,GAAAD,gBAAA,CACA5U,GAAA,WAAA6F,OAAA;gBAAA,gBAAAtG,MAAA,CAAAsG,OAAA,CAAArM,OAAA,IAAAqM,OAAA,CAAAzM,KAAA;cAAA,GACAiJ,IAAA,QAEA;cAAA6T,UAAA,CAAAjV,IAAA;cAAA,OACA,IAAAqV,4BAAA,EACA7B,OAAA,CAAAnd,aAAA,CAAA+K,IAAA,KACA;YAAA;cAFAyS,gBAAA,GAAAoB,UAAA,CAAA3R,IAAA;cAAA,KAAAoQ,qBAAA,GAGAG,gBAAA,CAAA9d,IAAA,cAAA2d,qBAAA,eAAAA,qBAAA,CAAAxU,MAAA;gBAAA+V,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAsV,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAA9d,IAAA,CACAgJ,GAAA,WAAA6F,OAAA,EAAAkF,KAAA;gBAAA,IAAAyL,qBAAA,EAAAC,sBAAA;gBACA,IAAArd,KAAA,GACA,EAAAod,qBAAA,GAAA5B,gBAAA,CAAA7J,KAAA,eAAAyL,qBAAA,uBAAAA,qBAAA,CAAAhd,OAAA,OAAAid,sBAAA,GACA7B,gBAAA,CAAA7J,KAAA,eAAA0L,sBAAA,uBAAAA,sBAAA,CAAArd,KAAA,KACA;gBACA,IAAAE,OAAA,GAAAuM,OAAA,CAAAvM,OAAA;gBACA,uBAAAiG,MAAA,CAAAwL,KAAA,yCAAAxL,MAAA,CAAAnG,KAAA,gBAAAmG,MAAA,CAAAjG,OAAA;cACA,GACA+I,IAAA,yDAEA;cACAoS,OAAA,CAAA1Z,YAAA,CAAA0K,IAAA;gBACAiR,IAAA;gBACApd,OAAA,qDAAAiG,MAAA,CAAAkV,OAAA,CAAAnd,aAAA,CAAA6I,MAAA,gCAAAZ,MAAA,CAAAsV,MAAA;cACA;;cAEA;cACAG,SAAA;gBACA0B,IAAA;gBACApd,OAAA;cACA;cACAmb,OAAA,CAAA1Z,YAAA,CAAA0K,IAAA,CAAAuP,SAAA;;cAEA;cACAC,MAAA,GACAR,OAAA,CAAAvY,eAAA,CACAuC,OAAA,kBAAAgW,OAAA,CAAAnd,aAAA,CAAA6I,MAAA,EACA1B,OAAA,yFAAAc,MAAA,CACAwV,eAAA,GAEA;cAAAmB,UAAA,CAAAjV,IAAA;cAAA,OACA,IAAA0V,YAAA,EACA5B,eAAA,EACA,aACA,qBACA;YAAA;cAJA5T,QAAA,GAAA+U,UAAA,CAAA3R,IAAA;cAAA,IAKApD,QAAA,CAAAyV,EAAA;gBAAAV,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAsV,KAAA;YAAA;cAGA;cACArB,MAAA,GAAA/T,QAAA,CAAA0V,IAAA,CAAAC,SAAA;cACArC,OAAA,CAAAzY,aAAA,GAAAkZ,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACAC,aAAA;cACAC,YAAA;cAEA;cACAC,aAAA,YAAAA,cAAA1V,GAAA;gBACA,OAAAA,GAAA,CAAApB,OAAA,gCAAAU,KAAA;kBACA,OAAAvI,MAAA,CAAAogB,YAAA,CAAAC,QAAA,CAAA9X,KAAA,CAAAV,OAAA;gBACA;cACA,GAEA;cACA+W,aAAA,YAAAA,cAAA0B,UAAA;gBACA;kBACA,IAAAC,eAAA,OAAAC,cAAA,EAAAF,UAAA,EAAAzC,OAAA,CAAAtZ,eAAA;kBACA6Z,SAAA,CAAA1b,OAAA,GAAA6d,eAAA;;kBAEA;kBACA1C,OAAA,CAAA4C,SAAA;oBACA,IAAAtc,YAAA,GAAA0Z,OAAA,CAAA3R,KAAA,CAAA/H,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAiI,SAAA,GAAAjI,YAAA,CAAAuc,YAAA;oBACA;kBACA;gBACA,SAAA9L,KAAA;kBACA2I,OAAA,CAAA3I,KAAA,aAAAA,KAAA;gBACA;cACA,GAEA;YAAA;cAAA,KACA;gBAAA0K,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAA,KAEAwT,OAAA,CAAA1Y,SAAA;gBAAAma,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAsV,KAAA;YAAA;cAAAL,UAAA,CAAAjV,IAAA;cAAA,OAGAiU,MAAA,CAAAqC,IAAA;YAAA;cAAA9B,kBAAA,GAAAS,UAAA,CAAA3R,IAAA;cAAAsF,IAAA,GAAA4L,kBAAA,CAAA5L,IAAA;cAAAzL,KAAA,GAAAqX,kBAAA,CAAArX,KAAA;cAAA,KAEAyL,IAAA;gBAAAqM,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cACA;cACA,IAAAoU,aAAA;gBACA;kBACAK,QAAA,GAAA7N,IAAA,CAAAC,KAAA,CAAAuN,aAAA;kBACA,IAAAK,QAAA,CAAAM,MAAA;oBACA;oBACAL,aAAA,GAAAJ,aAAA,CAAAG,QAAA,CAAAM,MAAA;oBACAZ,MAAA,IAAAO,aAAA;oBACAH,aAAA,CAAAJ,MAAA;kBACA;gBACA,SAAAvJ,CAAA;kBACAsI,OAAA,CAAAqD,IAAA,gBAAA3L,CAAA;gBACA;cACA;cAAA,OAAAqK,UAAA,CAAAhV,MAAA;YAAA;cAIA0U,KAAA,GAAAT,OAAA,CAAAsC,MAAA,CAAArZ,KAAA;cACAiX,aAAA,IAAAO,KAAA;;cAEA;YAAA;cAAA,KACAP,aAAA,CAAApW,QAAA;gBAAAiX,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cACA4U,YAAA,GAAAR,aAAA,CAAAlK,OAAA;cACA2K,IAAA,GAAAT,aAAA,CAAAqC,KAAA,IAAA7B,YAAA,EAAAnX,IAAA;cACA2W,aAAA,GAAAA,aAAA,CAAAqC,KAAA,CAAA7B,YAAA;cAAA,MAEA,CAAAC,IAAA,IAAAA,IAAA,iBAAAA,IAAA,CAAA6B,UAAA;gBAAAzB,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAA,OAAAiV,UAAA,CAAAhV,MAAA;YAAA;cAAAgV,UAAA,CAAAlV,IAAA;cAKAhK,IAAA,GAAA8e,IAAA,CAAA4B,KAAA,IAAAhZ,IAAA;cAAA,MACA1H,IAAA;gBAAAkf,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAA,OAAAiV,UAAA,CAAAhV,MAAA;YAAA;cAIA6U,QAAA,GAAAlO,IAAA,CAAAC,KAAA,CAAA9Q,IAAA;cAAA,IACA+e,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAA,OAAAiV,UAAA,CAAAhV,MAAA;YAAA;cAAA,MAKA6U,QAAA,CAAAC,MAAA,cAAAD,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cAAA,OAAAiV,UAAA,CAAAhV,MAAA;YAAA;cAIA;cACA8U,MAAA,GAAAT,aAAA,CAAAQ,QAAA,CAAAC,MAAA,GAEA;cAAA,KACAA,MAAA,CAAA/W,QAAA;gBAAAiX,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cACAqU,YAAA;cAAA,OAAAY,UAAA,CAAAhV,MAAA;YAAA;cAAA,KAKA8U,MAAA,CAAA/W,QAAA;gBAAAiX,UAAA,CAAAjV,IAAA;gBAAA;cAAA;cACAqU,YAAA;cAAA,OAAAY,UAAA,CAAAhV,MAAA;YAAA;cAIA;cACA,KAAAoU,YAAA,IAAAU,MAAA;gBACAZ,MAAA,IAAAY,MAAA;gBACAR,aAAA,CAAAJ,MAAA;cACA;cAAAc,UAAA,CAAAjV,IAAA;cAAA;YAAA;cAAAiV,UAAA,CAAAlV,IAAA;cAAAkV,UAAA,CAAAtK,EAAA,GAAAsK,UAAA;cAEA/B,OAAA,CAAAqD,IAAA;gBACA1B,IAAA,EAAAA,IAAA;gBACAtK,KAAA,EAAA0K,UAAA,CAAAtK,EAAA,CAAAvS,OAAA;gBACAgc,aAAA,EAAAA;cACA;cAAA,OAAAa,UAAA,CAAAhV,MAAA;YAAA;cAAAgV,UAAA,CAAAjV,IAAA;cAAA;YAAA;cAAAiV,UAAA,CAAAjV,IAAA;cAAA;YAAA;cAAAiV,UAAA,CAAAjV,IAAA;cAAA;YAAA;cAAAiV,UAAA,CAAAlV,IAAA;cAAAkV,UAAA,CAAA0B,EAAA,GAAA1B,UAAA;cAMA/B,OAAA,CAAA3I,KAAA,YAAA0K,UAAA,CAAA0B,EAAA;cACAnD,OAAA,CAAA7X,QAAA,CAAA4O,KAAA,CAAA0K,UAAA,CAAA0B,EAAA,CAAAve,OAAA;cACA,IAAAob,OAAA,CAAA1Z,YAAA;gBACA0Z,OAAA,CAAA1Z,YAAA,IAAAzB,OAAA;cACA;YAAA;cAAA4c,UAAA,CAAAlV,IAAA;cAEAyT,OAAA,CAAAzY,aAAA;cACA,IAAAyY,OAAA,CAAA3Z,eAAA;gBACA2Z,OAAA,CAAAzZ,UAAA;gBACAyZ,OAAA,CAAA3Y,YAAA;cACA;cAAA,OAAAoa,UAAA,CAAAnK,MAAA;YAAA;YAAA;cAAA,OAAAmK,UAAA,CAAAlU,IAAA;UAAA;QAAA,GAAA0S,SAAA;MAAA;IAEA;IACA;IACAmD,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAArX,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAoX,UAAA;QAAA,IAAAC,sBAAA,EAAApD,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAkD,UAAA,EAAAhD,MAAA,EAAA9T,QAAA,EAAA+T,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAA8C,cAAA,EAAAC,cAAA,EAAAC,UAAA,EAAA5C,aAAA,EAAA6C,aAAA;QAAA,WAAA3X,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAyX,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvX,IAAA,GAAAuX,UAAA,CAAAtX,IAAA;YAAA;cAAA,MACA6W,OAAA,CAAAxgB,aAAA,CAAA6I,MAAA;gBAAAoY,UAAA,CAAAtX,IAAA;gBAAA;cAAA;cAAA,OAAAsX,UAAA,CAAArX,MAAA,WACA4W,OAAA,CAAAlb,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAIAiiB,OAAA,CAAAhc,YAAA;gBAAAyc,UAAA,CAAAtX,IAAA;gBAAA;cAAA;cACA6W,OAAA,CAAA/b,SAAA;cAAA,KACA+b,OAAA,CAAA9b,aAAA;gBAAAuc,UAAA,CAAAtX,IAAA;gBAAA;cAAA;cAAAsX,UAAA,CAAAvX,IAAA;cAAAuX,UAAA,CAAAtX,IAAA;cAAA,OAEA6W,OAAA,CAAA9b,aAAA,CAAAqV,MAAA;YAAA;cAAAkH,UAAA,CAAAtX,IAAA;cAAA;YAAA;cAAAsX,UAAA,CAAAvX,IAAA;cAAAuX,UAAA,CAAApL,EAAA,GAAAoL,UAAA;cAEApE,OAAA,CAAAgC,GAAA,sOAAAoC,UAAA,CAAApL,EAAA;YAAA;cAAAoL,UAAA,CAAAtX,IAAA;cAAA,OAIA,IAAAmV,OAAA,WAAAC,OAAA;gBAAA,OAAA7S,UAAA,CAAA6S,OAAA;cAAA;YAAA;cAGAyB,OAAA,CAAAhc,YAAA;cACAgc,OAAA,CAAA/b,SAAA;cACA+b,OAAA,CAAAhd,eAAA;cACAgd,OAAA,CAAA/c,YAAA;cACA+c,OAAA,CAAA9c,UAAA;cAAAud,UAAA,CAAAvX,IAAA;cAGA;cACA4T,gBAAA,GAAAkD,OAAA,CAAAthB,WAAA,CAAAwG,MAAA,WAAA6I,OAAA;gBAAA,OACAiS,OAAA,CAAAxgB,aAAA,CAAA2H,QAAA,CAAA4G,OAAA,CAAAzB,EAAA;cAAA,CACA;cACAyQ,MAAA,GAAAD,gBAAA,CACA5U,GAAA,WAAA6F,OAAA;gBAAA,gBAAAtG,MAAA,CAAAsG,OAAA,CAAArM,OAAA,IAAAqM,OAAA,CAAAzM,KAAA;cAAA,GACAiJ,IAAA,QAEA;cAAAkW,UAAA,CAAAtX,IAAA;cAAA,OACA,IAAAqV,4BAAA,EACAwB,OAAA,CAAAxgB,aAAA,CAAA+K,IAAA,KACA;YAAA;cAFAyS,gBAAA,GAAAyD,UAAA,CAAAhU,IAAA;cAAA,KAAAyT,sBAAA,GAGAlD,gBAAA,CAAA9d,IAAA,cAAAghB,sBAAA,eAAAA,sBAAA,CAAA7X,MAAA;gBAAAoY,UAAA,CAAAtX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAsV,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAA9d,IAAA,CACAgJ,GAAA,WAAA6F,OAAA,EAAAkF,KAAA;gBAAA,IAAAyN,sBAAA,EAAAC,sBAAA;gBACA,IAAArf,KAAA,GACA,EAAAof,sBAAA,GAAA5D,gBAAA,CAAA7J,KAAA,eAAAyN,sBAAA,uBAAAA,sBAAA,CAAAhf,OAAA,OAAAif,sBAAA,GACA7D,gBAAA,CAAA7J,KAAA,eAAA0N,sBAAA,uBAAAA,sBAAA,CAAArf,KAAA,KACA;gBACA,IAAAE,OAAA,GAAAuM,OAAA,CAAAvM,OAAA;gBACA,uBAAAiG,MAAA,CAAAwL,KAAA,yCAAAxL,MAAA,CAAAnG,KAAA,gBAAAmG,MAAA,CAAAjG,OAAA;cACA,GACA+I,IAAA,yDAEA;cACAyV,OAAA,CAAA/c,YAAA,CAAA0K,IAAA;gBACAiR,IAAA;gBACApd,OAAA,qDAAAiG,MAAA,CAAAuY,OAAA,CAAAxgB,aAAA,CAAA6I,MAAA,gCAAAZ,MAAA,CAAAsV,MAAA;cACA;;cAEA;cACAG,UAAA;gBACA0B,IAAA;gBACApd,OAAA;cACA;cACAwe,OAAA,CAAA/c,YAAA,CAAA0K,IAAA,CAAAuP,UAAA;;cAEA;cACAC,MAAA,GACA6C,OAAA,CAAA5b,eAAA,CACAuC,OAAA,kBAAAqZ,OAAA,CAAAxgB,aAAA,CAAA6I,MAAA,EACA1B,OAAA,yFAAAc,MAAA,CACAwV,eAAA,GAEA;cAAAwD,UAAA,CAAAtX,IAAA;cAAA,OACA,IAAAyX,cAAA,EAAAzD,MAAA;YAAA;cAAA9T,QAAA,GAAAoX,UAAA,CAAAhU,IAAA;cAAA,IACApD,QAAA,CAAAyV,EAAA;gBAAA2B,UAAA,CAAAtX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAsV,KAAA;YAAA;cAGA;cACArB,MAAA,GAAA/T,QAAA,CAAA0V,IAAA,CAAAC,SAAA;cACAgB,OAAA,CAAA9b,aAAA,GAAAkZ,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACA8C,cAAA,GAAAtW,IAAA,CAAA+W,GAAA;cACAR,cAAA;cACAC,UAAA,OAEA;cACA5C,aAAA,YAAAA,cAAA0B,UAAA;gBACA,IAAA0B,WAAA,GAAAhX,IAAA,CAAA+W,GAAA;gBACA;gBACA,IAAAC,WAAA,GAAAV,cAAA;kBACAlD,UAAA,CAAA1b,OAAA,GAAA4d,UAAA;kBACAgB,cAAA,GAAAU,WAAA;kBACA;kBACAd,OAAA,CAAAT,SAAA;oBACA,IAAAtc,YAAA,GAAA+c,OAAA,CAAAhV,KAAA,CAAA/H,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAiI,SAAA,GAAAjI,YAAA,CAAAuc,YAAA;oBACA;kBACA;gBACA;cACA,GAEA;cACAe,aAAA;gBAAA,IAAAQ,KAAA,OAAApY,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAmY,UAAA;kBAAA,IAAAC,mBAAA,EAAAlP,IAAA,EAAAzL,KAAA,EAAAwX,KAAA,EAAAoD,KAAA,EAAAC,UAAA,EAAAC,MAAA,EAAApD,IAAA,EAAAC,QAAA,EAAAoD,SAAA,EAAAC,eAAA,EAAAC,aAAA;kBAAA,WAAA3Y,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAyY,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAAvY,IAAA,GAAAuY,UAAA,CAAAtY,IAAA;sBAAA;wBAAAsY,UAAA,CAAAvY,IAAA;sBAAA;wBAAA,KAEA;0BAAAuY,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBAAA,KAEA6W,OAAA,CAAA/b,SAAA;0BAAAwd,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAAsV,KAAA;sBAAA;wBAAAgD,UAAA,CAAAtY,IAAA;wBAAA,OAGAiU,MAAA,CAAAqC,IAAA;sBAAA;wBAAAwB,mBAAA,GAAAQ,UAAA,CAAAhV,IAAA;wBAAAsF,IAAA,GAAAkP,mBAAA,CAAAlP,IAAA;wBAAAzL,KAAA,GAAA2a,mBAAA,CAAA3a,KAAA;wBAAA,KACAyL,IAAA;0BAAA0P,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBACA,IAAAmU,MAAA,CAAAjV,MAAA;0BACAqV,aAAA,CAAAJ,MAAA;wBACA;wBAAA,OAAAmE,UAAA,CAAArY,MAAA;sBAAA;wBAIA0U,KAAA,GAAAT,OAAA,CAAAsC,MAAA,CAAArZ,KAAA;wBACA4a,KAAA,GAAApD,KAAA,CAAA7V,KAAA,OAAA/C,MAAA,WAAA8Y,IAAA;0BAAA,OAAAA,IAAA,CAAApX,IAAA;wBAAA;wBAAAua,UAAA,OAAArO,2BAAA,CAAA7U,OAAA,EAEAijB,KAAA;wBAAAO,UAAA,CAAAvY,IAAA;wBAAAiY,UAAA,CAAAxN,CAAA;sBAAA;wBAAA,KAAAyN,MAAA,GAAAD,UAAA,CAAAvN,CAAA,IAAA7B,IAAA;0BAAA0P,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBAAA6U,IAAA,GAAAoD,MAAA,CAAA9a,KAAA;wBAAAmb,UAAA,CAAAvY,IAAA;wBAEA+U,QAAA,GAAAlO,IAAA,CAAAC,KAAA,CAAAgO,IAAA;wBAAA,IACAC,QAAA,CAAA5U,QAAA;0BAAAoY,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBAAA,OAAAsY,UAAA,CAAArY,MAAA;sBAAA;wBAEAC,SAAA,GAAA4U,QAAA,CAAA5U,QAAA,EAEA;wBAAA,MACAA,SAAA,cAAAA,SAAA;0BAAAoY,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBAAA,OAAAsY,UAAA,CAAArY,MAAA;sBAAA;wBAIAkX,UAAA,IAAAjX,SAAA;;wBAEA;sBAAA;wBAAA,KACA;0BAAAoY,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBACAmY,eAAA,GAAAhB,UAAA,CAAAjN,OAAA;wBACAkO,aAAA,GAAAjB,UAAA,CAAAjN,OAAA;wBAAA,MAEAiO,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBACA;wBACA,KAAAkX,cAAA;0BACA/C,MAAA,IAAAgD,UAAA;0BACA;0BACA5C,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA0C,OAAA,CAAA3c,eAAA;wBACA;wBACAid,UAAA;wBAAA,OAAAmB,UAAA,CAAArY,MAAA;sBAAA;wBAAA,MAEAkY,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBACA;wBACAkX,cAAA;wBACA,IAAAiB,eAAA;0BACAhE,MAAA,IAAAgD,UAAA,CAAA/H,SAAA,IAAA+I,eAAA;0BACA;0BACA5D,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA0C,OAAA,CAAA3c,eAAA;wBACA;wBACAid,UAAA,GAAAA,UAAA,CAAA/H,SAAA,CAAA+I,eAAA;wBAAA,OAAAG,UAAA,CAAArY,MAAA;sBAAA;wBAAA,MAEAkY,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBACA;wBACAkX,cAAA;wBACAC,UAAA,GAAAA,UAAA,CAAA/H,SAAA,CAAAgJ,aAAA;wBAAA,OAAAE,UAAA,CAAArY,MAAA;sBAAA;wBAGA;wBACA,IAAAkY,eAAA;0BACAhE,MAAA,IAAAgD,UAAA,CAAA/H,SAAA,IAAA+I,eAAA;0BACA;0BACA5D,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA0C,OAAA,CAAA3c,eAAA;wBACA;wBACAid,UAAA,GAAAA,UAAA,CAAA/H,SAAA,CAAAgJ,aAAA;wBACAlB,cAAA;wBAAA,OAAAoB,UAAA,CAAArY,MAAA;sBAAA;wBAAAqY,UAAA,CAAAtY,IAAA;wBAAA;sBAAA;wBAAAsY,UAAA,CAAAtY,IAAA;wBAAA;sBAAA;wBAAAsY,UAAA,CAAAvY,IAAA;wBAAAuY,UAAA,CAAApM,EAAA,GAAAoM,UAAA;wBAKApF,OAAA,CAAAqD,IAAA;0BACA1B,IAAA,EAAAA,IAAA;0BACAtK,KAAA,EAAA+N,UAAA,CAAApM,EAAA,CAAA9T;wBACA;sBAAA;wBAAAkgB,UAAA,CAAAtY,IAAA;wBAAA;sBAAA;wBAAAsY,UAAA,CAAAtY,IAAA;wBAAA;sBAAA;wBAAAsY,UAAA,CAAAvY,IAAA;wBAAAuY,UAAA,CAAA3N,EAAA,GAAA2N,UAAA;wBAAAN,UAAA,CAAApN,CAAA,CAAA0N,UAAA,CAAA3N,EAAA;sBAAA;wBAAA2N,UAAA,CAAAvY,IAAA;wBAAAiY,UAAA,CAAAnN,CAAA;wBAAA,OAAAyN,UAAA,CAAAxN,MAAA;sBAAA;wBAAAwN,UAAA,CAAAtY,IAAA;wBAAA;sBAAA;wBAAAsY,UAAA,CAAAtY,IAAA;wBAAA;sBAAA;wBAAAsY,UAAA,CAAAvY,IAAA;wBAAAuY,UAAA,CAAA3B,EAAA,GAAA2B,UAAA;wBAAA,MAKAA,UAAA,CAAA3B,EAAA,CAAAve,OAAA;0BAAAkgB,UAAA,CAAAtY,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAAsV,KAAA;sBAAA;wBAEApC,OAAA,CAAA3I,KAAA,eAAA+N,UAAA,CAAA3B,EAAA;wBAAA,MAAA2B,UAAA,CAAA3B,EAAA;sBAAA;sBAAA;wBAAA,OAAA2B,UAAA,CAAAvX,IAAA;oBAAA;kBAAA,GAAA8W,SAAA;gBAAA,CAGA;gBAAA,gBAzFAT,cAAA;kBAAA,OAAAQ,KAAA,CAAAW,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAAAlB,UAAA,CAAAtX,IAAA;cAAA,OA2FAoX,aAAA;YAAA;cAAAE,UAAA,CAAAtX,IAAA;cAAA;YAAA;cAAAsX,UAAA,CAAAvX,IAAA;cAAAuX,UAAA,CAAA3M,EAAA,GAAA2M,UAAA;cAAA,MAGAA,UAAA,CAAA3M,EAAA,CAAAvS,OAAA;gBAAAkf,UAAA,CAAAtX,IAAA;gBAAA;cAAA;cACAkT,OAAA,CAAAgC,GAAA;cAAA,OAAAoC,UAAA,CAAArX,MAAA;YAAA;cAGAiT,OAAA,CAAA3I,KAAA,YAAA+M,UAAA,CAAA3M,EAAA;cACAkM,OAAA,CAAAlb,QAAA,CAAA4O,KAAA,CAAA+M,UAAA,CAAA3M,EAAA,CAAAvS,OAAA;cACA,IAAAye,OAAA,CAAA/c,YAAA;gBACA+c,OAAA,CAAA/c,YAAA,IAAAzB,OAAA;cACA;YAAA;cAAAif,UAAA,CAAAvX,IAAA;cAEA8W,OAAA,CAAA9b,aAAA;cACA;cACA,IAAA8b,OAAA,CAAAhd,eAAA;gBACAgd,OAAA,CAAA9c,UAAA;gBACA8c,OAAA,CAAAhc,YAAA;cACA;cAAA,OAAAyc,UAAA,CAAAxM,MAAA;YAAA;YAAA;cAAA,OAAAwM,UAAA,CAAAvW,IAAA;UAAA;QAAA,GAAA+V,SAAA;MAAA;IAEA;IACA;IACA2B,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MAAA,WAAAlZ,kBAAA,CAAA1K,OAAA,mBAAA2K,oBAAA,CAAA3K,OAAA,IAAA4K,IAAA,UAAAiZ,UAAA;QAAA,IAAAhF,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAA8E,WAAA,EAAA5E,MAAA,EAAA9T,QAAA,EAAA+T,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAA0E,eAAA,EAAAtE,aAAA,EAAAuE,mBAAA,EAAAlQ,IAAA,EAAAzL,KAAA,EAAAwX,KAAA,EAAAoD,KAAA,EAAAgB,UAAA,EAAAC,MAAA,EAAAnE,IAAA,EAAA9e,IAAA,EAAAkjB,iBAAA,EAAAnE,QAAA,EAAAzc,OAAA;QAAA,WAAAoH,oBAAA,CAAA3K,OAAA,IAAA8K,IAAA,UAAAsZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApZ,IAAA,GAAAoZ,UAAA,CAAAnZ,IAAA;YAAA;cAAA,MACA0Y,OAAA,CAAAriB,aAAA,CAAA6I,MAAA;gBAAAia,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,OAAAmZ,UAAA,CAAAlZ,MAAA,WACAyY,OAAA,CAAA/c,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAIA8jB,OAAA,CAAA7d,YAAA;gBAAAse,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cACA0Y,OAAA,CAAA5d,SAAA;cAAA,KACA4d,OAAA,CAAA3d,aAAA;gBAAAoe,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAAmZ,UAAA,CAAApZ,IAAA;cAAAoZ,UAAA,CAAAnZ,IAAA;cAAA,OAEA0Y,OAAA,CAAA3d,aAAA,CAAAqV,MAAA;YAAA;cAAA+I,UAAA,CAAAnZ,IAAA;cAAA;YAAA;cAAAmZ,UAAA,CAAApZ,IAAA;cAAAoZ,UAAA,CAAAjN,EAAA,GAAAiN,UAAA;cAEAjG,OAAA,CAAAgC,GAAA,sOAAAiE,UAAA,CAAAjN,EAAA;YAAA;cAAAiN,UAAA,CAAAnZ,IAAA;cAAA,OAIA,IAAAmV,OAAA,WAAAC,OAAA;gBAAA,OAAA7S,UAAA,CAAA6S,OAAA;cAAA;YAAA;cAGAsD,OAAA,CAAA7d,YAAA;cACA6d,OAAA,CAAA5d,SAAA;cACA4d,OAAA,CAAA7e,eAAA;cACA6e,OAAA,CAAA5e,YAAA;cACA4e,OAAA,CAAA3e,UAAA;cAEA4Z,gBAAA,GAAA+E,OAAA,CAAAnjB,WAAA,CAAAwG,MAAA,WAAA6I,OAAA;gBAAA,OACA8T,OAAA,CAAAriB,aAAA,CAAA2H,QAAA,CAAA4G,OAAA,CAAAzB,EAAA;cAAA,CACA;cACAyQ,MAAA,GAAAD,gBAAA,CACA5U,GAAA,WAAA6F,OAAA;gBAAA,gBAAAtG,MAAA,CAAAsG,OAAA,CAAArM,OAAA,IAAAqM,OAAA,CAAAzM,KAAA;cAAA,GACAiJ,IAAA;cAAA+X,UAAA,CAAApZ,IAAA;cAAAoZ,UAAA,CAAAnZ,IAAA;cAAA,OAGA,IAAAqV,4BAAA,EACAqD,OAAA,CAAAriB,aAAA,CAAA+K,IAAA,KACA;YAAA;cAFAyS,gBAAA,GAAAsF,UAAA,CAAA7V,IAAA;cAAA,MAGA,CAAAuQ,gBAAA,CAAA9d,IAAA,KAAA8d,gBAAA,CAAA9d,IAAA,CAAAmJ,MAAA;gBAAAia,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAsV,KAAA;YAAA;cAGAxB,eAAA,GAAAD,gBAAA,CAAA9d,IAAA,CACAgJ,GAAA,WAAA6F,OAAA,EAAAkF,KAAA;gBAAA,IAAAsP,sBAAA,EAAAC,sBAAA;gBACA,IAAAlhB,KAAA,GACA,EAAAihB,sBAAA,GAAAzF,gBAAA,CAAA7J,KAAA,eAAAsP,sBAAA,uBAAAA,sBAAA,CAAA7gB,OAAA,OAAA8gB,sBAAA,GACA1F,gBAAA,CAAA7J,KAAA,eAAAuP,sBAAA,uBAAAA,sBAAA,CAAAlhB,KAAA,KACA;gBACA,IAAAE,OAAA,GAAAuM,OAAA,CAAAvM,OAAA;gBACA,uBAAAiG,MAAA,CAAAwL,KAAA,yCAAAxL,MAAA,CAAAnG,KAAA,gBAAAmG,MAAA,CAAAjG,OAAA;cACA,GACA+I,IAAA,yDAEA;cACAsX,OAAA,CAAA5e,YAAA,CAAA0K,IAAA;gBACAiR,IAAA;gBACApd,OAAA,qDAAAiG,MAAA,CAAAoa,OAAA,CAAAriB,aAAA,CAAA6I,MAAA,gCAAAZ,MAAA,CAAAsV,MAAA;cACA;;cAEA;cACAG,WAAA;gBACA0B,IAAA;gBACApd,OAAA;cACA;cACAqgB,OAAA,CAAA5e,YAAA,CAAA0K,IAAA,CAAAuP,WAAA;cACA2E,OAAA,CAAA3e,UAAA;cAEAia,MAAA,GACA0E,OAAA,CAAAzd,eAAA,CACAuC,OAAA,kBAAAkb,OAAA,CAAAriB,aAAA,CAAA6I,MAAA,EACA1B,OAAA,6FAAAc,MAAA,CACAwV,eAAA;cAAAqF,UAAA,CAAAnZ,IAAA;cAAA,OAEA,IAAAsZ,gBAAA,EAAAtF,MAAA;YAAA;cAAA9T,QAAA,GAAAiZ,UAAA,CAAA7V,IAAA;cAAA,KAEApD,QAAA,CAAAyV,EAAA;gBAAAwD,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cACAiU,MAAA,GAAA/T,QAAA,CAAA0V,IAAA,CAAAC,SAAA;cACA6C,OAAA,CAAA3d,aAAA,GAAAkZ,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACA8C,eAAA,GAAAtW,IAAA,CAAA+W,GAAA;cAEAnD,aAAA,YAAAA,cAAA0B,UAAA;gBACA,IAAA0B,WAAA,GAAAhX,IAAA,CAAA+W,GAAA;gBACA,IAAAC,WAAA,GAAAV,eAAA;kBACAlD,WAAA,CAAA1b,OAAA,GAAA4d,UAAA;kBACAgB,eAAA,GAAAU,WAAA;kBACAe,OAAA,CAAAtC,SAAA;oBACA,IAAAtc,YAAA,GAAA4e,OAAA,CAAA7W,KAAA,CAAA/H,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAiI,SAAA,GAAAjI,YAAA,CAAAuc,YAAA;oBACA;kBACA;gBACA;cACA;YAAA;cAAA,KAEA;gBAAA8C,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,KAEA0Y,OAAA,CAAA5d,SAAA;gBAAAqe,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAsV,KAAA;YAAA;cAAA6D,UAAA,CAAAnZ,IAAA;cAAA,OAGAiU,MAAA,CAAAqC,IAAA;YAAA;cAAAwC,mBAAA,GAAAK,UAAA,CAAA7V,IAAA;cAAAsF,IAAA,GAAAkQ,mBAAA,CAAAlQ,IAAA;cAAAzL,KAAA,GAAA2b,mBAAA,CAAA3b,KAAA;cAAA,KACAyL,IAAA;gBAAAuQ,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cACA,IAAAmU,MAAA,CAAAjV,MAAA;gBACAqV,aAAA,CAAAJ,MAAA;cACA;cAAA,OAAAgF,UAAA,CAAAlZ,MAAA;YAAA;cAIA0U,KAAA,GAAAT,OAAA,CAAAsC,MAAA,CAAArZ,KAAA;cAAAgc,UAAA,CAAApZ,IAAA;cAEAgY,KAAA,GAAApD,KAAA,CAAA7V,KAAA;cAAAia,UAAA,OAAApP,2BAAA,CAAA7U,OAAA,EAEAijB,KAAA;cAAAoB,UAAA,CAAApZ,IAAA;cAAAgZ,UAAA,CAAAvO,CAAA;YAAA;cAAA,KAAAwO,MAAA,GAAAD,UAAA,CAAAtO,CAAA,IAAA7B,IAAA;gBAAAuQ,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA6U,IAAA,GAAAmE,MAAA,CAAA7b,KAAA;cAAA,MACA,CAAA0X,IAAA,CAAApX,IAAA,OAAAoX,IAAA,CAAA6B,UAAA;gBAAAyC,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,OAAAmZ,UAAA,CAAAlZ,MAAA;YAAA;cAEAlK,IAAA,GAAA8e,IAAA,CAAA4B,KAAA;cAAA,MACA1gB,IAAA;gBAAAojB,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,OAAAmZ,UAAA,CAAAlZ,MAAA;YAAA;cAAAkZ,UAAA,CAAApZ,IAAA;cAGA+U,QAAA,GAAAlO,IAAA,CAAAC,KAAA,CAAA9Q,IAAA;cAAA,OAAAkjB,iBAAA,GACAnE,QAAA,CAAAyE,OAAA,cAAAN,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,iBAAAA,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,CAAAO,KAAA,cAAAP,iBAAA,eAAAA,iBAAA,CAAA5gB,OAAA;gBAAA8gB,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cACA3H,OAAA,GAAAyc,QAAA,CAAAyE,OAAA,IAAAC,KAAA,CAAAnhB,OAAA,EAEA;cAAA,MACAA,OAAA,cAAAA,OAAA;gBAAA8gB,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cAAA,OAAAmZ,UAAA,CAAAlZ,MAAA;YAAA;cAIAkU,MAAA,IAAA9b,OAAA;cACAkc,aAAA,CAAAJ,MAAA;YAAA;cAAAgF,UAAA,CAAAnZ,IAAA;cAAA;YAAA;cAAAmZ,UAAA,CAAApZ,IAAA;cAAAoZ,UAAA,CAAAxO,EAAA,GAAAwO,UAAA;cAGAjG,OAAA,CAAA3I,KAAA,wBAAA4O,UAAA,CAAAxO,EAAA;YAAA;cAAAwO,UAAA,CAAAnZ,IAAA;cAAA;YAAA;cAAAmZ,UAAA,CAAAnZ,IAAA;cAAA;YAAA;cAAAmZ,UAAA,CAAApZ,IAAA;cAAAoZ,UAAA,CAAAxC,EAAA,GAAAwC,UAAA;cAAAJ,UAAA,CAAAnO,CAAA,CAAAuO,UAAA,CAAAxC,EAAA;YAAA;cAAAwC,UAAA,CAAApZ,IAAA;cAAAgZ,UAAA,CAAAlO,CAAA;cAAA,OAAAsO,UAAA,CAAArO,MAAA;YAAA;cAAAqO,UAAA,CAAAnZ,IAAA;cAAA;YAAA;cAAAmZ,UAAA,CAAApZ,IAAA;cAAAoZ,UAAA,CAAAM,EAAA,GAAAN,UAAA;cAIAjG,OAAA,CAAA3I,KAAA,4BAAA4O,UAAA,CAAAM,EAAA;YAAA;cAAAN,UAAA,CAAAnZ,IAAA;cAAA;YAAA;cAAAmZ,UAAA,CAAAnZ,IAAA;cAAA;YAAA;cAAA,MAIA,IAAAsV,KAAA;YAAA;cAAA6D,UAAA,CAAAnZ,IAAA;cAAA;YAAA;cAAAmZ,UAAA,CAAApZ,IAAA;cAAAoZ,UAAA,CAAAO,EAAA,GAAAP,UAAA;cAAA,MAIAA,UAAA,CAAAO,EAAA,CAAAthB,OAAA;gBAAA+gB,UAAA,CAAAnZ,IAAA;gBAAA;cAAA;cACAkT,OAAA,CAAAgC,GAAA;cAAA,OAAAiE,UAAA,CAAAlZ,MAAA;YAAA;cAGAiT,OAAA,CAAA3I,KAAA,mBAAA4O,UAAA,CAAAO,EAAA;cACAhB,OAAA,CAAA/c,QAAA,CAAA4O,KAAA;cACA,IAAAmO,OAAA,CAAA5e,YAAA;gBACA4e,OAAA,CAAA5e,YAAA,IAAAzB,OAAA;cACA;YAAA;cAAA8gB,UAAA,CAAApZ,IAAA;cAEA2Y,OAAA,CAAA3d,aAAA;cACA;cACA,IAAA2d,OAAA,CAAA7e,eAAA;gBACA6e,OAAA,CAAA3e,UAAA;gBACA2e,OAAA,CAAA7d,YAAA;cACA;cAAA,OAAAse,UAAA,CAAArO,MAAA;YAAA;YAAA;cAAA,OAAAqO,UAAA,CAAApY,IAAA;UAAA;QAAA,GAAA4X,SAAA;MAAA;IAEA;IACA;IACAgB,aAAA,WAAAA,cAAA;MACA,KAAA7e,SAAA;MACA,SAAAC,aAAA;QACA,KAAAA,aAAA,CAAAqV,MAAA;MACA;MACA,KAAAvW,eAAA;MACA,KAAAC,YAAA;MACA,KAAAC,UAAA;MACA,KAAAc,YAAA;MACA,KAAAE,aAAA;IACA;IACA6e,aAAA,WAAAA,cAAA;MACA,SAAA5e,UAAA;QACA,KAAAuY,UAAA;MACA,gBAAAvY,UAAA;QACA,KAAA4b,YAAA;MACA,gBAAA5b,UAAA;QACA,KAAAyd,cAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}