{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\mytask\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\mytask\\index.vue", "mtime": 1754010084484}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_articleList", "_articleHistory", "_list", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "listList", "title", "open", "queryParams", "pageNum", "pageSize", "sn", "cnTitle", "sourceType", "sourceArea", "sourceName", "sourceSn", "originalUrl", "shortUrl", "author", "keywords", "description", "summary", "cnSummary", "cover", "publishType", "publishCode", "publishArea", "publishTime", "numberLikes", "numberReads", "numberCollects", "numberShares", "numberComments", "emotion", "status", "userId", "deptId", "fileUrl", "firstPublishTime", "firstCreateTime", "firstWebstePublishTime", "webstePublishTime", "isChanged", "isTranslated", "isTechnology", "isReviewed", "isPulled", "isFile", "deleteBy", "deleteTime", "form", "rules", "required", "message", "content", "created", "getList", "methods", "_this", "getlistByUser", "then", "response", "rows", "$nextTick", "scrollToTop", "$refs", "tableRef", "tableEl", "$el", "querySelector", "scrollTop", "cancel", "reset", "id", "remark", "createBy", "createTime", "updateBy", "updateTime", "delFlag", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "API", "AreaInfo", "docId", "toString", "submitForm", "_this3", "validate", "valid", "queryForm", "JSON", "parse", "stringify", "articleListEdit", "$modal", "msgSuccess", "addList", "handleDelete", "_this4", "confirm", "deleteByIds", "catch", "openArticle", "window", "concat", "handleUpdate0", "_this5", "articleIds", "str", "join", "updateArticleTech0", "handleUpdate1", "_this6", "updateArticleTech1", "handleUpdate2", "_this7", "updateArticleTech2", "processTechnologyState", "state", "processSourceType", "processSourceArea", "processReviewedState", "processPulledState", "handlePass", "_this8", "articlePass", "handleNoPass", "_this9", "articleNoPass", "handleCancelPass", "_this10", "cancelArticlePass", "onCreateTask", "_this11", "getTask", "handleRadioChange", "e", "console", "log"], "sources": ["src/views/article/mytask/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"80px\"\r\n      class=\"queryForm\"\r\n    >\r\n      <el-row type=\"flex\" justify=\"space-between\">\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"标题\" prop=\"title\">\r\n            <el-input\r\n              v-model=\"queryParams.title\"\r\n              placeholder=\"请输入标题\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"网址\" prop=\"originalUrl\">\r\n            <el-input\r\n              v-model=\"queryParams.originalUrl\"\r\n              placeholder=\"请输入网址\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"主键\" prop=\"id\">\r\n            <el-input\r\n              v-model=\"queryParams.id\"\r\n              placeholder=\"请输入主键\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"所属领域\" prop=\"sourceArea\">\r\n            <el-select\r\n              v-model=\"queryParams.sourceArea\"\r\n              placeholder=\"请选择所属领域\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.source_area\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row type=\"flex\" justify=\"space-between\">\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"小信优选\" prop=\"isTechnology\">\r\n            <el-select\r\n              v-model=\"queryParams.isTechnology\"\r\n              placeholder=\"请选择小信优选\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_technology\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n            <el-select\r\n              v-model=\"queryParams.sourceType\"\r\n              placeholder=\"请选择平台类型\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.source_type\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否审核\" prop=\"isReviewed\">\r\n            <el-select\r\n              v-model=\"queryParams.isReviewed\"\r\n              placeholder=\"审核状态\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_reviewed\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否翻译\" prop=\"isTranslated\">\r\n            <el-select\r\n              v-model=\"queryParams.isTranslated\"\r\n              placeholder=\"正文翻译状态\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_translated\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否推送\" prop=\"isPulled\">\r\n            <el-select\r\n              v-model=\"queryParams.isPulled\"\r\n              placeholder=\"推送状态\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_pulled\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-form-item style=\"width: 240px\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleUpdate0\"\r\n          >设置为小信优选未选中</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleUpdate1\"\r\n          >设置为小信优选选中</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleUpdate2\"\r\n          >设置为小信优选待定</el-button\r\n        >\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handlePass\"\r\n          >审核通过</el-button\r\n        >\r\n      </el-col> -->\r\n      <!--      <el-col :span=\"1.5\">-->\r\n      <!--        <el-button type=\"primary\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"multiple\" @click=\"handleCancelPass\" v-hasPermi=\"['model:tech:cancel']\">撤回审核通过</el-button>-->\r\n      <!--      </el-col>-->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleNoPass\"\r\n          >审核不通过</el-button\r\n        >\r\n      </el-col> -->\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <!--    <el-table v-loading=\"loading\" :data=\"listList\" @selection-change=\"handleSelectionChange\" @cell-click=\"openArticle\">-->\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"listList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      height=\"calc(100vh - 330px)\"\r\n      ref=\"tableRef\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column prop=\"isReviewed\" label=\"审核\" align=\"center\" width=\"90\">\r\n        <template slot-scope=\"scope\">\r\n          <el-radio-group\r\n            v-model=\"scope.row.isReviewed\"\r\n            class=\"radio-group\"\r\n            @input=\"(e) => handleRadioChange(e, scope.row.id)\"\r\n          >\r\n            <el-radio :label=\"'1'\" style=\"color: #67c23a\">通过</el-radio>\r\n            <el-radio :label=\"'2'\" style=\"color: #f56c6c\">不通过</el-radio>\r\n          </el-radio-group>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"主键\" align=\"center\" prop=\"id\" width=\"70\" />\r\n      <!--      <el-table-column label=\"文章唯一标识\" align=\"center\" prop=\"sn\" />-->\r\n      <!--      <el-table-column label=\"标题\" align=\"center\" width=\"150\" prop=\"title\" />-->\r\n      <el-table-column prop=\"title\" label=\"标题\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-link\r\n            :href=\"`/expressDetails?id=${scope.row.id}&docId=${scope.row.docId}`\"\r\n            :underline=\"false\"\r\n            target=\"_blank\"\r\n          >\r\n            <span\r\n              class=\"el-icon-document\"\r\n              style=\"word-break: normal; word-wrap: break-word\"\r\n            >\r\n              {{ scope.row.title }}\r\n            </span>\r\n          </el-link>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"中文标题\" align=\"center\" prop=\"cnTitle\">\r\n        <template slot-scope=\"scope\">\r\n          <span\r\n            class=\"el-icon-document\"\r\n            style=\"word-break: normal; word-wrap: break-word\"\r\n          >\r\n            {{ scope.row.cnTitle }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"sourceType\"\r\n        label=\"平台类型\"\r\n        align=\"center\"\r\n        width=\"80\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processSourceType(scope.row.sourceType) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.sourceType\"\r\n            :options=\"dict.type.source_type\"\r\n            :value=\"scope.row.sourceType\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"sourceArea\"\r\n        label=\"所属领域\"\r\n        align=\"center\"\r\n        width=\"80\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processSourceArea(scope.row.sourceArea) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.sourceArea\"\r\n            :options=\"dict.type.source_area\"\r\n            :value=\"scope.row.sourceArea\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"来源名称\"\r\n        align=\"center\"\r\n        prop=\"sourceName\"\r\n        width=\"120\"\r\n      />\r\n      <!--      <el-table-column label=\"是否科技\" align=\"center\" prop=\"isTechnology\" />-->\r\n      <el-table-column\r\n        prop=\"isTechnology\"\r\n        label=\"科技相关\"\r\n        align=\"center\"\r\n        width=\"80\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processTechnologyState(scope.row.isTechnology) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.isTechnology\"\r\n            :options=\"dict.type.is_technology\"\r\n            :value=\"scope.row.isTechnology\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"isTranslated\"\r\n        label=\"翻译\"\r\n        align=\"center\"\r\n        width=\"65\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processTranslateState(scope.row.isTranslated) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.isTranslated\"\r\n            :options=\"dict.type.is_translated\"\r\n            :value=\"scope.row.isTranslated\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"contentLength\"\r\n        label=\"长度\"\r\n        align=\"center\"\r\n        width=\"60\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.contentLength }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"isPulled\" label=\"推送\" width=\"65\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processPulledState(scope.row.isPulled) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.isPulled\"\r\n            :options=\"dict.type.is_pulled\"\r\n            :value=\"scope.row.isPulled\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"发布时间\"\r\n        align=\"center\"\r\n        prop=\"publishTime\"\r\n        width=\"95\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.publishTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"创建时间\"\r\n        align=\"center\"\r\n        prop=\"createTime\"\r\n        width=\"95\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n        width=\"60\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <div\r\n            style=\"\r\n              display: flex;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n              align-items: center;\r\n            \"\r\n          >\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              v-hasPermi=\"['mytask:list:edit']\"\r\n              >修改</el-button\r\n            >\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              v-hasPermi=\"['mytask:list:remove']\"\r\n              style=\"margin-left: 0\"\r\n              >删除</el-button\r\n            >\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改文章列对话框 -->\r\n    <!--    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>-->\r\n    <!--      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">-->\r\n    <!--        <el-form-item label=\"文章唯一标识\" prop=\"sn\">-->\r\n    <!--          <el-input v-model=\"form.sn\" placeholder=\"请输入文章唯一标识\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"标题\" prop=\"title\">-->\r\n    <!--          <el-input v-model=\"form.title\" type=\"textarea\" placeholder=\"请输入内容\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"中文标题\" prop=\"cnTitle\">-->\r\n    <!--          <el-input v-model=\"form.cnTitle\" type=\"textarea\" placeholder=\"请输入内容\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"发布时间\" prop=\"publishTime\">-->\r\n    <!--          <el-date-picker clearable-->\r\n    <!--            v-model=\"form.publishTime\"-->\r\n    <!--            type=\"date\"-->\r\n    <!--            value-format=\"yyyy-MM-dd\"-->\r\n    <!--            placeholder=\"请选择发布时间\">-->\r\n    <!--          </el-date-picker>-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"是否与科技有关\" prop=\"isTechnology\">-->\r\n    <!--          <el-input v-model=\"form.isTechnology\" placeholder=\"是否与科技有关(0.无；1有；2其他)\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--     </el-form>-->\r\n    <!--      <div slot=\"footer\" class=\"dialog-footer\">-->\r\n    <!--        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>-->\r\n    <!--        <el-button @click=\"cancel\">取 消</el-button>-->\r\n    <!--      </div>-->\r\n    <!--    </el-dialog>-->\r\n\r\n    <el-dialog\r\n      :title=\"'修改文章'\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"dialog_Box\">\r\n        <el-form\r\n          :model=\"form\"\r\n          class=\"form_Style\"\r\n          label-position=\"top\"\r\n          ref=\"form\"\r\n          :rules=\"rules\"\r\n          size=\"mini\"\r\n        >\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"文章标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"中文标题\" prop=\"cnTitle\">\r\n                <el-input v-model=\"form.cnTitle\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n                <el-select\r\n                  v-model=\"form.sourceType\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.source_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"媒体来源\" prop=\"sourceName\">\r\n                <!-- <el-select\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  filterable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeLists\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.name\"\r\n                  ></el-option>\r\n                </el-select> -->\r\n                <el-input\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发布时间\" prop=\"publishTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.publishTime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"datetime\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  placeholder=\"选择日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"文章作者\" prop=\"author\">\r\n                <el-input\r\n                  v-model=\"form.author\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"原文链接\" prop=\"originalUrl\">\r\n            <el-input v-model=\"form.originalUrl\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"摘要\" prop=\"summary\">\r\n            <el-input\r\n              v-model=\"form.summary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文摘要\" prop=\"cnSummary\">\r\n            <el-input\r\n              v-model=\"form.cnSummary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文内容\" prop=\"cnContent\">\r\n            <editor v-model=\"form.cnContent\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <el-form-item label=\"文章内容\" prop=\"content\">\r\n            <editor v-model=\"form.content\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listList,\r\n  getList,\r\n  delList,\r\n  addList,\r\n  updateList,\r\n  getlistByUser,\r\n  getTask,\r\n} from \"@/api/article/articleList\";\r\nimport {\r\n  updateArticleTech0,\r\n  updateArticleTech1,\r\n  updateArticleTech2,\r\n  articlePass,\r\n  articleNoPass,\r\n  cancelArticlePass,\r\n  deleteByIds,\r\n} from \"@/api/article/articleHistory\";\r\nimport { articleListEdit, uploadCover } from \"@/api/articleCrawler/list\";\r\n\r\nexport default {\r\n  name: \"mytask\",\r\n  dicts: [\r\n    \"is_technology\",\r\n    \"is_reviewed\",\r\n    \"is_pulled\",\r\n    \"source_type\",\r\n    \"source_area\",\r\n    \"is_translated\",\r\n  ],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 文章列表格数据\r\n      listList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        sn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceArea: null,\r\n        sourceName: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        keywords: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        userId: null,\r\n        deptId: null,\r\n        fileUrl: null,\r\n        firstPublishTime: null,\r\n        firstCreateTime: null,\r\n        firstWebstePublishTime: null,\r\n        webstePublishTime: null,\r\n        isChanged: null,\r\n        isTranslated: null,\r\n        isTechnology: null,\r\n        isReviewed: \"0\",\r\n        isPulled: \"0\",\r\n        isFile: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        // 表单校验\r\n        title: [{ required: true, message: \"文章标题为必填项\" }],\r\n        content: [{ required: true, message: \"文章详情为必填项\" }],\r\n        publishTime: [{ required: true, message: \"发布时间为必填项\" }],\r\n        cnTitle: [{ required: true, message: \"中文名称为必填项\" }],\r\n        sourceType: [{ required: true, message: \"平台类型为必填项\" }],\r\n        originalUrl: [{ required: true, message: \"原文网址为必填项\" }],\r\n        summary: [{ required: true, message: \"请填写摘要\" }],\r\n        // cnSummary: [{ required: true, message: '请填写中文摘要' }],\r\n        sn: [{ required: true, message: \"请填写文章地址唯一识别号\" }],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询文章列列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      getlistByUser(this.queryParams).then((response) => {\r\n        this.listList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n        this.$nextTick(() => {\r\n          this.scrollToTop();\r\n        });\r\n      });\r\n    },\r\n    // 表格滚动到顶部\r\n    scrollToTop() {\r\n      if (this.$refs.tableRef) {\r\n        const tableEl = this.$refs.tableRef.$el.querySelector(\r\n          \".el-table__body-wrapper\"\r\n        );\r\n        if (tableEl) {\r\n          tableEl.scrollTop = 0;\r\n        }\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        sn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceName: null,\r\n        sourceArea: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        keywords: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: null,\r\n        deptId: null,\r\n        fileUrl: null,\r\n        firstPublishTime: null,\r\n        firstCreateTime: null,\r\n        firstWebstePublishTime: null,\r\n        webstePublishTime: null,\r\n        isChanged: null,\r\n        isTranslated: null,\r\n        isTechnology: 1,\r\n        isFile: null,\r\n        delFlag: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加文章列\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      // const id = row.id || this.ids\r\n      // getList(id).then(response => {\r\n      //   this.form = response.data;\r\n      //   this.open = true;\r\n      //   this.title = \"修改文章列\";\r\n      // });\r\n      API.AreaInfo(row.id).then((response) => {\r\n        this.form = response.data;\r\n        // this.form.sourceType = Number(this.form.sourceType)\r\n        this.form.docId = row.id.toString();\r\n        this.open = true;\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            // updateList(this.form).then(response => {\r\n            //   this.$modal.msgSuccess(\"修改成功\");\r\n            //   this.open = false;\r\n            //   this.getList();\r\n            // });\r\n            let queryForm = JSON.parse(JSON.stringify(this.form));\r\n            articleListEdit(queryForm).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addList(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除文章列编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          // return delList(ids);\r\n          return deleteByIds(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n      // this.$modal.confirm('是否确认删除该条文章？\"').then(() => {\r\n      //   return API.monitoringEsRemove({ id: row.id, docId: row.id.toString() });\r\n      // }).then(() => {\r\n      //   this.Refresh();\r\n      //   this.$modal.msgSuccess(\"删除成功\");\r\n      // }).catch(() => { });\r\n    },\r\n    // 文章详情\r\n    openArticle(item, row) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n    handleUpdate0(row) {\r\n      const articleIds = row.id || this.ids;\r\n      var str = articleIds.join(\",\");\r\n      this.$modal\r\n        .confirm('是否确认修改编号为\"' + str + '\"的数据为科技无关项？')\r\n        .then(function () {\r\n          return updateArticleTech0(str);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleUpdate1(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认修改编号为\"' + articleIds + '\"的数据为科技有关项？')\r\n        .then(function () {\r\n          return updateArticleTech1(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleUpdate2(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认修改编号为\"' + articleIds + '\"的数据为其他项？')\r\n        .then(function () {\r\n          return updateArticleTech2(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    processTechnologyState(state) {\r\n      if (state == 0) return \"无关\";\r\n      if (state == 1) return \"有关\";\r\n      if (state == 2) return \"其他\";\r\n      return \"未定义\";\r\n    },\r\n    processSourceType(state) {\r\n      if (state == 1) return \"国内\";\r\n      if (state == 2) return \"国外\";\r\n      if (state == 3) return \"境内科技\";\r\n      if (state == 4) return \"境外科技\";\r\n      return \"未定义\";\r\n    },\r\n    processSourceArea(state) {\r\n      if (state == 1) return \"网络数据工控安全\";\r\n      if (state == 2) return \"数字化转型数字经济\";\r\n      if (state == 3) return \"软件\";\r\n      if (state == 4) return \"产业链供应链\";\r\n      if (state == 5) return \"集成电路\";\r\n      if (state == 6) return \"人工智能\";\r\n      return \"未定义\";\r\n    },\r\n    processReviewedState(state) {\r\n      if (state == 0) return \"未审核\";\r\n      if (state == 1) return \"审核通过\";\r\n      if (state == 2) return \"审核不通过\";\r\n      if (state == 3) return \"待修改\";\r\n      if (state == 4) return \"待撤回\";\r\n      return \"未定义\";\r\n    },\r\n    processPulledState(state) {\r\n      if (state == 0) return \"未推送\";\r\n      if (state == 1) return \"已推送\";\r\n      return \"未定义\";\r\n    },\r\n    handlePass(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm(\"是否确认审核通过编号为\" + articleIds + \"的数据？\")\r\n        .then(function () {\r\n          return articlePass(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"审核通过\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleNoPass(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm(\"是否确认审核不通过编号为\" + articleIds + \"的数据？\")\r\n        .then(function () {\r\n          return articleNoPass(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"审核不通过\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleCancelPass(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm(\"是否确认撤回审核通过编号为\" + articleIds + \"的数据？\")\r\n        .then(function () {\r\n          return cancelArticlePass(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"撤回审核通过\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    onCreateTask() {\r\n      this.$modal\r\n        .confirm(\"是否确认要领取任务？\")\r\n        .then(function () {\r\n          return getTask();\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"领取任务成功!\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleRadioChange(e, id) {\r\n      if (e == \"1\") {\r\n        console.log(\"通过\");\r\n        return articlePass(id);\r\n      } else if (e == \"2\") {\r\n        console.log(\"不通过\");\r\n        return articleNoPass(id);\r\n      } else if (e == \"4\") {\r\n        console.log(\"待撤回\");\r\n        return cancelArticlePass(id);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n::v-deep .queryForm {\r\n  .el-form-item {\r\n    width: 100%;\r\n    margin-right: 0;\r\n  }\r\n  .el-form-item__content {\r\n    width: calc(100% - 80px);\r\n  }\r\n}\r\n\r\n::v-deep .radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  .el-radio {\r\n    margin-right: 0;\r\n    margin-bottom: 2px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;AA+kBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AASA,IAAAE,eAAA,GAAAF,OAAA;AASA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,KAAA,GACA,iBACA,eACA,aACA,eACA,eACA,gBACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,EAAA;QACAL,KAAA;QACAM,OAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QACAC,KAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,cAAA;QACAC,OAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,sBAAA;QACAC,iBAAA;QACAC,SAAA;QACAC,YAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACA;QACA9C,KAAA;UAAA+C,QAAA;UAAAC,OAAA;QAAA;QACAC,OAAA;UAAAF,QAAA;UAAAC,OAAA;QAAA;QACA1B,WAAA;UAAAyB,QAAA;UAAAC,OAAA;QAAA;QACA1C,OAAA;UAAAyC,QAAA;UAAAC,OAAA;QAAA;QACAzC,UAAA;UAAAwC,QAAA;UAAAC,OAAA;QAAA;QACArC,WAAA;UAAAoC,QAAA;UAAAC,OAAA;QAAA;QACAhC,OAAA;UAAA+B,QAAA;UAAAC,OAAA;QAAA;QACA;QACA3C,EAAA;UAAA0C,QAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,cACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA5D,OAAA;MACA,IAAA6D,0BAAA,OAAApD,WAAA,EAAAqD,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAtD,QAAA,GAAAyD,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAvD,KAAA,GAAA0D,QAAA,CAAA1D,KAAA;QACAuD,KAAA,CAAA5D,OAAA;QACA4D,KAAA,CAAAK,SAAA;UACAL,KAAA,CAAAM,WAAA;QACA;MACA;IACA;IACA;IACAA,WAAA,WAAAA,YAAA;MACA,SAAAC,KAAA,CAAAC,QAAA;QACA,IAAAC,OAAA,QAAAF,KAAA,CAAAC,QAAA,CAAAE,GAAA,CAAAC,aAAA,CACA,yBACA;QACA,IAAAF,OAAA;UACAA,OAAA,CAAAG,SAAA;QACA;MACA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAjE,IAAA;MACA,KAAAkE,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAtB,IAAA;QACAuB,EAAA;QACA/D,EAAA;QACAL,KAAA;QACAM,OAAA;QACAC,UAAA;QACAE,UAAA;QACAD,UAAA;QACAE,QAAA;QACAC,WAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;QACAC,KAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,cAAA;QACAC,OAAA;QACAC,MAAA;QACAwC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACA3C,MAAA;QACAC,MAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,sBAAA;QACAC,iBAAA;QACAC,SAAA;QACAC,YAAA;QACAC,YAAA;QACAG,MAAA;QACAgC,OAAA;QACA/B,QAAA;QACAC,UAAA;MACA;MACA,KAAA+B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA1E,WAAA,CAAAC,OAAA;MACA,KAAAgD,OAAA;IACA;IACA,aACA0B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAArF,GAAA,GAAAqF,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAb,EAAA;MAAA;MACA,KAAAzE,MAAA,GAAAoF,SAAA,CAAAG,MAAA;MACA,KAAAtF,QAAA,IAAAmF,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAhB,KAAA;MACA,KAAAlE,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAoF,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAnB,KAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACAoB,cAAA,CAAAC,QAAA,CAAAH,GAAA,CAAAjB,EAAA,EAAAb,IAAA,WAAAC,QAAA;QACA8B,MAAA,CAAAzC,IAAA,GAAAW,QAAA,CAAAhE,IAAA;QACA;QACA8F,MAAA,CAAAzC,IAAA,CAAA4C,KAAA,GAAAJ,GAAA,CAAAjB,EAAA,CAAAsB,QAAA;QACAJ,MAAA,CAAArF,IAAA;MACA;IACA;IACA,WACA0F,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhC,KAAA,SAAAiC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAA/C,IAAA,CAAAuB,EAAA;YACA;YACA;YACA;YACA;YACA;YACA,IAAA2B,SAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAN,MAAA,CAAA/C,IAAA;YACA,IAAAsD,qBAAA,EAAAJ,SAAA,EAAAxC,IAAA,WAAAC,QAAA;cACAoC,MAAA,CAAAQ,MAAA,CAAAC,UAAA;cACAT,MAAA,CAAA3F,IAAA;cACA2F,MAAA,CAAAzC,OAAA;YACA;UACA;YACA,IAAAmD,oBAAA,EAAAV,MAAA,CAAA/C,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAoC,MAAA,CAAAQ,MAAA,CAAAC,UAAA;cACAT,MAAA,CAAA3F,IAAA;cACA2F,MAAA,CAAAzC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAoD,YAAA,WAAAA,aAAAlB,GAAA;MAAA,IAAAmB,MAAA;MACA,IAAA9G,GAAA,GAAA2F,GAAA,CAAAjB,EAAA,SAAA1E,GAAA;MACA,KAAA0G,MAAA,CACAK,OAAA,mBAAA/G,GAAA,aACA6D,IAAA;QACA;QACA,WAAAmD,2BAAA,EAAAhH,GAAA;MACA,GACA6D,IAAA;QACAiD,MAAA,CAAArD,OAAA;QACAqD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA3B,IAAA,EAAAI,GAAA;MACAwB,MAAA,CAAA5G,IAAA,uBAAA6G,MAAA,CACA7B,IAAA,CAAAb,EAAA,aAAA0C,MAAA,CAAA7B,IAAA,CAAAQ,KAAA,GACA,QACA;IACA;IACAsB,aAAA,WAAAA,cAAA1B,GAAA;MAAA,IAAA2B,MAAA;MACA,IAAAC,UAAA,GAAA5B,GAAA,CAAAjB,EAAA,SAAA1E,GAAA;MACA,IAAAwH,GAAA,GAAAD,UAAA,CAAAE,IAAA;MACA,KAAAf,MAAA,CACAK,OAAA,gBAAAS,GAAA,kBACA3D,IAAA;QACA,WAAA6D,kCAAA,EAAAF,GAAA;MACA,GACA3D,IAAA;QACAyD,MAAA,CAAA7D,OAAA;QACA6D,MAAA,CAAAZ,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACAU,aAAA,WAAAA,cAAAhC,GAAA;MAAA,IAAAiC,MAAA;MACA,IAAAL,UAAA,GAAA5B,GAAA,CAAAjB,EAAA,SAAA1E,GAAA;MACA,KAAA0G,MAAA,CACAK,OAAA,gBAAAQ,UAAA,kBACA1D,IAAA;QACA,WAAAgE,kCAAA,EAAAN,UAAA;MACA,GACA1D,IAAA;QACA+D,MAAA,CAAAnE,OAAA;QACAmE,MAAA,CAAAlB,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACAa,aAAA,WAAAA,cAAAnC,GAAA;MAAA,IAAAoC,MAAA;MACA,IAAAR,UAAA,GAAA5B,GAAA,CAAAjB,EAAA,SAAA1E,GAAA;MACA,KAAA0G,MAAA,CACAK,OAAA,gBAAAQ,UAAA,gBACA1D,IAAA;QACA,WAAAmE,kCAAA,EAAAT,UAAA;MACA,GACA1D,IAAA;QACAkE,MAAA,CAAAtE,OAAA;QACAsE,MAAA,CAAArB,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACAgB,sBAAA,WAAAA,uBAAAC,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAAD,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IACAE,iBAAA,WAAAA,kBAAAF,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IACAG,oBAAA,WAAAA,qBAAAH,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IACAI,kBAAA,WAAAA,mBAAAJ,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IACAK,UAAA,WAAAA,WAAA5C,GAAA;MAAA,IAAA6C,MAAA;MACA,IAAAjB,UAAA,GAAA5B,GAAA,CAAAjB,EAAA,SAAA1E,GAAA;MACA,KAAA0G,MAAA,CACAK,OAAA,iBAAAQ,UAAA,WACA1D,IAAA;QACA,WAAA4E,2BAAA,EAAAlB,UAAA;MACA,GACA1D,IAAA;QACA2E,MAAA,CAAA/E,OAAA;QACA+E,MAAA,CAAA9B,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACAyB,YAAA,WAAAA,aAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACA,IAAApB,UAAA,GAAA5B,GAAA,CAAAjB,EAAA,SAAA1E,GAAA;MACA,KAAA0G,MAAA,CACAK,OAAA,kBAAAQ,UAAA,WACA1D,IAAA;QACA,WAAA+E,6BAAA,EAAArB,UAAA;MACA,GACA1D,IAAA;QACA8E,MAAA,CAAAlF,OAAA;QACAkF,MAAA,CAAAjC,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACA4B,gBAAA,WAAAA,iBAAAlD,GAAA;MAAA,IAAAmD,OAAA;MACA,IAAAvB,UAAA,GAAA5B,GAAA,CAAAjB,EAAA,SAAA1E,GAAA;MACA,KAAA0G,MAAA,CACAK,OAAA,mBAAAQ,UAAA,WACA1D,IAAA;QACA,WAAAkF,iCAAA,EAAAxB,UAAA;MACA,GACA1D,IAAA;QACAiF,OAAA,CAAArF,OAAA;QACAqF,OAAA,CAAApC,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACA+B,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAvC,MAAA,CACAK,OAAA,eACAlD,IAAA;QACA,WAAAqF,oBAAA;MACA,GACArF,IAAA;QACAoF,OAAA,CAAAxF,OAAA;QACAwF,OAAA,CAAAvC,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACAkC,iBAAA,WAAAA,kBAAAC,CAAA,EAAA1E,EAAA;MACA,IAAA0E,CAAA;QACAC,OAAA,CAAAC,GAAA;QACA,WAAAb,2BAAA,EAAA/D,EAAA;MACA,WAAA0E,CAAA;QACAC,OAAA,CAAAC,GAAA;QACA,WAAAV,6BAAA,EAAAlE,EAAA;MACA,WAAA0E,CAAA;QACAC,OAAA,CAAAC,GAAA;QACA,WAAAP,iCAAA,EAAArE,EAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}