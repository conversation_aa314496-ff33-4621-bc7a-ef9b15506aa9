{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\artificialIntelligence.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\artificialIntelligence.vue", "mtime": 1754010096859}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_topSeach", "_MainArticle", "_articleHistory", "_splitpanes", "components", "topSeach", "MainArticle", "Splitpanes", "Pane", "dicts", "data", "width", "isReSize", "currentPage", "pageSize", "total", "ArticleList", "filterText", "treeData", "treeDataTransfer", "checkList", "treeCurrentPage", "treePageSize", "treeTotal", "searchDebounceTimer", "SeachData", "metaMode", "keyword", "timeRange", "customDay", "collectionDateType", "collectionTime", "isTechnology", "sortMode", "buttonDisabled", "ActiveData", "seniorSerchFlag", "areaList", "countryList", "KeList", "funEsSeach", "tree<PERSON>uery", "filterwords", "domainList", "industryList", "showHistory", "historyList", "historyTimeout", "dialogVisible1", "historyLoading", "queryParams1", "pageNum", "total1", "historyList1", "nodeCheckList", "isTreeSlotProcessing", "initializationCompleted", "tableLoading", "treeReady", "<PERSON><PERSON><PERSON><PERSON>", "queryDebounceTimer", "created", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "EsSeach", "getArticleHistory", "initializeData", "t0", "console", "error", "$message", "stop", "mounted", "_this2", "$nextTick", "checkTreeReadyStatus", "watch", "val", "_this3", "clearTimeout", "setTimeout", "handleTreeSearch", "handler", "newVal", "oldVal", "handleFilterChange", "methods", "_this4", "_callee2", "_callee2$", "_context2", "queryTreeData", "queryArticleList", "clearTreeSelections", "scrollToTopImmediately", "scrollTreeToTop", "queryTreeAndList", "$refs", "tree", "set<PERSON><PERSON><PERSON><PERSON>eys", "_this5", "_callee3", "_callee3$", "_context3", "Promise", "all", "handleCurrentChange", "current", "handleSizeChange", "size", "_this6", "attempts", "maxAttempts", "checkInterval", "setInterval", "children", "length", "clearInterval", "ensureTreeReady", "_this7", "_callee4", "_callee4$", "_context4", "abrupt", "resolve", "checkTree", "_this8", "_callee5", "params", "res", "dataList", "mapData", "_callee5$", "_context5", "platformType", "id", "$route", "query", "m", "dateType", "startTime", "endTime", "collectionStartTime", "collectionEndTime", "keywords", "menuType", "api", "monitoringMedium", "sent", "code", "rows", "map", "item", "index", "sourceSn", "concat", "label", "cnName", "count", "articleCount", "orderNum", "country", "countryOf<PERSON><PERSON>in", "url", "_this9", "_callee7", "_callee7$", "_context7", "_callee6", "_callee6$", "_context6", "isSort", "foundItem", "find", "row", "filter", "sn", "weChatName", "String", "addArticleHistory", "type", "then", "KeIntegration", "_objectSpread2", "list", "Math", "max", "ceil", "msg", "finish", "_this10", "scrollBoxElement", "document", "querySelector", "scrollTop", "mainArticle", "scroll", "rightMain", "treeBox", "handleTreeCheck", "checkedInfo", "allCheckedNodes", "checkedNodes", "selectedNodes", "node", "checkChange", "is<PERSON><PERSON><PERSON>", "_this11", "_callee8", "_callee8$", "_context8", "_toConsumableArray2", "allIds", "existingIndex", "findIndex", "push", "treeNodeClick", "_this12", "_callee9", "_callee9$", "_context9", "remainingIds", "treeClear", "_this13", "_callee10", "_callee10$", "_context10", "SwitchInfo", "isInfo", "<PERSON><PERSON><PERSON><PERSON>", "getArea", "_this14", "_callee11", "Response", "_callee11$", "_context11", "getAreaList", "warn", "_this15", "_callee12", "_callee12$", "_context12", "listArticleHistory", "filterNode", "value", "indexOf", "flag", "resetting", "removeHistory", "_this16", "_callee13", "_callee13$", "_context13", "delArticleHistory", "focus", "getArticleHistory1", "showHistoryList", "hideHistoryList", "_this17", "keywordsChange", "clearHistory", "_this18", "_callee14", "_callee14$", "_context14", "cleanArticleHistory", "moreHistory", "_this19", "_callee15", "response", "_callee15$", "_context15", "openUrl", "window", "open", "handleTreeCurrentChange", "page", "handleTreePageSizeChange", "openNewView", "docId", "sourceType", "handleHistoryPagination"], "sources": ["src/views/domainClassification/artificialIntelligence.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"funEsSeach\">\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"26\"\r\n      >\r\n        <div class=\"treeMain\" style=\"width: 100%; margin: 0\">\r\n          <!-- 搜索框 - 直接从Wechat.vue复制 -->\r\n          <div\r\n            style=\"\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              gap: 10px;\r\n            \"\r\n          >\r\n            <el-input\r\n              placeholder=\"输入关键字进行过滤\"\r\n              v-model=\"filterText\"\r\n              clearable\r\n              style=\"margin-bottom: 10px\"\r\n              class=\"input_Fixed\"\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </div>\r\n          <!-- 控制区域 -->\r\n          <div\r\n            style=\"\r\n              margin-top: -10px;\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              padding: 0 10px;\r\n            \"\r\n          >\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              content=\"重置\"\r\n              placement=\"top\"\r\n            >\r\n              <el-button\r\n                icon=\"el-icon-refresh\"\r\n                @click=\"treeClear\"\r\n                type=\"text\"\r\n                style=\"color: #666\"\r\n                >重置</el-button\r\n              >\r\n            </el-tooltip>\r\n          </div>\r\n          <div class=\"treeBox\">\r\n            <el-tree\r\n              :data=\"treeData\"\r\n              ref=\"tree\"\r\n              show-checkbox\r\n              node-key=\"id\"\r\n              :default-expanded-keys=\"[1000]\"\r\n              @check=\"handleTreeCheck\"\r\n              @node-click=\"treeNodeClick\"\r\n              :expand-on-click-node=\"false\"\r\n              :filter-node-method=\"filterNode\"\r\n            >\r\n              <template slot-scope=\"scoped\">\r\n                <div style=\"display: flex; align-items: center\">\r\n                  <div>{{ scoped.data.label }}</div>\r\n                  <div\r\n                    v-if=\"scoped.data.country && scoped.data.country !== '0'\"\r\n                    style=\"display: flex; align-items: center\"\r\n                  >\r\n                    <div>[</div>\r\n                    <dict-tag\r\n                      :options=\"dict.type.country\"\r\n                      :value=\"scoped.data.country\"\r\n                    />\r\n                    <div>]</div>\r\n                  </div>\r\n                  <div style=\"font-weight: 600\">\r\n                    {{ `${scoped.data.count ? `(${scoped.data.count})` : \"\"}` }}\r\n                  </div>\r\n                  <div v-if=\"scoped.data.label !== '数据源' && scoped.data.url\">\r\n                    <el-tooltip\r\n                      content=\"打开数据源链接\"\r\n                      placement=\"top-start\"\r\n                      effect=\"light\"\r\n                    >\r\n                      <i\r\n                        class=\"el-icon-connection\"\r\n                        style=\"\r\n                          font-size: 20px;\r\n                          color: #409eff;\r\n                          margin-left: 5px;\r\n                          margin-top: 4px;\r\n                        \"\r\n                        @click=\"openUrl(scoped.data.url)\"\r\n                      ></i>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </el-tree>\r\n          </div>\r\n          <!-- 分页组件 -->\r\n          <div class=\"tree-pagination\">\r\n            <el-pagination\r\n              @current-change=\"handleTreeCurrentChange\"\r\n              @size-change=\"handleTreePageSizeChange\"\r\n              :current-page=\"treeCurrentPage\"\r\n              :pager-count=\"5\"\r\n              :page-size=\"treePageSize\"\r\n              :page-sizes=\"[50, 100, 150]\"\r\n              layout=\"total, sizes, prev, pager, next\"\r\n              :total=\"treeTotal\"\r\n              :small=\"true\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"74\">\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n        >\r\n          <div class=\"toolBox\">\r\n            <!-- <div class=\"title\" :style=\"{ height: ActiveData.title ? '' : '50px' }\">\r\n              <p v-if=\"ActiveData.title\">{{ ActiveData.title }}</p>\r\n              <p v-else></p>\r\n            </div> -->\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == '' ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = ''\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >是否与科技有关:</span\r\n                >\r\n                <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                  <el-radio-button\r\n                    v-for=\"dict in dict.type.is_technology\"\r\n                    :label=\"dict.value\"\r\n                    :key=\"'is_technology' + dict.value\"\r\n                    >{{ dict.label }}</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </p>\r\n              <div class=\"keyword\">\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >关键词:</span\r\n                >\r\n                <el-input\r\n                  ref=\"keywordRef\"\r\n                  placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                  style=\"width: 430px\"\r\n                  v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\"\r\n                  @blur=\"hideHistoryList()\"\r\n                >\r\n                </el-input>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"funEsSeach('filter')\"\r\n                  :loading=\"buttonDisabled\"\r\n                  style=\"margin-left: 10px; height: 36px\"\r\n                  >搜索</el-button\r\n                >\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div\r\n                    class=\"historyItem\"\r\n                    v-for=\"(history, index) in historyList\"\r\n                    :key=\"index\"\r\n                    v-loading=\"historyLoading\"\r\n                  >\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                      {{ history.keyword }}\r\n                    </div>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"removeHistory(history, 1)\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >删除</el-button\r\n                    >\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\"\r\n                      >更多</el-button\r\n                    >\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"clearHistory()\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >清空</el-button\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"btn\">\r\n              <el-button size=\"mini\" @click=\"resetting\">重置</el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"funEsSeach('filter')\"\r\n                :loading=\"buttonDisabled\"\r\n                >搜索</el-button\r\n              >\r\n            </div> -->\r\n          </div>\r\n          <MainArticle\r\n            v-loading=\"tableLoading\"\r\n            :flag=\"'artificialIntelligence'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach('filter')\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport topSeach from \"@/views/components/topSeach.vue\";\r\nimport MainArticle from \"../components/MainArticle.vue\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\n\r\nexport default {\r\n  components: { topSeach, MainArticle, Splitpanes, Pane },\r\n  dicts: [\"is_technology\", \"country\"],\r\n  data() {\r\n    return {\r\n      width: \"258\",\r\n      isReSize: false,\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      /* 左侧tree数据 */\r\n      filterText: \"\",\r\n      treeData: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n      /* 搜索防抖 */\r\n      searchDebounceTimer: null,\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: \"\" /* 匹配模式 */,\r\n        keyword: \"\" /* 关键词 */,\r\n        timeRange: \"4\" /* 时间范围 */,\r\n        customDay: [] /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: [] /* 自定义天 */,\r\n        isTechnology: \"1\",\r\n        sortMode: \"0\",\r\n      } /* 搜索条件 */,\r\n      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      ActiveData: {},\r\n      seniorSerchFlag: false /* 普通检索或高级检索 */,\r\n      areaList: [] /* 国内地区 */,\r\n      countryList: [] /* 国家或地区 */,\r\n      KeList: [],\r\n      funEsSeach: null,\r\n      treeQuery: {\r\n        filterwords: \"\", // 添加树搜索关键字\r\n      },\r\n      domainList: [],\r\n      industryList: [],\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      nodeCheckList: [],\r\n      isTreeSlotProcessing: false,\r\n      initializationCompleted: false, // 标记初始化是否完成\r\n      // 从Wechat.vue同步的属性\r\n      tableLoading: false, // 表格loading状态\r\n      treeReady: false, // 树组件是否已准备好\r\n      isQuerying: false, // 查询防抖\r\n      queryDebounceTimer: null, // 查询防抖定时器\r\n    };\r\n  },\r\n  async created() {\r\n    try {\r\n      // 初始化funEsSeach方法\r\n      this.funEsSeach = this.EsSeach;\r\n\r\n      // 先加载基础数据\r\n      await this.getArticleHistory();\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // 在mounted中再次检查树组件状态\r\n    this.$nextTick(() => {\r\n      this.checkTreeReadyStatus();\r\n    });\r\n  },\r\n  watch: {\r\n    // 监听过滤文本变化，调用后端接口搜索 - 直接从Wechat.vue复制\r\n    filterText(val) {\r\n      // 清除之前的防抖定时器\r\n      if (this.searchDebounceTimer) {\r\n        clearTimeout(this.searchDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，500ms后执行搜索\r\n      this.searchDebounceTimer = setTimeout(() => {\r\n        this.handleTreeSearch(val);\r\n      }, 500);\r\n    },\r\n\r\n    // 监听筛选条件变化\r\n    \"SeachData.timeRange\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.collectionDateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 先加载树数据\r\n        await this.queryTreeData();\r\n\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n\r\n        // 再加载文章列表（内部已经处理了 tableLoading）\r\n        await this.queryArticleList();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // 处理筛选条件变化 - 直接从Wechat.vue复制\r\n    handleFilterChange() {\r\n      // 清空左侧树选中状态\r\n      this.clearTreeSelections();\r\n\r\n      // 重置分页到第一页\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n      this.scrollTreeToTop(); // 右侧筛选条件变化时也要滚动左侧树到顶部\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 清空树选中状态\r\n    clearTreeSelections() {\r\n      if (this.$refs.tree) {\r\n        this.$refs.tree.setCheckedKeys([]);\r\n      }\r\n      this.checkList = [];\r\n    },\r\n\r\n    // 同时查询树和列表 - 直接从Wechat.vue复制\r\n    async queryTreeAndList() {\r\n      try {\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 检查树组件准备状态\r\n    checkTreeReadyStatus() {\r\n      let attempts = 0;\r\n      const maxAttempts = 50; // 最多检查2.5秒\r\n\r\n      const checkInterval = setInterval(() => {\r\n        attempts++;\r\n\r\n        if (\r\n          this.$refs.tree &&\r\n          this.treeData &&\r\n          this.treeData[0] &&\r\n          this.treeData[0].children &&\r\n          this.treeData[0].children.length > 0\r\n        ) {\r\n          this.treeReady = true;\r\n          clearInterval(checkInterval);\r\n        } else if (attempts >= maxAttempts) {\r\n          clearInterval(checkInterval);\r\n        }\r\n      }, 50);\r\n    },\r\n\r\n    // 确保树组件准备就绪\r\n    async ensureTreeReady() {\r\n      if (\r\n        this.treeReady &&\r\n        this.$refs.tree &&\r\n        this.treeData[0].children.length > 0\r\n      ) {\r\n        return Promise.resolve();\r\n      }\r\n\r\n      return new Promise((resolve) => {\r\n        let attempts = 0;\r\n        const maxAttempts = 100; // 最多等待5秒\r\n\r\n        const checkTree = () => {\r\n          attempts++;\r\n\r\n          if (\r\n            this.$refs.tree &&\r\n            this.treeData &&\r\n            this.treeData[0] &&\r\n            this.treeData[0].children &&\r\n            this.treeData[0].children.length > 0\r\n          ) {\r\n            this.treeReady = true;\r\n\r\n            resolve();\r\n          } else if (attempts >= maxAttempts) {\r\n            resolve(); // 超时也要resolve，避免卡死\r\n          } else {\r\n            setTimeout(checkTree, 50);\r\n          }\r\n        };\r\n        checkTree();\r\n      });\r\n    },\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      try {\r\n        const params = {\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          platformType: 1,\r\n          id: this.$route.query.id || 1,\r\n          m: 1,\r\n          dateType:\r\n            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n          startTime: this.SeachData.customDay\r\n            ? this.SeachData.customDay[0]\r\n            : \"\",\r\n          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : \"\",\r\n          collectionDateType:\r\n            this.SeachData.collectionDateType != 6\r\n              ? this.SeachData.collectionDateType\r\n              : \"\",\r\n          collectionStartTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[0]\r\n            : \"\",\r\n          collectionEndTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[1]\r\n            : \"\",\r\n          keywords: this.SeachData.keyword,\r\n          isTechnology: this.SeachData.isTechnology,\r\n          // 添加关键字过滤参数\r\n          filterwords: this.treeQuery.filterwords || \"\",\r\n          // 添加 artificialIntelligence 特有的参数\r\n          menuType: this.$route.query.menuType,\r\n        };\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: item.sourceSn || `node_${index}`, // 使用sourceSn作为ID，确保唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeData = [\r\n            {\r\n              id: 1000,\r\n              label: \"数据源\",\r\n              children: mapData(dataList),\r\n            },\r\n          ];\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n\r\n          // 等待DOM更新后标记树组件为准备就绪\r\n          this.$nextTick(() => {\r\n            // 简单延迟，让mounted中的检查来处理准备状态\r\n            setTimeout(() => {\r\n              // 不在这里设置treeReady，让mounted中的检查来处理\r\n            }, 100);\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      }\r\n    },\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList() {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      // 立即显示loading状态\r\n      this.tableLoading = true;\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.$route.query.id || 1,\r\n            isSort: this.SeachData.sortMode,\r\n            dateType:\r\n              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n            startTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[0]\r\n              : \"\",\r\n            endTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[1]\r\n              : \"\",\r\n            collectionDateType:\r\n              this.SeachData.collectionDateType != 6\r\n                ? this.SeachData.collectionDateType\r\n                : \"\",\r\n            collectionStartTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[0]\r\n              : \"\",\r\n            collectionEndTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[1]\r\n              : \"\",\r\n            keywords: this.SeachData.keyword,\r\n            isTechnology: this.SeachData.isTechnology,\r\n            platformType: 1,\r\n            // 添加 artificialIntelligence 特有的参数\r\n            menuType: this.$route.query.menuType,\r\n          };\r\n\r\n          // 如果有选中的数据源，添加数据源参数 - 直接从Wechat.vue复制\r\n          if (this.checkList && this.checkList.length > 0) {\r\n            const data = this.checkList.map((item) => item.label);\r\n            const sourceSn = data\r\n              .map((item) => {\r\n                const foundItem = this.treeDataTransfer.find(\r\n                  (row) => row.label === item\r\n                );\r\n                return foundItem ? foundItem.sourceSn : null;\r\n              })\r\n              .filter((sn) => sn !== null);\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 2 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          const res = await api.KeIntegration({ ...params, ...this.treeQuery });\r\n\r\n          if (res.code == 200) {\r\n            this.ArticleList = res.data.list || [];\r\n            this.total = res.data.total || 0;\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.pageSize * (this.currentPage - 1) >= this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.currentPage = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n          this.buttonDisabled = false;\r\n        }\r\n      }, 300);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      this.$nextTick(() => {\r\n        // 尝试多种滚动方式确保滚动成功\r\n        const scrollBoxElement = document.querySelector(\".scollBox\");\r\n        if (scrollBoxElement) {\r\n          scrollBoxElement.scrollTop = 0;\r\n        }\r\n\r\n        // 如果MainArticle组件有scroll引用，也尝试滚动它\r\n        if (\r\n          this.$refs.mainArticle &&\r\n          this.$refs.mainArticle.$refs &&\r\n          this.$refs.mainArticle.$refs.scroll\r\n        ) {\r\n          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;\r\n        }\r\n\r\n        // 滚动整个右侧区域\r\n        const rightMain = document.querySelector(\".rightMain\");\r\n        if (rightMain) {\r\n          rightMain.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 滚动左侧树到顶部\r\n    scrollTreeToTop() {\r\n      this.$nextTick(() => {\r\n        const treeBox = document.querySelector(\".treeBox\");\r\n        if (treeBox) {\r\n          treeBox.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    // 树节点勾选处理（使用check事件）- 直接从Wechat.vue复制\r\n    handleTreeCheck(data, checkedInfo) {\r\n      // 如果树组件还没准备好，直接返回\r\n      if (!this.treeReady || !this.$refs.tree) {\r\n        return;\r\n      }\r\n\r\n      // check事件的参数：\r\n      // data: 当前点击的节点数据\r\n      // checkedInfo: { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }\r\n\r\n      // 获取所有选中的节点（排除根节点）\r\n      const allCheckedNodes = checkedInfo.checkedNodes || [];\r\n      const selectedNodes = allCheckedNodes.filter(\r\n        (node) => node.id !== 1000 && node.label !== \"数据源\"\r\n      );\r\n\r\n      // 更新选中列表\r\n      this.checkList = selectedNodes.map((node) => ({ ...node }));\r\n\r\n      // 重置页码并查询内容 - 修改为KeMonitor的分页参数\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // checkChange方法 - 直接从Wechat.vue复制\r\n    async checkChange(item, isCheck) {\r\n      // 如果树组件还没准备好，直接返回，不处理勾选\r\n      if (!this.treeReady || !this.$refs.tree) {\r\n        return;\r\n      }\r\n\r\n      // 延迟执行，确保Element UI树组件内部状态已经更新\r\n      setTimeout(() => {\r\n        // 如果勾选的是数据源根节点，处理全选/取消全选\r\n        if (item.id === 1000 || item.label === \"数据源\") {\r\n          if (isCheck) {\r\n            // 全选：选择所有可见的子节点\r\n            this.checkList = [...this.treeData[0].children];\r\n            const allIds = this.treeData[0].children.map((node) => node.id);\r\n\r\n            this.$refs.tree.setCheckedKeys(allIds);\r\n          } else {\r\n            // 取消全选：清空所有选择\r\n            this.checkList = [];\r\n            this.$refs.tree.setCheckedKeys([]);\r\n          }\r\n        } else {\r\n          // 处理单个节点的勾选/取消勾选\r\n\r\n          if (isCheck) {\r\n            // 添加到选中列表（多选）\r\n            const existingIndex = this.checkList.findIndex(\r\n              (row) => row.label === item.label\r\n            );\r\n            if (existingIndex === -1) {\r\n              this.checkList.push({ ...item });\r\n            }\r\n          } else {\r\n            // 从选中列表中移除\r\n            this.checkList = this.checkList.filter(\r\n              (row) => row.label !== item.label\r\n            );\r\n          }\r\n        }\r\n\r\n        // 重置页码并查询内容 - 修改为KeMonitor的分页参数\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      }, 50); // 延迟50ms执行\r\n    },\r\n\r\n    // 树节点点击处理（混合选择模式）- 直接从Wechat.vue复制\r\n    async treeNodeClick(item) {\r\n      // 如果点击的是数据源根节点，不进行操作\r\n      if (item.id === 1000 || item.label === \"数据源\") {\r\n        return;\r\n      }\r\n\r\n      // 如果树组件还没准备好，直接返回，不处理点击\r\n      if (!this.treeReady || !this.$refs.tree) {\r\n        return;\r\n      }\r\n\r\n      // 先滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 延迟执行，确保Element UI树组件内部状态已经更新\r\n      setTimeout(() => {\r\n        // 检查是否已选中该节点\r\n        const existingIndex = this.checkList.findIndex(\r\n          (row) => row.label === item.label\r\n        );\r\n\r\n        if (existingIndex !== -1) {\r\n          // 如果当前项已被选中，则只取消该项的选择（保持其他选择）\r\n          this.checkList = this.checkList.filter(\r\n            (row) => row.label !== item.label\r\n          );\r\n\r\n          // 更新树组件的选中状态\r\n          const remainingIds = this.checkList.map((node) => node.id);\r\n          this.$refs.tree.setCheckedKeys(remainingIds);\r\n        } else {\r\n          // 如果当前项未被选中，清空所有选择，设置为唯一选中项（单选）\r\n          this.checkList = [{ ...item }];\r\n\r\n          // 更新树组件的选中状态\r\n          this.$refs.tree.setCheckedKeys([item.id]);\r\n        }\r\n\r\n        // 重置页码并查询内容 - 修改为KeMonitor的分页参数\r\n        this.currentPage = 1;\r\n        this.queryArticleList();\r\n      }, 50); // 延迟50ms执行\r\n    },\r\n    // 重置树选择 - 直接从Wechat.vue复制\r\n    treeClear() {\r\n      // 清空选中状态\r\n      this.clearTreeSelections();\r\n\r\n      // 重置页码并查询列表数据 - 修改为KeMonitor的分页参数\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.scrollTreeToTop();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 处理树搜索 - 直接从Wechat.vue复制并适配KeMonitor\r\n    async handleTreeSearch(keyword) {\r\n      try {\r\n        // 树搜索不应该影响右侧列表的loading状态\r\n        // this.tableLoading = true; // 移除这行\r\n\r\n        // 更新搜索关键字\r\n        this.treeQuery.filterwords = keyword || \"\";\r\n\r\n        if (keyword) {\r\n          // 使用filterwords参数调用树接口进行搜索\r\n          this.scrollTreeToTop(); // 关键字过滤时滚动到顶部\r\n          await this.queryTreeData();\r\n        } else {\r\n          // 如果搜索关键字为空，重新获取原始数据\r\n          this.treeCurrentPage = 1;\r\n          this.scrollTreeToTop(); // 清空关键字时滚动到顶部\r\n          await this.queryTreeData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"搜索树节点失败:\", error);\r\n        this.$message.error(\"搜索失败，请重试\");\r\n      }\r\n      // 不需要finally块来关闭tableLoading\r\n    },\r\n\r\n    // 保留原有的必要方法\r\n    SwitchInfo(data) {\r\n      this.isInfo = data;\r\n    },\r\n\r\n    seniorSerch() {\r\n      this.seniorSerchFlag = !this.seniorSerchFlag;\r\n    },\r\n\r\n    async getArea() {\r\n      try {\r\n        const Response = await api.getAreaList();\r\n        if (Response && Response.code == 200 && Response.data) {\r\n          this.areaList = Response.data[0] || [];\r\n          this.countryList = Response.data[1] || [];\r\n        } else {\r\n          console.warn(\"获取地区数据失败或数据为空\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取区域数据失败:\", err);\r\n        this.$message.error(\"地区数据获取失败\");\r\n      }\r\n    },\r\n\r\n    async getArticleHistory() {\r\n      try {\r\n        const res = await listArticleHistory({\r\n          pageNum: 1,\r\n          pageSize: 5,\r\n          type: 2,\r\n        });\r\n        if (res && res.code === 200) {\r\n          this.historyList = res.rows || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取历史记录失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 树节点过滤方法\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n\r\n    // 添加EsSeach方法以兼容原有调用\r\n    EsSeach(flag) {\r\n      if (flag === \"filter\") {\r\n        // 筛选变化时同时查询树和列表\r\n        this.queryTreeAndList();\r\n      } else {\r\n        // 其他情况只查询列表\r\n        this.queryArticleList();\r\n      }\r\n    },\r\n\r\n    // 重置搜索条件 - 简化版本\r\n    resetting() {\r\n      try {\r\n        this.SeachData = {\r\n          metaMode: \"\" /* 匹配模式 */,\r\n          keyword: \"\" /* 关键词 */,\r\n          timeRange: \"\" /* 时间范围 */,\r\n          customDay: [] /* 自定义天 */,\r\n          collectionDateType: null /* 时间范围 */,\r\n          collectionTime: [] /* 自定义天 */,\r\n          isTechnology: \"1\",\r\n          sortMode: \"0\",\r\n        };\r\n\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryTreeAndList();\r\n      } catch (error) {\r\n        console.error(\"重置搜索条件时出错:\", error);\r\n        this.$message.error(\"重置搜索条件失败\");\r\n      }\r\n    },\r\n    async removeHistory(item, type) {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (item && item.id) {\r\n          await delArticleHistory([item.id]);\r\n\r\n          if (type == 1) {\r\n            if (this.$refs[\"keywordRef\"]) {\r\n              this.$refs[\"keywordRef\"].focus();\r\n            }\r\n            await this.getArticleHistory();\r\n          } else {\r\n            await this.getArticleHistory();\r\n            await this.getArticleHistory1();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"删除历史记录时出错:\", error);\r\n        this.$message.error(\"删除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      try {\r\n        this.showHistory = true;\r\n      } catch (error) {\r\n        console.error(\"显示历史列表时出错:\", error);\r\n      }\r\n    },\r\n\r\n    hideHistoryList() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        this.historyTimeout = setTimeout(() => {\r\n          this.showHistory = false;\r\n        }, 500);\r\n      } catch (error) {\r\n        console.error(\"隐藏历史列表时出错:\", error);\r\n        this.showHistory = false; // 确保在出错时也能隐藏列表\r\n      }\r\n    },\r\n\r\n    // 关键词历史选择 - 直接从Wechat.vue复制\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.currentPage = 1;\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    async clearHistory() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (this.$refs[\"keywordRef\"]) {\r\n          this.$refs[\"keywordRef\"].focus();\r\n        }\r\n\r\n        await cleanArticleHistory(2);\r\n        await this.getArticleHistory();\r\n      } catch (error) {\r\n        console.error(\"清除历史记录时出错:\", error);\r\n        this.$message.error(\"清除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    moreHistory() {\r\n      try {\r\n        this.historyLoading = true;\r\n        this.getArticleHistory1();\r\n        this.dialogVisible1 = true;\r\n      } catch (error) {\r\n        console.error(\"加载更多历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n      }\r\n    },\r\n\r\n    async getArticleHistory1() {\r\n      try {\r\n        this.historyLoading = true;\r\n        const response = await listArticleHistory({\r\n          ...this.queryParams1,\r\n          type: 2,\r\n        });\r\n\r\n        if (response) {\r\n          this.historyList1 = response.rows || [];\r\n          this.total1 = response.total || 0;\r\n        }\r\n\r\n        this.historyLoading = false;\r\n      } catch (error) {\r\n        console.error(\"获取文章历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n        this.$message.error(\"获取搜索历史失败\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 处理树形分页页码变化\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.clearTreeSelections();\r\n      this.scrollTreeToTop(); // 左侧树翻页时滚动到顶部\r\n      this.queryTreeData();\r\n    },\r\n\r\n    // 处理树形分页每页大小变化\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.clearTreeSelections();\r\n      this.scrollTreeToTop(); // 左侧树页面大小变化时滚动到顶部\r\n      this.queryTreeData();\r\n    },\r\n\r\n    // 添加缺失的openNewView方法\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 添加缺失的handleHistoryPagination方法\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 180px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  // box-shadow: -1px 2px 15px #cecdcd;\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA8XA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,eAAA,GAAAH,OAAA;AAMA,IAAAI,WAAA,GAAAJ,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,UAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,IAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,QAAA;MACA;MACAC,WAAA;MACAC,QAAA;MACAC,KAAA;MACAC,WAAA;MACA;MACAC,UAAA;MACAC,QAAA;MACAC,gBAAA;MACAC,SAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,SAAA;MACA;MACAC,mBAAA;MACA;MACAC,SAAA;QACAC,QAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,YAAA;QACAC,QAAA;MACA;;MACA;MACAC,cAAA;MACAC,UAAA;MACAC,eAAA;MACAC,QAAA;MACAC,WAAA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;QACAC,WAAA;MACA;MACAC,UAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;QACAC,OAAA;QACArC,QAAA;MACA;MACAsC,MAAA;MACAC,YAAA;MACAC,aAAA;MACAC,oBAAA;MACAC,uBAAA;MAAA;MACA;MACAC,YAAA;MAAA;MACAC,SAAA;MAAA;MACAC,UAAA;MAAA;MACAC,kBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAEA;YACAT,KAAA,CAAAtB,UAAA,GAAAsB,KAAA,CAAAW,OAAA;;YAEA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OACAV,KAAA,CAAAY,iBAAA;UAAA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OAGAV,KAAA,CAAAa,cAAA;UAAA;YAEA;YACAb,KAAA,CAAAN,uBAAA;YAAAc,QAAA,CAAAE,IAAA;YAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAM,EAAA,GAAAN,QAAA;YAEAO,OAAA,CAAAC,KAAA,aAAAR,QAAA,CAAAM,EAAA;YACAd,KAAA,CAAAiB,QAAA,CAAAD,KAAA;UAAA;UAAA;YAAA,OAAAR,QAAA,CAAAU,IAAA;QAAA;MAAA,GAAAb,OAAA;IAAA;EAEA;EAEAc,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,SAAA;MACAD,MAAA,CAAAE,oBAAA;IACA;EACA;EACAC,KAAA;IACA;IACApE,UAAA,WAAAA,WAAAqE,GAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA/D,mBAAA;QACAgE,YAAA,MAAAhE,mBAAA;MACA;;MAEA;MACA,KAAAA,mBAAA,GAAAiE,UAAA;QACAF,MAAA,CAAAG,gBAAA,CAAAJ,GAAA;MACA;IACA;IAEA;IACA;MACAK,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAArC,uBAAA,IAAAoC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAArC,uBAAA,IAAAoC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAArC,uBAAA,IAAAoC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;EACA;EACAC,OAAA;IACA;IACApB,cAAA,WAAAA,eAAA;MAAA,IAAAqB,MAAA;MAAA,WAAAjC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+B,SAAA;QAAA,WAAAhC,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5B,IAAA,GAAA4B,SAAA,CAAA3B,IAAA;YAAA;cAAA2B,SAAA,CAAA5B,IAAA;cAAA4B,SAAA,CAAA3B,IAAA;cAAA,OAGAwB,MAAA,CAAAI,aAAA;YAAA;cAAAD,SAAA,CAAA3B,IAAA;cAAA,OAGAwB,MAAA,CAAAb,SAAA;YAAA;cAAAgB,SAAA,CAAA3B,IAAA;cAAA,OAGAwB,MAAA,CAAAK,gBAAA;YAAA;cAAAF,SAAA,CAAA3B,IAAA;cAAA;YAAA;cAAA2B,SAAA,CAAA5B,IAAA;cAAA4B,SAAA,CAAAvB,EAAA,GAAAuB,SAAA;cAEAtB,OAAA,CAAAC,KAAA,aAAAqB,SAAA,CAAAvB,EAAA;cACAoB,MAAA,CAAAjB,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAqB,SAAA,CAAAnB,IAAA;UAAA;QAAA,GAAAiB,QAAA;MAAA;IAEA;IAEA;IACAH,kBAAA,WAAAA,mBAAA;MACA;MACA,KAAAQ,mBAAA;;MAEA;MACA,KAAAzF,WAAA;MACA,KAAAQ,eAAA;;MAEA;MACA,KAAAkF,sBAAA;MACA,KAAAC,eAAA;;MAEA;MACA,KAAAC,gBAAA;IACA;IAEA;IACAH,mBAAA,WAAAA,oBAAA;MACA,SAAAI,KAAA,CAAAC,IAAA;QACA,KAAAD,KAAA,CAAAC,IAAA,CAAAC,cAAA;MACA;MACA,KAAAxF,SAAA;IACA;IAEA;IACAqF,gBAAA,WAAAA,iBAAA;MAAA,IAAAI,MAAA;MAAA,WAAA9C,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4C,SAAA;QAAA,WAAA7C,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA2C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzC,IAAA,GAAAyC,SAAA,CAAAxC,IAAA;YAAA;cAAAwC,SAAA,CAAAzC,IAAA;cAAAyC,SAAA,CAAAxC,IAAA;cAAA,OAEAyC,OAAA,CAAAC,GAAA,EACAL,MAAA,CAAAT,aAAA,IACAS,MAAA,CAAAR,gBAAA;cAAA,CACA;YAAA;cAAAW,SAAA,CAAAxC,IAAA;cAAA;YAAA;cAAAwC,SAAA,CAAAzC,IAAA;cAAAyC,SAAA,CAAApC,EAAA,GAAAoC,SAAA;cAEAnC,OAAA,CAAAC,KAAA,gBAAAkC,SAAA,CAAApC,EAAA;cACAiC,MAAA,CAAA9B,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAkC,SAAA,CAAAhC,IAAA;UAAA;QAAA,GAAA8B,QAAA;MAAA;IAEA;IAEA;IACAK,mBAAA,WAAAA,oBAAAC,OAAA;MACA,KAAAvG,WAAA,GAAAuG,OAAA;MACA,KAAAb,sBAAA;MACA,KAAAF,gBAAA;IACA;IAEAgB,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAxG,QAAA,GAAAwG,IAAA;MACA,KAAAzG,WAAA;MACA,KAAA0F,sBAAA;MACA,KAAAF,gBAAA;IACA;IAEA;IACAjB,oBAAA,WAAAA,qBAAA;MAAA,IAAAmC,MAAA;MACA,IAAAC,QAAA;MACA,IAAAC,WAAA;;MAEA,IAAAC,aAAA,GAAAC,WAAA;QACAH,QAAA;QAEA,IACAD,MAAA,CAAAb,KAAA,CAAAC,IAAA,IACAY,MAAA,CAAArG,QAAA,IACAqG,MAAA,CAAArG,QAAA,OACAqG,MAAA,CAAArG,QAAA,IAAA0G,QAAA,IACAL,MAAA,CAAArG,QAAA,IAAA0G,QAAA,CAAAC,MAAA,MACA;UACAN,MAAA,CAAA7D,SAAA;UACAoE,aAAA,CAAAJ,aAAA;QACA,WAAAF,QAAA,IAAAC,WAAA;UACAK,aAAA,CAAAJ,aAAA;QACA;MACA;IACA;IAEA;IACAK,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjE,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+D,SAAA;QAAA,WAAAhE,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;YAAA;cAAA,MAEAwD,MAAA,CAAAtE,SAAA,IACAsE,MAAA,CAAAtB,KAAA,CAAAC,IAAA,IACAqB,MAAA,CAAA9G,QAAA,IAAA0G,QAAA,CAAAC,MAAA;gBAAAM,SAAA,CAAA3D,IAAA;gBAAA;cAAA;cAAA,OAAA2D,SAAA,CAAAC,MAAA,WAEAnB,OAAA,CAAAoB,OAAA;YAAA;cAAA,OAAAF,SAAA,CAAAC,MAAA,WAGA,IAAAnB,OAAA,WAAAoB,OAAA;gBACA,IAAAb,QAAA;gBACA,IAAAC,WAAA;;gBAEA,IAAAa,UAAA,YAAAA,UAAA;kBACAd,QAAA;kBAEA,IACAQ,MAAA,CAAAtB,KAAA,CAAAC,IAAA,IACAqB,MAAA,CAAA9G,QAAA,IACA8G,MAAA,CAAA9G,QAAA,OACA8G,MAAA,CAAA9G,QAAA,IAAA0G,QAAA,IACAI,MAAA,CAAA9G,QAAA,IAAA0G,QAAA,CAAAC,MAAA,MACA;oBACAG,MAAA,CAAAtE,SAAA;oBAEA2E,OAAA;kBACA,WAAAb,QAAA,IAAAC,WAAA;oBACAY,OAAA;kBACA;oBACA5C,UAAA,CAAA6C,UAAA;kBACA;gBACA;gBACAA,UAAA;cACA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAAiD,QAAA;MAAA;IACA;IACA;IACA7B,aAAA,WAAAA,cAAA;MAAA,IAAAmC,MAAA;MAAA,WAAAxE,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAsE,SAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA,EAAAC,QAAA,EAAA5H,KAAA,EAAA6H,OAAA;QAAA,WAAA3E,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAyE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAtE,IAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAEAkE,MAAA;gBACAtF,OAAA,EAAAoF,MAAA,CAAAlH,eAAA;gBACAP,QAAA,EAAAyH,MAAA,CAAAjH,YAAA;gBACAyH,YAAA;gBACAC,EAAA,EAAAT,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAF,EAAA;gBACAG,CAAA;gBACAC,QAAA,EACAb,MAAA,CAAA9G,SAAA,CAAAG,SAAA,QAAA2G,MAAA,CAAA9G,SAAA,CAAAG,SAAA;gBACAyH,SAAA,EAAAd,MAAA,CAAA9G,SAAA,CAAAI,SAAA,GACA0G,MAAA,CAAA9G,SAAA,CAAAI,SAAA,MACA;gBACAyH,OAAA,EAAAf,MAAA,CAAA9G,SAAA,CAAAI,SAAA,GAAA0G,MAAA,CAAA9G,SAAA,CAAAI,SAAA;gBACAC,kBAAA,EACAyG,MAAA,CAAA9G,SAAA,CAAAK,kBAAA,QACAyG,MAAA,CAAA9G,SAAA,CAAAK,kBAAA,GACA;gBACAyH,mBAAA,EAAAhB,MAAA,CAAA9G,SAAA,CAAAM,cAAA,GACAwG,MAAA,CAAA9G,SAAA,CAAAM,cAAA,MACA;gBACAyH,iBAAA,EAAAjB,MAAA,CAAA9G,SAAA,CAAAM,cAAA,GACAwG,MAAA,CAAA9G,SAAA,CAAAM,cAAA,MACA;gBACA0H,QAAA,EAAAlB,MAAA,CAAA9G,SAAA,CAAAE,OAAA;gBACAK,YAAA,EAAAuG,MAAA,CAAA9G,SAAA,CAAAO,YAAA;gBACA;gBACAU,WAAA,EAAA6F,MAAA,CAAA9F,SAAA,CAAAC,WAAA;gBACA;gBACAgH,QAAA,EAAAnB,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAQ;cACA;cAAAZ,SAAA,CAAAtE,IAAA;cAAA,OAEAmF,cAAA,CAAAC,gBAAA,CAAAnB,MAAA;YAAA;cAAAC,GAAA,GAAAI,SAAA,CAAAe,IAAA;cAEA,IAAAnB,GAAA,CAAAoB,IAAA;gBACAnB,QAAA,GAAAD,GAAA,CAAAqB,IAAA;gBACAhJ,KAAA,GAAA2H,GAAA,CAAA3H,KAAA;gBAEA6H,OAAA,YAAAA,QAAAlI,IAAA;kBAAA,OACAA,IAAA,CAAAsJ,GAAA,WAAAC,IAAA,EAAAC,KAAA;oBAAA;sBACAlB,EAAA,EAAAiB,IAAA,CAAAE,QAAA,YAAAC,MAAA,CAAAF,KAAA;sBAAA;sBACAG,KAAA,EAAAJ,IAAA,CAAAK,MAAA;sBACAC,KAAA,EAAAN,IAAA,CAAAO,YAAA;sBACAC,QAAA,EAAAR,IAAA,CAAAQ,QAAA;sBACAC,OAAA,EAAAT,IAAA,CAAAU,eAAA;sBACAR,QAAA,EAAAF,IAAA,CAAAE,QAAA;sBACAS,GAAA,EAAAX,IAAA,CAAAW,GAAA;oBACA;kBAAA;gBAAA;gBAEArC,MAAA,CAAArH,QAAA,IACA;kBACA8H,EAAA;kBACAqB,KAAA;kBACAzC,QAAA,EAAAgB,OAAA,CAAAD,QAAA;gBACA,EACA;gBACAJ,MAAA,CAAApH,gBAAA,GAAAyH,OAAA,CAAAD,QAAA;gBACAJ,MAAA,CAAAhH,SAAA,GAAAR,KAAA;;gBAEA;gBACAwH,MAAA,CAAApD,SAAA;kBACA;kBACAM,UAAA;oBACA;kBAAA,CACA;gBACA;cACA;cAAAqD,SAAA,CAAAtE,IAAA;cAAA;YAAA;cAAAsE,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAAlE,EAAA,GAAAkE,SAAA;cAEAjE,OAAA,CAAAC,KAAA,aAAAgE,SAAA,CAAAlE,EAAA;cACA2D,MAAA,CAAAxD,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAgE,SAAA,CAAA9D,IAAA;UAAA;QAAA,GAAAwD,QAAA;MAAA;IAEA;IACA;IACAnC,gBAAA,WAAAA,iBAAA;MAAA,IAAAwE,MAAA;MAAA,WAAA9G,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4G,SAAA;QAAA,WAAA7G,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA2G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzG,IAAA,GAAAyG,SAAA,CAAAxG,IAAA;YAAA;cAAA,KAEAqG,MAAA,CAAAlH,UAAA;gBAAAqH,SAAA,CAAAxG,IAAA;gBAAA;cAAA;cAAA,OAAAwG,SAAA,CAAA5C,MAAA;YAAA;cAIA;cACAyC,MAAA,CAAApH,YAAA;;cAEA;cACA,IAAAoH,MAAA,CAAAjH,kBAAA;gBACA4B,YAAA,CAAAqF,MAAA,CAAAjH,kBAAA;cACA;;cAEA;cACAiH,MAAA,CAAAjH,kBAAA,GAAA6B,UAAA,kBAAA1B,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+G,SAAA;gBAAA,IAAAxC,MAAA,EAAA/H,IAAA,EAAAyJ,QAAA,EAAAzB,GAAA;gBAAA,WAAAzE,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8G,UAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAA5G,IAAA,GAAA4G,SAAA,CAAA3G,IAAA;oBAAA;sBAAA2G,SAAA,CAAA5G,IAAA;sBAEAsG,MAAA,CAAAlH,UAAA;sBAEA8E,MAAA;wBACAU,CAAA;wBACAhG,OAAA,EAAA0H,MAAA,CAAAhK,WAAA;wBACAC,QAAA,EAAA+J,MAAA,CAAA/J,QAAA;wBACAkI,EAAA,EAAA6B,MAAA,CAAA5B,MAAA,CAAAC,KAAA,CAAAF,EAAA;wBACAoC,MAAA,EAAAP,MAAA,CAAApJ,SAAA,CAAAQ,QAAA;wBACAmH,QAAA,EACAyB,MAAA,CAAApJ,SAAA,CAAAG,SAAA,QAAAiJ,MAAA,CAAApJ,SAAA,CAAAG,SAAA;wBACAyH,SAAA,EAAAwB,MAAA,CAAApJ,SAAA,CAAAI,SAAA,GACAgJ,MAAA,CAAApJ,SAAA,CAAAI,SAAA,MACA;wBACAyH,OAAA,EAAAuB,MAAA,CAAApJ,SAAA,CAAAI,SAAA,GACAgJ,MAAA,CAAApJ,SAAA,CAAAI,SAAA,MACA;wBACAC,kBAAA,EACA+I,MAAA,CAAApJ,SAAA,CAAAK,kBAAA,QACA+I,MAAA,CAAApJ,SAAA,CAAAK,kBAAA,GACA;wBACAyH,mBAAA,EAAAsB,MAAA,CAAApJ,SAAA,CAAAM,cAAA,GACA8I,MAAA,CAAApJ,SAAA,CAAAM,cAAA,MACA;wBACAyH,iBAAA,EAAAqB,MAAA,CAAApJ,SAAA,CAAAM,cAAA,GACA8I,MAAA,CAAApJ,SAAA,CAAAM,cAAA,MACA;wBACA0H,QAAA,EAAAoB,MAAA,CAAApJ,SAAA,CAAAE,OAAA;wBACAK,YAAA,EAAA6I,MAAA,CAAApJ,SAAA,CAAAO,YAAA;wBACA+G,YAAA;wBACA;wBACAW,QAAA,EAAAmB,MAAA,CAAA5B,MAAA,CAAAC,KAAA,CAAAQ;sBACA,GAEA;sBACA,IAAAmB,MAAA,CAAAzJ,SAAA,IAAAyJ,MAAA,CAAAzJ,SAAA,CAAAyG,MAAA;wBACAnH,IAAA,GAAAmK,MAAA,CAAAzJ,SAAA,CAAA4I,GAAA,WAAAC,IAAA;0BAAA,OAAAA,IAAA,CAAAI,KAAA;wBAAA;wBACAF,QAAA,GAAAzJ,IAAA,CACAsJ,GAAA,WAAAC,IAAA;0BACA,IAAAoB,SAAA,GAAAR,MAAA,CAAA1J,gBAAA,CAAAmK,IAAA,CACA,UAAAC,GAAA;4BAAA,OAAAA,GAAA,CAAAlB,KAAA,KAAAJ,IAAA;0BAAA,CACA;0BACA,OAAAoB,SAAA,GAAAA,SAAA,CAAAlB,QAAA;wBACA,GACAqB,MAAA,WAAAC,EAAA;0BAAA,OAAAA,EAAA;wBAAA;wBAEAhD,MAAA,CAAAiD,UAAA,GAAAC,MAAA,CAAAjL,IAAA;wBACA+H,MAAA,CAAA0B,QAAA,GAAAwB,MAAA,CAAAxB,QAAA;sBACA;;sBAEA;sBACA,IAAA1B,MAAA,CAAAgB,QAAA;wBACA,IAAAmC,iCAAA;0BAAAjK,OAAA,EAAA8G,MAAA,CAAAgB,QAAA;0BAAAoC,IAAA;wBAAA,GAAAC,IAAA,CACA;0BACAjB,MAAA,CAAAnG,iBAAA;wBACA,CACA;sBACA;sBAAAyG,SAAA,CAAA3G,IAAA;sBAAA,OAEAmF,cAAA,CAAAoC,aAAA,KAAAC,cAAA,CAAAhI,OAAA,MAAAgI,cAAA,CAAAhI,OAAA,MAAAyE,MAAA,GAAAoC,MAAA,CAAApI,SAAA;oBAAA;sBAAAiG,GAAA,GAAAyC,SAAA,CAAAtB,IAAA;sBAAA,MAEAnB,GAAA,CAAAoB,IAAA;wBAAAqB,SAAA,CAAA3G,IAAA;wBAAA;sBAAA;sBACAqG,MAAA,CAAA7J,WAAA,GAAA0H,GAAA,CAAAhI,IAAA,CAAAuL,IAAA;sBACApB,MAAA,CAAA9J,KAAA,GAAA2H,GAAA,CAAAhI,IAAA,CAAAK,KAAA;;sBAEA;sBAAA,MAEA8J,MAAA,CAAA7J,WAAA,CAAA6G,MAAA,SACAgD,MAAA,CAAA/J,QAAA,IAAA+J,MAAA,CAAAhK,WAAA,SAAAgK,MAAA,CAAA9J,KAAA,IACA8J,MAAA,CAAA9J,KAAA;wBAAAoK,SAAA,CAAA3G,IAAA;wBAAA;sBAAA;sBAEAqG,MAAA,CAAAhK,WAAA,GAAAqL,IAAA,CAAAC,GAAA,CACA,GACAD,IAAA,CAAAE,IAAA,CAAAvB,MAAA,CAAA9J,KAAA,GAAA8J,MAAA,CAAA/J,QAAA,CACA;sBACA;sBAAAqK,SAAA,CAAA3G,IAAA;sBAAA,OACAqG,MAAA,CAAAxE,gBAAA;oBAAA;sBAAA,OAAA8E,SAAA,CAAA/C,MAAA;oBAAA;sBAAA+C,SAAA,CAAA3G,IAAA;sBAAA;oBAAA;sBAIAqG,MAAA,CAAA9F,QAAA,CAAAD,KAAA,CAAA4D,GAAA,CAAA2D,GAAA;oBAAA;sBAAAlB,SAAA,CAAA3G,IAAA;sBAAA;oBAAA;sBAAA2G,SAAA,CAAA5G,IAAA;sBAAA4G,SAAA,CAAAvG,EAAA,GAAAuG,SAAA;sBAGAtG,OAAA,CAAAC,KAAA,cAAAqG,SAAA,CAAAvG,EAAA;sBACAiG,MAAA,CAAA9F,QAAA,CAAAD,KAAA;oBAAA;sBAAAqG,SAAA,CAAA5G,IAAA;sBAEAsG,MAAA,CAAAlH,UAAA;sBACAkH,MAAA,CAAApH,YAAA;sBACAoH,MAAA,CAAA3I,cAAA;sBAAA,OAAAiJ,SAAA,CAAAmB,MAAA;oBAAA;oBAAA;sBAAA,OAAAnB,SAAA,CAAAnG,IAAA;kBAAA;gBAAA,GAAAiG,QAAA;cAAA,CAEA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAhG,IAAA;UAAA;QAAA,GAAA8F,QAAA;MAAA;IACA;IAEA;IACAvE,sBAAA,WAAAA,uBAAA;MAAA,IAAAgG,OAAA;MACA,KAAApH,SAAA;QACA;QACA,IAAAqH,gBAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,gBAAA;UACAA,gBAAA,CAAAG,SAAA;QACA;;QAEA;QACA,IACAJ,OAAA,CAAA7F,KAAA,CAAAkG,WAAA,IACAL,OAAA,CAAA7F,KAAA,CAAAkG,WAAA,CAAAlG,KAAA,IACA6F,OAAA,CAAA7F,KAAA,CAAAkG,WAAA,CAAAlG,KAAA,CAAAmG,MAAA,EACA;UACAN,OAAA,CAAA7F,KAAA,CAAAkG,WAAA,CAAAlG,KAAA,CAAAmG,MAAA,CAAAF,SAAA;QACA;;QAEA;QACA,IAAAG,SAAA,GAAAL,QAAA,CAAAC,aAAA;QACA,IAAAI,SAAA;UACAA,SAAA,CAAAH,SAAA;QACA;MACA;IACA;IAEA;IACAnG,eAAA,WAAAA,gBAAA;MACA,KAAArB,SAAA;QACA,IAAA4H,OAAA,GAAAN,QAAA,CAAAC,aAAA;QACA,IAAAK,OAAA;UACAA,OAAA,CAAAJ,SAAA;QACA;MACA;IACA;IACA;IACAK,eAAA,WAAAA,gBAAAtM,IAAA,EAAAuM,WAAA;MACA;MACA,UAAAvJ,SAAA,UAAAgD,KAAA,CAAAC,IAAA;QACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA,IAAAuG,eAAA,GAAAD,WAAA,CAAAE,YAAA;MACA,IAAAC,aAAA,GAAAF,eAAA,CAAA1B,MAAA,CACA,UAAA6B,IAAA;QAAA,OAAAA,IAAA,CAAArE,EAAA,aAAAqE,IAAA,CAAAhD,KAAA;MAAA,CACA;;MAEA;MACA,KAAAjJ,SAAA,GAAAgM,aAAA,CAAApD,GAAA,WAAAqD,IAAA;QAAA,WAAArB,cAAA,CAAAhI,OAAA,MAAAqJ,IAAA;MAAA;;MAEA;MACA,KAAAxM,WAAA;MACA,KAAA0F,sBAAA;MACA,KAAAF,gBAAA;IACA;IAEA;IACAiH,WAAA,WAAAA,YAAArD,IAAA,EAAAsD,OAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzJ,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuJ,SAAA;QAAA,WAAAxJ,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApJ,IAAA,GAAAoJ,SAAA,CAAAnJ,IAAA;YAAA;cAAA,MAEA,CAAAgJ,OAAA,CAAA9J,SAAA,KAAA8J,OAAA,CAAA9G,KAAA,CAAAC,IAAA;gBAAAgH,SAAA,CAAAnJ,IAAA;gBAAA;cAAA;cAAA,OAAAmJ,SAAA,CAAAvF,MAAA;YAAA;cAIA;cACA3C,UAAA;gBACA;gBACA,IAAAwE,IAAA,CAAAjB,EAAA,aAAAiB,IAAA,CAAAI,KAAA;kBACA,IAAAkD,OAAA;oBACA;oBACAC,OAAA,CAAApM,SAAA,OAAAwM,mBAAA,CAAA5J,OAAA,EAAAwJ,OAAA,CAAAtM,QAAA,IAAA0G,QAAA;oBACA,IAAAiG,MAAA,GAAAL,OAAA,CAAAtM,QAAA,IAAA0G,QAAA,CAAAoC,GAAA,WAAAqD,IAAA;sBAAA,OAAAA,IAAA,CAAArE,EAAA;oBAAA;oBAEAwE,OAAA,CAAA9G,KAAA,CAAAC,IAAA,CAAAC,cAAA,CAAAiH,MAAA;kBACA;oBACA;oBACAL,OAAA,CAAApM,SAAA;oBACAoM,OAAA,CAAA9G,KAAA,CAAAC,IAAA,CAAAC,cAAA;kBACA;gBACA;kBACA;;kBAEA,IAAA2G,OAAA;oBACA;oBACA,IAAAO,aAAA,GAAAN,OAAA,CAAApM,SAAA,CAAA2M,SAAA,CACA,UAAAxC,GAAA;sBAAA,OAAAA,GAAA,CAAAlB,KAAA,KAAAJ,IAAA,CAAAI,KAAA;oBAAA,CACA;oBACA,IAAAyD,aAAA;sBACAN,OAAA,CAAApM,SAAA,CAAA4M,IAAA,KAAAhC,cAAA,CAAAhI,OAAA,MAAAiG,IAAA;oBACA;kBACA;oBACA;oBACAuD,OAAA,CAAApM,SAAA,GAAAoM,OAAA,CAAApM,SAAA,CAAAoK,MAAA,CACA,UAAAD,GAAA;sBAAA,OAAAA,GAAA,CAAAlB,KAAA,KAAAJ,IAAA,CAAAI,KAAA;oBAAA,CACA;kBACA;gBACA;;gBAEA;gBACAmD,OAAA,CAAA3M,WAAA;gBACA2M,OAAA,CAAAjH,sBAAA;gBACAiH,OAAA,CAAAnH,gBAAA;cACA;YAAA;YAAA;cAAA,OAAAsH,SAAA,CAAA3I,IAAA;UAAA;QAAA,GAAAyI,QAAA;MAAA;IACA;IAEA;IACAQ,aAAA,WAAAA,cAAAhE,IAAA;MAAA,IAAAiE,OAAA;MAAA,WAAAnK,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiK,SAAA;QAAA,WAAAlK,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAgK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9J,IAAA,GAAA8J,SAAA,CAAA7J,IAAA;YAAA;cAAA,MAEAyF,IAAA,CAAAjB,EAAA,aAAAiB,IAAA,CAAAI,KAAA;gBAAAgE,SAAA,CAAA7J,IAAA;gBAAA;cAAA;cAAA,OAAA6J,SAAA,CAAAjG,MAAA;YAAA;cAAA,MAKA,CAAA8F,OAAA,CAAAxK,SAAA,KAAAwK,OAAA,CAAAxH,KAAA,CAAAC,IAAA;gBAAA0H,SAAA,CAAA7J,IAAA;gBAAA;cAAA;cAAA,OAAA6J,SAAA,CAAAjG,MAAA;YAAA;cAIA;cACA8F,OAAA,CAAA3H,sBAAA;;cAEA;cACAd,UAAA;gBACA;gBACA,IAAAqI,aAAA,GAAAI,OAAA,CAAA9M,SAAA,CAAA2M,SAAA,CACA,UAAAxC,GAAA;kBAAA,OAAAA,GAAA,CAAAlB,KAAA,KAAAJ,IAAA,CAAAI,KAAA;gBAAA,CACA;gBAEA,IAAAyD,aAAA;kBACA;kBACAI,OAAA,CAAA9M,SAAA,GAAA8M,OAAA,CAAA9M,SAAA,CAAAoK,MAAA,CACA,UAAAD,GAAA;oBAAA,OAAAA,GAAA,CAAAlB,KAAA,KAAAJ,IAAA,CAAAI,KAAA;kBAAA,CACA;;kBAEA;kBACA,IAAAiE,YAAA,GAAAJ,OAAA,CAAA9M,SAAA,CAAA4I,GAAA,WAAAqD,IAAA;oBAAA,OAAAA,IAAA,CAAArE,EAAA;kBAAA;kBACAkF,OAAA,CAAAxH,KAAA,CAAAC,IAAA,CAAAC,cAAA,CAAA0H,YAAA;gBACA;kBACA;kBACAJ,OAAA,CAAA9M,SAAA,QAAA4K,cAAA,CAAAhI,OAAA,MAAAiG,IAAA;;kBAEA;kBACAiE,OAAA,CAAAxH,KAAA,CAAAC,IAAA,CAAAC,cAAA,EAAAqD,IAAA,CAAAjB,EAAA;gBACA;;gBAEA;gBACAkF,OAAA,CAAArN,WAAA;gBACAqN,OAAA,CAAA7H,gBAAA;cACA;YAAA;YAAA;cAAA,OAAAgI,SAAA,CAAArJ,IAAA;UAAA;QAAA,GAAAmJ,QAAA;MAAA;IACA;IACA;IACAI,SAAA,WAAAA,UAAA;MACA;MACA,KAAAjI,mBAAA;;MAEA;MACA,KAAAzF,WAAA;MACA,KAAA0F,sBAAA;MACA,KAAAC,eAAA;MACA,KAAAH,gBAAA;IACA;IAEA;IACAX,gBAAA,WAAAA,iBAAA/D,OAAA;MAAA,IAAA6M,OAAA;MAAA,WAAAzK,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuK,UAAA;QAAA,WAAAxK,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsK,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApK,IAAA,GAAAoK,UAAA,CAAAnK,IAAA;YAAA;cAAAmK,UAAA,CAAApK,IAAA;cAEA;cACA;;cAEA;cACAiK,OAAA,CAAA/L,SAAA,CAAAC,WAAA,GAAAf,OAAA;cAAA,KAEAA,OAAA;gBAAAgN,UAAA,CAAAnK,IAAA;gBAAA;cAAA;cACA;cACAgK,OAAA,CAAAhI,eAAA;cAAAmI,UAAA,CAAAnK,IAAA;cAAA,OACAgK,OAAA,CAAApI,aAAA;YAAA;cAAAuI,UAAA,CAAAnK,IAAA;cAAA;YAAA;cAEA;cACAgK,OAAA,CAAAnN,eAAA;cACAmN,OAAA,CAAAhI,eAAA;cAAAmI,UAAA,CAAAnK,IAAA;cAAA,OACAgK,OAAA,CAAApI,aAAA;YAAA;cAAAuI,UAAA,CAAAnK,IAAA;cAAA;YAAA;cAAAmK,UAAA,CAAApK,IAAA;cAAAoK,UAAA,CAAA/J,EAAA,GAAA+J,UAAA;cAGA9J,OAAA,CAAAC,KAAA,aAAA6J,UAAA,CAAA/J,EAAA;cACA4J,OAAA,CAAAzJ,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAA6J,UAAA,CAAA3J,IAAA;UAAA;QAAA,GAAAyJ,SAAA;MAAA;IAGA;IAEA;IACAG,UAAA,WAAAA,WAAAlO,IAAA;MACA,KAAAmO,MAAA,GAAAnO,IAAA;IACA;IAEAoO,WAAA,WAAAA,YAAA;MACA,KAAA1M,eAAA,SAAAA,eAAA;IACA;IAEA2M,OAAA,WAAAA,QAAA;MAAA,IAAAC,OAAA;MAAA,WAAAjL,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+K,UAAA;QAAA,IAAAC,QAAA;QAAA,WAAAjL,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA+K,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7K,IAAA,GAAA6K,UAAA,CAAA5K,IAAA;YAAA;cAAA4K,UAAA,CAAA7K,IAAA;cAAA6K,UAAA,CAAA5K,IAAA;cAAA,OAEAmF,cAAA,CAAA0F,WAAA;YAAA;cAAAH,QAAA,GAAAE,UAAA,CAAAvF,IAAA;cACA,IAAAqF,QAAA,IAAAA,QAAA,CAAApF,IAAA,WAAAoF,QAAA,CAAAxO,IAAA;gBACAsO,OAAA,CAAA3M,QAAA,GAAA6M,QAAA,CAAAxO,IAAA;gBACAsO,OAAA,CAAA1M,WAAA,GAAA4M,QAAA,CAAAxO,IAAA;cACA;gBACAmE,OAAA,CAAAyK,IAAA;cACA;cAAAF,UAAA,CAAA5K,IAAA;cAAA;YAAA;cAAA4K,UAAA,CAAA7K,IAAA;cAAA6K,UAAA,CAAAxK,EAAA,GAAAwK,UAAA;cAEAvK,OAAA,CAAAC,KAAA,cAAAsK,UAAA,CAAAxK,EAAA;cACAoK,OAAA,CAAAjK,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAsK,UAAA,CAAApK,IAAA;UAAA;QAAA,GAAAiK,SAAA;MAAA;IAEA;IAEAvK,iBAAA,WAAAA,kBAAA;MAAA,IAAA6K,OAAA;MAAA,WAAAxL,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAsL,UAAA;QAAA,IAAA9G,GAAA;QAAA,WAAAzE,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAqL,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnL,IAAA,GAAAmL,UAAA,CAAAlL,IAAA;YAAA;cAAAkL,UAAA,CAAAnL,IAAA;cAAAmL,UAAA,CAAAlL,IAAA;cAAA,OAEA,IAAAmL,kCAAA;gBACAxM,OAAA;gBACArC,QAAA;gBACA+K,IAAA;cACA;YAAA;cAJAnD,GAAA,GAAAgH,UAAA,CAAA7F,IAAA;cAKA,IAAAnB,GAAA,IAAAA,GAAA,CAAAoB,IAAA;gBACAyF,OAAA,CAAAzM,WAAA,GAAA4F,GAAA,CAAAqB,IAAA;cACA;cAAA2F,UAAA,CAAAlL,IAAA;cAAA;YAAA;cAAAkL,UAAA,CAAAnL,IAAA;cAAAmL,UAAA,CAAA9K,EAAA,GAAA8K,UAAA;cAEA7K,OAAA,CAAAC,KAAA,cAAA4K,UAAA,CAAA9K,EAAA;YAAA;YAAA;cAAA,OAAA8K,UAAA,CAAA1K,IAAA;UAAA;QAAA,GAAAwK,SAAA;MAAA;IAEA;IAEA;IACAI,UAAA,WAAAA,WAAAC,KAAA,EAAAnP,IAAA;MACA,KAAAmP,KAAA;MACA,OAAAnP,IAAA,CAAA2J,KAAA,CAAAyF,OAAA,CAAAD,KAAA;IACA;IAEA;IACApL,OAAA,WAAAA,QAAAsL,IAAA;MACA,IAAAA,IAAA;QACA;QACA,KAAAtJ,gBAAA;MACA;QACA;QACA,KAAAJ,gBAAA;MACA;IACA;IAEA;IACA2J,SAAA,WAAAA,UAAA;MACA;QACA,KAAAvO,SAAA;UACAC,QAAA;UACAC,OAAA;UACAC,SAAA;UACAC,SAAA;UACAC,kBAAA;UACAC,cAAA;UACAC,YAAA;UACAC,QAAA;QACA;QAEA,KAAApB,WAAA;QACA,KAAA0F,sBAAA;QACA,KAAAE,gBAAA;MACA,SAAA3B,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;MACA;IACA;IACAmL,aAAA,WAAAA,cAAAhG,IAAA,EAAA4B,IAAA;MAAA,IAAAqE,OAAA;MAAA,WAAAnM,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiM,UAAA;QAAA,WAAAlM,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAgM,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9L,IAAA,GAAA8L,UAAA,CAAA7L,IAAA;YAAA;cAAA6L,UAAA,CAAA9L,IAAA;cAEA,IAAA2L,OAAA,CAAAnN,cAAA;gBACAyC,YAAA,CAAA0K,OAAA,CAAAnN,cAAA;cACA;cAAA,MAEAkH,IAAA,IAAAA,IAAA,CAAAjB,EAAA;gBAAAqH,UAAA,CAAA7L,IAAA;gBAAA;cAAA;cAAA6L,UAAA,CAAA7L,IAAA;cAAA,OACA,IAAA8L,iCAAA,GAAArG,IAAA,CAAAjB,EAAA;YAAA;cAAA,MAEA6C,IAAA;gBAAAwE,UAAA,CAAA7L,IAAA;gBAAA;cAAA;cACA,IAAA0L,OAAA,CAAAxJ,KAAA;gBACAwJ,OAAA,CAAAxJ,KAAA,eAAA6J,KAAA;cACA;cAAAF,UAAA,CAAA7L,IAAA;cAAA,OACA0L,OAAA,CAAAxL,iBAAA;YAAA;cAAA2L,UAAA,CAAA7L,IAAA;cAAA;YAAA;cAAA6L,UAAA,CAAA7L,IAAA;cAAA,OAEA0L,OAAA,CAAAxL,iBAAA;YAAA;cAAA2L,UAAA,CAAA7L,IAAA;cAAA,OACA0L,OAAA,CAAAM,kBAAA;YAAA;cAAAH,UAAA,CAAA7L,IAAA;cAAA;YAAA;cAAA6L,UAAA,CAAA9L,IAAA;cAAA8L,UAAA,CAAAzL,EAAA,GAAAyL,UAAA;cAIAxL,OAAA,CAAAC,KAAA,eAAAuL,UAAA,CAAAzL,EAAA;cACAsL,OAAA,CAAAnL,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAuL,UAAA,CAAArL,IAAA;UAAA;QAAA,GAAAmL,SAAA;MAAA;IAEA;IAEAM,eAAA,WAAAA,gBAAA;MACA;QACA,KAAA5N,WAAA;MACA,SAAAiC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;MACA;IACA;IAEA4L,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA;QACA,SAAA5N,cAAA;UACAyC,YAAA,MAAAzC,cAAA;QACA;QAEA,KAAAA,cAAA,GAAA0C,UAAA;UACAkL,OAAA,CAAA9N,WAAA;QACA;MACA,SAAAiC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA,KAAAjC,WAAA;MACA;IACA;IAEA;IACA+N,cAAA,WAAAA,eAAA3G,IAAA;MACA,KAAAxI,SAAA,CAAAE,OAAA,GAAAsI,IAAA,CAAAtI,OAAA;MACA,KAAAqB,cAAA;MACA,KAAAuD,sBAAA;MACA,KAAA1F,WAAA;MACA,KAAA4F,gBAAA;IACA;IAEAoK,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/M,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA6M,UAAA;QAAA,WAAA9M,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA4M,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1M,IAAA,GAAA0M,UAAA,CAAAzM,IAAA;YAAA;cAAAyM,UAAA,CAAA1M,IAAA;cAEA,IAAAuM,OAAA,CAAA/N,cAAA;gBACAyC,YAAA,CAAAsL,OAAA,CAAA/N,cAAA;cACA;cAEA,IAAA+N,OAAA,CAAApK,KAAA;gBACAoK,OAAA,CAAApK,KAAA,eAAA6J,KAAA;cACA;cAAAU,UAAA,CAAAzM,IAAA;cAAA,OAEA,IAAA0M,mCAAA;YAAA;cAAAD,UAAA,CAAAzM,IAAA;cAAA,OACAsM,OAAA,CAAApM,iBAAA;YAAA;cAAAuM,UAAA,CAAAzM,IAAA;cAAA;YAAA;cAAAyM,UAAA,CAAA1M,IAAA;cAAA0M,UAAA,CAAArM,EAAA,GAAAqM,UAAA;cAEApM,OAAA,CAAAC,KAAA,eAAAmM,UAAA,CAAArM,EAAA;cACAkM,OAAA,CAAA/L,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAmM,UAAA,CAAAjM,IAAA;UAAA;QAAA,GAAA+L,SAAA;MAAA;IAEA;IAEAI,WAAA,WAAAA,YAAA;MACA;QACA,KAAAlO,cAAA;QACA,KAAAuN,kBAAA;QACA,KAAAxN,cAAA;MACA,SAAA8B,KAAA;QACAD,OAAA,CAAAC,KAAA,iBAAAA,KAAA;QACA,KAAA7B,cAAA;MACA;IACA;IAEAuN,kBAAA,WAAAA,mBAAA;MAAA,IAAAY,OAAA;MAAA,WAAArN,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAmN,UAAA;QAAA,IAAAC,QAAA;QAAA,WAAArN,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAmN,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAjN,IAAA,GAAAiN,UAAA,CAAAhN,IAAA;YAAA;cAAAgN,UAAA,CAAAjN,IAAA;cAEA6M,OAAA,CAAAnO,cAAA;cAAAuO,UAAA,CAAAhN,IAAA;cAAA,OACA,IAAAmL,kCAAA,MAAA3D,cAAA,CAAAhI,OAAA,MAAAgI,cAAA,CAAAhI,OAAA,MACAoN,OAAA,CAAAlO,YAAA;gBACA2I,IAAA;cAAA,EACA;YAAA;cAHAyF,QAAA,GAAAE,UAAA,CAAA3H,IAAA;cAKA,IAAAyH,QAAA;gBACAF,OAAA,CAAA/N,YAAA,GAAAiO,QAAA,CAAAvH,IAAA;gBACAqH,OAAA,CAAAhO,MAAA,GAAAkO,QAAA,CAAAvQ,KAAA;cACA;cAEAqQ,OAAA,CAAAnO,cAAA;cAAAuO,UAAA,CAAAhN,IAAA;cAAA;YAAA;cAAAgN,UAAA,CAAAjN,IAAA;cAAAiN,UAAA,CAAA5M,EAAA,GAAA4M,UAAA;cAEA3M,OAAA,CAAAC,KAAA,iBAAA0M,UAAA,CAAA5M,EAAA;cACAwM,OAAA,CAAAnO,cAAA;cACAmO,OAAA,CAAArM,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAA0M,UAAA,CAAAxM,IAAA;UAAA;QAAA,GAAAqM,SAAA;MAAA;IAEA;IAEAI,OAAA,WAAAA,QAAA7G,GAAA;MACA8G,MAAA,CAAAC,IAAA,CAAA/G,GAAA;IACA;IAEA;IACAgH,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAAxQ,eAAA,GAAAwQ,IAAA;MACA,KAAAvL,mBAAA;MACA,KAAAE,eAAA;MACA,KAAAJ,aAAA;IACA;IAEA;IACA0L,wBAAA,WAAAA,yBAAAxK,IAAA;MACA,KAAAhG,YAAA,GAAAgG,IAAA;MACA,KAAAjG,eAAA;MACA,KAAAiF,mBAAA;MACA,KAAAE,eAAA;MACA,KAAAJ,aAAA;IACA;IAEA;IACA2L,WAAA,WAAAA,YAAA9H,IAAA;MACAyH,MAAA,CAAAC,IAAA,uBAAAvH,MAAA,CACAH,IAAA,CAAAjB,EAAA,aAAAoB,MAAA,CAAAH,IAAA,CAAA+H,KAAA,kBAAA5H,MAAA,CAAAH,IAAA,CAAAgI,UAAA,GACA,QACA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAA;MACA,KAAA1B,kBAAA;IACA;EACA;AACA", "ignoreList": []}]}