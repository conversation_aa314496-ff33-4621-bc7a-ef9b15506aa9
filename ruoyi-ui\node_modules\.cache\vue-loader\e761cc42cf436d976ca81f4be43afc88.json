{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\tech\\index.vue?vue&type=template&id=e32f3d28&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\tech\\index.vue", "mtime": 1754010073836}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}