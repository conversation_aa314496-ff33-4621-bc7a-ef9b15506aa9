{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue", "mtime": 1754027680200}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_topSeach", "_MainArticle", "_articleHistory", "_splitpanes", "_index2", "components", "topSeach", "MainArticle", "Splitpanes", "Pane", "TreeTable", "dicts", "data", "width", "isReSize", "currentPage", "pageSize", "total", "ArticleList", "treeDataTransfer", "checkList", "treeCurrentPage", "treePageSize", "treeTotal", "SeachData", "metaMode", "keyword", "timeRange", "customDay", "collectionDateType", "collectionTime", "isTechnology", "sortMode", "emotion", "<PERSON><PERSON><PERSON>", "buttonDisabled", "ActiveData", "seniorSerchFlag", "areaList", "countryList", "KeList", "funEsSeach", "tree<PERSON>uery", "filterwords", "domainList", "industryList", "showHistory", "historyList", "historyTimeout", "dialogVisible1", "historyLoading", "queryParams1", "pageNum", "total1", "historyList1", "initializationCompleted", "loading", "tableLoading", "<PERSON><PERSON><PERSON><PERSON>", "queryDebounceTimer", "isRightFilter", "isLeftReset", "selectedClassify", "selectedCountry", "savedCheckboxData", "globalLoading", "created", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "EsSeach", "getArticleHistory", "initializeData", "t0", "console", "error", "$message", "stop", "mounted", "watch", "handler", "newVal", "oldVal", "handleFilterChange", "methods", "_this2", "_callee2", "firstPageData", "_callee2$", "_context2", "queryTreeData", "$nextTick", "length", "_toConsumableArray2", "$refs", "treeTable", "restoreSelectionSilently", "setTimeout", "queryArticleList", "handleSelectionChange", "selectedData", "operationType", "currentPageIds", "map", "item", "sourceSn", "filteredCheckList", "filter", "includes", "filteredSavedData", "combinedCheckList", "concat", "combinedSavedData", "deduplicateBySourceSn", "scrollToTopImmediately", "dataArray", "seen", "Set", "has", "add", "handleReset", "queryTreeAndList", "_this3", "_callee3", "savedData", "_callee3$", "_context3", "Promise", "all", "restoreFromSavedCheckboxData", "_this4", "matchedItems", "for<PERSON>ach", "savedItem", "foundItem", "find", "treeItem", "push", "queryTreeDataWithRestoreFromSaved", "_this5", "_callee4", "_callee4$", "_context4", "handleCurrentChange", "current", "handleSizeChange", "size", "_this6", "_callee5", "params", "res", "dataList", "mapData", "_callee5$", "_context5", "platformType", "id", "$route", "query", "menuType", "m", "dateType", "startTime", "endTime", "collectionStartTime", "collectionEndTime", "keywords", "thinkTankClassification", "countryOf<PERSON><PERSON>in", "is<PERSON>ummary", "isSwdt01", "isSwdt02", "isContentTranslated", "isTranslated", "api", "monitoringMedium", "sent", "code", "rows", "index", "Date", "now", "Math", "random", "toString", "substring", "label", "cnName", "count", "articleCount", "orderNum", "country", "url", "finish", "flag", "_this7", "_callee7", "_callee7$", "_context7", "abrupt", "clearTimeout", "_callee6", "qykjdtParams", "articleList", "_callee6$", "_context6", "isSort", "weChatName", "String", "addArticleHistory", "type", "then", "domain", "qykjdtArticleList", "_objectSpread2", "KeIntegration", "list", "trim", "deduplicateArticles", "max", "ceil", "msg", "t1", "_this8", "scrollBoxElement", "document", "querySelector", "scrollTop", "mainArticle", "scroll", "rightMain", "getArea", "_this9", "_callee8", "Response", "_callee8$", "_context8", "getAreaList", "warn", "_this10", "_callee9", "_callee9$", "_context9", "listArticleHistory", "filterNode", "value", "indexOf", "resetting", "removeHistory", "_this11", "_callee10", "_callee10$", "_context10", "delArticleHistory", "focus", "getArticleHistory1", "showHistoryList", "hideHistoryList", "_this12", "keywordsChange", "clearHistory", "_this13", "_callee11", "_callee11$", "_context11", "cleanArticleHistory", "moreHistory", "_this14", "_callee12", "response", "_callee12$", "_context12", "openUrl", "window", "open", "handleFilterSearch", "handleClassifyChange", "classifyValue", "handleCountryChange", "countryValue", "handleTreeCurrentChange", "page", "handleTreePageSizeChange", "openNewView", "docId", "sourceType", "handleHistoryPagination", "articles", "titleMap", "Map", "result", "article", "cleanTitle", "title", "replace", "get", "set", "originalTitle", "_ref2"], "sources": ["src/views/KeMonitor/keMonitor.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    v-loading=\"globalLoading\"\r\n    element-loading-text=\"数据加载中\"\r\n    v-if=\"funEsSeach\"\r\n  >\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"28\"\r\n        v-if=\"!$route.query.domain\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n          @country-change=\"handleCountryChange\"\r\n        />\r\n      </pane>\r\n      <pane\r\n        :min-size=\"$route.query.domain ? '100' : '50'\"\r\n        :max-size=\"$route.query.domain ? '100' : '80'\"\r\n        :size=\"$route.query.domain ? '100' : '72'\"\r\n      >\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <div class=\"toolBox\" v-if=\"!$route.query.domain\">\r\n            <!-- <div class=\"title\" :style=\"{ height: ActiveData.title ? '' : '50px' }\">\r\n              <p v-if=\"ActiveData.title\">{{ ActiveData.title }}</p>\r\n              <p v-else></p>\r\n            </div> -->\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 10 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 10\"\r\n                  >全部</el-button\r\n                >\r\n                <!-- <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 7 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 7\"\r\n                  >近三个月</el-button\r\n                >\r\n                <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 8 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 8\"\r\n                  >近半年</el-button\r\n                > -->\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <div style=\"display: flex\">\r\n                <p style=\"margin-right: 30px; margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信优选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                    <el-radio-button\r\n                      v-for=\"dict in dict.type.is_technology\"\r\n                      :label=\"dict.value\"\r\n                      :key=\"'is_technology' + dict.value\"\r\n                      >{{ dict.label }}</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"null\" :key=\"'is_technologyAll'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n                <p style=\"margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信精选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.emotion\" size=\"small\">\r\n                    <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                      >选中</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n              </div>\r\n              <div class=\"keyword\">\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >关键词:</span\r\n                >\r\n                <el-input\r\n                  ref=\"keywordRef\"\r\n                  placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                  style=\"width: 430px\"\r\n                  v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\"\r\n                  @blur=\"hideHistoryList()\"\r\n                >\r\n                </el-input>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"funEsSeach()\"\r\n                  :loading=\"buttonDisabled\"\r\n                  style=\"margin-left: 10px; height: 36px\"\r\n                  >搜索</el-button\r\n                >\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div\r\n                    class=\"historyItem\"\r\n                    v-for=\"(history, index) in historyList\"\r\n                    :key=\"index\"\r\n                    v-loading=\"historyLoading\"\r\n                  >\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                      {{ history.keyword }}\r\n                    </div>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"removeHistory(history, 1)\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >删除</el-button\r\n                    >\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\"\r\n                      >更多</el-button\r\n                    >\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"clearHistory()\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >清空</el-button\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"keyword-tip\">\r\n                *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"btn\">\r\n              <el-button size=\"mini\" @click=\"resetting\">重置</el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"funEsSeach('filter')\"\r\n                :loading=\"buttonDisabled\"\r\n                >搜索</el-button\r\n              >\r\n            </div> -->\r\n          </div>\r\n          <MainArticle\r\n            v-if=\"$route.query.menuType\"\r\n            :flag=\"'artificialIntelligence'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n          <MainArticle\r\n            v-else\r\n            :flag=\"'MonitorUse'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport topSeach from \"@/views/components/topSeach.vue\";\r\nimport MainArticle from \"../components/MainArticle.vue\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\n\r\nexport default {\r\n  components: { topSeach, MainArticle, Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      width: \"258\",\r\n      isReSize: false,\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: \"\" /* 匹配模式 */,\r\n        keyword: \"\" /* 关键词 */,\r\n        timeRange: 4 /* 时间范围 */,\r\n        customDay: [] /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: [] /* 自定义天 */,\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      } /* 搜索条件 */,\r\n      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      ActiveData: {},\r\n      seniorSerchFlag: false /* 普通检索或高级检索 */,\r\n      areaList: [] /* 国内地区 */,\r\n      countryList: [] /* 国家或地区 */,\r\n      KeList: [],\r\n      funEsSeach: null,\r\n      treeQuery: {\r\n        filterwords: \"\", // 添加树搜索关键字\r\n      },\r\n      domainList: [],\r\n      industryList: [],\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      initializationCompleted: false, // 标记初始化是否完成\r\n      // 从Wechat.vue同步的属性\r\n      loading: false, // 树组件loading状态\r\n      tableLoading: false, // 表格loading状态\r\n      isQuerying: false, // 查询防抖\r\n      queryDebounceTimer: null, // 查询防抖定时器\r\n      isRightFilter: false, // 标记右侧筛选条件是否发生变化\r\n      isLeftReset: false, // 标记左侧树是否重置\r\n      selectedClassify: \"5\", // 选中的数据源分类\r\n      selectedCountry: null, // 选中的国家\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  async created() {\r\n    try {\r\n      // 初始化funEsSeach方法\r\n      this.funEsSeach = this.EsSeach;\r\n\r\n      // 先加载基础数据\r\n      this.getArticleHistory();\r\n\r\n      // if (this.$route.query.menuType && this.$route.query.menuType === \"8\") {\r\n      //   this.SeachData.timeRange = 7;\r\n      // }\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // TreeTable 组件不需要特殊的状态检查\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"SeachData.timeRange\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.collectionDateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        this.globalLoading = true;\r\n        // 先加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n\r\n        // 默认全选第一页数据源\r\n        if (this.treeDataTransfer && this.treeDataTransfer.length > 0) {\r\n          // 全选第一页的所有数据源\r\n          const firstPageData = [...this.treeDataTransfer];\r\n          this.checkList = firstPageData;\r\n          this.savedCheckboxData = firstPageData;\r\n\r\n          // 通知 TreeTable 组件设置选中状态\r\n          this.$nextTick(() => {\r\n            if (this.$refs.treeTable) {\r\n              this.$refs.treeTable.restoreSelectionSilently(firstPageData);\r\n            }\r\n          });\r\n\r\n          // 延迟一下再查询文章列表，确保选中状态已设置\r\n          setTimeout(() => {\r\n            this.queryArticleList();\r\n          }, 100);\r\n        } else {\r\n          // 如果没有数据源，直接查询文章列表\r\n          this.queryArticleList();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n        this.globalLoading = false;\r\n      }\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤关键字，避免触发 handleFilterSearch\r\n      this.treeQuery.filterwords = \"\";\r\n      this.selectedClassify = null;\r\n      this.selectedCountry = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 处理筛选条件变化 - 来自右侧筛选条件的变化\r\n    handleFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表 - 直接从Wechat.vue复制\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          platformType: 1,\r\n          id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n          m: 1,\r\n          dateType:\r\n            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n          startTime: this.SeachData.customDay\r\n            ? this.SeachData.customDay[0]\r\n            : \"\",\r\n          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : \"\",\r\n          collectionDateType:\r\n            this.SeachData.collectionDateType != 6\r\n              ? this.SeachData.collectionDateType\r\n              : \"\",\r\n          collectionStartTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[0]\r\n            : \"\",\r\n          collectionEndTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[1]\r\n            : \"\",\r\n          keywords: this.SeachData.keyword,\r\n          isTechnology: this.SeachData.isTechnology,\r\n          emotion: this.SeachData.emotion,\r\n          // 添加关键字过滤参数\r\n          filterwords: this.treeQuery.filterwords || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          // 添加国家筛选参数\r\n          countryOfOrigin: this.selectedCountry,\r\n          hasCache: this.SeachData.hasCache,\r\n          // 小信精选附加参数\r\n          isSummary: this.SeachData.emotion,\r\n          isSwdt01: this.SeachData.emotion,\r\n          isSwdt02: this.SeachData.emotion,\r\n          isContentTranslated: this.SeachData.emotion,\r\n          isTranslated: this.SeachData.emotion,\r\n        };\r\n\r\n        if (this.$route.query.menuType) {\r\n          params.menuType = this.$route.query.menuType;\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n            isSort: this.SeachData.sortMode,\r\n            dateType:\r\n              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n            startTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[0]\r\n              : \"\",\r\n            endTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[1]\r\n              : \"\",\r\n            collectionDateType:\r\n              this.SeachData.collectionDateType != 6\r\n                ? this.SeachData.collectionDateType\r\n                : \"\",\r\n            collectionStartTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[0]\r\n              : \"\",\r\n            collectionEndTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[1]\r\n              : \"\",\r\n            keywords: this.SeachData.keyword,\r\n            isTechnology: this.SeachData.isTechnology,\r\n            emotion: this.SeachData.emotion,\r\n            platformType: 1,\r\n            // 小信精选附加参数\r\n            isSummary: this.SeachData.emotion,\r\n            isSwdt01: this.SeachData.emotion,\r\n            isSwdt02: this.SeachData.emotion,\r\n            isContentTranslated: this.SeachData.emotion,\r\n            isTranslated: this.SeachData.emotion,\r\n          };\r\n\r\n          if (this.$route.query.menuType) {\r\n            params.menuType = this.$route.query.menuType;\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 2 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          let qykjdtParams;\r\n\r\n          if (this.$route.query.domain) {\r\n            qykjdtParams = {\r\n              pageNum: this.currentPage,\r\n              pageSize: this.pageSize,\r\n              sourceSn: this.$route.query.domain,\r\n              isSort: this.SeachData.sortMode,\r\n            };\r\n          }\r\n\r\n          const res = this.$route.query.domain\r\n            ? await api.qykjdtArticleList({ ...qykjdtParams })\r\n            : await api.KeIntegration({ ...params });\r\n\r\n          if (res.code == 200) {\r\n            let articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              articleList = res.rows || [];\r\n            } else {\r\n              articleList = res.data.list || [];\r\n            }\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.SeachData.keyword ||\r\n              this.SeachData.keyword.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              this.total = res.total || 0;\r\n            } else {\r\n              this.total = res.data.total || 0;\r\n            }\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.pageSize * (this.currentPage - 1) >= this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.currentPage = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n          this.buttonDisabled = false;\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      this.$nextTick(() => {\r\n        // 尝试多种滚动方式确保滚动成功\r\n        const scrollBoxElement = document.querySelector(\".scollBox\");\r\n        if (scrollBoxElement) {\r\n          scrollBoxElement.scrollTop = 0;\r\n        }\r\n\r\n        // 如果MainArticle组件有scroll引用，也尝试滚动它\r\n        if (\r\n          this.$refs.mainArticle &&\r\n          this.$refs.mainArticle.$refs &&\r\n          this.$refs.mainArticle.$refs.scroll\r\n        ) {\r\n          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;\r\n        }\r\n\r\n        // 滚动整个右侧区域\r\n        const rightMain = document.querySelector(\".rightMain\");\r\n        if (rightMain) {\r\n          rightMain.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    async getArea() {\r\n      try {\r\n        const Response = await api.getAreaList();\r\n        if (Response && Response.code == 200 && Response.data) {\r\n          this.areaList = Response.data[0] || [];\r\n          this.countryList = Response.data[1] || [];\r\n        } else {\r\n          console.warn(\"获取地区数据失败或数据为空\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取区域数据失败:\", err);\r\n        this.$message.error(\"地区数据获取失败\");\r\n      }\r\n    },\r\n\r\n    async getArticleHistory() {\r\n      try {\r\n        const res = await listArticleHistory({\r\n          pageNum: 1,\r\n          pageSize: 5,\r\n          type: 2,\r\n        });\r\n        if (res && res.code === 200) {\r\n          this.historyList = res.rows || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取历史记录失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 树节点过滤方法\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n\r\n    // 添加EsSeach方法以兼容原有调用\r\n    EsSeach(flag) {\r\n      if (flag === \"filter\") {\r\n        this.scrollToTopImmediately();\r\n        // 筛选变化时同时查询树和列表\r\n        this.queryTreeAndList();\r\n      } else {\r\n        // 其他情况只查询列表\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      }\r\n    },\r\n\r\n    // 重置搜索条件 - 简化版本\r\n    resetting() {\r\n      try {\r\n        this.SeachData = {\r\n          metaMode: \"\" /* 匹配模式 */,\r\n          keyword: \"\" /* 关键词 */,\r\n          timeRange: \"\" /* 时间范围 */,\r\n          customDay: [] /* 自定义天 */,\r\n          collectionDateType: null /* 时间范围 */,\r\n          collectionTime: [] /* 自定义天 */,\r\n          isTechnology: \"1\",\r\n          sortMode: \"4\",\r\n          emotion: \"0\",\r\n        };\r\n\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryTreeAndList();\r\n      } catch (error) {\r\n        console.error(\"重置搜索条件时出错:\", error);\r\n        this.$message.error(\"重置搜索条件失败\");\r\n      }\r\n    },\r\n\r\n    async removeHistory(item, type) {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (item && item.id) {\r\n          await delArticleHistory([item.id]);\r\n\r\n          if (type == 1) {\r\n            if (this.$refs[\"keywordRef\"]) {\r\n              this.$refs[\"keywordRef\"].focus();\r\n            }\r\n            await this.getArticleHistory();\r\n          } else {\r\n            await this.getArticleHistory();\r\n            await this.getArticleHistory1();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"删除历史记录时出错:\", error);\r\n        this.$message.error(\"删除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      try {\r\n        this.showHistory = true;\r\n      } catch (error) {\r\n        console.error(\"显示历史列表时出错:\", error);\r\n      }\r\n    },\r\n\r\n    hideHistoryList() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        this.historyTimeout = setTimeout(() => {\r\n          this.showHistory = false;\r\n        }, 500);\r\n      } catch (error) {\r\n        console.error(\"隐藏历史列表时出错:\", error);\r\n        this.showHistory = false; // 确保在出错时也能隐藏列表\r\n      }\r\n    },\r\n\r\n    // 关键词历史选择 - 直接从Wechat.vue复制\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.currentPage = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    async clearHistory() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (this.$refs[\"keywordRef\"]) {\r\n          this.$refs[\"keywordRef\"].focus();\r\n        }\r\n\r\n        await cleanArticleHistory(2);\r\n        await this.getArticleHistory();\r\n      } catch (error) {\r\n        console.error(\"清除历史记录时出错:\", error);\r\n        this.$message.error(\"清除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    moreHistory() {\r\n      try {\r\n        this.historyLoading = true;\r\n        this.getArticleHistory1();\r\n        this.dialogVisible1 = true;\r\n      } catch (error) {\r\n        console.error(\"加载更多历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n      }\r\n    },\r\n\r\n    async getArticleHistory1() {\r\n      try {\r\n        this.historyLoading = true;\r\n        const response = await listArticleHistory({\r\n          ...this.queryParams1,\r\n          type: 2,\r\n        });\r\n\r\n        if (response) {\r\n          this.historyList1 = response.rows || [];\r\n          this.total1 = response.total || 0;\r\n        }\r\n\r\n        this.historyLoading = false;\r\n      } catch (error) {\r\n        console.error(\"获取文章历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n        this.$message.error(\"获取搜索历史失败\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新查询参数中的 filterwords\r\n      this.treeQuery.filterwords = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理国家筛选变化（来自 TreeTable 组件）\r\n    handleCountryChange(countryValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的国家\r\n      this.selectedCountry = countryValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页页码变化\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页每页大小变化\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 添加缺失的openNewView方法\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 添加缺失的handleHistoryPagination方法\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 176px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  // box-shadow: -1px 2px 15px #cecdcd;\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 65px;\r\n  line-height: 1;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8VA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,eAAA,GAAAH,OAAA;AAMA,IAAAI,WAAA,GAAAJ,OAAA;AACAA,OAAA;AACA,IAAAK,OAAA,GAAAN,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,UAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,IAAA,EAAAA,gBAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,QAAA;MACA;MACAC,WAAA;MACAC,QAAA;MACAC,KAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,SAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,SAAA;MAEA;MACAC,SAAA;QACAC,QAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,YAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MACA;MACAC,cAAA;MACAC,UAAA;MACAC,eAAA;MACAC,QAAA;MACAC,WAAA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;QACAC,WAAA;MACA;MACAC,UAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;QACAC,OAAA;QACApC,QAAA;MACA;MACAqC,MAAA;MACAC,YAAA;MACAC,uBAAA;MAAA;MACA;MACAC,OAAA;MAAA;MACAC,YAAA;MAAA;MACAC,UAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,WAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;MAAA;MACA;MACAC,iBAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAEA;YACAT,KAAA,CAAA1B,UAAA,GAAA0B,KAAA,CAAAW,OAAA;;YAEA;YACAX,KAAA,CAAAY,iBAAA;;YAEA;YACA;YACA;;YAEA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAV,KAAA,CAAAa,cAAA;UAAA;YAEA;YACAb,KAAA,CAAAZ,uBAAA;YAAAoB,QAAA,CAAAE,IAAA;YAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAM,EAAA,GAAAN,QAAA;YAEAO,OAAA,CAAAC,KAAA,aAAAR,QAAA,CAAAM,EAAA;YACAd,KAAA,CAAAiB,QAAA,CAAAD,KAAA;UAAA;UAAA;YAAA,OAAAR,QAAA,CAAAU,IAAA;QAAA;MAAA,GAAAb,OAAA;IAAA;EAEA;EAEAc,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,KAAA;IACA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;EACA;EACAC,OAAA;IACA;IACAZ,cAAA,WAAAA,eAAA;MAAA,IAAAa,MAAA;MAAA,WAAAzB,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuB,SAAA;QAAA,IAAAC,aAAA;QAAA,WAAAzB,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAuB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArB,IAAA,GAAAqB,SAAA,CAAApB,IAAA;YAAA;cAAAoB,SAAA,CAAArB,IAAA;cAEAiB,MAAA,CAAA5B,aAAA;cACA;cAAAgC,SAAA,CAAApB,IAAA;cAAA,OACAgB,MAAA,CAAAK,aAAA;YAAA;cAAAD,SAAA,CAAApB,IAAA;cAAA,OAEAgB,MAAA,CAAAM,SAAA;YAAA;cAEA;cACA,IAAAN,MAAA,CAAA1E,gBAAA,IAAA0E,MAAA,CAAA1E,gBAAA,CAAAiF,MAAA;gBACA;gBACAL,aAAA,OAAAM,mBAAA,CAAAhC,OAAA,EAAAwB,MAAA,CAAA1E,gBAAA;gBACA0E,MAAA,CAAAzE,SAAA,GAAA2E,aAAA;gBACAF,MAAA,CAAA7B,iBAAA,GAAA+B,aAAA;;gBAEA;gBACAF,MAAA,CAAAM,SAAA;kBACA,IAAAN,MAAA,CAAAS,KAAA,CAAAC,SAAA;oBACAV,MAAA,CAAAS,KAAA,CAAAC,SAAA,CAAAC,wBAAA,CAAAT,aAAA;kBACA;gBACA;;gBAEA;gBACAU,UAAA;kBACAZ,MAAA,CAAAa,gBAAA;gBACA;cACA;gBACA;gBACAb,MAAA,CAAAa,gBAAA;cACA;cAAAT,SAAA,CAAApB,IAAA;cAAA;YAAA;cAAAoB,SAAA,CAAArB,IAAA;cAAAqB,SAAA,CAAAhB,EAAA,GAAAgB,SAAA;cAEAf,OAAA,CAAAC,KAAA,aAAAc,SAAA,CAAAhB,EAAA;cACAY,MAAA,CAAAT,QAAA,CAAAD,KAAA;cACAU,MAAA,CAAA5B,aAAA;YAAA;YAAA;cAAA,OAAAgC,SAAA,CAAAZ,IAAA;UAAA;QAAA,GAAAS,QAAA;MAAA;IAEA;IAEA;IAEA;IACAa,qBAAA,WAAAA,sBAAAC,YAAA,EAAAC,aAAA;MACA,IAAAA,aAAA,oBAAAA,aAAA;QACA;QACA,KAAAzF,SAAA,OAAAiF,mBAAA,CAAAhC,OAAA,EAAAuC,YAAA;QACA,KAAA5C,iBAAA,OAAAqC,mBAAA,CAAAhC,OAAA,EAAAuC,YAAA;MACA,WACAC,aAAA,0BACAA,aAAA,mBACA;QACA;QACA;QACA,IAAAC,cAAA,QAAA3F,gBAAA,CAAA4F,GAAA,CACA,UAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,QAAA;QAAA,CACA;QACA,IAAAC,iBAAA,QAAA9F,SAAA,CAAA+F,MAAA,CACA,UAAAH,IAAA;UAAA,QAAAF,cAAA,CAAAM,QAAA,CAAAJ,IAAA,CAAAC,QAAA;QAAA,CACA;QACA,IAAAI,iBAAA,QAAArD,iBAAA,CAAAmD,MAAA,CACA,UAAAH,IAAA;UAAA,QAAAF,cAAA,CAAAM,QAAA,CAAAJ,IAAA,CAAAC,QAAA;QAAA,CACA;;QAEA;QACA,IAAAK,iBAAA,MAAAC,MAAA,KAAAlB,mBAAA,CAAAhC,OAAA,EAAA6C,iBAAA,OAAAb,mBAAA,CAAAhC,OAAA,EAAAuC,YAAA;QACA,IAAAY,iBAAA,MAAAD,MAAA,KAAAlB,mBAAA,CAAAhC,OAAA,EAAAgD,iBAAA,OAAAhB,mBAAA,CAAAhC,OAAA,EAAAuC,YAAA;;QAEA;QACA,KAAAxF,SAAA,QAAAqG,qBAAA,CAAAH,iBAAA;QACA,KAAAtD,iBAAA,QAAAyD,qBAAA,CAAAD,iBAAA;MACA;QACA;QACA,KAAApG,SAAA,OAAAiF,mBAAA,CAAAhC,OAAA,EAAAuC,YAAA;QACA,KAAA5C,iBAAA,OAAAqC,mBAAA,CAAAhC,OAAA,EAAAuC,YAAA;MACA;;MAEA;MACA,KAAA7F,WAAA;MACA,KAAA2G,sBAAA;MACA,UAAA9D,aAAA;QACA,KAAA8C,gBAAA;MACA;IACA;IAEA;IACAe,qBAAA,WAAAA,sBAAAE,SAAA;MACA,IAAAC,IAAA,OAAAC,GAAA;MACA,OAAAF,SAAA,CAAAR,MAAA,WAAAH,IAAA;QACA,IAAAY,IAAA,CAAAE,GAAA,CAAAd,IAAA,CAAAC,QAAA;UACA;QACA;QACAW,IAAA,CAAAG,GAAA,CAAAf,IAAA,CAAAC,QAAA;QACA;MACA;IACA;IAEA;IACAe,WAAA,WAAAA,YAAA;MACA;MACA,KAAAtF,SAAA,CAAAC,WAAA;MACA,KAAAmB,gBAAA;MACA,KAAAC,eAAA;;MAEA;MACA,KAAAF,WAAA;;MAEA;MACA,KAAAzC,SAAA;;MAEA;MACA,KAAA4C,iBAAA;;MAEA;MACA,KAAAjD,WAAA;MACA,KAAAM,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;MACA,KAAAwF,sBAAA;;MAEA;MACA,KAAAO,gBAAA;IACA;IAEA;IACAtC,kBAAA,WAAAA,mBAAA;MACA,KAAA/B,aAAA;;MAEA;MACA;;MAEA;MACA,KAAA7C,WAAA;MACA,KAAAM,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAAwF,sBAAA;;MAEA;MACA,KAAAO,gBAAA;IACA;IAEA;IACAA,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9D,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4D,SAAA;QAAA,IAAAC,SAAA;QAAA,WAAA9D,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA4D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1D,IAAA,GAAA0D,SAAA,CAAAzD,IAAA;YAAA;cAAAyD,SAAA,CAAA1D,IAAA;cAEA;cACAwD,SAAA,OAAA/B,mBAAA,CAAAhC,OAAA,EAAA6D,MAAA,CAAAlE,iBAAA,GAEA;cACA,IAAAoE,SAAA,IAAAA,SAAA,CAAAhC,MAAA;gBACA8B,MAAA,CAAA9G,SAAA,OAAAiF,mBAAA,CAAAhC,OAAA,EAAA+D,SAAA;cACA;gBACA;gBACAF,MAAA,CAAA9G,SAAA;cACA;;cAEA;cAAAkH,SAAA,CAAAzD,IAAA;cAAA,OACA0D,OAAA,CAAAC,GAAA,EACAN,MAAA,CAAAhC,aAAA,IACAgC,MAAA,CAAAxB,gBAAA;cAAA,CACA;YAAA;cAEA;cACAwB,MAAA,CAAAlE,iBAAA,GAAAoE,SAAA;;cAEA;cACA,IAAAF,MAAA,CAAAlE,iBAAA,IAAAkE,MAAA,CAAAlE,iBAAA,CAAAoC,MAAA;gBACA8B,MAAA,CAAAO,4BAAA;cACA;;cAEA;cACAP,MAAA,CAAAtE,aAAA;cACA6C,UAAA;gBACAyB,MAAA,CAAArE,WAAA;cACA;cAAAyE,SAAA,CAAAzD,IAAA;cAAA;YAAA;cAAAyD,SAAA,CAAA1D,IAAA;cAAA0D,SAAA,CAAArD,EAAA,GAAAqD,SAAA;cAEApD,OAAA,CAAAC,KAAA,gBAAAmD,SAAA,CAAArD,EAAA;cACAiD,MAAA,CAAA9C,QAAA,CAAAD,KAAA;cACA;cACA+C,MAAA,CAAAtE,aAAA;cACA6C,UAAA;gBACAyB,MAAA,CAAArE,WAAA;cACA;YAAA;YAAA;cAAA,OAAAyE,SAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA8C,QAAA;MAAA;IAEA;IAEA;IAEA;IACAM,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,UAAA1E,iBAAA,SAAAA,iBAAA,CAAAoC,MAAA;QACA;MACA;;MAEA;MACA,IAAAuC,YAAA;MACA,KAAA3E,iBAAA,CAAA4E,OAAA,WAAAC,SAAA;QACA,IAAAC,SAAA,GAAAJ,MAAA,CAAAvH,gBAAA,CAAA4H,IAAA,CACA,UAAAC,QAAA;UAAA,OAAAA,QAAA,CAAA/B,QAAA,KAAA4B,SAAA,CAAA5B,QAAA;QAAA,CACA;QACA,IAAA6B,SAAA;UACAH,YAAA,CAAAM,IAAA,CAAAH,SAAA;QACA;MACA;MAEA,IAAAH,YAAA,CAAAvC,MAAA;QACA;QACA,KAAAhF,SAAA,GAAAuH,YAAA;QACA;QACA,KAAAxC,SAAA;UACA,IAAAuC,MAAA,CAAApC,KAAA,CAAAC,SAAA;YACAmC,MAAA,CAAApC,KAAA,CAAAC,SAAA,CAAAC,wBAAA,CAAAmC,YAAA;UACA;QACA;MACA;QACA;QACA,KAAAvH,SAAA;MACA;IACA;IAEA;IAEA;IACA8H,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,MAAA;MAAA,WAAA/E,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA6E,SAAA;QAAA,WAAA9E,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA4E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1E,IAAA,GAAA0E,SAAA,CAAAzE,IAAA;YAAA;cAAAyE,SAAA,CAAA1E,IAAA;cAEA;cACA,IAAAuE,MAAA,CAAAnF,iBAAA,IAAAmF,MAAA,CAAAnF,iBAAA,CAAAoC,MAAA;gBACA+C,MAAA,CAAA/H,SAAA,OAAAiF,mBAAA,CAAAhC,OAAA,EAAA8E,MAAA,CAAAnF,iBAAA;cACA;gBACAmF,MAAA,CAAA/H,SAAA;cACA;;cAEA;cAAAkI,SAAA,CAAAzE,IAAA;cAAA,OACAsE,MAAA,CAAAjD,aAAA;YAAA;cAEA;cACA,IAAAiD,MAAA,CAAAnF,iBAAA,IAAAmF,MAAA,CAAAnF,iBAAA,CAAAoC,MAAA;gBACA+C,MAAA,CAAAV,4BAAA;cACA;cAAAa,SAAA,CAAAzE,IAAA;cAAA;YAAA;cAAAyE,SAAA,CAAA1E,IAAA;cAAA0E,SAAA,CAAArE,EAAA,GAAAqE,SAAA;cAEApE,OAAA,CAAAC,KAAA,CACA,6BAAAmE,SAAA,CAAArE,EAEA;YAAA;YAAA;cAAA,OAAAqE,SAAA,CAAAjE,IAAA;UAAA;QAAA,GAAA+D,QAAA;MAAA;IAEA;IAEA;IACAG,mBAAA,WAAAA,oBAAAC,OAAA;MACA,KAAAzI,WAAA,GAAAyI,OAAA;MACA,KAAA9B,sBAAA;MACA,KAAAhB,gBAAA;IACA;IAEA+C,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAA1I,QAAA,GAAA0I,IAAA;MACA,KAAA3I,WAAA;MACA,KAAA2G,sBAAA;MACA,KAAAhB,gBAAA;IACA;IAEA;IACAR,aAAA,WAAAA,cAAA;MAAA,IAAAyD,MAAA;MAAA,WAAAvF,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqF,SAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA,EAAAC,QAAA,EAAA9I,KAAA,EAAA+I,OAAA;QAAA,WAAA1F,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAwF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtF,IAAA,GAAAsF,SAAA,CAAArF,IAAA;YAAA;cACA8E,MAAA,CAAAnG,OAAA;cAAA0G,SAAA,CAAAtF,IAAA;cAEAiF,MAAA;gBACAzG,OAAA,EAAAuG,MAAA,CAAAtI,eAAA;gBACAL,QAAA,EAAA2I,MAAA,CAAArI,YAAA;gBACA6I,YAAA;gBACAC,EAAA,EAAAT,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,QAAA,SAAAZ,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAF,EAAA;gBACAI,CAAA;gBACAC,QAAA,EACAd,MAAA,CAAAnI,SAAA,CAAAG,SAAA,QAAAgI,MAAA,CAAAnI,SAAA,CAAAG,SAAA;gBACA+I,SAAA,EAAAf,MAAA,CAAAnI,SAAA,CAAAI,SAAA,GACA+H,MAAA,CAAAnI,SAAA,CAAAI,SAAA,MACA;gBACA+I,OAAA,EAAAhB,MAAA,CAAAnI,SAAA,CAAAI,SAAA,GAAA+H,MAAA,CAAAnI,SAAA,CAAAI,SAAA;gBACAC,kBAAA,EACA8H,MAAA,CAAAnI,SAAA,CAAAK,kBAAA,QACA8H,MAAA,CAAAnI,SAAA,CAAAK,kBAAA,GACA;gBACA+I,mBAAA,EAAAjB,MAAA,CAAAnI,SAAA,CAAAM,cAAA,GACA6H,MAAA,CAAAnI,SAAA,CAAAM,cAAA,MACA;gBACA+I,iBAAA,EAAAlB,MAAA,CAAAnI,SAAA,CAAAM,cAAA,GACA6H,MAAA,CAAAnI,SAAA,CAAAM,cAAA,MACA;gBACAgJ,QAAA,EAAAnB,MAAA,CAAAnI,SAAA,CAAAE,OAAA;gBACAK,YAAA,EAAA4H,MAAA,CAAAnI,SAAA,CAAAO,YAAA;gBACAE,OAAA,EAAA0H,MAAA,CAAAnI,SAAA,CAAAS,OAAA;gBACA;gBACAU,WAAA,EAAAgH,MAAA,CAAAjH,SAAA,CAAAC,WAAA;gBACA;gBACAoI,uBAAA,EAAApB,MAAA,CAAA7F,gBAAA;gBACA;gBACAkH,eAAA,EAAArB,MAAA,CAAA5F,eAAA;gBACA7B,QAAA,EAAAyH,MAAA,CAAAnI,SAAA,CAAAU,QAAA;gBACA;gBACA+I,SAAA,EAAAtB,MAAA,CAAAnI,SAAA,CAAAS,OAAA;gBACAiJ,QAAA,EAAAvB,MAAA,CAAAnI,SAAA,CAAAS,OAAA;gBACAkJ,QAAA,EAAAxB,MAAA,CAAAnI,SAAA,CAAAS,OAAA;gBACAmJ,mBAAA,EAAAzB,MAAA,CAAAnI,SAAA,CAAAS,OAAA;gBACAoJ,YAAA,EAAA1B,MAAA,CAAAnI,SAAA,CAAAS;cACA;cAEA,IAAA0H,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,QAAA;gBACAV,MAAA,CAAAU,QAAA,GAAAZ,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,QAAA;cACA;cAAAL,SAAA,CAAArF,IAAA;cAAA,OAEAyG,cAAA,CAAAC,gBAAA,CAAA1B,MAAA;YAAA;cAAAC,GAAA,GAAAI,SAAA,CAAAsB,IAAA;cAEA,IAAA1B,GAAA,CAAA2B,IAAA;gBACA1B,QAAA,GAAAD,GAAA,CAAA4B,IAAA;gBACAzK,KAAA,GAAA6I,GAAA,CAAA7I,KAAA;gBAEA+I,OAAA,YAAAA,QAAApJ,IAAA;kBAAA,OACAA,IAAA,CAAAmG,GAAA,WAAAC,IAAA,EAAA2E,KAAA;oBAAA;sBACAvB,EAAA,KAAA7C,MAAA,CACAP,IAAA,CAAAC,QAAA,oBAAAM,MAAA,CACAoE,KAAA,OAAApE,MAAA,CAAAqE,IAAA,CAAAC,GAAA,SAAAtE,MAAA,CAAAuE,IAAA,CAAAC,MAAA,GACAC,QAAA,KACAC,SAAA;sBAAA;sBACAC,KAAA,EAAAlF,IAAA,CAAAmF,MAAA;sBACAC,KAAA,EAAApF,IAAA,CAAAqF,YAAA;sBACAC,QAAA,EAAAtF,IAAA,CAAAsF,QAAA;sBACAC,OAAA,EAAAvF,IAAA,CAAAgE,eAAA;sBACA/D,QAAA,EAAAD,IAAA,CAAAC,QAAA;sBACAuF,GAAA,EAAAxF,IAAA,CAAAwF,GAAA;oBACA;kBAAA;gBAAA;gBAEA7C,MAAA,CAAAxI,gBAAA,GAAA6I,OAAA,CAAAD,QAAA;gBACAJ,MAAA,CAAApI,SAAA,GAAAN,KAAA;cACA;cAAAiJ,SAAA,CAAArF,IAAA;cAAA;YAAA;cAAAqF,SAAA,CAAAtF,IAAA;cAAAsF,SAAA,CAAAjF,EAAA,GAAAiF,SAAA;cAEAhF,OAAA,CAAAC,KAAA,aAAA+E,SAAA,CAAAjF,EAAA;cACA0E,MAAA,CAAAvE,QAAA,CAAAD,KAAA;YAAA;cAAA+E,SAAA,CAAAtF,IAAA;cAEA+E,MAAA,CAAAnG,OAAA;cAAA,OAAA0G,SAAA,CAAAuC,MAAA;YAAA;YAAA;cAAA,OAAAvC,SAAA,CAAA7E,IAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA;IAEA;IAEA;IACAlD,gBAAA,WAAAA,iBAAAgG,IAAA;MAAA,IAAAC,MAAA;MAAA,WAAAvI,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqI,SAAA;QAAA,WAAAtI,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlI,IAAA,GAAAkI,SAAA,CAAAjI,IAAA;YAAA;cAAA,KAEA8H,MAAA,CAAAjJ,UAAA;gBAAAoJ,SAAA,CAAAjI,IAAA;gBAAA;cAAA;cAAA,OAAAiI,SAAA,CAAAC,MAAA;YAAA;cAIA,KAAAL,IAAA;gBACAC,MAAA,CAAAlJ,YAAA;cACA;;cAEA;cACA,IAAAkJ,MAAA,CAAAhJ,kBAAA;gBACAqJ,YAAA,CAAAL,MAAA,CAAAhJ,kBAAA;cACA;;cAEA;cACAgJ,MAAA,CAAAhJ,kBAAA,GAAA8C,UAAA,kBAAArC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA0I,SAAA;gBAAA,IAAApD,MAAA,EAAAjJ,IAAA,EAAAqG,QAAA,EAAAiG,YAAA,EAAApD,GAAA,EAAAqD,WAAA;gBAAA,WAAA7I,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA2I,UAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAzI,IAAA,GAAAyI,SAAA,CAAAxI,IAAA;oBAAA;sBAAAwI,SAAA,CAAAzI,IAAA;sBAEA,IAAA8H,IAAA;wBACAC,MAAA,CAAA1I,aAAA;sBACA;sBAEA0I,MAAA,CAAAjJ,UAAA;sBAEAmG,MAAA;wBACAW,CAAA;wBACApH,OAAA,EAAAuJ,MAAA,CAAA5L,WAAA;wBACAC,QAAA,EAAA2L,MAAA,CAAA3L,QAAA;wBACAoJ,EAAA,EAAAuC,MAAA,CAAAtC,MAAA,CAAAC,KAAA,CAAAC,QAAA,SAAAoC,MAAA,CAAAtC,MAAA,CAAAC,KAAA,CAAAF,EAAA;wBACAkD,MAAA,EAAAX,MAAA,CAAAnL,SAAA,CAAAQ,QAAA;wBACAyI,QAAA,EACAkC,MAAA,CAAAnL,SAAA,CAAAG,SAAA,QAAAgL,MAAA,CAAAnL,SAAA,CAAAG,SAAA;wBACA+I,SAAA,EAAAiC,MAAA,CAAAnL,SAAA,CAAAI,SAAA,GACA+K,MAAA,CAAAnL,SAAA,CAAAI,SAAA,MACA;wBACA+I,OAAA,EAAAgC,MAAA,CAAAnL,SAAA,CAAAI,SAAA,GACA+K,MAAA,CAAAnL,SAAA,CAAAI,SAAA,MACA;wBACAC,kBAAA,EACA8K,MAAA,CAAAnL,SAAA,CAAAK,kBAAA,QACA8K,MAAA,CAAAnL,SAAA,CAAAK,kBAAA,GACA;wBACA+I,mBAAA,EAAA+B,MAAA,CAAAnL,SAAA,CAAAM,cAAA,GACA6K,MAAA,CAAAnL,SAAA,CAAAM,cAAA,MACA;wBACA+I,iBAAA,EAAA8B,MAAA,CAAAnL,SAAA,CAAAM,cAAA,GACA6K,MAAA,CAAAnL,SAAA,CAAAM,cAAA,MACA;wBACAgJ,QAAA,EAAA6B,MAAA,CAAAnL,SAAA,CAAAE,OAAA;wBACAK,YAAA,EAAA4K,MAAA,CAAAnL,SAAA,CAAAO,YAAA;wBACAE,OAAA,EAAA0K,MAAA,CAAAnL,SAAA,CAAAS,OAAA;wBACAkI,YAAA;wBACA;wBACAc,SAAA,EAAA0B,MAAA,CAAAnL,SAAA,CAAAS,OAAA;wBACAiJ,QAAA,EAAAyB,MAAA,CAAAnL,SAAA,CAAAS,OAAA;wBACAkJ,QAAA,EAAAwB,MAAA,CAAAnL,SAAA,CAAAS,OAAA;wBACAmJ,mBAAA,EAAAuB,MAAA,CAAAnL,SAAA,CAAAS,OAAA;wBACAoJ,YAAA,EAAAsB,MAAA,CAAAnL,SAAA,CAAAS;sBACA;sBAEA,IAAA0K,MAAA,CAAAtC,MAAA,CAAAC,KAAA,CAAAC,QAAA;wBACAV,MAAA,CAAAU,QAAA,GAAAoC,MAAA,CAAAtC,MAAA,CAAAC,KAAA,CAAAC,QAAA;sBACA;;sBAEA;sBACA,IAAAoC,MAAA,CAAA3I,iBAAA,IAAA2I,MAAA,CAAA3I,iBAAA,CAAAoC,MAAA;wBACAxF,IAAA,GAAA+L,MAAA,CAAA3I,iBAAA,CAAA+C,GAAA,WAAAC,IAAA;0BAAA,OAAAA,IAAA,CAAAkF,KAAA;wBAAA;wBACAjF,QAAA,GAAA0F,MAAA,CAAA3I,iBAAA,CAAA+C,GAAA,CACA,UAAAC,IAAA;0BAAA,OAAAA,IAAA,CAAAC,QAAA;wBAAA,CACA;wBAEA4C,MAAA,CAAA0D,UAAA,GAAAC,MAAA,CAAA5M,IAAA;wBACAiJ,MAAA,CAAA5C,QAAA,GAAAuG,MAAA,CAAAvG,QAAA;sBACA;;sBAEA;sBACA,IAAA4C,MAAA,CAAAiB,QAAA;wBACA,IAAA2C,iCAAA;0BAAA/L,OAAA,EAAAmI,MAAA,CAAAiB,QAAA;0BAAA4C,IAAA;wBAAA,GAAAC,IAAA,CACA;0BACAhB,MAAA,CAAA5H,iBAAA;wBACA,CACA;sBACA;sBAIA,IAAA4H,MAAA,CAAAtC,MAAA,CAAAC,KAAA,CAAAsD,MAAA;wBACAV,YAAA;0BACA9J,OAAA,EAAAuJ,MAAA,CAAA5L,WAAA;0BACAC,QAAA,EAAA2L,MAAA,CAAA3L,QAAA;0BACAiG,QAAA,EAAA0F,MAAA,CAAAtC,MAAA,CAAAC,KAAA,CAAAsD,MAAA;0BACAN,MAAA,EAAAX,MAAA,CAAAnL,SAAA,CAAAQ;wBACA;sBACA;sBAAA,KAEA2K,MAAA,CAAAtC,MAAA,CAAAC,KAAA,CAAAsD,MAAA;wBAAAP,SAAA,CAAAxI,IAAA;wBAAA;sBAAA;sBAAAwI,SAAA,CAAAxI,IAAA;sBAAA,OACAyG,cAAA,CAAAuC,iBAAA,KAAAC,cAAA,CAAAzJ,OAAA,MAAA6I,YAAA;oBAAA;sBAAAG,SAAA,CAAApI,EAAA,GAAAoI,SAAA,CAAA7B,IAAA;sBAAA6B,SAAA,CAAAxI,IAAA;sBAAA;oBAAA;sBAAAwI,SAAA,CAAAxI,IAAA;sBAAA,OACAyG,cAAA,CAAAyC,aAAA,KAAAD,cAAA,CAAAzJ,OAAA,MAAAwF,MAAA;oBAAA;sBAAAwD,SAAA,CAAApI,EAAA,GAAAoI,SAAA,CAAA7B,IAAA;oBAAA;sBAFA1B,GAAA,GAAAuD,SAAA,CAAApI,EAAA;sBAAA,MAIA6E,GAAA,CAAA2B,IAAA;wBAAA4B,SAAA,CAAAxI,IAAA;wBAAA;sBAAA;sBAGA,IAAA8H,MAAA,CAAAtC,MAAA,CAAAC,KAAA,CAAAsD,MAAA;wBACAT,WAAA,GAAArD,GAAA,CAAA4B,IAAA;sBACA;wBACAyB,WAAA,GAAArD,GAAA,CAAAlJ,IAAA,CAAAoN,IAAA;sBACA;;sBAEA;sBACA,IACA,CAAArB,MAAA,CAAAnL,SAAA,CAAAE,OAAA,IACAiL,MAAA,CAAAnL,SAAA,CAAAE,OAAA,CAAAuM,IAAA,WACA;wBACAd,WAAA,GAAAR,MAAA,CAAAuB,mBAAA,CAAAf,WAAA;sBACA;sBAEAR,MAAA,CAAAzL,WAAA,GAAAiM,WAAA;sBAEA,IAAAR,MAAA,CAAAtC,MAAA,CAAAC,KAAA,CAAAsD,MAAA;wBACAjB,MAAA,CAAA1L,KAAA,GAAA6I,GAAA,CAAA7I,KAAA;sBACA;wBACA0L,MAAA,CAAA1L,KAAA,GAAA6I,GAAA,CAAAlJ,IAAA,CAAAK,KAAA;sBACA;;sBAEA;sBACA,IAAA0L,MAAA,CAAA3I,iBAAA,IAAA2I,MAAA,CAAA3I,iBAAA,CAAAoC,MAAA;wBACAuG,MAAA,CAAAlE,4BAAA;sBACA;;sBAEA;sBAAA,MAEAkE,MAAA,CAAAzL,WAAA,CAAAkF,MAAA,SACAuG,MAAA,CAAA3L,QAAA,IAAA2L,MAAA,CAAA5L,WAAA,SAAA4L,MAAA,CAAA1L,KAAA,IACA0L,MAAA,CAAA1L,KAAA;wBAAAoM,SAAA,CAAAxI,IAAA;wBAAA;sBAAA;sBAEA8H,MAAA,CAAA5L,WAAA,GAAA+K,IAAA,CAAAqC,GAAA,CACA,GACArC,IAAA,CAAAsC,IAAA,CAAAzB,MAAA,CAAA1L,KAAA,GAAA0L,MAAA,CAAA3L,QAAA,CACA;sBACA;sBAAAqM,SAAA,CAAAxI,IAAA;sBAAA,OACA8H,MAAA,CAAAjG,gBAAA;oBAAA;sBAAA,OAAA2G,SAAA,CAAAN,MAAA;oBAAA;sBAAAM,SAAA,CAAAxI,IAAA;sBAAA;oBAAA;sBAIA8H,MAAA,CAAAvH,QAAA,CAAAD,KAAA,CAAA2E,GAAA,CAAAuE,GAAA;oBAAA;sBAAAhB,SAAA,CAAAxI,IAAA;sBAAA;oBAAA;sBAAAwI,SAAA,CAAAzI,IAAA;sBAAAyI,SAAA,CAAAiB,EAAA,GAAAjB,SAAA;sBAGAnI,OAAA,CAAAC,KAAA,cAAAkI,SAAA,CAAAiB,EAAA;sBACA3B,MAAA,CAAAvH,QAAA,CAAAD,KAAA;oBAAA;sBAAAkI,SAAA,CAAAzI,IAAA;sBAEA+H,MAAA,CAAAjJ,UAAA;sBACAiJ,MAAA,CAAA1I,aAAA;sBACA0I,MAAA,CAAAlJ,YAAA;sBACAkJ,MAAA,CAAAxK,cAAA;sBAAA,OAAAkL,SAAA,CAAAZ,MAAA;oBAAA;oBAAA;sBAAA,OAAAY,SAAA,CAAAhI,IAAA;kBAAA;gBAAA,GAAA4H,QAAA;cAAA,CAEA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAzH,IAAA;UAAA;QAAA,GAAAuH,QAAA;MAAA;IACA;IAEA;IACAlF,sBAAA,WAAAA,uBAAA;MAAA,IAAA6G,MAAA;MACA,KAAApI,SAAA;QACA;QACA,IAAAqI,gBAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,gBAAA;UACAA,gBAAA,CAAAG,SAAA;QACA;;QAEA;QACA,IACAJ,MAAA,CAAAjI,KAAA,CAAAsI,WAAA,IACAL,MAAA,CAAAjI,KAAA,CAAAsI,WAAA,CAAAtI,KAAA,IACAiI,MAAA,CAAAjI,KAAA,CAAAsI,WAAA,CAAAtI,KAAA,CAAAuI,MAAA,EACA;UACAN,MAAA,CAAAjI,KAAA,CAAAsI,WAAA,CAAAtI,KAAA,CAAAuI,MAAA,CAAAF,SAAA;QACA;;QAEA;QACA,IAAAG,SAAA,GAAAL,QAAA,CAAAC,aAAA;QACA,IAAAI,SAAA;UACAA,SAAA,CAAAH,SAAA;QACA;MACA;IACA;IACAI,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAA5K,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA0K,SAAA;QAAA,IAAAC,QAAA;QAAA,WAAA5K,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxK,IAAA,GAAAwK,SAAA,CAAAvK,IAAA;YAAA;cAAAuK,SAAA,CAAAxK,IAAA;cAAAwK,SAAA,CAAAvK,IAAA;cAAA,OAEAyG,cAAA,CAAA+D,WAAA;YAAA;cAAAH,QAAA,GAAAE,SAAA,CAAA5D,IAAA;cACA,IAAA0D,QAAA,IAAAA,QAAA,CAAAzD,IAAA,WAAAyD,QAAA,CAAAtO,IAAA;gBACAoO,MAAA,CAAA1M,QAAA,GAAA4M,QAAA,CAAAtO,IAAA;gBACAoO,MAAA,CAAAzM,WAAA,GAAA2M,QAAA,CAAAtO,IAAA;cACA;gBACAsE,OAAA,CAAAoK,IAAA;cACA;cAAAF,SAAA,CAAAvK,IAAA;cAAA;YAAA;cAAAuK,SAAA,CAAAxK,IAAA;cAAAwK,SAAA,CAAAnK,EAAA,GAAAmK,SAAA;cAEAlK,OAAA,CAAAC,KAAA,cAAAiK,SAAA,CAAAnK,EAAA;cACA+J,MAAA,CAAA5J,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAiK,SAAA,CAAA/J,IAAA;UAAA;QAAA,GAAA4J,QAAA;MAAA;IAEA;IAEAlK,iBAAA,WAAAA,kBAAA;MAAA,IAAAwK,OAAA;MAAA,WAAAnL,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiL,SAAA;QAAA,IAAA1F,GAAA;QAAA,WAAAxF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAgL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9K,IAAA,GAAA8K,SAAA,CAAA7K,IAAA;YAAA;cAAA6K,SAAA,CAAA9K,IAAA;cAAA8K,SAAA,CAAA7K,IAAA;cAAA,OAEA,IAAA8K,kCAAA;gBACAvM,OAAA;gBACApC,QAAA;gBACA0M,IAAA;cACA;YAAA;cAJA5D,GAAA,GAAA4F,SAAA,CAAAlE,IAAA;cAKA,IAAA1B,GAAA,IAAAA,GAAA,CAAA2B,IAAA;gBACA8D,OAAA,CAAAxM,WAAA,GAAA+G,GAAA,CAAA4B,IAAA;cACA;cAAAgE,SAAA,CAAA7K,IAAA;cAAA;YAAA;cAAA6K,SAAA,CAAA9K,IAAA;cAAA8K,SAAA,CAAAzK,EAAA,GAAAyK,SAAA;cAEAxK,OAAA,CAAAC,KAAA,cAAAuK,SAAA,CAAAzK,EAAA;YAAA;YAAA;cAAA,OAAAyK,SAAA,CAAArK,IAAA;UAAA;QAAA,GAAAmK,QAAA;MAAA;IAEA;IAEA;IACAI,UAAA,WAAAA,WAAAC,KAAA,EAAAjP,IAAA;MACA,KAAAiP,KAAA;MACA,OAAAjP,IAAA,CAAAsL,KAAA,CAAA4D,OAAA,CAAAD,KAAA;IACA;IAEA;IACA/K,OAAA,WAAAA,QAAA4H,IAAA;MACA,IAAAA,IAAA;QACA,KAAAhF,sBAAA;QACA;QACA,KAAAO,gBAAA;MACA;QACA;QACA,KAAAP,sBAAA;QACA,KAAAhB,gBAAA;MACA;IACA;IAEA;IACAqJ,SAAA,WAAAA,UAAA;MACA;QACA,KAAAvO,SAAA;UACAC,QAAA;UACAC,OAAA;UACAC,SAAA;UACAC,SAAA;UACAC,kBAAA;UACAC,cAAA;UACAC,YAAA;UACAC,QAAA;UACAC,OAAA;QACA;QAEA,KAAAlB,WAAA;QACA,KAAA2G,sBAAA;QACA,KAAAO,gBAAA;MACA,SAAA9C,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;MACA;IACA;IAEA6K,aAAA,WAAAA,cAAAhJ,IAAA,EAAA0G,IAAA;MAAA,IAAAuC,OAAA;MAAA,WAAA7L,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA2L,UAAA;QAAA,WAAA5L,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0L,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxL,IAAA,GAAAwL,UAAA,CAAAvL,IAAA;YAAA;cAAAuL,UAAA,CAAAxL,IAAA;cAEA,IAAAqL,OAAA,CAAAjN,cAAA;gBACAgK,YAAA,CAAAiD,OAAA,CAAAjN,cAAA;cACA;cAAA,MAEAgE,IAAA,IAAAA,IAAA,CAAAoD,EAAA;gBAAAgG,UAAA,CAAAvL,IAAA;gBAAA;cAAA;cAAAuL,UAAA,CAAAvL,IAAA;cAAA,OACA,IAAAwL,iCAAA,GAAArJ,IAAA,CAAAoD,EAAA;YAAA;cAAA,MAEAsD,IAAA;gBAAA0C,UAAA,CAAAvL,IAAA;gBAAA;cAAA;cACA,IAAAoL,OAAA,CAAA3J,KAAA;gBACA2J,OAAA,CAAA3J,KAAA,eAAAgK,KAAA;cACA;cAAAF,UAAA,CAAAvL,IAAA;cAAA,OACAoL,OAAA,CAAAlL,iBAAA;YAAA;cAAAqL,UAAA,CAAAvL,IAAA;cAAA;YAAA;cAAAuL,UAAA,CAAAvL,IAAA;cAAA,OAEAoL,OAAA,CAAAlL,iBAAA;YAAA;cAAAqL,UAAA,CAAAvL,IAAA;cAAA,OACAoL,OAAA,CAAAM,kBAAA;YAAA;cAAAH,UAAA,CAAAvL,IAAA;cAAA;YAAA;cAAAuL,UAAA,CAAAxL,IAAA;cAAAwL,UAAA,CAAAnL,EAAA,GAAAmL,UAAA;cAIAlL,OAAA,CAAAC,KAAA,eAAAiL,UAAA,CAAAnL,EAAA;cACAgL,OAAA,CAAA7K,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAiL,UAAA,CAAA/K,IAAA;UAAA;QAAA,GAAA6K,SAAA;MAAA;IAEA;IAEAM,eAAA,WAAAA,gBAAA;MACA;QACA,KAAA1N,WAAA;MACA,SAAAqC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;MACA;IACA;IAEAsL,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA;QACA,SAAA1N,cAAA;UACAgK,YAAA,MAAAhK,cAAA;QACA;QAEA,KAAAA,cAAA,GAAAyD,UAAA;UACAiK,OAAA,CAAA5N,WAAA;QACA;MACA,SAAAqC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA,KAAArC,WAAA;MACA;IACA;IAEA;IACA6N,cAAA,WAAAA,eAAA3J,IAAA;MACA,KAAAxF,SAAA,CAAAE,OAAA,GAAAsF,IAAA,CAAAtF,OAAA;MACA,KAAAuB,cAAA;MACA,KAAAyE,sBAAA;MACA,KAAA3G,WAAA;MACA;MACA,KAAA2F,gBAAA;IACA;IAEAkK,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzM,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuM,UAAA;QAAA,WAAAxM,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsM,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApM,IAAA,GAAAoM,UAAA,CAAAnM,IAAA;YAAA;cAAAmM,UAAA,CAAApM,IAAA;cAEA,IAAAiM,OAAA,CAAA7N,cAAA;gBACAgK,YAAA,CAAA6D,OAAA,CAAA7N,cAAA;cACA;cAEA,IAAA6N,OAAA,CAAAvK,KAAA;gBACAuK,OAAA,CAAAvK,KAAA,eAAAgK,KAAA;cACA;cAAAU,UAAA,CAAAnM,IAAA;cAAA,OAEA,IAAAoM,mCAAA;YAAA;cAAAD,UAAA,CAAAnM,IAAA;cAAA,OACAgM,OAAA,CAAA9L,iBAAA;YAAA;cAAAiM,UAAA,CAAAnM,IAAA;cAAA;YAAA;cAAAmM,UAAA,CAAApM,IAAA;cAAAoM,UAAA,CAAA/L,EAAA,GAAA+L,UAAA;cAEA9L,OAAA,CAAAC,KAAA,eAAA6L,UAAA,CAAA/L,EAAA;cACA4L,OAAA,CAAAzL,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAA6L,UAAA,CAAA3L,IAAA;UAAA;QAAA,GAAAyL,SAAA;MAAA;IAEA;IAEAI,WAAA,WAAAA,YAAA;MACA;QACA,KAAAhO,cAAA;QACA,KAAAqN,kBAAA;QACA,KAAAtN,cAAA;MACA,SAAAkC,KAAA;QACAD,OAAA,CAAAC,KAAA,iBAAAA,KAAA;QACA,KAAAjC,cAAA;MACA;IACA;IAEAqN,kBAAA,WAAAA,mBAAA;MAAA,IAAAY,OAAA;MAAA,WAAA/M,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA6M,UAAA;QAAA,IAAAC,QAAA;QAAA,WAAA/M,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA6M,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3M,IAAA,GAAA2M,UAAA,CAAA1M,IAAA;YAAA;cAAA0M,UAAA,CAAA3M,IAAA;cAEAuM,OAAA,CAAAjO,cAAA;cAAAqO,UAAA,CAAA1M,IAAA;cAAA,OACA,IAAA8K,kCAAA,MAAA7B,cAAA,CAAAzJ,OAAA,MAAAyJ,cAAA,CAAAzJ,OAAA,MACA8M,OAAA,CAAAhO,YAAA;gBACAuK,IAAA;cAAA,EACA;YAAA;cAHA2D,QAAA,GAAAE,UAAA,CAAA/F,IAAA;cAKA,IAAA6F,QAAA;gBACAF,OAAA,CAAA7N,YAAA,GAAA+N,QAAA,CAAA3F,IAAA;gBACAyF,OAAA,CAAA9N,MAAA,GAAAgO,QAAA,CAAApQ,KAAA;cACA;cAEAkQ,OAAA,CAAAjO,cAAA;cAAAqO,UAAA,CAAA1M,IAAA;cAAA;YAAA;cAAA0M,UAAA,CAAA3M,IAAA;cAAA2M,UAAA,CAAAtM,EAAA,GAAAsM,UAAA;cAEArM,OAAA,CAAAC,KAAA,iBAAAoM,UAAA,CAAAtM,EAAA;cACAkM,OAAA,CAAAjO,cAAA;cACAiO,OAAA,CAAA/L,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAoM,UAAA,CAAAlM,IAAA;UAAA;QAAA,GAAA+L,SAAA;MAAA;IAEA;IAEAI,OAAA,WAAAA,QAAAhF,GAAA;MACAiF,MAAA,CAAAC,IAAA,CAAAlF,GAAA;IACA;IAEA;IACAmF,kBAAA,WAAAA,mBAAAjQ,OAAA;MACA,SAAAmC,WAAA;QACA;MACA;;MAEA;;MAEA;MACA,KAAAnB,SAAA,CAAAC,WAAA,GAAAjB,OAAA;;MAEA;MACA,KAAAL,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAAgH,iCAAA;IACA;IAEA;IACA0I,oBAAA,WAAAA,qBAAAC,aAAA;MACA;;MAEA;MACA,KAAA/N,gBAAA,GAAA+N,aAAA;;MAEA;MACA,KAAAxQ,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAAgH,iCAAA;IACA;IAEA;IACA4I,mBAAA,WAAAA,oBAAAC,YAAA;MACA;;MAEA;MACA,KAAAhO,eAAA,GAAAgO,YAAA;;MAEA;MACA,KAAA1Q,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAAgH,iCAAA;IACA;IAEA;IACA8I,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAA5Q,eAAA,GAAA4Q,IAAA;MACA,KAAAzQ,SAAA,CAAAU,QAAA;MACA,KAAAgH,iCAAA;IACA;IAEA;IACAgJ,wBAAA,WAAAA,yBAAAxI,IAAA;MACA,KAAApI,YAAA,GAAAoI,IAAA;MACA,KAAArI,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;MACA,KAAAgH,iCAAA;IACA;IAEA;IACAiJ,WAAA,WAAAA,YAAAnL,IAAA;MACAyK,MAAA,CAAAC,IAAA,uBAAAnK,MAAA,CACAP,IAAA,CAAAoD,EAAA,aAAA7C,MAAA,CAAAP,IAAA,CAAAoL,KAAA,kBAAA7K,MAAA,CAAAP,IAAA,CAAAqL,UAAA,GACA,QACA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAA;MACA,KAAA/B,kBAAA;IACA;IAEA;IACArC,mBAAA,WAAAA,oBAAAqE,QAAA;MACA,KAAAA,QAAA,IAAAA,QAAA,CAAAnM,MAAA;QACA,OAAAmM,QAAA;MACA;MAEA,IAAAC,QAAA,OAAAC,GAAA;MACA,IAAAC,MAAA;;MAEA;MACAH,QAAA,CAAA3J,OAAA,WAAA+J,OAAA;QACA;QACA,IAAAC,UAAA,GAAAD,OAAA,CAAAE,KAAA,GACAF,OAAA,CAAAE,KAAA,CAAAC,OAAA,iBAAAA,OAAA,eACA;QAEA,IAAAN,QAAA,CAAA1K,GAAA,CAAA8K,UAAA;UACAJ,QAAA,CAAAO,GAAA,CAAAH,UAAA,EAAAxG,KAAA;QACA;UACAoG,QAAA,CAAAQ,GAAA,CAAAJ,UAAA;YACAD,OAAA,MAAA7E,cAAA,CAAAzJ,OAAA,MAAAsO,OAAA;YACAvG,KAAA;YACA6G,aAAA,EAAAN,OAAA,CAAAE,KAAA;UACA;QACA;MACA;;MAEA;MACAL,QAAA,CAAA5J,OAAA,WAAAsK,KAAA;QAAA,IAAAP,OAAA,GAAAO,KAAA,CAAAP,OAAA;UAAAvG,KAAA,GAAA8G,KAAA,CAAA9G,KAAA;UAAA6G,aAAA,GAAAC,KAAA,CAAAD,aAAA;QACA,IAAA7G,KAAA;UACA;UACA;UACAuG,OAAA,CAAAE,KAAA,MAAAtL,MAAA,CAAA0L,aAAA,kBAAA1L,MAAA,CAAA6E,KAAA;QACA;QACAsG,MAAA,CAAAzJ,IAAA,CAAA0J,OAAA;MACA;MAEA,OAAAD,MAAA;IACA;EACA;AACA", "ignoreList": []}]}