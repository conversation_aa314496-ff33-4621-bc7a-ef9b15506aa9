{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\artificialIntelligence.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\artificialIntelligence.vue", "mtime": 1754010096859}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXBpIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHRvcFNlYWNoIGZyb20gIkAvdmlld3MvY29tcG9uZW50cy90b3BTZWFjaC52dWUiOw0KaW1wb3J0IE1haW5BcnRpY2xlIGZyb20gIi4uL2NvbXBvbmVudHMvTWFpbkFydGljbGUudnVlIjsNCmltcG9ydCB7DQogIGxpc3RBcnRpY2xlSGlzdG9yeSwNCiAgZGVsQXJ0aWNsZUhpc3RvcnksDQogIGFkZEFydGljbGVIaXN0b3J5LA0KICBjbGVhbkFydGljbGVIaXN0b3J5LA0KfSBmcm9tICJAL2FwaS9hcnRpY2xlL2FydGljbGVIaXN0b3J5IjsNCmltcG9ydCB7IFNwbGl0cGFuZXMsIFBhbmUgfSBmcm9tICJzcGxpdHBhbmVzIjsNCmltcG9ydCAic3BsaXRwYW5lcy9kaXN0L3NwbGl0cGFuZXMuY3NzIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IHRvcFNlYWNoLCBNYWluQXJ0aWNsZSwgU3BsaXRwYW5lcywgUGFuZSB9LA0KICBkaWN0czogWyJpc190ZWNobm9sb2d5IiwgImNvdW50cnkiXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgd2lkdGg6ICIyNTgiLA0KICAgICAgaXNSZVNpemU6IGZhbHNlLA0KICAgICAgLyog5paH56ug5Li75L2T57uE5Lu25pWw5o2uICovDQogICAgICBjdXJyZW50UGFnZTogMSwNCiAgICAgIHBhZ2VTaXplOiA1MCwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgQXJ0aWNsZUxpc3Q6IFtdLA0KICAgICAgLyog5bem5L6ndHJlZeaVsOaNriAqLw0KICAgICAgZmlsdGVyVGV4dDogIiIsDQogICAgICB0cmVlRGF0YTogW10sDQogICAgICB0cmVlRGF0YVRyYW5zZmVyOiBbXSwNCiAgICAgIGNoZWNrTGlzdDogW10sDQogICAgICAvKiDmoJHlvaLliIbpobXmlbDmja4gKi8NCiAgICAgIHRyZWVDdXJyZW50UGFnZTogMSwNCiAgICAgIHRyZWVQYWdlU2l6ZTogMTAwLA0KICAgICAgdHJlZVRvdGFsOiAwLA0KICAgICAgLyog5pCc57Si6Ziy5oqWICovDQogICAgICBzZWFyY2hEZWJvdW5jZVRpbWVyOiBudWxsLA0KICAgICAgLyog5pCc57Si57uE5Lu25pWw5o2uICovDQogICAgICBTZWFjaERhdGE6IHsNCiAgICAgICAgbWV0YU1vZGU6ICIiIC8qIOWMuemFjeaooeW8jyAqLywNCiAgICAgICAga2V5d29yZDogIiIgLyog5YWz6ZSu6K+NICovLA0KICAgICAgICB0aW1lUmFuZ2U6ICI0IiAvKiDml7bpl7TojIPlm7QgKi8sDQogICAgICAgIGN1c3RvbURheTogW10gLyog6Ieq5a6a5LmJ5aSpICovLA0KICAgICAgICBjb2xsZWN0aW9uRGF0ZVR5cGU6IG51bGwgLyog5pe26Ze06IyD5Zu0ICovLA0KICAgICAgICBjb2xsZWN0aW9uVGltZTogW10gLyog6Ieq5a6a5LmJ5aSpICovLA0KICAgICAgICBpc1RlY2hub2xvZ3k6ICIxIiwNCiAgICAgICAgc29ydE1vZGU6ICIwIiwNCiAgICAgIH0gLyog5pCc57Si5p2h5Lu2ICovLA0KICAgICAgLyog5o6S5bqP5qih5byPIC0g5Y2V54us5o+Q5Y+W77yM6YG/5YWN6Kem5Y+RU2VhY2hEYXRh55qE5rex5bqm55uR5ZCsICovDQogICAgICBidXR0b25EaXNhYmxlZDogZmFsc2UgLyog5oyJ6ZKu6Ziy5oqWICovLA0KICAgICAgQWN0aXZlRGF0YToge30sDQogICAgICBzZW5pb3JTZXJjaEZsYWc6IGZhbHNlIC8qIOaZrumAmuajgOe0ouaIlumrmOe6p+ajgOe0oiAqLywNCiAgICAgIGFyZWFMaXN0OiBbXSAvKiDlm73lhoXlnLDljLogKi8sDQogICAgICBjb3VudHJ5TGlzdDogW10gLyog5Zu95a625oiW5Zyw5Yy6ICovLA0KICAgICAgS2VMaXN0OiBbXSwNCiAgICAgIGZ1bkVzU2VhY2g6IG51bGwsDQogICAgICB0cmVlUXVlcnk6IHsNCiAgICAgICAgZmlsdGVyd29yZHM6ICIiLCAvLyDmt7vliqDmoJHmkJzntKLlhbPplK7lrZcNCiAgICAgIH0sDQogICAgICBkb21haW5MaXN0OiBbXSwNCiAgICAgIGluZHVzdHJ5TGlzdDogW10sDQogICAgICBzaG93SGlzdG9yeTogZmFsc2UsDQogICAgICBoaXN0b3J5TGlzdDogW10sDQogICAgICBoaXN0b3J5VGltZW91dDogbnVsbCwNCiAgICAgIGRpYWxvZ1Zpc2libGUxOiBmYWxzZSwNCiAgICAgIGhpc3RvcnlMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHF1ZXJ5UGFyYW1zMTogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICB9LA0KICAgICAgdG90YWwxOiAwLA0KICAgICAgaGlzdG9yeUxpc3QxOiBbXSwNCiAgICAgIG5vZGVDaGVja0xpc3Q6IFtdLA0KICAgICAgaXNUcmVlU2xvdFByb2Nlc3Npbmc6IGZhbHNlLA0KICAgICAgaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQ6IGZhbHNlLCAvLyDmoIforrDliJ3lp4vljJbmmK/lkKblrozmiJANCiAgICAgIC8vIOS7jldlY2hhdC52dWXlkIzmraXnmoTlsZ7mgKcNCiAgICAgIHRhYmxlTG9hZGluZzogZmFsc2UsIC8vIOihqOagvGxvYWRpbmfnirbmgIENCiAgICAgIHRyZWVSZWFkeTogZmFsc2UsIC8vIOagkee7hOS7tuaYr+WQpuW3suWHhuWkh+WlvQ0KICAgICAgaXNRdWVyeWluZzogZmFsc2UsIC8vIOafpeivoumYsuaKlg0KICAgICAgcXVlcnlEZWJvdW5jZVRpbWVyOiBudWxsLCAvLyDmn6Xor6LpmLLmipblrprml7blmagNCiAgICB9Ow0KICB9LA0KICBhc3luYyBjcmVhdGVkKCkgew0KICAgIHRyeSB7DQogICAgICAvLyDliJ3lp4vljJZmdW5Fc1NlYWNo5pa55rOVDQogICAgICB0aGlzLmZ1bkVzU2VhY2ggPSB0aGlzLkVzU2VhY2g7DQoNCiAgICAgIC8vIOWFiOWKoOi9veWfuuehgOaVsOaNrg0KICAgICAgYXdhaXQgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KDQogICAgICAvLyDliqDovb3moJHmlbDmja7lkozlhoXlrrnmlbDmja4NCiAgICAgIGF3YWl0IHRoaXMuaW5pdGlhbGl6ZURhdGEoKTsNCg0KICAgICAgLy8g5qCH6K6w5Yid5aeL5YyW5a6M5oiQ77yM6L+Z5qC3d2F0Y2jnm5HlkKzlmajmiY3kvJrlvIDlp4vlt6XkvZwNCiAgICAgIHRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgPSB0cnVlOw0KICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICBjb25zb2xlLmVycm9yKCLnu4Tku7bliJ3lp4vljJblpLHotKU6IiwgZXJyb3IpOw0KICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yid5aeL5YyW5aSx6LSl77yM6K+35Yi35paw6aG16Z2i6YeN6K+VIik7DQogICAgfQ0KICB9LA0KDQogIG1vdW50ZWQoKSB7DQogICAgLy8g5ZyobW91bnRlZOS4reWGjeasoeajgOafpeagkee7hOS7tueKtuaAgQ0KICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgIHRoaXMuY2hlY2tUcmVlUmVhZHlTdGF0dXMoKTsNCiAgICB9KTsNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAvLyDnm5HlkKzov4fmu6TmlofmnKzlj5jljJbvvIzosIPnlKjlkI7nq6/mjqXlj6PmkJzntKIgLSDnm7TmjqXku45XZWNoYXQudnVl5aSN5Yi2DQogICAgZmlsdGVyVGV4dCh2YWwpIHsNCiAgICAgIC8vIOa4hemZpOS5i+WJjeeahOmYsuaKluWumuaXtuWZqA0KICAgICAgaWYgKHRoaXMuc2VhcmNoRGVib3VuY2VUaW1lcikgew0KICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5zZWFyY2hEZWJvdW5jZVRpbWVyKTsNCiAgICAgIH0NCg0KICAgICAgLy8g6K6+572u6Ziy5oqW77yMNTAwbXPlkI7miafooYzmkJzntKINCiAgICAgIHRoaXMuc2VhcmNoRGVib3VuY2VUaW1lciA9IHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLmhhbmRsZVRyZWVTZWFyY2godmFsKTsNCiAgICAgIH0sIDUwMCk7DQogICAgfSwNCg0KICAgIC8vIOebkeWQrOetm+mAieadoeS7tuWPmOWMlg0KICAgICJTZWFjaERhdGEudGltZVJhbmdlIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwgbmV3VmFsID09PSBvbGRWYWwpIHJldHVybjsNCiAgICAgICAgdGhpcy5oYW5kbGVGaWx0ZXJDaGFuZ2UoKTsNCiAgICAgIH0sDQogICAgfSwNCiAgICAiU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZSI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgaWYgKCF0aGlzLmluaXRpYWxpemF0aW9uQ29tcGxldGVkIHx8IG5ld1ZhbCA9PT0gb2xkVmFsKSByZXR1cm47DQogICAgICAgIHRoaXMuaGFuZGxlRmlsdGVyQ2hhbmdlKCk7DQogICAgICB9LA0KICAgIH0sDQogICAgIlNlYWNoRGF0YS5pc1RlY2hub2xvZ3kiOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7DQogICAgICAgIGlmICghdGhpcy5pbml0aWFsaXphdGlvbkNvbXBsZXRlZCB8fCBuZXdWYWwgPT09IG9sZFZhbCkgcmV0dXJuOw0KICAgICAgICB0aGlzLmhhbmRsZUZpbHRlckNoYW5nZSgpOw0KICAgICAgfSwNCiAgICB9LA0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5Yid5aeL5YyW5pWw5o2uDQogICAgYXN5bmMgaW5pdGlhbGl6ZURhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDlhYjliqDovb3moJHmlbDmja4NCiAgICAgICAgYXdhaXQgdGhpcy5xdWVyeVRyZWVEYXRhKCk7DQoNCiAgICAgICAgLy8g562J5b6F5qCR57uE5Lu25a6M5YWo5riy5p+TDQogICAgICAgIGF3YWl0IHRoaXMuJG5leHRUaWNrKCk7DQoNCiAgICAgICAgLy8g5YaN5Yqg6L295paH56ug5YiX6KGo77yI5YaF6YOo5bey57uP5aSE55CG5LqGIHRhYmxlTG9hZGluZ++8iQ0KICAgICAgICBhd2FpdCB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIneWni+WMluaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWIneWni+WMluWksei0pe+8jOivt+WIt+aWsOmhtemdoumHjeivlSIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbnrZvpgInmnaHku7blj5jljJYgLSDnm7TmjqXku45XZWNoYXQudnVl5aSN5Yi2DQogICAgaGFuZGxlRmlsdGVyQ2hhbmdlKCkgew0KICAgICAgLy8g5riF56m65bem5L6n5qCR6YCJ5Lit54q25oCBDQogICAgICB0aGlzLmNsZWFyVHJlZVNlbGVjdGlvbnMoKTsNCg0KICAgICAgLy8g6YeN572u5YiG6aG15Yiw56ys5LiA6aG1DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsNCiAgICAgIHRoaXMudHJlZUN1cnJlbnRQYWdlID0gMTsNCg0KICAgICAgLy8g5rua5Yqo5Yiw6aG26YOoDQogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgIHRoaXMuc2Nyb2xsVHJlZVRvVG9wKCk7IC8vIOWPs+S+p+etm+mAieadoeS7tuWPmOWMluaXtuS5n+imgea7muWKqOW3puS+p+agkeWIsOmhtumDqA0KDQogICAgICAvLyDlkIzml7bmn6Xor6LmoJHlkozliJfooagNCiAgICAgIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvLyDmuIXnqbrmoJHpgInkuK3nirbmgIENCiAgICBjbGVhclRyZWVTZWxlY3Rpb25zKCkgew0KICAgICAgaWYgKHRoaXMuJHJlZnMudHJlZSkgew0KICAgICAgICB0aGlzLiRyZWZzLnRyZWUuc2V0Q2hlY2tlZEtleXMoW10pOw0KICAgICAgfQ0KICAgICAgdGhpcy5jaGVja0xpc3QgPSBbXTsNCiAgICB9LA0KDQogICAgLy8g5ZCM5pe25p+l6K+i5qCR5ZKM5YiX6KGoIC0g55u05o6l5LuOV2VjaGF0LnZ1ZeWkjeWItg0KICAgIGFzeW5jIHF1ZXJ5VHJlZUFuZExpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChbDQogICAgICAgICAgdGhpcy5xdWVyeVRyZWVEYXRhKCksDQogICAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCksIC8vIHF1ZXJ5QXJ0aWNsZUxpc3Qg5YaF6YOo5bey57uP5aSE55CG5LqGIHRhYmxlTG9hZGluZw0KICAgICAgICBdKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWQjOaXtuafpeivouagkeWSjOWIl+ihqOWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuafpeivouWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliIbpobXlpITnkIYNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKGN1cnJlbnQpIHsNCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSBjdXJyZW50Ow0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICB9LA0KDQogICAgaGFuZGxlU2l6ZUNoYW5nZShzaXplKSB7DQogICAgICB0aGlzLnBhZ2VTaXplID0gc2l6ZTsNCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICB9LA0KDQogICAgLy8g5qOA5p+l5qCR57uE5Lu25YeG5aSH54q25oCBDQogICAgY2hlY2tUcmVlUmVhZHlTdGF0dXMoKSB7DQogICAgICBsZXQgYXR0ZW1wdHMgPSAwOw0KICAgICAgY29uc3QgbWF4QXR0ZW1wdHMgPSA1MDsgLy8g5pyA5aSa5qOA5p+lMi4156eSDQoNCiAgICAgIGNvbnN0IGNoZWNrSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7DQogICAgICAgIGF0dGVtcHRzKys7DQoNCiAgICAgICAgaWYgKA0KICAgICAgICAgIHRoaXMuJHJlZnMudHJlZSAmJg0KICAgICAgICAgIHRoaXMudHJlZURhdGEgJiYNCiAgICAgICAgICB0aGlzLnRyZWVEYXRhWzBdICYmDQogICAgICAgICAgdGhpcy50cmVlRGF0YVswXS5jaGlsZHJlbiAmJg0KICAgICAgICAgIHRoaXMudHJlZURhdGFbMF0uY2hpbGRyZW4ubGVuZ3RoID4gMA0KICAgICAgICApIHsNCiAgICAgICAgICB0aGlzLnRyZWVSZWFkeSA9IHRydWU7DQogICAgICAgICAgY2xlYXJJbnRlcnZhbChjaGVja0ludGVydmFsKTsNCiAgICAgICAgfSBlbHNlIGlmIChhdHRlbXB0cyA+PSBtYXhBdHRlbXB0cykgew0KICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCk7DQogICAgICAgIH0NCiAgICAgIH0sIDUwKTsNCiAgICB9LA0KDQogICAgLy8g56Gu5L+d5qCR57uE5Lu25YeG5aSH5bCx57uqDQogICAgYXN5bmMgZW5zdXJlVHJlZVJlYWR5KCkgew0KICAgICAgaWYgKA0KICAgICAgICB0aGlzLnRyZWVSZWFkeSAmJg0KICAgICAgICB0aGlzLiRyZWZzLnRyZWUgJiYNCiAgICAgICAgdGhpcy50cmVlRGF0YVswXS5jaGlsZHJlbi5sZW5ndGggPiAwDQogICAgICApIHsNCiAgICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHsNCiAgICAgICAgbGV0IGF0dGVtcHRzID0gMDsNCiAgICAgICAgY29uc3QgbWF4QXR0ZW1wdHMgPSAxMDA7IC8vIOacgOWkmuetieW+hTXnp5INCg0KICAgICAgICBjb25zdCBjaGVja1RyZWUgPSAoKSA9PiB7DQogICAgICAgICAgYXR0ZW1wdHMrKzsNCg0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIHRoaXMuJHJlZnMudHJlZSAmJg0KICAgICAgICAgICAgdGhpcy50cmVlRGF0YSAmJg0KICAgICAgICAgICAgdGhpcy50cmVlRGF0YVswXSAmJg0KICAgICAgICAgICAgdGhpcy50cmVlRGF0YVswXS5jaGlsZHJlbiAmJg0KICAgICAgICAgICAgdGhpcy50cmVlRGF0YVswXS5jaGlsZHJlbi5sZW5ndGggPiAwDQogICAgICAgICAgKSB7DQogICAgICAgICAgICB0aGlzLnRyZWVSZWFkeSA9IHRydWU7DQoNCiAgICAgICAgICAgIHJlc29sdmUoKTsNCiAgICAgICAgICB9IGVsc2UgaWYgKGF0dGVtcHRzID49IG1heEF0dGVtcHRzKSB7DQogICAgICAgICAgICByZXNvbHZlKCk7IC8vIOi2heaXtuS5n+imgXJlc29sdmXvvIzpgb/lhY3ljaHmrbsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgc2V0VGltZW91dChjaGVja1RyZWUsIDUwKTsNCiAgICAgICAgICB9DQogICAgICAgIH07DQogICAgICAgIGNoZWNrVHJlZSgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDmn6Xor6LmoJHmlbDmja4NCiAgICBhc3luYyBxdWVyeVRyZWVEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIHBhZ2VOdW06IHRoaXMudHJlZUN1cnJlbnRQYWdlLA0KICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLnRyZWVQYWdlU2l6ZSwNCiAgICAgICAgICBwbGF0Zm9ybVR5cGU6IDEsDQogICAgICAgICAgaWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkIHx8IDEsDQogICAgICAgICAgbTogMSwNCiAgICAgICAgICBkYXRlVHlwZToNCiAgICAgICAgICAgIHRoaXMuU2VhY2hEYXRhLnRpbWVSYW5nZSAhPSA2ID8gdGhpcy5TZWFjaERhdGEudGltZVJhbmdlIDogIiIsDQogICAgICAgICAgc3RhcnRUaW1lOiB0aGlzLlNlYWNoRGF0YS5jdXN0b21EYXkNCiAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5WzBdDQogICAgICAgICAgICA6ICIiLA0KICAgICAgICAgIGVuZFRpbWU6IHRoaXMuU2VhY2hEYXRhLmN1c3RvbURheSA/IHRoaXMuU2VhY2hEYXRhLmN1c3RvbURheVsxXSA6ICIiLA0KICAgICAgICAgIGNvbGxlY3Rpb25EYXRlVHlwZToNCiAgICAgICAgICAgIHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZSAhPSA2DQogICAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvbkRhdGVUeXBlDQogICAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgY29sbGVjdGlvblN0YXJ0VGltZTogdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWUNCiAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWVbMF0NCiAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgY29sbGVjdGlvbkVuZFRpbWU6IHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25UaW1lDQogICAgICAgICAgICA/IHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25UaW1lWzFdDQogICAgICAgICAgICA6ICIiLA0KICAgICAgICAgIGtleXdvcmRzOiB0aGlzLlNlYWNoRGF0YS5rZXl3b3JkLA0KICAgICAgICAgIGlzVGVjaG5vbG9neTogdGhpcy5TZWFjaERhdGEuaXNUZWNobm9sb2d5LA0KICAgICAgICAgIC8vIOa3u+WKoOWFs+mUruWtl+i/h+a7pOWPguaVsA0KICAgICAgICAgIGZpbHRlcndvcmRzOiB0aGlzLnRyZWVRdWVyeS5maWx0ZXJ3b3JkcyB8fCAiIiwNCiAgICAgICAgICAvLyDmt7vliqAgYXJ0aWZpY2lhbEludGVsbGlnZW5jZSDnibnmnInnmoTlj4LmlbANCiAgICAgICAgICBtZW51VHlwZTogdGhpcy4kcm91dGUucXVlcnkubWVudVR5cGUsDQogICAgICAgIH07DQoNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgYXBpLm1vbml0b3JpbmdNZWRpdW0ocGFyYW1zKTsNCg0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIGNvbnN0IGRhdGFMaXN0ID0gcmVzLnJvd3MgfHwgW107DQogICAgICAgICAgY29uc3QgdG90YWwgPSByZXMudG90YWwgfHwgMDsNCg0KICAgICAgICAgIGNvbnN0IG1hcERhdGEgPSAoZGF0YSkgPT4NCiAgICAgICAgICAgIGRhdGEubWFwKChpdGVtLCBpbmRleCkgPT4gKHsNCiAgICAgICAgICAgICAgaWQ6IGl0ZW0uc291cmNlU24gfHwgYG5vZGVfJHtpbmRleH1gLCAvLyDkvb/nlKhzb3VyY2VTbuS9nOS4uklE77yM56Gu5L+d5ZSv5LiA5oCnDQogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmNuTmFtZSwNCiAgICAgICAgICAgICAgY291bnQ6IGl0ZW0uYXJ0aWNsZUNvdW50IHx8IDAsDQogICAgICAgICAgICAgIG9yZGVyTnVtOiBpdGVtLm9yZGVyTnVtLA0KICAgICAgICAgICAgICBjb3VudHJ5OiBpdGVtLmNvdW50cnlPZk9yaWdpbiwNCiAgICAgICAgICAgICAgc291cmNlU246IGl0ZW0uc291cmNlU24sDQogICAgICAgICAgICAgIHVybDogaXRlbS51cmwgfHwgbnVsbCwNCiAgICAgICAgICAgIH0pKTsNCg0KICAgICAgICAgIHRoaXMudHJlZURhdGEgPSBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGlkOiAxMDAwLA0KICAgICAgICAgICAgICBsYWJlbDogIuaVsOaNrua6kCIsDQogICAgICAgICAgICAgIGNoaWxkcmVuOiBtYXBEYXRhKGRhdGFMaXN0KSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgXTsNCiAgICAgICAgICB0aGlzLnRyZWVEYXRhVHJhbnNmZXIgPSBtYXBEYXRhKGRhdGFMaXN0KTsNCiAgICAgICAgICB0aGlzLnRyZWVUb3RhbCA9IHRvdGFsOw0KDQogICAgICAgICAgLy8g562J5b6FRE9N5pu05paw5ZCO5qCH6K6w5qCR57uE5Lu25Li65YeG5aSH5bCx57uqDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgLy8g566A5Y2V5bu26L+f77yM6K6pbW91bnRlZOS4reeahOajgOafpeadpeWkhOeQhuWHhuWkh+eKtuaAgQ0KICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgIC8vIOS4jeWcqOi/memHjOiuvue9rnRyZWVSZWFkee+8jOiuqW1vdW50ZWTkuK3nmoTmo4Dmn6XmnaXlpITnkIYNCiAgICAgICAgICAgIH0sIDEwMCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuafpeivouagkeaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluaVsOaNrua6kOWksei0pSIpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5p+l6K+i5paH56ug5YiX6KGo77yI5bim6Ziy5oqW77yJDQogICAgYXN5bmMgcXVlcnlBcnRpY2xlTGlzdCgpIHsNCiAgICAgIC8vIOmYsuatoumHjeWkjeafpeivog0KICAgICAgaWYgKHRoaXMuaXNRdWVyeWluZykgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOeri+WNs+aYvuekumxvYWRpbmfnirbmgIENCiAgICAgIHRoaXMudGFibGVMb2FkaW5nID0gdHJ1ZTsNCg0KICAgICAgLy8g5riF6Zmk5LmL5YmN55qE6Ziy5oqW5a6a5pe25ZmoDQogICAgICBpZiAodGhpcy5xdWVyeURlYm91bmNlVGltZXIpIHsNCiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucXVlcnlEZWJvdW5jZVRpbWVyKTsNCiAgICAgIH0NCg0KICAgICAgLy8g6K6+572u6Ziy5oqW77yMMzAwbXPlkI7miafooYzmn6Xor6INCiAgICAgIHRoaXMucXVlcnlEZWJvdW5jZVRpbWVyID0gc2V0VGltZW91dChhc3luYyAoKSA9PiB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy5pc1F1ZXJ5aW5nID0gdHJ1ZTsNCg0KICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICAgIG06IDEsDQogICAgICAgICAgICBwYWdlTnVtOiB0aGlzLmN1cnJlbnRQYWdlLA0KICAgICAgICAgICAgcGFnZVNpemU6IHRoaXMucGFnZVNpemUsDQogICAgICAgICAgICBpZDogdGhpcy4kcm91dGUucXVlcnkuaWQgfHwgMSwNCiAgICAgICAgICAgIGlzU29ydDogdGhpcy5TZWFjaERhdGEuc29ydE1vZGUsDQogICAgICAgICAgICBkYXRlVHlwZToNCiAgICAgICAgICAgICAgdGhpcy5TZWFjaERhdGEudGltZVJhbmdlICE9IDYgPyB0aGlzLlNlYWNoRGF0YS50aW1lUmFuZ2UgOiAiIiwNCiAgICAgICAgICAgIHN0YXJ0VGltZTogdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5DQogICAgICAgICAgICAgID8gdGhpcy5TZWFjaERhdGEuY3VzdG9tRGF5WzBdDQogICAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgICBlbmRUaW1lOiB0aGlzLlNlYWNoRGF0YS5jdXN0b21EYXkNCiAgICAgICAgICAgICAgPyB0aGlzLlNlYWNoRGF0YS5jdXN0b21EYXlbMV0NCiAgICAgICAgICAgICAgOiAiIiwNCiAgICAgICAgICAgIGNvbGxlY3Rpb25EYXRlVHlwZToNCiAgICAgICAgICAgICAgdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvbkRhdGVUeXBlICE9IDYNCiAgICAgICAgICAgICAgICA/IHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25EYXRlVHlwZQ0KICAgICAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgICBjb2xsZWN0aW9uU3RhcnRUaW1lOiB0aGlzLlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZQ0KICAgICAgICAgICAgICA/IHRoaXMuU2VhY2hEYXRhLmNvbGxlY3Rpb25UaW1lWzBdDQogICAgICAgICAgICAgIDogIiIsDQogICAgICAgICAgICBjb2xsZWN0aW9uRW5kVGltZTogdGhpcy5TZWFjaERhdGEuY29sbGVjdGlvblRpbWUNCiAgICAgICAgICAgICAgPyB0aGlzLlNlYWNoRGF0YS5jb2xsZWN0aW9uVGltZVsxXQ0KICAgICAgICAgICAgICA6ICIiLA0KICAgICAgICAgICAga2V5d29yZHM6IHRoaXMuU2VhY2hEYXRhLmtleXdvcmQsDQogICAgICAgICAgICBpc1RlY2hub2xvZ3k6IHRoaXMuU2VhY2hEYXRhLmlzVGVjaG5vbG9neSwNCiAgICAgICAgICAgIHBsYXRmb3JtVHlwZTogMSwNCiAgICAgICAgICAgIC8vIOa3u+WKoCBhcnRpZmljaWFsSW50ZWxsaWdlbmNlIOeJueacieeahOWPguaVsA0KICAgICAgICAgICAgbWVudVR5cGU6IHRoaXMuJHJvdXRlLnF1ZXJ5Lm1lbnVUeXBlLA0KICAgICAgICAgIH07DQoNCiAgICAgICAgICAvLyDlpoLmnpzmnInpgInkuK3nmoTmlbDmja7mupDvvIzmt7vliqDmlbDmja7mupDlj4LmlbAgLSDnm7TmjqXku45XZWNoYXQudnVl5aSN5Yi2DQogICAgICAgICAgaWYgKHRoaXMuY2hlY2tMaXN0ICYmIHRoaXMuY2hlY2tMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSB0aGlzLmNoZWNrTGlzdC5tYXAoKGl0ZW0pID0+IGl0ZW0ubGFiZWwpOw0KICAgICAgICAgICAgY29uc3Qgc291cmNlU24gPSBkYXRhDQogICAgICAgICAgICAgIC5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgICBjb25zdCBmb3VuZEl0ZW0gPSB0aGlzLnRyZWVEYXRhVHJhbnNmZXIuZmluZCgNCiAgICAgICAgICAgICAgICAgIChyb3cpID0+IHJvdy5sYWJlbCA9PT0gaXRlbQ0KICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgICAgcmV0dXJuIGZvdW5kSXRlbSA/IGZvdW5kSXRlbS5zb3VyY2VTbiA6IG51bGw7DQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIC5maWx0ZXIoKHNuKSA9PiBzbiAhPT0gbnVsbCk7DQoNCiAgICAgICAgICAgIHBhcmFtcy53ZUNoYXROYW1lID0gU3RyaW5nKGRhdGEpOw0KICAgICAgICAgICAgcGFyYW1zLnNvdXJjZVNuID0gU3RyaW5nKHNvdXJjZVNuKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDorrDlvZXlhbPplK7or43ljoblj7INCiAgICAgICAgICBpZiAocGFyYW1zLmtleXdvcmRzKSB7DQogICAgICAgICAgICBhZGRBcnRpY2xlSGlzdG9yeSh7IGtleXdvcmQ6IHBhcmFtcy5rZXl3b3JkcywgdHlwZTogMiB9KS50aGVuKA0KICAgICAgICAgICAgICAoKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICApOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGFwaS5LZUludGVncmF0aW9uKHsgLi4ucGFyYW1zLCAuLi50aGlzLnRyZWVRdWVyeSB9KTsNCg0KICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuQXJ0aWNsZUxpc3QgPSByZXMuZGF0YS5saXN0IHx8IFtdOw0KICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDA7DQoNCiAgICAgICAgICAgIC8vIOWkhOeQhuWIhumhteS4uuepuueahOaDheWGtQ0KICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICB0aGlzLkFydGljbGVMaXN0Lmxlbmd0aCA9PSAwICYmDQogICAgICAgICAgICAgIHRoaXMucGFnZVNpemUgKiAodGhpcy5jdXJyZW50UGFnZSAtIDEpID49IHRoaXMudG90YWwgJiYNCiAgICAgICAgICAgICAgdGhpcy50b3RhbCAhPSAwDQogICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IE1hdGgubWF4KA0KICAgICAgICAgICAgICAgIDEsDQogICAgICAgICAgICAgICAgTWF0aC5jZWlsKHRoaXMudG90YWwgLyB0aGlzLnBhZ2VTaXplKQ0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICAvLyDph43mlrDmn6Xor6INCiAgICAgICAgICAgICAgYXdhaXQgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICAgICAgICAgIHJldHVybjsgLy8g6YeN5paw5p+l6K+i5pe25LiN6KaB5YWz6ZetbG9hZGluZw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgIuiOt+WPluaVsOaNruWksei0pSIpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLmn6Xor6Lmlofnq6DliJfooajlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuafpeivouWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgICB9IGZpbmFsbHkgew0KICAgICAgICAgIHRoaXMuaXNRdWVyeWluZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMudGFibGVMb2FkaW5nID0gZmFsc2U7IC8vIOafpeivouWujOaIkOWQjuWFs+mXrWxvYWRpbmcNCiAgICAgICAgICB0aGlzLmJ1dHRvbkRpc2FibGVkID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0sIDMwMCk7DQogICAgfSwNCg0KICAgIC8vIOa7muWKqOWIsOmhtumDqA0KICAgIHNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKSB7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIC8vIOWwneivleWkmuenjea7muWKqOaWueW8j+ehruS/nea7muWKqOaIkOWKnw0KICAgICAgICBjb25zdCBzY3JvbGxCb3hFbGVtZW50ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiLnNjb2xsQm94Iik7DQogICAgICAgIGlmIChzY3JvbGxCb3hFbGVtZW50KSB7DQogICAgICAgICAgc2Nyb2xsQm94RWxlbWVudC5zY3JvbGxUb3AgPSAwOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aaC5p6cTWFpbkFydGljbGXnu4Tku7bmnIlzY3JvbGzlvJXnlKjvvIzkuZ/lsJ3or5Xmu5rliqjlroMNCiAgICAgICAgaWYgKA0KICAgICAgICAgIHRoaXMuJHJlZnMubWFpbkFydGljbGUgJiYNCiAgICAgICAgICB0aGlzLiRyZWZzLm1haW5BcnRpY2xlLiRyZWZzICYmDQogICAgICAgICAgdGhpcy4kcmVmcy5tYWluQXJ0aWNsZS4kcmVmcy5zY3JvbGwNCiAgICAgICAgKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5tYWluQXJ0aWNsZS4kcmVmcy5zY3JvbGwuc2Nyb2xsVG9wID0gMDsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOa7muWKqOaVtOS4quWPs+S+p+WMuuWfnw0KICAgICAgICBjb25zdCByaWdodE1haW4gPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIucmlnaHRNYWluIik7DQogICAgICAgIGlmIChyaWdodE1haW4pIHsNCiAgICAgICAgICByaWdodE1haW4uc2Nyb2xsVG9wID0gMDsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOa7muWKqOW3puS+p+agkeWIsOmhtumDqA0KICAgIHNjcm9sbFRyZWVUb1RvcCgpIHsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgY29uc3QgdHJlZUJveCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIi50cmVlQm94Iik7DQogICAgICAgIGlmICh0cmVlQm94KSB7DQogICAgICAgICAgdHJlZUJveC5zY3JvbGxUb3AgPSAwOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOagkeiKgueCueWLvumAieWkhOeQhu+8iOS9v+eUqGNoZWNr5LqL5Lu277yJLSDnm7TmjqXku45XZWNoYXQudnVl5aSN5Yi2DQogICAgaGFuZGxlVHJlZUNoZWNrKGRhdGEsIGNoZWNrZWRJbmZvKSB7DQogICAgICAvLyDlpoLmnpzmoJHnu4Tku7bov5jmsqHlh4blpIflpb3vvIznm7TmjqXov5Tlm54NCiAgICAgIGlmICghdGhpcy50cmVlUmVhZHkgfHwgIXRoaXMuJHJlZnMudHJlZSkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIGNoZWNr5LqL5Lu255qE5Y+C5pWw77yaDQogICAgICAvLyBkYXRhOiDlvZPliY3ngrnlh7vnmoToioLngrnmlbDmja4NCiAgICAgIC8vIGNoZWNrZWRJbmZvOiB7IGNoZWNrZWROb2RlcywgY2hlY2tlZEtleXMsIGhhbGZDaGVja2VkTm9kZXMsIGhhbGZDaGVja2VkS2V5cyB9DQoNCiAgICAgIC8vIOiOt+WPluaJgOaciemAieS4reeahOiKgueCue+8iOaOkumZpOagueiKgueCue+8iQ0KICAgICAgY29uc3QgYWxsQ2hlY2tlZE5vZGVzID0gY2hlY2tlZEluZm8uY2hlY2tlZE5vZGVzIHx8IFtdOw0KICAgICAgY29uc3Qgc2VsZWN0ZWROb2RlcyA9IGFsbENoZWNrZWROb2Rlcy5maWx0ZXIoDQogICAgICAgIChub2RlKSA9PiBub2RlLmlkICE9PSAxMDAwICYmIG5vZGUubGFiZWwgIT09ICLmlbDmja7mupAiDQogICAgICApOw0KDQogICAgICAvLyDmm7TmlrDpgInkuK3liJfooagNCiAgICAgIHRoaXMuY2hlY2tMaXN0ID0gc2VsZWN0ZWROb2Rlcy5tYXAoKG5vZGUpID0+ICh7IC4uLm5vZGUgfSkpOw0KDQogICAgICAvLyDph43nva7pobXnoIHlubbmn6Xor6LlhoXlrrkgLSDkv67mlLnkuLpLZU1vbml0b3LnmoTliIbpobXlj4LmlbANCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICB9LA0KDQogICAgLy8gY2hlY2tDaGFuZ2Xmlrnms5UgLSDnm7TmjqXku45XZWNoYXQudnVl5aSN5Yi2DQogICAgYXN5bmMgY2hlY2tDaGFuZ2UoaXRlbSwgaXNDaGVjaykgew0KICAgICAgLy8g5aaC5p6c5qCR57uE5Lu26L+Y5rKh5YeG5aSH5aW977yM55u05o6l6L+U5Zue77yM5LiN5aSE55CG5Yu+6YCJDQogICAgICBpZiAoIXRoaXMudHJlZVJlYWR5IHx8ICF0aGlzLiRyZWZzLnRyZWUpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlu7bov5/miafooYzvvIznoa7kv51FbGVtZW50IFVJ5qCR57uE5Lu25YaF6YOo54q25oCB5bey57uP5pu05pawDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgLy8g5aaC5p6c5Yu+6YCJ55qE5piv5pWw5o2u5rqQ5qC56IqC54K577yM5aSE55CG5YWo6YCJL+WPlua2iOWFqOmAiQ0KICAgICAgICBpZiAoaXRlbS5pZCA9PT0gMTAwMCB8fCBpdGVtLmxhYmVsID09PSAi5pWw5o2u5rqQIikgew0KICAgICAgICAgIGlmIChpc0NoZWNrKSB7DQogICAgICAgICAgICAvLyDlhajpgInvvJrpgInmi6nmiYDmnInlj6/op4HnmoTlrZDoioLngrkNCiAgICAgICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gWy4uLnRoaXMudHJlZURhdGFbMF0uY2hpbGRyZW5dOw0KICAgICAgICAgICAgY29uc3QgYWxsSWRzID0gdGhpcy50cmVlRGF0YVswXS5jaGlsZHJlbi5tYXAoKG5vZGUpID0+IG5vZGUuaWQpOw0KDQogICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWUuc2V0Q2hlY2tlZEtleXMoYWxsSWRzKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5Y+W5raI5YWo6YCJ77ya5riF56m65omA5pyJ6YCJ5oupDQogICAgICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFtdOw0KICAgICAgICAgICAgdGhpcy4kcmVmcy50cmVlLnNldENoZWNrZWRLZXlzKFtdKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5aSE55CG5Y2V5Liq6IqC54K555qE5Yu+6YCJL+WPlua2iOWLvumAiQ0KDQogICAgICAgICAgaWYgKGlzQ2hlY2spIHsNCiAgICAgICAgICAgIC8vIOa3u+WKoOWIsOmAieS4reWIl+ihqO+8iOWkmumAie+8iQ0KICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdJbmRleCA9IHRoaXMuY2hlY2tMaXN0LmZpbmRJbmRleCgNCiAgICAgICAgICAgICAgKHJvdykgPT4gcm93LmxhYmVsID09PSBpdGVtLmxhYmVsDQogICAgICAgICAgICApOw0KICAgICAgICAgICAgaWYgKGV4aXN0aW5nSW5kZXggPT09IC0xKSB7DQogICAgICAgICAgICAgIHRoaXMuY2hlY2tMaXN0LnB1c2goeyAuLi5pdGVtIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDku47pgInkuK3liJfooajkuK3np7vpmaQNCiAgICAgICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gdGhpcy5jaGVja0xpc3QuZmlsdGVyKA0KICAgICAgICAgICAgICAocm93KSA9PiByb3cubGFiZWwgIT09IGl0ZW0ubGFiZWwNCiAgICAgICAgICAgICk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6YeN572u6aG156CB5bm25p+l6K+i5YaF5a65IC0g5L+u5pS55Li6S2VNb25pdG9y55qE5YiG6aG15Y+C5pWwDQogICAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOw0KICAgICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICB9LCA1MCk7IC8vIOW7tui/nzUwbXPmiafooYwNCiAgICB9LA0KDQogICAgLy8g5qCR6IqC54K554K55Ye75aSE55CG77yI5re35ZCI6YCJ5oup5qih5byP77yJLSDnm7TmjqXku45XZWNoYXQudnVl5aSN5Yi2DQogICAgYXN5bmMgdHJlZU5vZGVDbGljayhpdGVtKSB7DQogICAgICAvLyDlpoLmnpzngrnlh7vnmoTmmK/mlbDmja7mupDmoLnoioLngrnvvIzkuI3ov5vooYzmk43kvZwNCiAgICAgIGlmIChpdGVtLmlkID09PSAxMDAwIHx8IGl0ZW0ubGFiZWwgPT09ICLmlbDmja7mupAiKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5qCR57uE5Lu26L+Y5rKh5YeG5aSH5aW977yM55u05o6l6L+U5Zue77yM5LiN5aSE55CG54K55Ye7DQogICAgICBpZiAoIXRoaXMudHJlZVJlYWR5IHx8ICF0aGlzLiRyZWZzLnRyZWUpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlhYjmu5rliqjliLDpobbpg6gNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KDQogICAgICAvLyDlu7bov5/miafooYzvvIznoa7kv51FbGVtZW50IFVJ5qCR57uE5Lu25YaF6YOo54q25oCB5bey57uP5pu05pawDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey6YCJ5Lit6K+l6IqC54K5DQogICAgICAgIGNvbnN0IGV4aXN0aW5nSW5kZXggPSB0aGlzLmNoZWNrTGlzdC5maW5kSW5kZXgoDQogICAgICAgICAgKHJvdykgPT4gcm93LmxhYmVsID09PSBpdGVtLmxhYmVsDQogICAgICAgICk7DQoNCiAgICAgICAgaWYgKGV4aXN0aW5nSW5kZXggIT09IC0xKSB7DQogICAgICAgICAgLy8g5aaC5p6c5b2T5YmN6aG55bey6KKr6YCJ5Lit77yM5YiZ5Y+q5Y+W5raI6K+l6aG555qE6YCJ5oup77yI5L+d5oyB5YW25LuW6YCJ5oup77yJDQogICAgICAgICAgdGhpcy5jaGVja0xpc3QgPSB0aGlzLmNoZWNrTGlzdC5maWx0ZXIoDQogICAgICAgICAgICAocm93KSA9PiByb3cubGFiZWwgIT09IGl0ZW0ubGFiZWwNCiAgICAgICAgICApOw0KDQogICAgICAgICAgLy8g5pu05paw5qCR57uE5Lu255qE6YCJ5Lit54q25oCBDQogICAgICAgICAgY29uc3QgcmVtYWluaW5nSWRzID0gdGhpcy5jaGVja0xpc3QubWFwKChub2RlKSA9PiBub2RlLmlkKTsNCiAgICAgICAgICB0aGlzLiRyZWZzLnRyZWUuc2V0Q2hlY2tlZEtleXMocmVtYWluaW5nSWRzKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlpoLmnpzlvZPliY3pobnmnKrooqvpgInkuK3vvIzmuIXnqbrmiYDmnInpgInmi6nvvIzorr7nva7kuLrllK/kuIDpgInkuK3pobnvvIjljZXpgInvvIkNCiAgICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFt7IC4uLml0ZW0gfV07DQoNCiAgICAgICAgICAvLyDmm7TmlrDmoJHnu4Tku7bnmoTpgInkuK3nirbmgIENCiAgICAgICAgICB0aGlzLiRyZWZzLnRyZWUuc2V0Q2hlY2tlZEtleXMoW2l0ZW0uaWRdKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmHjee9rumhteeggeW5tuafpeivouWGheWuuSAtIOS/ruaUueS4uktlTW9uaXRvcueahOWIhumhteWPguaVsA0KICAgICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsNCiAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICB9LCA1MCk7IC8vIOW7tui/nzUwbXPmiafooYwNCiAgICB9LA0KICAgIC8vIOmHjee9ruagkemAieaLqSAtIOebtOaOpeS7jldlY2hhdC52dWXlpI3liLYNCiAgICB0cmVlQ2xlYXIoKSB7DQogICAgICAvLyDmuIXnqbrpgInkuK3nirbmgIENCiAgICAgIHRoaXMuY2xlYXJUcmVlU2VsZWN0aW9ucygpOw0KDQogICAgICAvLyDph43nva7pobXnoIHlubbmn6Xor6LliJfooajmlbDmja4gLSDkv67mlLnkuLpLZU1vbml0b3LnmoTliIbpobXlj4LmlbANCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICB0aGlzLnNjcm9sbFRyZWVUb1RvcCgpOw0KICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuagkeaQnOe0oiAtIOebtOaOpeS7jldlY2hhdC52dWXlpI3liLblubbpgILphY1LZU1vbml0b3INCiAgICBhc3luYyBoYW5kbGVUcmVlU2VhcmNoKGtleXdvcmQpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOagkeaQnOe0ouS4jeW6lOivpeW9seWTjeWPs+S+p+WIl+ihqOeahGxvYWRpbmfnirbmgIENCiAgICAgICAgLy8gdGhpcy50YWJsZUxvYWRpbmcgPSB0cnVlOyAvLyDnp7vpmaTov5nooYwNCg0KICAgICAgICAvLyDmm7TmlrDmkJzntKLlhbPplK7lrZcNCiAgICAgICAgdGhpcy50cmVlUXVlcnkuZmlsdGVyd29yZHMgPSBrZXl3b3JkIHx8ICIiOw0KDQogICAgICAgIGlmIChrZXl3b3JkKSB7DQogICAgICAgICAgLy8g5L2/55SoZmlsdGVyd29yZHPlj4LmlbDosIPnlKjmoJHmjqXlj6Pov5vooYzmkJzntKINCiAgICAgICAgICB0aGlzLnNjcm9sbFRyZWVUb1RvcCgpOyAvLyDlhbPplK7lrZfov4fmu6Tml7bmu5rliqjliLDpobbpg6gNCiAgICAgICAgICBhd2FpdCB0aGlzLnF1ZXJ5VHJlZURhdGEoKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlpoLmnpzmkJzntKLlhbPplK7lrZfkuLrnqbrvvIzph43mlrDojrflj5bljp/lp4vmlbDmja4NCiAgICAgICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICAgICAgdGhpcy5zY3JvbGxUcmVlVG9Ub3AoKTsgLy8g5riF56m65YWz6ZSu5a2X5pe25rua5Yqo5Yiw6aG26YOoDQogICAgICAgICAgYXdhaXQgdGhpcy5xdWVyeVRyZWVEYXRhKCk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuaQnOe0ouagkeiKgueCueWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaQnOe0ouWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgfQ0KICAgICAgLy8g5LiN6ZyA6KaBZmluYWxseeWdl+adpeWFs+mXrXRhYmxlTG9hZGluZw0KICAgIH0sDQoNCiAgICAvLyDkv53nlZnljp/mnInnmoTlv4XopoHmlrnms5UNCiAgICBTd2l0Y2hJbmZvKGRhdGEpIHsNCiAgICAgIHRoaXMuaXNJbmZvID0gZGF0YTsNCiAgICB9LA0KDQogICAgc2VuaW9yU2VyY2goKSB7DQogICAgICB0aGlzLnNlbmlvclNlcmNoRmxhZyA9ICF0aGlzLnNlbmlvclNlcmNoRmxhZzsNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0QXJlYSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IFJlc3BvbnNlID0gYXdhaXQgYXBpLmdldEFyZWFMaXN0KCk7DQogICAgICAgIGlmIChSZXNwb25zZSAmJiBSZXNwb25zZS5jb2RlID09IDIwMCAmJiBSZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5hcmVhTGlzdCA9IFJlc3BvbnNlLmRhdGFbMF0gfHwgW107DQogICAgICAgICAgdGhpcy5jb3VudHJ5TGlzdCA9IFJlc3BvbnNlLmRhdGFbMV0gfHwgW107DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCLojrflj5blnLDljLrmlbDmja7lpLHotKXmiJbmlbDmja7kuLrnqboiKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluWMuuWfn+aVsOaNruWksei0pToiLCBlcnIpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlnLDljLrmlbDmja7ojrflj5blpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0QXJ0aWNsZUhpc3RvcnkoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBsaXN0QXJ0aWNsZUhpc3Rvcnkoew0KICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgcGFnZVNpemU6IDUsDQogICAgICAgICAgdHlwZTogMiwNCiAgICAgICAgfSk7DQogICAgICAgIGlmIChyZXMgJiYgcmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuaGlzdG9yeUxpc3QgPSByZXMucm93cyB8fCBbXTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5Y6G5Y+y6K6w5b2V5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5qCR6IqC54K56L+H5ruk5pa55rOVDQogICAgZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgew0KICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7DQogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTE7DQogICAgfSwNCg0KICAgIC8vIOa3u+WKoEVzU2VhY2jmlrnms5Xku6Xlhbzlrrnljp/mnInosIPnlKgNCiAgICBFc1NlYWNoKGZsYWcpIHsNCiAgICAgIGlmIChmbGFnID09PSAiZmlsdGVyIikgew0KICAgICAgICAvLyDnrZvpgInlj5jljJbml7blkIzml7bmn6Xor6LmoJHlkozliJfooagNCiAgICAgICAgdGhpcy5xdWVyeVRyZWVBbmRMaXN0KCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlhbbku5bmg4XlhrXlj6rmn6Xor6LliJfooagNCiAgICAgICAgdGhpcy5xdWVyeUFydGljbGVMaXN0KCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOmHjee9ruaQnOe0ouadoeS7tiAtIOeugOWMlueJiOacrA0KICAgIHJlc2V0dGluZygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuU2VhY2hEYXRhID0gew0KICAgICAgICAgIG1ldGFNb2RlOiAiIiAvKiDljLnphY3mqKHlvI8gKi8sDQogICAgICAgICAga2V5d29yZDogIiIgLyog5YWz6ZSu6K+NICovLA0KICAgICAgICAgIHRpbWVSYW5nZTogIiIgLyog5pe26Ze06IyD5Zu0ICovLA0KICAgICAgICAgIGN1c3RvbURheTogW10gLyog6Ieq5a6a5LmJ5aSpICovLA0KICAgICAgICAgIGNvbGxlY3Rpb25EYXRlVHlwZTogbnVsbCAvKiDml7bpl7TojIPlm7QgKi8sDQogICAgICAgICAgY29sbGVjdGlvblRpbWU6IFtdIC8qIOiHquWumuS5ieWkqSAqLywNCiAgICAgICAgICBpc1RlY2hub2xvZ3k6ICIxIiwNCiAgICAgICAgICBzb3J0TW9kZTogIjAiLA0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOw0KICAgICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgICAgdGhpcy5xdWVyeVRyZWVBbmRMaXN0KCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLph43nva7mkJzntKLmnaHku7bml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLph43nva7mkJzntKLmnaHku7blpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIHJlbW92ZUhpc3RvcnkoaXRlbSwgdHlwZSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKHRoaXMuaGlzdG9yeVRpbWVvdXQpIHsNCiAgICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5oaXN0b3J5VGltZW91dCk7DQogICAgICAgIH0NCg0KICAgICAgICBpZiAoaXRlbSAmJiBpdGVtLmlkKSB7DQogICAgICAgICAgYXdhaXQgZGVsQXJ0aWNsZUhpc3RvcnkoW2l0ZW0uaWRdKTsNCg0KICAgICAgICAgIGlmICh0eXBlID09IDEpIHsNCiAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzWyJrZXl3b3JkUmVmIl0pIHsNCiAgICAgICAgICAgICAgdGhpcy4kcmVmc1sia2V5d29yZFJlZiJdLmZvY3VzKCk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBhd2FpdCB0aGlzLmdldEFydGljbGVIaXN0b3J5KCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkoKTsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkxKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliKDpmaTljoblj7LorrDlvZXml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliKDpmaTljoblj7LorrDlvZXlpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgc2hvd0hpc3RvcnlMaXN0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5zaG93SGlzdG9yeSA9IHRydWU7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLmmL7npLrljoblj7LliJfooajml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoaWRlSGlzdG9yeUxpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICBpZiAodGhpcy5oaXN0b3J5VGltZW91dCkgew0KICAgICAgICAgIGNsZWFyVGltZW91dCh0aGlzLmhpc3RvcnlUaW1lb3V0KTsNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMuaGlzdG9yeVRpbWVvdXQgPSBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLnNob3dIaXN0b3J5ID0gZmFsc2U7DQogICAgICAgIH0sIDUwMCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLpmpDol4/ljoblj7LliJfooajml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLnNob3dIaXN0b3J5ID0gZmFsc2U7IC8vIOehruS/neWcqOWHuumUmeaXtuS5n+iDvemakOiXj+WIl+ihqA0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlhbPplK7or43ljoblj7LpgInmi6kgLSDnm7TmjqXku45XZWNoYXQudnVl5aSN5Yi2DQogICAga2V5d29yZHNDaGFuZ2UoaXRlbSkgew0KICAgICAgdGhpcy5TZWFjaERhdGEua2V5d29yZCA9IGl0ZW0ua2V5d29yZDsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZTEgPSBmYWxzZTsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLnF1ZXJ5VHJlZUFuZExpc3QoKTsNCiAgICB9LA0KDQogICAgYXN5bmMgY2xlYXJIaXN0b3J5KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgaWYgKHRoaXMuaGlzdG9yeVRpbWVvdXQpIHsNCiAgICAgICAgICBjbGVhclRpbWVvdXQodGhpcy5oaXN0b3J5VGltZW91dCk7DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy4kcmVmc1sia2V5d29yZFJlZiJdKSB7DQogICAgICAgICAgdGhpcy4kcmVmc1sia2V5d29yZFJlZiJdLmZvY3VzKCk7DQogICAgICAgIH0NCg0KICAgICAgICBhd2FpdCBjbGVhbkFydGljbGVIaXN0b3J5KDIpOw0KICAgICAgICBhd2FpdCB0aGlzLmdldEFydGljbGVIaXN0b3J5KCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLmuIXpmaTljoblj7LorrDlvZXml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmuIXpmaTljoblj7LorrDlvZXlpLHotKUiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgbW9yZUhpc3RvcnkoKSB7DQogICAgICB0cnkgew0KICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeTEoKTsNCiAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlMSA9IHRydWU7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliqDovb3mm7TlpJrljoblj7LorrDlvZXml7blh7rplJk6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIGdldEFydGljbGVIaXN0b3J5MSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSB0cnVlOw0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGxpc3RBcnRpY2xlSGlzdG9yeSh7DQogICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtczEsDQogICAgICAgICAgdHlwZTogMiwNCiAgICAgICAgfSk7DQoNCiAgICAgICAgaWYgKHJlc3BvbnNlKSB7DQogICAgICAgICAgdGhpcy5oaXN0b3J5TGlzdDEgPSByZXNwb25zZS5yb3dzIHx8IFtdOw0KICAgICAgICAgIHRoaXMudG90YWwxID0gcmVzcG9uc2UudG90YWwgfHwgMDsNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluaWh+eroOWOhuWPsuiusOW9leaXtuWHuumUmToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+W5pCc57Si5Y6G5Y+y5aSx6LSlIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIG9wZW5VcmwodXJsKSB7DQogICAgICB3aW5kb3cub3Blbih1cmwsICJfYmxhbmsiKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG5qCR5b2i5YiG6aG16aG156CB5Y+Y5YyWDQogICAgaGFuZGxlVHJlZUN1cnJlbnRDaGFuZ2UocGFnZSkgew0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSBwYWdlOw0KICAgICAgdGhpcy5jbGVhclRyZWVTZWxlY3Rpb25zKCk7DQogICAgICB0aGlzLnNjcm9sbFRyZWVUb1RvcCgpOyAvLyDlt6bkvqfmoJHnv7vpobXml7bmu5rliqjliLDpobbpg6gNCiAgICAgIHRoaXMucXVlcnlUcmVlRGF0YSgpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbmoJHlvaLliIbpobXmr4/pobXlpKflsI/lj5jljJYNCiAgICBoYW5kbGVUcmVlUGFnZVNpemVDaGFuZ2Uoc2l6ZSkgew0KICAgICAgdGhpcy50cmVlUGFnZVNpemUgPSBzaXplOw0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5jbGVhclRyZWVTZWxlY3Rpb25zKCk7DQogICAgICB0aGlzLnNjcm9sbFRyZWVUb1RvcCgpOyAvLyDlt6bkvqfmoJHpobXpnaLlpKflsI/lj5jljJbml7bmu5rliqjliLDpobbpg6gNCiAgICAgIHRoaXMucXVlcnlUcmVlRGF0YSgpOw0KICAgIH0sDQoNCiAgICAvLyDmt7vliqDnvLrlpLHnmoRvcGVuTmV3Vmlld+aWueazlQ0KICAgIG9wZW5OZXdWaWV3KGl0ZW0pIHsNCiAgICAgIHdpbmRvdy5vcGVuKA0KICAgICAgICBgL2V4cHJlc3NEZXRhaWxzP2lkPSR7aXRlbS5pZH0mZG9jSWQ9JHtpdGVtLmRvY0lkfSZzb3VyY2VUeXBlPSR7aXRlbS5zb3VyY2VUeXBlfWAsDQogICAgICAgICJfYmxhbmsiDQogICAgICApOw0KICAgIH0sDQoNCiAgICAvLyDmt7vliqDnvLrlpLHnmoRoYW5kbGVIaXN0b3J5UGFnaW5hdGlvbuaWueazlQ0KICAgIGhhbmRsZUhpc3RvcnlQYWdpbmF0aW9uKCkgew0KICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeTEoKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["artificialIntelligence.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8XA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "artificialIntelligence.vue", "sourceRoot": "src/views/domainClassification", "sourcesContent": ["<template>\r\n  <div v-if=\"funEsSeach\">\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"26\"\r\n      >\r\n        <div class=\"treeMain\" style=\"width: 100%; margin: 0\">\r\n          <!-- 搜索框 - 直接从Wechat.vue复制 -->\r\n          <div\r\n            style=\"\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              gap: 10px;\r\n            \"\r\n          >\r\n            <el-input\r\n              placeholder=\"输入关键字进行过滤\"\r\n              v-model=\"filterText\"\r\n              clearable\r\n              style=\"margin-bottom: 10px\"\r\n              class=\"input_Fixed\"\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </div>\r\n          <!-- 控制区域 -->\r\n          <div\r\n            style=\"\r\n              margin-top: -10px;\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              padding: 0 10px;\r\n            \"\r\n          >\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              content=\"重置\"\r\n              placement=\"top\"\r\n            >\r\n              <el-button\r\n                icon=\"el-icon-refresh\"\r\n                @click=\"treeClear\"\r\n                type=\"text\"\r\n                style=\"color: #666\"\r\n                >重置</el-button\r\n              >\r\n            </el-tooltip>\r\n          </div>\r\n          <div class=\"treeBox\">\r\n            <el-tree\r\n              :data=\"treeData\"\r\n              ref=\"tree\"\r\n              show-checkbox\r\n              node-key=\"id\"\r\n              :default-expanded-keys=\"[1000]\"\r\n              @check=\"handleTreeCheck\"\r\n              @node-click=\"treeNodeClick\"\r\n              :expand-on-click-node=\"false\"\r\n              :filter-node-method=\"filterNode\"\r\n            >\r\n              <template slot-scope=\"scoped\">\r\n                <div style=\"display: flex; align-items: center\">\r\n                  <div>{{ scoped.data.label }}</div>\r\n                  <div\r\n                    v-if=\"scoped.data.country && scoped.data.country !== '0'\"\r\n                    style=\"display: flex; align-items: center\"\r\n                  >\r\n                    <div>[</div>\r\n                    <dict-tag\r\n                      :options=\"dict.type.country\"\r\n                      :value=\"scoped.data.country\"\r\n                    />\r\n                    <div>]</div>\r\n                  </div>\r\n                  <div style=\"font-weight: 600\">\r\n                    {{ `${scoped.data.count ? `(${scoped.data.count})` : \"\"}` }}\r\n                  </div>\r\n                  <div v-if=\"scoped.data.label !== '数据源' && scoped.data.url\">\r\n                    <el-tooltip\r\n                      content=\"打开数据源链接\"\r\n                      placement=\"top-start\"\r\n                      effect=\"light\"\r\n                    >\r\n                      <i\r\n                        class=\"el-icon-connection\"\r\n                        style=\"\r\n                          font-size: 20px;\r\n                          color: #409eff;\r\n                          margin-left: 5px;\r\n                          margin-top: 4px;\r\n                        \"\r\n                        @click=\"openUrl(scoped.data.url)\"\r\n                      ></i>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </el-tree>\r\n          </div>\r\n          <!-- 分页组件 -->\r\n          <div class=\"tree-pagination\">\r\n            <el-pagination\r\n              @current-change=\"handleTreeCurrentChange\"\r\n              @size-change=\"handleTreePageSizeChange\"\r\n              :current-page=\"treeCurrentPage\"\r\n              :pager-count=\"5\"\r\n              :page-size=\"treePageSize\"\r\n              :page-sizes=\"[50, 100, 150]\"\r\n              layout=\"total, sizes, prev, pager, next\"\r\n              :total=\"treeTotal\"\r\n              :small=\"true\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"74\">\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n        >\r\n          <div class=\"toolBox\">\r\n            <!-- <div class=\"title\" :style=\"{ height: ActiveData.title ? '' : '50px' }\">\r\n              <p v-if=\"ActiveData.title\">{{ ActiveData.title }}</p>\r\n              <p v-else></p>\r\n            </div> -->\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == '' ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = ''\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >是否与科技有关:</span\r\n                >\r\n                <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                  <el-radio-button\r\n                    v-for=\"dict in dict.type.is_technology\"\r\n                    :label=\"dict.value\"\r\n                    :key=\"'is_technology' + dict.value\"\r\n                    >{{ dict.label }}</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </p>\r\n              <div class=\"keyword\">\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >关键词:</span\r\n                >\r\n                <el-input\r\n                  ref=\"keywordRef\"\r\n                  placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                  style=\"width: 430px\"\r\n                  v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\"\r\n                  @blur=\"hideHistoryList()\"\r\n                >\r\n                </el-input>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"funEsSeach('filter')\"\r\n                  :loading=\"buttonDisabled\"\r\n                  style=\"margin-left: 10px; height: 36px\"\r\n                  >搜索</el-button\r\n                >\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div\r\n                    class=\"historyItem\"\r\n                    v-for=\"(history, index) in historyList\"\r\n                    :key=\"index\"\r\n                    v-loading=\"historyLoading\"\r\n                  >\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                      {{ history.keyword }}\r\n                    </div>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"removeHistory(history, 1)\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >删除</el-button\r\n                    >\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\"\r\n                      >更多</el-button\r\n                    >\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"clearHistory()\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >清空</el-button\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"btn\">\r\n              <el-button size=\"mini\" @click=\"resetting\">重置</el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"funEsSeach('filter')\"\r\n                :loading=\"buttonDisabled\"\r\n                >搜索</el-button\r\n              >\r\n            </div> -->\r\n          </div>\r\n          <MainArticle\r\n            v-loading=\"tableLoading\"\r\n            :flag=\"'artificialIntelligence'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach('filter')\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport topSeach from \"@/views/components/topSeach.vue\";\r\nimport MainArticle from \"../components/MainArticle.vue\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\n\r\nexport default {\r\n  components: { topSeach, MainArticle, Splitpanes, Pane },\r\n  dicts: [\"is_technology\", \"country\"],\r\n  data() {\r\n    return {\r\n      width: \"258\",\r\n      isReSize: false,\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      /* 左侧tree数据 */\r\n      filterText: \"\",\r\n      treeData: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n      /* 搜索防抖 */\r\n      searchDebounceTimer: null,\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: \"\" /* 匹配模式 */,\r\n        keyword: \"\" /* 关键词 */,\r\n        timeRange: \"4\" /* 时间范围 */,\r\n        customDay: [] /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: [] /* 自定义天 */,\r\n        isTechnology: \"1\",\r\n        sortMode: \"0\",\r\n      } /* 搜索条件 */,\r\n      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      ActiveData: {},\r\n      seniorSerchFlag: false /* 普通检索或高级检索 */,\r\n      areaList: [] /* 国内地区 */,\r\n      countryList: [] /* 国家或地区 */,\r\n      KeList: [],\r\n      funEsSeach: null,\r\n      treeQuery: {\r\n        filterwords: \"\", // 添加树搜索关键字\r\n      },\r\n      domainList: [],\r\n      industryList: [],\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      nodeCheckList: [],\r\n      isTreeSlotProcessing: false,\r\n      initializationCompleted: false, // 标记初始化是否完成\r\n      // 从Wechat.vue同步的属性\r\n      tableLoading: false, // 表格loading状态\r\n      treeReady: false, // 树组件是否已准备好\r\n      isQuerying: false, // 查询防抖\r\n      queryDebounceTimer: null, // 查询防抖定时器\r\n    };\r\n  },\r\n  async created() {\r\n    try {\r\n      // 初始化funEsSeach方法\r\n      this.funEsSeach = this.EsSeach;\r\n\r\n      // 先加载基础数据\r\n      await this.getArticleHistory();\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // 在mounted中再次检查树组件状态\r\n    this.$nextTick(() => {\r\n      this.checkTreeReadyStatus();\r\n    });\r\n  },\r\n  watch: {\r\n    // 监听过滤文本变化，调用后端接口搜索 - 直接从Wechat.vue复制\r\n    filterText(val) {\r\n      // 清除之前的防抖定时器\r\n      if (this.searchDebounceTimer) {\r\n        clearTimeout(this.searchDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，500ms后执行搜索\r\n      this.searchDebounceTimer = setTimeout(() => {\r\n        this.handleTreeSearch(val);\r\n      }, 500);\r\n    },\r\n\r\n    // 监听筛选条件变化\r\n    \"SeachData.timeRange\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.collectionDateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 先加载树数据\r\n        await this.queryTreeData();\r\n\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n\r\n        // 再加载文章列表（内部已经处理了 tableLoading）\r\n        await this.queryArticleList();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // 处理筛选条件变化 - 直接从Wechat.vue复制\r\n    handleFilterChange() {\r\n      // 清空左侧树选中状态\r\n      this.clearTreeSelections();\r\n\r\n      // 重置分页到第一页\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n      this.scrollTreeToTop(); // 右侧筛选条件变化时也要滚动左侧树到顶部\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 清空树选中状态\r\n    clearTreeSelections() {\r\n      if (this.$refs.tree) {\r\n        this.$refs.tree.setCheckedKeys([]);\r\n      }\r\n      this.checkList = [];\r\n    },\r\n\r\n    // 同时查询树和列表 - 直接从Wechat.vue复制\r\n    async queryTreeAndList() {\r\n      try {\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 检查树组件准备状态\r\n    checkTreeReadyStatus() {\r\n      let attempts = 0;\r\n      const maxAttempts = 50; // 最多检查2.5秒\r\n\r\n      const checkInterval = setInterval(() => {\r\n        attempts++;\r\n\r\n        if (\r\n          this.$refs.tree &&\r\n          this.treeData &&\r\n          this.treeData[0] &&\r\n          this.treeData[0].children &&\r\n          this.treeData[0].children.length > 0\r\n        ) {\r\n          this.treeReady = true;\r\n          clearInterval(checkInterval);\r\n        } else if (attempts >= maxAttempts) {\r\n          clearInterval(checkInterval);\r\n        }\r\n      }, 50);\r\n    },\r\n\r\n    // 确保树组件准备就绪\r\n    async ensureTreeReady() {\r\n      if (\r\n        this.treeReady &&\r\n        this.$refs.tree &&\r\n        this.treeData[0].children.length > 0\r\n      ) {\r\n        return Promise.resolve();\r\n      }\r\n\r\n      return new Promise((resolve) => {\r\n        let attempts = 0;\r\n        const maxAttempts = 100; // 最多等待5秒\r\n\r\n        const checkTree = () => {\r\n          attempts++;\r\n\r\n          if (\r\n            this.$refs.tree &&\r\n            this.treeData &&\r\n            this.treeData[0] &&\r\n            this.treeData[0].children &&\r\n            this.treeData[0].children.length > 0\r\n          ) {\r\n            this.treeReady = true;\r\n\r\n            resolve();\r\n          } else if (attempts >= maxAttempts) {\r\n            resolve(); // 超时也要resolve，避免卡死\r\n          } else {\r\n            setTimeout(checkTree, 50);\r\n          }\r\n        };\r\n        checkTree();\r\n      });\r\n    },\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      try {\r\n        const params = {\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          platformType: 1,\r\n          id: this.$route.query.id || 1,\r\n          m: 1,\r\n          dateType:\r\n            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n          startTime: this.SeachData.customDay\r\n            ? this.SeachData.customDay[0]\r\n            : \"\",\r\n          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : \"\",\r\n          collectionDateType:\r\n            this.SeachData.collectionDateType != 6\r\n              ? this.SeachData.collectionDateType\r\n              : \"\",\r\n          collectionStartTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[0]\r\n            : \"\",\r\n          collectionEndTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[1]\r\n            : \"\",\r\n          keywords: this.SeachData.keyword,\r\n          isTechnology: this.SeachData.isTechnology,\r\n          // 添加关键字过滤参数\r\n          filterwords: this.treeQuery.filterwords || \"\",\r\n          // 添加 artificialIntelligence 特有的参数\r\n          menuType: this.$route.query.menuType,\r\n        };\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: item.sourceSn || `node_${index}`, // 使用sourceSn作为ID，确保唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeData = [\r\n            {\r\n              id: 1000,\r\n              label: \"数据源\",\r\n              children: mapData(dataList),\r\n            },\r\n          ];\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n\r\n          // 等待DOM更新后标记树组件为准备就绪\r\n          this.$nextTick(() => {\r\n            // 简单延迟，让mounted中的检查来处理准备状态\r\n            setTimeout(() => {\r\n              // 不在这里设置treeReady，让mounted中的检查来处理\r\n            }, 100);\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      }\r\n    },\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList() {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      // 立即显示loading状态\r\n      this.tableLoading = true;\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.$route.query.id || 1,\r\n            isSort: this.SeachData.sortMode,\r\n            dateType:\r\n              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n            startTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[0]\r\n              : \"\",\r\n            endTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[1]\r\n              : \"\",\r\n            collectionDateType:\r\n              this.SeachData.collectionDateType != 6\r\n                ? this.SeachData.collectionDateType\r\n                : \"\",\r\n            collectionStartTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[0]\r\n              : \"\",\r\n            collectionEndTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[1]\r\n              : \"\",\r\n            keywords: this.SeachData.keyword,\r\n            isTechnology: this.SeachData.isTechnology,\r\n            platformType: 1,\r\n            // 添加 artificialIntelligence 特有的参数\r\n            menuType: this.$route.query.menuType,\r\n          };\r\n\r\n          // 如果有选中的数据源，添加数据源参数 - 直接从Wechat.vue复制\r\n          if (this.checkList && this.checkList.length > 0) {\r\n            const data = this.checkList.map((item) => item.label);\r\n            const sourceSn = data\r\n              .map((item) => {\r\n                const foundItem = this.treeDataTransfer.find(\r\n                  (row) => row.label === item\r\n                );\r\n                return foundItem ? foundItem.sourceSn : null;\r\n              })\r\n              .filter((sn) => sn !== null);\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 2 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          const res = await api.KeIntegration({ ...params, ...this.treeQuery });\r\n\r\n          if (res.code == 200) {\r\n            this.ArticleList = res.data.list || [];\r\n            this.total = res.data.total || 0;\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.pageSize * (this.currentPage - 1) >= this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.currentPage = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n          this.buttonDisabled = false;\r\n        }\r\n      }, 300);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      this.$nextTick(() => {\r\n        // 尝试多种滚动方式确保滚动成功\r\n        const scrollBoxElement = document.querySelector(\".scollBox\");\r\n        if (scrollBoxElement) {\r\n          scrollBoxElement.scrollTop = 0;\r\n        }\r\n\r\n        // 如果MainArticle组件有scroll引用，也尝试滚动它\r\n        if (\r\n          this.$refs.mainArticle &&\r\n          this.$refs.mainArticle.$refs &&\r\n          this.$refs.mainArticle.$refs.scroll\r\n        ) {\r\n          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;\r\n        }\r\n\r\n        // 滚动整个右侧区域\r\n        const rightMain = document.querySelector(\".rightMain\");\r\n        if (rightMain) {\r\n          rightMain.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 滚动左侧树到顶部\r\n    scrollTreeToTop() {\r\n      this.$nextTick(() => {\r\n        const treeBox = document.querySelector(\".treeBox\");\r\n        if (treeBox) {\r\n          treeBox.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    // 树节点勾选处理（使用check事件）- 直接从Wechat.vue复制\r\n    handleTreeCheck(data, checkedInfo) {\r\n      // 如果树组件还没准备好，直接返回\r\n      if (!this.treeReady || !this.$refs.tree) {\r\n        return;\r\n      }\r\n\r\n      // check事件的参数：\r\n      // data: 当前点击的节点数据\r\n      // checkedInfo: { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }\r\n\r\n      // 获取所有选中的节点（排除根节点）\r\n      const allCheckedNodes = checkedInfo.checkedNodes || [];\r\n      const selectedNodes = allCheckedNodes.filter(\r\n        (node) => node.id !== 1000 && node.label !== \"数据源\"\r\n      );\r\n\r\n      // 更新选中列表\r\n      this.checkList = selectedNodes.map((node) => ({ ...node }));\r\n\r\n      // 重置页码并查询内容 - 修改为KeMonitor的分页参数\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // checkChange方法 - 直接从Wechat.vue复制\r\n    async checkChange(item, isCheck) {\r\n      // 如果树组件还没准备好，直接返回，不处理勾选\r\n      if (!this.treeReady || !this.$refs.tree) {\r\n        return;\r\n      }\r\n\r\n      // 延迟执行，确保Element UI树组件内部状态已经更新\r\n      setTimeout(() => {\r\n        // 如果勾选的是数据源根节点，处理全选/取消全选\r\n        if (item.id === 1000 || item.label === \"数据源\") {\r\n          if (isCheck) {\r\n            // 全选：选择所有可见的子节点\r\n            this.checkList = [...this.treeData[0].children];\r\n            const allIds = this.treeData[0].children.map((node) => node.id);\r\n\r\n            this.$refs.tree.setCheckedKeys(allIds);\r\n          } else {\r\n            // 取消全选：清空所有选择\r\n            this.checkList = [];\r\n            this.$refs.tree.setCheckedKeys([]);\r\n          }\r\n        } else {\r\n          // 处理单个节点的勾选/取消勾选\r\n\r\n          if (isCheck) {\r\n            // 添加到选中列表（多选）\r\n            const existingIndex = this.checkList.findIndex(\r\n              (row) => row.label === item.label\r\n            );\r\n            if (existingIndex === -1) {\r\n              this.checkList.push({ ...item });\r\n            }\r\n          } else {\r\n            // 从选中列表中移除\r\n            this.checkList = this.checkList.filter(\r\n              (row) => row.label !== item.label\r\n            );\r\n          }\r\n        }\r\n\r\n        // 重置页码并查询内容 - 修改为KeMonitor的分页参数\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      }, 50); // 延迟50ms执行\r\n    },\r\n\r\n    // 树节点点击处理（混合选择模式）- 直接从Wechat.vue复制\r\n    async treeNodeClick(item) {\r\n      // 如果点击的是数据源根节点，不进行操作\r\n      if (item.id === 1000 || item.label === \"数据源\") {\r\n        return;\r\n      }\r\n\r\n      // 如果树组件还没准备好，直接返回，不处理点击\r\n      if (!this.treeReady || !this.$refs.tree) {\r\n        return;\r\n      }\r\n\r\n      // 先滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 延迟执行，确保Element UI树组件内部状态已经更新\r\n      setTimeout(() => {\r\n        // 检查是否已选中该节点\r\n        const existingIndex = this.checkList.findIndex(\r\n          (row) => row.label === item.label\r\n        );\r\n\r\n        if (existingIndex !== -1) {\r\n          // 如果当前项已被选中，则只取消该项的选择（保持其他选择）\r\n          this.checkList = this.checkList.filter(\r\n            (row) => row.label !== item.label\r\n          );\r\n\r\n          // 更新树组件的选中状态\r\n          const remainingIds = this.checkList.map((node) => node.id);\r\n          this.$refs.tree.setCheckedKeys(remainingIds);\r\n        } else {\r\n          // 如果当前项未被选中，清空所有选择，设置为唯一选中项（单选）\r\n          this.checkList = [{ ...item }];\r\n\r\n          // 更新树组件的选中状态\r\n          this.$refs.tree.setCheckedKeys([item.id]);\r\n        }\r\n\r\n        // 重置页码并查询内容 - 修改为KeMonitor的分页参数\r\n        this.currentPage = 1;\r\n        this.queryArticleList();\r\n      }, 50); // 延迟50ms执行\r\n    },\r\n    // 重置树选择 - 直接从Wechat.vue复制\r\n    treeClear() {\r\n      // 清空选中状态\r\n      this.clearTreeSelections();\r\n\r\n      // 重置页码并查询列表数据 - 修改为KeMonitor的分页参数\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.scrollTreeToTop();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 处理树搜索 - 直接从Wechat.vue复制并适配KeMonitor\r\n    async handleTreeSearch(keyword) {\r\n      try {\r\n        // 树搜索不应该影响右侧列表的loading状态\r\n        // this.tableLoading = true; // 移除这行\r\n\r\n        // 更新搜索关键字\r\n        this.treeQuery.filterwords = keyword || \"\";\r\n\r\n        if (keyword) {\r\n          // 使用filterwords参数调用树接口进行搜索\r\n          this.scrollTreeToTop(); // 关键字过滤时滚动到顶部\r\n          await this.queryTreeData();\r\n        } else {\r\n          // 如果搜索关键字为空，重新获取原始数据\r\n          this.treeCurrentPage = 1;\r\n          this.scrollTreeToTop(); // 清空关键字时滚动到顶部\r\n          await this.queryTreeData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"搜索树节点失败:\", error);\r\n        this.$message.error(\"搜索失败，请重试\");\r\n      }\r\n      // 不需要finally块来关闭tableLoading\r\n    },\r\n\r\n    // 保留原有的必要方法\r\n    SwitchInfo(data) {\r\n      this.isInfo = data;\r\n    },\r\n\r\n    seniorSerch() {\r\n      this.seniorSerchFlag = !this.seniorSerchFlag;\r\n    },\r\n\r\n    async getArea() {\r\n      try {\r\n        const Response = await api.getAreaList();\r\n        if (Response && Response.code == 200 && Response.data) {\r\n          this.areaList = Response.data[0] || [];\r\n          this.countryList = Response.data[1] || [];\r\n        } else {\r\n          console.warn(\"获取地区数据失败或数据为空\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取区域数据失败:\", err);\r\n        this.$message.error(\"地区数据获取失败\");\r\n      }\r\n    },\r\n\r\n    async getArticleHistory() {\r\n      try {\r\n        const res = await listArticleHistory({\r\n          pageNum: 1,\r\n          pageSize: 5,\r\n          type: 2,\r\n        });\r\n        if (res && res.code === 200) {\r\n          this.historyList = res.rows || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取历史记录失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 树节点过滤方法\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n\r\n    // 添加EsSeach方法以兼容原有调用\r\n    EsSeach(flag) {\r\n      if (flag === \"filter\") {\r\n        // 筛选变化时同时查询树和列表\r\n        this.queryTreeAndList();\r\n      } else {\r\n        // 其他情况只查询列表\r\n        this.queryArticleList();\r\n      }\r\n    },\r\n\r\n    // 重置搜索条件 - 简化版本\r\n    resetting() {\r\n      try {\r\n        this.SeachData = {\r\n          metaMode: \"\" /* 匹配模式 */,\r\n          keyword: \"\" /* 关键词 */,\r\n          timeRange: \"\" /* 时间范围 */,\r\n          customDay: [] /* 自定义天 */,\r\n          collectionDateType: null /* 时间范围 */,\r\n          collectionTime: [] /* 自定义天 */,\r\n          isTechnology: \"1\",\r\n          sortMode: \"0\",\r\n        };\r\n\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryTreeAndList();\r\n      } catch (error) {\r\n        console.error(\"重置搜索条件时出错:\", error);\r\n        this.$message.error(\"重置搜索条件失败\");\r\n      }\r\n    },\r\n    async removeHistory(item, type) {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (item && item.id) {\r\n          await delArticleHistory([item.id]);\r\n\r\n          if (type == 1) {\r\n            if (this.$refs[\"keywordRef\"]) {\r\n              this.$refs[\"keywordRef\"].focus();\r\n            }\r\n            await this.getArticleHistory();\r\n          } else {\r\n            await this.getArticleHistory();\r\n            await this.getArticleHistory1();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"删除历史记录时出错:\", error);\r\n        this.$message.error(\"删除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      try {\r\n        this.showHistory = true;\r\n      } catch (error) {\r\n        console.error(\"显示历史列表时出错:\", error);\r\n      }\r\n    },\r\n\r\n    hideHistoryList() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        this.historyTimeout = setTimeout(() => {\r\n          this.showHistory = false;\r\n        }, 500);\r\n      } catch (error) {\r\n        console.error(\"隐藏历史列表时出错:\", error);\r\n        this.showHistory = false; // 确保在出错时也能隐藏列表\r\n      }\r\n    },\r\n\r\n    // 关键词历史选择 - 直接从Wechat.vue复制\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.currentPage = 1;\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    async clearHistory() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (this.$refs[\"keywordRef\"]) {\r\n          this.$refs[\"keywordRef\"].focus();\r\n        }\r\n\r\n        await cleanArticleHistory(2);\r\n        await this.getArticleHistory();\r\n      } catch (error) {\r\n        console.error(\"清除历史记录时出错:\", error);\r\n        this.$message.error(\"清除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    moreHistory() {\r\n      try {\r\n        this.historyLoading = true;\r\n        this.getArticleHistory1();\r\n        this.dialogVisible1 = true;\r\n      } catch (error) {\r\n        console.error(\"加载更多历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n      }\r\n    },\r\n\r\n    async getArticleHistory1() {\r\n      try {\r\n        this.historyLoading = true;\r\n        const response = await listArticleHistory({\r\n          ...this.queryParams1,\r\n          type: 2,\r\n        });\r\n\r\n        if (response) {\r\n          this.historyList1 = response.rows || [];\r\n          this.total1 = response.total || 0;\r\n        }\r\n\r\n        this.historyLoading = false;\r\n      } catch (error) {\r\n        console.error(\"获取文章历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n        this.$message.error(\"获取搜索历史失败\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 处理树形分页页码变化\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.clearTreeSelections();\r\n      this.scrollTreeToTop(); // 左侧树翻页时滚动到顶部\r\n      this.queryTreeData();\r\n    },\r\n\r\n    // 处理树形分页每页大小变化\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.clearTreeSelections();\r\n      this.scrollTreeToTop(); // 左侧树页面大小变化时滚动到顶部\r\n      this.queryTreeData();\r\n    },\r\n\r\n    // 添加缺失的openNewView方法\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 添加缺失的handleHistoryPagination方法\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 180px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  // box-shadow: -1px 2px 15px #cecdcd;\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}