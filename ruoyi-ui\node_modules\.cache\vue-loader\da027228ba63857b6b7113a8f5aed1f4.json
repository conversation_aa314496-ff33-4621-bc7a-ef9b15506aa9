{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\tech\\index.vue?vue&type=style&index=0&id=e32f3d28&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\tech\\index.vue", "mtime": 1754010073836}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCjo6di1kZWVwIC5xdWVyeUZvcm0gew0KICAuZWwtZm9ybS1pdGVtIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBtYXJnaW4tcmlnaHQ6IDA7DQogIH0NCiAgLmVsLWZvcm0taXRlbV9fY29udGVudCB7DQogICAgd2lkdGg6IGNhbGMoMTAwJSAtIDgwcHgpOw0KICB9DQp9DQoNCjo6di1kZWVwIC5yYWRpby1ncm91cCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICAuZWwtcmFkaW8gew0KICAgIG1hcmdpbi1yaWdodDogMDsNCiAgICBtYXJnaW4tYm90dG9tOiAycHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0hCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/article/tech", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"80px\"\r\n      class=\"queryForm\"\r\n    >\r\n      <el-row type=\"flex\" justify=\"space-between\">\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"标题\" prop=\"title\">\r\n            <el-input\r\n              v-model=\"queryParams.title\"\r\n              placeholder=\"请输入标题\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"网址\" prop=\"originalUrl\">\r\n            <el-input\r\n              v-model=\"queryParams.originalUrl\"\r\n              placeholder=\"请输入网址\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"主键\" prop=\"id\">\r\n            <el-input\r\n              v-model=\"queryParams.id\"\r\n              placeholder=\"请输入主键\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"所属领域\" prop=\"sourceArea\">\r\n            <el-select\r\n              v-model=\"queryParams.sourceArea\"\r\n              placeholder=\"请选择所属领域\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.source_area\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row type=\"flex\" justify=\"space-between\">\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否与科技有关\" prop=\"isTechnology\">\r\n            <el-select\r\n              v-model=\"queryParams.isTechnology\"\r\n              placeholder=\"请选择小信优选\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_technology\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n            <el-select\r\n              v-model=\"queryParams.sourceType\"\r\n              placeholder=\"请选择平台类型\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.source_type\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否审核\" prop=\"isReviewed\">\r\n            <el-select\r\n              v-model=\"queryParams.isReviewed\"\r\n              placeholder=\"审核状态\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_reviewed\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否翻译\" prop=\"isTranslated\">\r\n            <el-select\r\n              v-model=\"queryParams.isTranslated\"\r\n              placeholder=\"正文翻译状态\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_translated\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否推送\" prop=\"isPulled\">\r\n            <el-select\r\n              v-model=\"queryParams.isPulled\"\r\n              placeholder=\"推送状态\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_pulled\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-form-item style=\"width: 240px\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!--      <el-col :span=\"1.5\">-->\r\n      <!--        <el-button-->\r\n      <!--          type=\"success\"-->\r\n      <!--          plain-->\r\n      <!--          icon=\"el-icon-edit\"-->\r\n      <!--          size=\"mini\"-->\r\n      <!--          :disabled=\"single\"-->\r\n      <!--          @click=\"handleUpdate\"-->\r\n      <!--          v-hasPermi=\"['system:list:edit']\"-->\r\n      <!--        >修改</el-button>-->\r\n      <!--      </el-col&ndash;&gt;-->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-if=\"false\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleUpdate0\"\r\n          >设置为小信优选未选中</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleUpdate1\"\r\n          >设置为小信优选选中</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleUpdate2\"\r\n          >设置为小信优选待定</el-button\r\n        >\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handlePass\"\r\n          >审核通过</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleCancelPass\"\r\n          >撤回审核通过</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleNoPass\"\r\n          >审核不通过</el-button\r\n        >\r\n      </el-col> -->\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <!--    <el-table v-loading=\"loading\" :data=\"listList\" @selection-change=\"handleSelectionChange\" @cell-click=\"openArticle\">-->\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"listList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      height=\"calc(100vh - 330px)\"\r\n      ref=\"tableRef\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column prop=\"isReviewed\" label=\"审核\" align=\"center\" width=\"90\">\r\n        <template slot-scope=\"scope\">\r\n          <el-radio-group\r\n            v-model=\"scope.row.isReviewed\"\r\n            class=\"radio-group\"\r\n            @input=\"(e) => handleRadioChange(e, scope.row.id)\"\r\n          >\r\n            <el-radio :label=\"'1'\" style=\"color: #67c23a\">通过</el-radio>\r\n            <el-radio :label=\"'2'\" style=\"color: #f56c6c\">不通过</el-radio>\r\n            <el-radio :label=\"'4'\" style=\"color: #e6a23c\">待撤回</el-radio>\r\n          </el-radio-group>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"主键\" align=\"center\" prop=\"id\" width=\"70\" />\r\n      <!--      <el-table-column label=\"文章唯一标识\" align=\"center\" prop=\"sn\" />-->\r\n      <!--      <el-table-column label=\"标题\" align=\"center\" width=\"150\" prop=\"title\" />-->\r\n      <el-table-column prop=\"title\" label=\"标题\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-link\r\n            :href=\"`/expressDetails?id=${scope.row.id}&docId=${scope.row.docId}`\"\r\n            :underline=\"false\"\r\n            target=\"_blank\"\r\n          >\r\n            <span\r\n              class=\"el-icon-document\"\r\n              style=\"word-break: normal; word-wrap: break-word\"\r\n            >\r\n              {{ scope.row.title }}\r\n            </span>\r\n          </el-link>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"中文标题\" align=\"center\" prop=\"cnTitle\">\r\n        <template slot-scope=\"scope\">\r\n          <span\r\n            class=\"el-icon-document\"\r\n            style=\"word-break: normal; word-wrap: break-word\"\r\n          >\r\n            {{ scope.row.cnTitle }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"sourceType\"\r\n        label=\"平台类型\"\r\n        align=\"center\"\r\n        width=\"80\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processSourceType(scope.row.sourceType) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.sourceType\"\r\n            :options=\"dict.type.source_type\"\r\n            :value=\"scope.row.sourceType\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"sourceArea\"\r\n        label=\"所属领域\"\r\n        align=\"center\"\r\n        width=\"80\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processSourceArea(scope.row.sourceArea) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.sourceArea\"\r\n            :options=\"dict.type.source_area\"\r\n            :value=\"scope.row.sourceArea\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"来源名称\"\r\n        align=\"center\"\r\n        prop=\"sourceName\"\r\n        width=\"120\"\r\n      />\r\n      <!--      <el-table-column label=\"是否科技\" align=\"center\" prop=\"isTechnology\" />-->\r\n      <el-table-column\r\n        prop=\"isTechnology\"\r\n        label=\"科技相关\"\r\n        align=\"center\"\r\n        width=\"80\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processTechnologyState(scope.row.isTechnology) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.isTechnology\"\r\n            :options=\"dict.type.is_technology\"\r\n            :value=\"scope.row.isTechnology\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"isTranslated\"\r\n        label=\"翻译\"\r\n        align=\"center\"\r\n        width=\"65\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processTranslateState(scope.row.isTranslated) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.isTranslated\"\r\n            :options=\"dict.type.is_translated\"\r\n            :value=\"scope.row.isTranslated\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"contentLength\"\r\n        label=\"长度\"\r\n        align=\"center\"\r\n        width=\"60\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.contentLength }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"isPulled\" label=\"推送\" width=\"65\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processPulledState(scope.row.isPulled) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.isPulled\"\r\n            :options=\"dict.type.is_pulled\"\r\n            :value=\"scope.row.isPulled\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"发布时间\"\r\n        align=\"center\"\r\n        prop=\"publishTime\"\r\n        width=\"95\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.publishTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"创建时间\"\r\n        align=\"center\"\r\n        prop=\"createTime\"\r\n        width=\"95\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n        width=\"60\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <div\r\n            style=\"\r\n              display: flex;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n              align-items: center;\r\n            \"\r\n          >\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              >修改</el-button\r\n            >\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              style=\"margin-left: 0\"\r\n              >删除</el-button\r\n            >\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改文章列对话框 -->\r\n    <!--    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>-->\r\n    <!--      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">-->\r\n    <!--        <el-form-item label=\"文章唯一标识\" prop=\"sn\">-->\r\n    <!--          <el-input v-model=\"form.sn\" placeholder=\"请输入文章唯一标识\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"标题\" prop=\"title\">-->\r\n    <!--          <el-input v-model=\"form.title\" type=\"textarea\" placeholder=\"请输入内容\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"中文标题\" prop=\"cnTitle\">-->\r\n    <!--          <el-input v-model=\"form.cnTitle\" type=\"textarea\" placeholder=\"请输入内容\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"发布时间\" prop=\"publishTime\">-->\r\n    <!--          <el-date-picker clearable-->\r\n    <!--            v-model=\"form.publishTime\"-->\r\n    <!--            type=\"date\"-->\r\n    <!--            value-format=\"yyyy-MM-dd\"-->\r\n    <!--            placeholder=\"请选择发布时间\">-->\r\n    <!--          </el-date-picker>-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"是否与科技有关\" prop=\"isTechnology\">-->\r\n    <!--          <el-input v-model=\"form.isTechnology\" placeholder=\"是否与科技有关(0.无；1有；2其他)\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--     </el-form>-->\r\n    <!--      <div slot=\"footer\" class=\"dialog-footer\">-->\r\n    <!--        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>-->\r\n    <!--        <el-button @click=\"cancel\">取 消</el-button>-->\r\n    <!--      </div>-->\r\n    <!--    </el-dialog>-->\r\n\r\n    <el-dialog\r\n      :title=\"'修改文章'\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"dialog_Box\">\r\n        <el-form\r\n          :model=\"form\"\r\n          class=\"form_Style\"\r\n          label-position=\"top\"\r\n          ref=\"form\"\r\n          :rules=\"rules\"\r\n          size=\"mini\"\r\n        >\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"文章标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"中文标题\" prop=\"cnTitle\">\r\n                <el-input v-model=\"form.cnTitle\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n                <el-select\r\n                  v-model=\"form.sourceType\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.source_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"媒体来源\" prop=\"sourceName\">\r\n                <!-- <el-select\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  filterable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeLists\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.name\"\r\n                  ></el-option>\r\n                </el-select> -->\r\n                <el-input\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发布时间\" prop=\"publishTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.publishTime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"datetime\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  placeholder=\"选择日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"文章作者\" prop=\"author\">\r\n                <el-input\r\n                  v-model=\"form.author\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"原文链接\" prop=\"originalUrl\">\r\n            <el-input v-model=\"form.originalUrl\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"摘要\" prop=\"summary\">\r\n            <el-input\r\n              v-model=\"form.summary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文摘要\" prop=\"cnSummary\">\r\n            <el-input\r\n              v-model=\"form.cnSummary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文内容\" prop=\"cnContent\">\r\n            <editor v-model=\"form.cnContent\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <el-form-item label=\"文章内容\" prop=\"content\">\r\n            <editor v-model=\"form.content\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listList,\r\n  getList,\r\n  delList,\r\n  addList,\r\n  updateList,\r\n} from \"@/api/article/articleList\";\r\nimport {\r\n  updateArticleTech0,\r\n  updateArticleTech1,\r\n  updateArticleTech2,\r\n  articlePass,\r\n  articleNoPass,\r\n  cancelArticlePass,\r\n  getListByIds,\r\n  deleteByIds,\r\n} from \"@/api/article/articleHistory\";\r\nimport { articleListEdit, uploadCover } from \"@/api/articleCrawler/list\";\r\n\r\nexport default {\r\n  name: \"tech\",\r\n  dicts: [\r\n    \"is_technology\",\r\n    \"is_reviewed\",\r\n    \"is_pulled\",\r\n    \"source_type\",\r\n    \"source_area\",\r\n    \"is_translated\",\r\n  ],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 文章列表格数据\r\n      listList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        sn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceArea: null,\r\n        sourceName: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        keywords: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        userId: null,\r\n        deptId: null,\r\n        fileUrl: null,\r\n        firstPublishTime: null,\r\n        firstCreateTime: null,\r\n        firstWebstePublishTime: null,\r\n        webstePublishTime: null,\r\n        isChanged: null,\r\n        isTranslated: null,\r\n        isTechnology: \"1\",\r\n        isReviewed: \"0\",\r\n        isPulled: \"0\",\r\n        isFile: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        id: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        // 表单校验\r\n        title: [{ required: true, message: \"文章标题为必填项\" }],\r\n        content: [{ required: true, message: \"文章详情为必填项\" }],\r\n        publishTime: [{ required: true, message: \"发布时间为必填项\" }],\r\n        cnTitle: [{ required: true, message: \"中文名称为必填项\" }],\r\n        sourceType: [{ required: true, message: \"平台类型为必填项\" }],\r\n        originalUrl: [{ required: true, message: \"原文网址为必填项\" }],\r\n        summary: [{ required: true, message: \"请填写摘要\" }],\r\n        // cnSummary: [{ required: true, message: '请填写中文摘要' }],\r\n        sn: [{ required: true, message: \"请填写文章地址唯一识别号\" }],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询文章列列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listList(this.queryParams).then((response) => {\r\n        this.listList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n        this.$nextTick(() => {\r\n          this.scrollToTop();\r\n        });\r\n      });\r\n    },\r\n    // 表格滚动到顶部\r\n    scrollToTop() {\r\n      if (this.$refs.tableRef) {\r\n        const tableEl = this.$refs.tableRef.$el.querySelector(\r\n          \".el-table__body-wrapper\"\r\n        );\r\n        if (tableEl) {\r\n          tableEl.scrollTop = 0;\r\n        }\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        sn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceName: null,\r\n        sourceArea: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        keywords: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: null,\r\n        deptId: null,\r\n        fileUrl: null,\r\n        firstPublishTime: null,\r\n        firstCreateTime: null,\r\n        firstWebstePublishTime: null,\r\n        webstePublishTime: null,\r\n        isChanged: null,\r\n        isTranslated: null,\r\n        isTechnology: 1,\r\n        isFile: null,\r\n        delFlag: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加文章列\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      // const id = row.id || this.ids\r\n      // getList(id).then(response => {\r\n      //   this.form = response.data;\r\n      //   this.open = true;\r\n      //   this.title = \"修改文章列\";\r\n      // });\r\n      API.AreaInfo(row.id).then((response) => {\r\n        this.form = response.data;\r\n        // this.form.sourceType = Number(this.form.sourceType)\r\n        this.form.docId = row.id.toString();\r\n        this.open = true;\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            // updateList(this.form).then(response => {\r\n            //   this.$modal.msgSuccess(\"修改成功\");\r\n            //   this.open = false;\r\n            //   this.getList();\r\n            // });\r\n            let queryForm = JSON.parse(JSON.stringify(this.form));\r\n            articleListEdit(queryForm).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addList(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除文章列编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          // return delList(ids);\r\n          return deleteByIds(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n      // this.$modal.confirm('是否确认删除该条文章？\"').then(() => {\r\n      //   return API.monitoringEsRemove({ id: row.id, docId: row.id.toString() });\r\n      // }).then(() => {\r\n      //   this.Refresh();\r\n      //   this.$modal.msgSuccess(\"删除成功\");\r\n      // }).catch(() => { });\r\n    },\r\n    // 文章详情\r\n    openArticle(item, row) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n    handleUpdate0(row) {\r\n      const articleIds = row.id || this.ids;\r\n      var str = articleIds.join(\",\");\r\n      this.$modal\r\n        .confirm('是否确认修改编号为\"' + str + '\"的数据为科技无关项？')\r\n        .then(function () {\r\n          return updateArticleTech0(str);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleUpdate1(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认修改编号为\"' + articleIds + '\"的数据为科技有关项？')\r\n        .then(function () {\r\n          return updateArticleTech1(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleUpdate2(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认修改编号为\"' + articleIds + '\"的数据为其他项？')\r\n        .then(function () {\r\n          return updateArticleTech2(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    processTechnologyState(state) {\r\n      if (state == 0) return \"未选中\";\r\n      if (state == 1) return \"选中\";\r\n      if (state == 2) return \"待定\";\r\n      if (state == 3) return \"未检测\";\r\n      return \"未定义\";\r\n    },\r\n    processTranslateState(state) {\r\n      if (state == 0) return \"否\";\r\n      if (state == 1) return \"是\";\r\n      if (state == 2) return \"其他\";\r\n      return \"未定义\";\r\n    },\r\n    processSourceType(state) {\r\n      if (state == 1) return \"国内\";\r\n      if (state == 2) return \"国外\";\r\n      if (state == 3) return \"境内科技\";\r\n      if (state == 4) return \"境外科技\";\r\n      return \"未定义\";\r\n    },\r\n    processSourceArea(state) {\r\n      if (state == 1) return \"安全\";\r\n      if (state == 2) return \"数字化转型\";\r\n      if (state == 3) return \"软件\";\r\n      if (state == 4) return \"产业链\";\r\n      if (state == 5) return \"集成电路\";\r\n      if (state == 6) return \"人工智能\";\r\n      return \"未定义\";\r\n    },\r\n    processReviewedState(state) {\r\n      if (state == 0) return \"未审核\";\r\n      if (state == 1) return \"通过\";\r\n      if (state == 2) return \"不通过\";\r\n      if (state == 3) return \"待修改\";\r\n      if (state == 4) return \"待撤回\";\r\n      return \"未定义\";\r\n    },\r\n    processPulledState(state) {\r\n      if (state == 0) return \"否\";\r\n      if (state == 1) return \"是\";\r\n      return \"未定义\";\r\n    },\r\n    handlePass(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm(\"是否确认审核通过编号为\" + articleIds + \"的数据？\")\r\n        .then(function () {\r\n          return articlePass(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"审核通过\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleNoPass(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm(\"是否确认审核不通过编号为\" + articleIds + \"的数据？\")\r\n        .then(function () {\r\n          return articleNoPass(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"审核不通过\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleCancelPass(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm(\"是否确认撤回审核通过编号为\" + articleIds + \"的数据？\")\r\n        .then(function () {\r\n          // getListByIds(articleIds);\r\n          return cancelArticlePass(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"撤回审核通过\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleRadioChange(e, id) {\r\n      if (e == \"1\") {\r\n        console.log(\"通过\");\r\n        return articlePass(id);\r\n      } else if (e == \"2\") {\r\n        console.log(\"不通过\");\r\n        return articleNoPass(id);\r\n      } else if (e == \"4\") {\r\n        console.log(\"待撤回\");\r\n        return cancelArticlePass(id);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n::v-deep .queryForm {\r\n  .el-form-item {\r\n    width: 100%;\r\n    margin-right: 0;\r\n  }\r\n  .el-form-item__content {\r\n    width: calc(100% - 80px);\r\n  }\r\n}\r\n\r\n::v-deep .radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  .el-radio {\r\n    margin-right: 0;\r\n    margin-bottom: 2px;\r\n  }\r\n}\r\n</style>\r\n"]}]}