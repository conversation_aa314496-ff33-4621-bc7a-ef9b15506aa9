{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\mytask\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\article\\mytask\\index.vue", "mtime": 1754010084484}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQVBJIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHsNCiAgbGlzdExpc3QsDQogIGdldExpc3QsDQogIGRlbExpc3QsDQogIGFkZExpc3QsDQogIHVwZGF0ZUxpc3QsDQogIGdldGxpc3RCeVVzZXIsDQogIGdldFRhc2ssDQp9IGZyb20gIkAvYXBpL2FydGljbGUvYXJ0aWNsZUxpc3QiOw0KaW1wb3J0IHsNCiAgdXBkYXRlQXJ0aWNsZVRlY2gwLA0KICB1cGRhdGVBcnRpY2xlVGVjaDEsDQogIHVwZGF0ZUFydGljbGVUZWNoMiwNCiAgYXJ0aWNsZVBhc3MsDQogIGFydGljbGVOb1Bhc3MsDQogIGNhbmNlbEFydGljbGVQYXNzLA0KICBkZWxldGVCeUlkcywNCn0gZnJvbSAiQC9hcGkvYXJ0aWNsZS9hcnRpY2xlSGlzdG9yeSI7DQppbXBvcnQgeyBhcnRpY2xlTGlzdEVkaXQsIHVwbG9hZENvdmVyIH0gZnJvbSAiQC9hcGkvYXJ0aWNsZUNyYXdsZXIvbGlzdCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIm15dGFzayIsDQogIGRpY3RzOiBbDQogICAgImlzX3RlY2hub2xvZ3kiLA0KICAgICJpc19yZXZpZXdlZCIsDQogICAgImlzX3B1bGxlZCIsDQogICAgInNvdXJjZV90eXBlIiwNCiAgICAic291cmNlX2FyZWEiLA0KICAgICJpc190cmFuc2xhdGVkIiwNCiAgXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOaWh+eroOWIl+ihqOagvOaVsOaNrg0KICAgICAgbGlzdExpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogNTAsDQogICAgICAgIHNuOiBudWxsLA0KICAgICAgICB0aXRsZTogbnVsbCwNCiAgICAgICAgY25UaXRsZTogbnVsbCwNCiAgICAgICAgc291cmNlVHlwZTogbnVsbCwNCiAgICAgICAgc291cmNlQXJlYTogbnVsbCwNCiAgICAgICAgc291cmNlTmFtZTogbnVsbCwNCiAgICAgICAgc291cmNlU246IG51bGwsDQogICAgICAgIG9yaWdpbmFsVXJsOiBudWxsLA0KICAgICAgICBzaG9ydFVybDogbnVsbCwNCiAgICAgICAgYXV0aG9yOiBudWxsLA0KICAgICAgICBrZXl3b3JkczogbnVsbCwNCiAgICAgICAgZGVzY3JpcHRpb246IG51bGwsDQogICAgICAgIHN1bW1hcnk6IG51bGwsDQogICAgICAgIGNuU3VtbWFyeTogbnVsbCwNCiAgICAgICAgY292ZXI6IG51bGwsDQogICAgICAgIHB1Ymxpc2hUeXBlOiBudWxsLA0KICAgICAgICBwdWJsaXNoQ29kZTogbnVsbCwNCiAgICAgICAgcHVibGlzaEFyZWE6IG51bGwsDQogICAgICAgIHB1Ymxpc2hUaW1lOiBudWxsLA0KICAgICAgICBudW1iZXJMaWtlczogbnVsbCwNCiAgICAgICAgbnVtYmVyUmVhZHM6IG51bGwsDQogICAgICAgIG51bWJlckNvbGxlY3RzOiBudWxsLA0KICAgICAgICBudW1iZXJTaGFyZXM6IG51bGwsDQogICAgICAgIG51bWJlckNvbW1lbnRzOiBudWxsLA0KICAgICAgICBlbW90aW9uOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIHVzZXJJZDogbnVsbCwNCiAgICAgICAgZGVwdElkOiBudWxsLA0KICAgICAgICBmaWxlVXJsOiBudWxsLA0KICAgICAgICBmaXJzdFB1Ymxpc2hUaW1lOiBudWxsLA0KICAgICAgICBmaXJzdENyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIGZpcnN0V2Vic3RlUHVibGlzaFRpbWU6IG51bGwsDQogICAgICAgIHdlYnN0ZVB1Ymxpc2hUaW1lOiBudWxsLA0KICAgICAgICBpc0NoYW5nZWQ6IG51bGwsDQogICAgICAgIGlzVHJhbnNsYXRlZDogbnVsbCwNCiAgICAgICAgaXNUZWNobm9sb2d5OiBudWxsLA0KICAgICAgICBpc1Jldmlld2VkOiAiMCIsDQogICAgICAgIGlzUHVsbGVkOiAiMCIsDQogICAgICAgIGlzRmlsZTogbnVsbCwNCiAgICAgICAgZGVsZXRlQnk6IG51bGwsDQogICAgICAgIGRlbGV0ZVRpbWU6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICAgIHRpdGxlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaWh+eroOagh+mimOS4uuW/heWhq+mhuSIgfV0sDQogICAgICAgIGNvbnRlbnQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5paH56ug6K+m5oOF5Li65b+F5aGr6aG5IiB9XSwNCiAgICAgICAgcHVibGlzaFRpbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Y+R5biD5pe26Ze05Li65b+F5aGr6aG5IiB9XSwNCiAgICAgICAgY25UaXRsZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuK3mloflkI3np7DkuLrlv4XloavpobkiIH1dLA0KICAgICAgICBzb3VyY2VUeXBlOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuW5s+WPsOexu+Wei+S4uuW/heWhq+mhuSIgfV0sDQogICAgICAgIG9yaWdpbmFsVXJsOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWOn+aWh+e9keWdgOS4uuW/heWhq+mhuSIgfV0sDQogICAgICAgIHN1bW1hcnk6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+35aGr5YaZ5pGY6KaBIiB9XSwNCiAgICAgICAgLy8gY25TdW1tYXJ5OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeS4reaWh+aRmOimgScgfV0sDQogICAgICAgIHNuOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+Whq+WGmeaWh+eroOWcsOWdgOWUr+S4gOivhuWIq+WPtyIgfV0sDQogICAgICB9LA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i5paH56ug5YiX5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBnZXRsaXN0QnlVc2VyKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMubGlzdExpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5zY3JvbGxUb1RvcCgpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g6KGo5qC85rua5Yqo5Yiw6aG26YOoDQogICAgc2Nyb2xsVG9Ub3AoKSB7DQogICAgICBpZiAodGhpcy4kcmVmcy50YWJsZVJlZikgew0KICAgICAgICBjb25zdCB0YWJsZUVsID0gdGhpcy4kcmVmcy50YWJsZVJlZi4kZWwucXVlcnlTZWxlY3RvcigNCiAgICAgICAgICAiLmVsLXRhYmxlX19ib2R5LXdyYXBwZXIiDQogICAgICAgICk7DQogICAgICAgIGlmICh0YWJsZUVsKSB7DQogICAgICAgICAgdGFibGVFbC5zY3JvbGxUb3AgPSAwOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgc246IG51bGwsDQogICAgICAgIHRpdGxlOiBudWxsLA0KICAgICAgICBjblRpdGxlOiBudWxsLA0KICAgICAgICBzb3VyY2VUeXBlOiBudWxsLA0KICAgICAgICBzb3VyY2VOYW1lOiBudWxsLA0KICAgICAgICBzb3VyY2VBcmVhOiBudWxsLA0KICAgICAgICBzb3VyY2VTbjogbnVsbCwNCiAgICAgICAgb3JpZ2luYWxVcmw6IG51bGwsDQogICAgICAgIHNob3J0VXJsOiBudWxsLA0KICAgICAgICBhdXRob3I6IG51bGwsDQogICAgICAgIGtleXdvcmRzOiBudWxsLA0KICAgICAgICBkZXNjcmlwdGlvbjogbnVsbCwNCiAgICAgICAgc3VtbWFyeTogbnVsbCwNCiAgICAgICAgY25TdW1tYXJ5OiBudWxsLA0KICAgICAgICBjb3ZlcjogbnVsbCwNCiAgICAgICAgcHVibGlzaFR5cGU6IG51bGwsDQogICAgICAgIHB1Ymxpc2hDb2RlOiBudWxsLA0KICAgICAgICBwdWJsaXNoQXJlYTogbnVsbCwNCiAgICAgICAgcHVibGlzaFRpbWU6IG51bGwsDQogICAgICAgIG51bWJlckxpa2VzOiBudWxsLA0KICAgICAgICBudW1iZXJSZWFkczogbnVsbCwNCiAgICAgICAgbnVtYmVyQ29sbGVjdHM6IG51bGwsDQogICAgICAgIG51bWJlclNoYXJlczogbnVsbCwNCiAgICAgICAgbnVtYmVyQ29tbWVudHM6IG51bGwsDQogICAgICAgIGVtb3Rpb246IG51bGwsDQogICAgICAgIHN0YXR1czogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVzZXJJZDogbnVsbCwNCiAgICAgICAgZGVwdElkOiBudWxsLA0KICAgICAgICBmaWxlVXJsOiBudWxsLA0KICAgICAgICBmaXJzdFB1Ymxpc2hUaW1lOiBudWxsLA0KICAgICAgICBmaXJzdENyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIGZpcnN0V2Vic3RlUHVibGlzaFRpbWU6IG51bGwsDQogICAgICAgIHdlYnN0ZVB1Ymxpc2hUaW1lOiBudWxsLA0KICAgICAgICBpc0NoYW5nZWQ6IG51bGwsDQogICAgICAgIGlzVHJhbnNsYXRlZDogbnVsbCwNCiAgICAgICAgaXNUZWNobm9sb2d5OiAxLA0KICAgICAgICBpc0ZpbGU6IG51bGwsDQogICAgICAgIGRlbEZsYWc6IG51bGwsDQogICAgICAgIGRlbGV0ZUJ5OiBudWxsLA0KICAgICAgICBkZWxldGVUaW1lOiBudWxsLA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5pZCk7DQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5paH56ug5YiXIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAvLyBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgLy8gZ2V0TGlzdChpZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAvLyAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAvLyAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAvLyAgIHRoaXMudGl0bGUgPSAi5L+u5pS55paH56ug5YiXIjsNCiAgICAgIC8vIH0pOw0KICAgICAgQVBJLkFyZWFJbmZvKHJvdy5pZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgLy8gdGhpcy5mb3JtLnNvdXJjZVR5cGUgPSBOdW1iZXIodGhpcy5mb3JtLnNvdXJjZVR5cGUpDQogICAgICAgIHRoaXMuZm9ybS5kb2NJZCA9IHJvdy5pZC50b1N0cmluZygpOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICAvLyB1cGRhdGVMaXN0KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAvLyAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgLy8gICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIC8vICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAvLyB9KTsNCiAgICAgICAgICAgIGxldCBxdWVyeUZvcm0gPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuZm9ybSkpOw0KICAgICAgICAgICAgYXJ0aWNsZUxpc3RFZGl0KHF1ZXJ5Rm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRMaXN0KHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOaWh+eroOWIl+e8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgLy8gcmV0dXJuIGRlbExpc3QoaWRzKTsNCiAgICAgICAgICByZXR1cm4gZGVsZXRlQnlJZHMoaWRzKTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgICAgLy8gdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6K+l5p2h5paH56ug77yfIicpLnRoZW4oKCkgPT4gew0KICAgICAgLy8gICByZXR1cm4gQVBJLm1vbml0b3JpbmdFc1JlbW92ZSh7IGlkOiByb3cuaWQsIGRvY0lkOiByb3cuaWQudG9TdHJpbmcoKSB9KTsNCiAgICAgIC8vIH0pLnRoZW4oKCkgPT4gew0KICAgICAgLy8gICB0aGlzLlJlZnJlc2goKTsNCiAgICAgIC8vICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAvLyB9KS5jYXRjaCgoKSA9PiB7IH0pOw0KICAgIH0sDQogICAgLy8g5paH56ug6K+m5oOFDQogICAgb3BlbkFydGljbGUoaXRlbSwgcm93KSB7DQogICAgICB3aW5kb3cub3BlbigNCiAgICAgICAgYC9leHByZXNzRGV0YWlscz9pZD0ke2l0ZW0uaWR9JmRvY0lkPSR7aXRlbS5kb2NJZH1gLA0KICAgICAgICAiX2JsYW5rIg0KICAgICAgKTsNCiAgICB9LA0KICAgIGhhbmRsZVVwZGF0ZTAocm93KSB7DQogICAgICBjb25zdCBhcnRpY2xlSWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdmFyIHN0ciA9IGFydGljbGVJZHMuam9pbigiLCIpOw0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOS/ruaUuee8luWPt+S4uiInICsgc3RyICsgJyLnmoTmlbDmja7kuLrnp5HmioDml6DlhbPpobnvvJ8nKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgcmV0dXJuIHVwZGF0ZUFydGljbGVUZWNoMChzdHIpOw0KICAgICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICBoYW5kbGVVcGRhdGUxKHJvdykgew0KICAgICAgY29uc3QgYXJ0aWNsZUlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTkv67mlLnnvJblj7fkuLoiJyArIGFydGljbGVJZHMgKyAnIueahOaVsOaNruS4uuenkeaKgOacieWFs+mhue+8nycpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gdXBkYXRlQXJ0aWNsZVRlY2gxKGFydGljbGVJZHMpOw0KICAgICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICBoYW5kbGVVcGRhdGUyKHJvdykgew0KICAgICAgY29uc3QgYXJ0aWNsZUlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTkv67mlLnnvJblj7fkuLoiJyArIGFydGljbGVJZHMgKyAnIueahOaVsOaNruS4uuWFtuS7lumhue+8nycpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gdXBkYXRlQXJ0aWNsZVRlY2gyKGFydGljbGVJZHMpOw0KICAgICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICBwcm9jZXNzVGVjaG5vbG9neVN0YXRlKHN0YXRlKSB7DQogICAgICBpZiAoc3RhdGUgPT0gMCkgcmV0dXJuICLml6DlhbMiOw0KICAgICAgaWYgKHN0YXRlID09IDEpIHJldHVybiAi5pyJ5YWzIjsNCiAgICAgIGlmIChzdGF0ZSA9PSAyKSByZXR1cm4gIuWFtuS7liI7DQogICAgICByZXR1cm4gIuacquWumuS5iSI7DQogICAgfSwNCiAgICBwcm9jZXNzU291cmNlVHlwZShzdGF0ZSkgew0KICAgICAgaWYgKHN0YXRlID09IDEpIHJldHVybiAi5Zu95YaFIjsNCiAgICAgIGlmIChzdGF0ZSA9PSAyKSByZXR1cm4gIuWbveWkliI7DQogICAgICBpZiAoc3RhdGUgPT0gMykgcmV0dXJuICLlooPlhoXnp5HmioAiOw0KICAgICAgaWYgKHN0YXRlID09IDQpIHJldHVybiAi5aKD5aSW56eR5oqAIjsNCiAgICAgIHJldHVybiAi5pyq5a6a5LmJIjsNCiAgICB9LA0KICAgIHByb2Nlc3NTb3VyY2VBcmVhKHN0YXRlKSB7DQogICAgICBpZiAoc3RhdGUgPT0gMSkgcmV0dXJuICLnvZHnu5zmlbDmja7lt6XmjqflronlhagiOw0KICAgICAgaWYgKHN0YXRlID09IDIpIHJldHVybiAi5pWw5a2X5YyW6L2s5Z6L5pWw5a2X57uP5rWOIjsNCiAgICAgIGlmIChzdGF0ZSA9PSAzKSByZXR1cm4gIui9r+S7tiI7DQogICAgICBpZiAoc3RhdGUgPT0gNCkgcmV0dXJuICLkuqfkuJrpk77kvpvlupTpk74iOw0KICAgICAgaWYgKHN0YXRlID09IDUpIHJldHVybiAi6ZuG5oiQ55S16LevIjsNCiAgICAgIGlmIChzdGF0ZSA9PSA2KSByZXR1cm4gIuS6uuW3peaZuuiDvSI7DQogICAgICByZXR1cm4gIuacquWumuS5iSI7DQogICAgfSwNCiAgICBwcm9jZXNzUmV2aWV3ZWRTdGF0ZShzdGF0ZSkgew0KICAgICAgaWYgKHN0YXRlID09IDApIHJldHVybiAi5pyq5a6h5qC4IjsNCiAgICAgIGlmIChzdGF0ZSA9PSAxKSByZXR1cm4gIuWuoeaguOmAmui/hyI7DQogICAgICBpZiAoc3RhdGUgPT0gMikgcmV0dXJuICLlrqHmoLjkuI3pgJrov4ciOw0KICAgICAgaWYgKHN0YXRlID09IDMpIHJldHVybiAi5b6F5L+u5pS5IjsNCiAgICAgIGlmIChzdGF0ZSA9PSA0KSByZXR1cm4gIuW+heaSpOWbniI7DQogICAgICByZXR1cm4gIuacquWumuS5iSI7DQogICAgfSwNCiAgICBwcm9jZXNzUHVsbGVkU3RhdGUoc3RhdGUpIHsNCiAgICAgIGlmIChzdGF0ZSA9PSAwKSByZXR1cm4gIuacquaOqOmAgSI7DQogICAgICBpZiAoc3RhdGUgPT0gMSkgcmV0dXJuICLlt7LmjqjpgIEiOw0KICAgICAgcmV0dXJuICLmnKrlrprkuYkiOw0KICAgIH0sDQogICAgaGFuZGxlUGFzcyhyb3cpIHsNCiAgICAgIGNvbnN0IGFydGljbGVJZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAuY29uZmlybSgi5piv5ZCm56Gu6K6k5a6h5qC46YCa6L+H57yW5Y+35Li6IiArIGFydGljbGVJZHMgKyAi55qE5pWw5o2u77yfIikNCiAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgIHJldHVybiBhcnRpY2xlUGFzcyhhcnRpY2xlSWRzKTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWuoeaguOmAmui/hyIpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgaGFuZGxlTm9QYXNzKHJvdykgew0KICAgICAgY29uc3QgYXJ0aWNsZUlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCLmmK/lkKbnoa7orqTlrqHmoLjkuI3pgJrov4fnvJblj7fkuLoiICsgYXJ0aWNsZUlkcyArICLnmoTmlbDmja7vvJ8iKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgcmV0dXJuIGFydGljbGVOb1Bhc3MoYXJ0aWNsZUlkcyk7DQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlrqHmoLjkuI3pgJrov4ciKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIGhhbmRsZUNhbmNlbFBhc3Mocm93KSB7DQogICAgICBjb25zdCBhcnRpY2xlSWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oIuaYr+WQpuehruiupOaSpOWbnuWuoeaguOmAmui/h+e8luWPt+S4uiIgKyBhcnRpY2xlSWRzICsgIueahOaVsOaNru+8nyIpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gY2FuY2VsQXJ0aWNsZVBhc3MoYXJ0aWNsZUlkcyk7DQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmkqTlm57lrqHmoLjpgJrov4ciKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIG9uQ3JlYXRlVGFzaygpIHsNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCLmmK/lkKbnoa7orqTopoHpooblj5bku7vliqHvvJ8iKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgcmV0dXJuIGdldFRhc2soKTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIumihuWPluS7u+WKoeaIkOWKnyEiKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIGhhbmRsZVJhZGlvQ2hhbmdlKGUsIGlkKSB7DQogICAgICBpZiAoZSA9PSAiMSIpIHsNCiAgICAgICAgY29uc29sZS5sb2coIumAmui/hyIpOw0KICAgICAgICByZXR1cm4gYXJ0aWNsZVBhc3MoaWQpOw0KICAgICAgfSBlbHNlIGlmIChlID09ICIyIikgew0KICAgICAgICBjb25zb2xlLmxvZygi5LiN6YCa6L+HIik7DQogICAgICAgIHJldHVybiBhcnRpY2xlTm9QYXNzKGlkKTsNCiAgICAgIH0gZWxzZSBpZiAoZSA9PSAiNCIpIHsNCiAgICAgICAgY29uc29sZS5sb2coIuW+heaSpOWbniIpOw0KICAgICAgICByZXR1cm4gY2FuY2VsQXJ0aWNsZVBhc3MoaWQpOw0KICAgICAgfQ0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+k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file": "index.vue", "sourceRoot": "src/views/article/mytask", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"80px\"\r\n      class=\"queryForm\"\r\n    >\r\n      <el-row type=\"flex\" justify=\"space-between\">\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"标题\" prop=\"title\">\r\n            <el-input\r\n              v-model=\"queryParams.title\"\r\n              placeholder=\"请输入标题\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"网址\" prop=\"originalUrl\">\r\n            <el-input\r\n              v-model=\"queryParams.originalUrl\"\r\n              placeholder=\"请输入网址\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"主键\" prop=\"id\">\r\n            <el-input\r\n              v-model=\"queryParams.id\"\r\n              placeholder=\"请输入主键\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"所属领域\" prop=\"sourceArea\">\r\n            <el-select\r\n              v-model=\"queryParams.sourceArea\"\r\n              placeholder=\"请选择所属领域\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.source_area\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row type=\"flex\" justify=\"space-between\">\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"小信优选\" prop=\"isTechnology\">\r\n            <el-select\r\n              v-model=\"queryParams.isTechnology\"\r\n              placeholder=\"请选择小信优选\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_technology\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n            <el-select\r\n              v-model=\"queryParams.sourceType\"\r\n              placeholder=\"请选择平台类型\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.source_type\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否审核\" prop=\"isReviewed\">\r\n            <el-select\r\n              v-model=\"queryParams.isReviewed\"\r\n              placeholder=\"审核状态\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_reviewed\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否翻译\" prop=\"isTranslated\">\r\n            <el-select\r\n              v-model=\"queryParams.isTranslated\"\r\n              placeholder=\"正文翻译状态\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_translated\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"6\">\r\n          <el-form-item label=\"是否推送\" prop=\"isPulled\">\r\n            <el-select\r\n              v-model=\"queryParams.isPulled\"\r\n              placeholder=\"推送状态\"\r\n              clearable\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.is_pulled\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-form-item style=\"width: 240px\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleUpdate0\"\r\n          >设置为小信优选未选中</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleUpdate1\"\r\n          >设置为小信优选选中</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleUpdate2\"\r\n          >设置为小信优选待定</el-button\r\n        >\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handlePass\"\r\n          >审核通过</el-button\r\n        >\r\n      </el-col> -->\r\n      <!--      <el-col :span=\"1.5\">-->\r\n      <!--        <el-button type=\"primary\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"multiple\" @click=\"handleCancelPass\" v-hasPermi=\"['model:tech:cancel']\">撤回审核通过</el-button>-->\r\n      <!--      </el-col>-->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleNoPass\"\r\n          >审核不通过</el-button\r\n        >\r\n      </el-col> -->\r\n      <right-toolbar\r\n        :showSearch.sync=\"showSearch\"\r\n        @queryTable=\"getList\"\r\n      ></right-toolbar>\r\n    </el-row>\r\n\r\n    <!--    <el-table v-loading=\"loading\" :data=\"listList\" @selection-change=\"handleSelectionChange\" @cell-click=\"openArticle\">-->\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"listList\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      height=\"calc(100vh - 330px)\"\r\n      ref=\"tableRef\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column prop=\"isReviewed\" label=\"审核\" align=\"center\" width=\"90\">\r\n        <template slot-scope=\"scope\">\r\n          <el-radio-group\r\n            v-model=\"scope.row.isReviewed\"\r\n            class=\"radio-group\"\r\n            @input=\"(e) => handleRadioChange(e, scope.row.id)\"\r\n          >\r\n            <el-radio :label=\"'1'\" style=\"color: #67c23a\">通过</el-radio>\r\n            <el-radio :label=\"'2'\" style=\"color: #f56c6c\">不通过</el-radio>\r\n          </el-radio-group>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"主键\" align=\"center\" prop=\"id\" width=\"70\" />\r\n      <!--      <el-table-column label=\"文章唯一标识\" align=\"center\" prop=\"sn\" />-->\r\n      <!--      <el-table-column label=\"标题\" align=\"center\" width=\"150\" prop=\"title\" />-->\r\n      <el-table-column prop=\"title\" label=\"标题\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-link\r\n            :href=\"`/expressDetails?id=${scope.row.id}&docId=${scope.row.docId}`\"\r\n            :underline=\"false\"\r\n            target=\"_blank\"\r\n          >\r\n            <span\r\n              class=\"el-icon-document\"\r\n              style=\"word-break: normal; word-wrap: break-word\"\r\n            >\r\n              {{ scope.row.title }}\r\n            </span>\r\n          </el-link>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"中文标题\" align=\"center\" prop=\"cnTitle\">\r\n        <template slot-scope=\"scope\">\r\n          <span\r\n            class=\"el-icon-document\"\r\n            style=\"word-break: normal; word-wrap: break-word\"\r\n          >\r\n            {{ scope.row.cnTitle }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"sourceType\"\r\n        label=\"平台类型\"\r\n        align=\"center\"\r\n        width=\"80\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processSourceType(scope.row.sourceType) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.sourceType\"\r\n            :options=\"dict.type.source_type\"\r\n            :value=\"scope.row.sourceType\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"sourceArea\"\r\n        label=\"所属领域\"\r\n        align=\"center\"\r\n        width=\"80\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processSourceArea(scope.row.sourceArea) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.sourceArea\"\r\n            :options=\"dict.type.source_area\"\r\n            :value=\"scope.row.sourceArea\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"来源名称\"\r\n        align=\"center\"\r\n        prop=\"sourceName\"\r\n        width=\"120\"\r\n      />\r\n      <!--      <el-table-column label=\"是否科技\" align=\"center\" prop=\"isTechnology\" />-->\r\n      <el-table-column\r\n        prop=\"isTechnology\"\r\n        label=\"科技相关\"\r\n        align=\"center\"\r\n        width=\"80\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processTechnologyState(scope.row.isTechnology) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.isTechnology\"\r\n            :options=\"dict.type.is_technology\"\r\n            :value=\"scope.row.isTechnology\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"isTranslated\"\r\n        label=\"翻译\"\r\n        align=\"center\"\r\n        width=\"65\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processTranslateState(scope.row.isTranslated) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.isTranslated\"\r\n            :options=\"dict.type.is_translated\"\r\n            :value=\"scope.row.isTranslated\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"contentLength\"\r\n        label=\"长度\"\r\n        align=\"center\"\r\n        width=\"60\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.contentLength }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"isPulled\" label=\"推送\" width=\"65\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- <span>{{ processPulledState(scope.row.isPulled) }}</span> -->\r\n          <dict-tag\r\n            v-if=\"scope.row.isPulled\"\r\n            :options=\"dict.type.is_pulled\"\r\n            :value=\"scope.row.isPulled\"\r\n          />\r\n          <span v-else>未定义</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"发布时间\"\r\n        align=\"center\"\r\n        prop=\"publishTime\"\r\n        width=\"95\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.publishTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"创建时间\"\r\n        align=\"center\"\r\n        prop=\"createTime\"\r\n        width=\"95\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n        width=\"60\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <div\r\n            style=\"\r\n              display: flex;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n              align-items: center;\r\n            \"\r\n          >\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              v-hasPermi=\"['mytask:list:edit']\"\r\n              >修改</el-button\r\n            >\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              v-hasPermi=\"['mytask:list:remove']\"\r\n              style=\"margin-left: 0\"\r\n              >删除</el-button\r\n            >\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改文章列对话框 -->\r\n    <!--    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>-->\r\n    <!--      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">-->\r\n    <!--        <el-form-item label=\"文章唯一标识\" prop=\"sn\">-->\r\n    <!--          <el-input v-model=\"form.sn\" placeholder=\"请输入文章唯一标识\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"标题\" prop=\"title\">-->\r\n    <!--          <el-input v-model=\"form.title\" type=\"textarea\" placeholder=\"请输入内容\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"中文标题\" prop=\"cnTitle\">-->\r\n    <!--          <el-input v-model=\"form.cnTitle\" type=\"textarea\" placeholder=\"请输入内容\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"发布时间\" prop=\"publishTime\">-->\r\n    <!--          <el-date-picker clearable-->\r\n    <!--            v-model=\"form.publishTime\"-->\r\n    <!--            type=\"date\"-->\r\n    <!--            value-format=\"yyyy-MM-dd\"-->\r\n    <!--            placeholder=\"请选择发布时间\">-->\r\n    <!--          </el-date-picker>-->\r\n    <!--        </el-form-item>-->\r\n    <!--        <el-form-item label=\"是否与科技有关\" prop=\"isTechnology\">-->\r\n    <!--          <el-input v-model=\"form.isTechnology\" placeholder=\"是否与科技有关(0.无；1有；2其他)\" />-->\r\n    <!--        </el-form-item>-->\r\n    <!--     </el-form>-->\r\n    <!--      <div slot=\"footer\" class=\"dialog-footer\">-->\r\n    <!--        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>-->\r\n    <!--        <el-button @click=\"cancel\">取 消</el-button>-->\r\n    <!--      </div>-->\r\n    <!--    </el-dialog>-->\r\n\r\n    <el-dialog\r\n      :title=\"'修改文章'\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"dialog_Box\">\r\n        <el-form\r\n          :model=\"form\"\r\n          class=\"form_Style\"\r\n          label-position=\"top\"\r\n          ref=\"form\"\r\n          :rules=\"rules\"\r\n          size=\"mini\"\r\n        >\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"文章标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"中文标题\" prop=\"cnTitle\">\r\n                <el-input v-model=\"form.cnTitle\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n                <el-select\r\n                  v-model=\"form.sourceType\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.source_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"媒体来源\" prop=\"sourceName\">\r\n                <!-- <el-select\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  filterable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeLists\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.name\"\r\n                  ></el-option>\r\n                </el-select> -->\r\n                <el-input\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发布时间\" prop=\"publishTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.publishTime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"datetime\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  placeholder=\"选择日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"文章作者\" prop=\"author\">\r\n                <el-input\r\n                  v-model=\"form.author\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"原文链接\" prop=\"originalUrl\">\r\n            <el-input v-model=\"form.originalUrl\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"摘要\" prop=\"summary\">\r\n            <el-input\r\n              v-model=\"form.summary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文摘要\" prop=\"cnSummary\">\r\n            <el-input\r\n              v-model=\"form.cnSummary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文内容\" prop=\"cnContent\">\r\n            <editor v-model=\"form.cnContent\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <el-form-item label=\"文章内容\" prop=\"content\">\r\n            <editor v-model=\"form.content\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listList,\r\n  getList,\r\n  delList,\r\n  addList,\r\n  updateList,\r\n  getlistByUser,\r\n  getTask,\r\n} from \"@/api/article/articleList\";\r\nimport {\r\n  updateArticleTech0,\r\n  updateArticleTech1,\r\n  updateArticleTech2,\r\n  articlePass,\r\n  articleNoPass,\r\n  cancelArticlePass,\r\n  deleteByIds,\r\n} from \"@/api/article/articleHistory\";\r\nimport { articleListEdit, uploadCover } from \"@/api/articleCrawler/list\";\r\n\r\nexport default {\r\n  name: \"mytask\",\r\n  dicts: [\r\n    \"is_technology\",\r\n    \"is_reviewed\",\r\n    \"is_pulled\",\r\n    \"source_type\",\r\n    \"source_area\",\r\n    \"is_translated\",\r\n  ],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 文章列表格数据\r\n      listList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        sn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceArea: null,\r\n        sourceName: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        keywords: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        userId: null,\r\n        deptId: null,\r\n        fileUrl: null,\r\n        firstPublishTime: null,\r\n        firstCreateTime: null,\r\n        firstWebstePublishTime: null,\r\n        webstePublishTime: null,\r\n        isChanged: null,\r\n        isTranslated: null,\r\n        isTechnology: null,\r\n        isReviewed: \"0\",\r\n        isPulled: \"0\",\r\n        isFile: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        // 表单校验\r\n        title: [{ required: true, message: \"文章标题为必填项\" }],\r\n        content: [{ required: true, message: \"文章详情为必填项\" }],\r\n        publishTime: [{ required: true, message: \"发布时间为必填项\" }],\r\n        cnTitle: [{ required: true, message: \"中文名称为必填项\" }],\r\n        sourceType: [{ required: true, message: \"平台类型为必填项\" }],\r\n        originalUrl: [{ required: true, message: \"原文网址为必填项\" }],\r\n        summary: [{ required: true, message: \"请填写摘要\" }],\r\n        // cnSummary: [{ required: true, message: '请填写中文摘要' }],\r\n        sn: [{ required: true, message: \"请填写文章地址唯一识别号\" }],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询文章列列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      getlistByUser(this.queryParams).then((response) => {\r\n        this.listList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n        this.$nextTick(() => {\r\n          this.scrollToTop();\r\n        });\r\n      });\r\n    },\r\n    // 表格滚动到顶部\r\n    scrollToTop() {\r\n      if (this.$refs.tableRef) {\r\n        const tableEl = this.$refs.tableRef.$el.querySelector(\r\n          \".el-table__body-wrapper\"\r\n        );\r\n        if (tableEl) {\r\n          tableEl.scrollTop = 0;\r\n        }\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        sn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceName: null,\r\n        sourceArea: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        keywords: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: null,\r\n        deptId: null,\r\n        fileUrl: null,\r\n        firstPublishTime: null,\r\n        firstCreateTime: null,\r\n        firstWebstePublishTime: null,\r\n        webstePublishTime: null,\r\n        isChanged: null,\r\n        isTranslated: null,\r\n        isTechnology: 1,\r\n        isFile: null,\r\n        delFlag: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加文章列\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      // const id = row.id || this.ids\r\n      // getList(id).then(response => {\r\n      //   this.form = response.data;\r\n      //   this.open = true;\r\n      //   this.title = \"修改文章列\";\r\n      // });\r\n      API.AreaInfo(row.id).then((response) => {\r\n        this.form = response.data;\r\n        // this.form.sourceType = Number(this.form.sourceType)\r\n        this.form.docId = row.id.toString();\r\n        this.open = true;\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            // updateList(this.form).then(response => {\r\n            //   this.$modal.msgSuccess(\"修改成功\");\r\n            //   this.open = false;\r\n            //   this.getList();\r\n            // });\r\n            let queryForm = JSON.parse(JSON.stringify(this.form));\r\n            articleListEdit(queryForm).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addList(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除文章列编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          // return delList(ids);\r\n          return deleteByIds(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n      // this.$modal.confirm('是否确认删除该条文章？\"').then(() => {\r\n      //   return API.monitoringEsRemove({ id: row.id, docId: row.id.toString() });\r\n      // }).then(() => {\r\n      //   this.Refresh();\r\n      //   this.$modal.msgSuccess(\"删除成功\");\r\n      // }).catch(() => { });\r\n    },\r\n    // 文章详情\r\n    openArticle(item, row) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n    handleUpdate0(row) {\r\n      const articleIds = row.id || this.ids;\r\n      var str = articleIds.join(\",\");\r\n      this.$modal\r\n        .confirm('是否确认修改编号为\"' + str + '\"的数据为科技无关项？')\r\n        .then(function () {\r\n          return updateArticleTech0(str);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleUpdate1(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认修改编号为\"' + articleIds + '\"的数据为科技有关项？')\r\n        .then(function () {\r\n          return updateArticleTech1(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleUpdate2(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认修改编号为\"' + articleIds + '\"的数据为其他项？')\r\n        .then(function () {\r\n          return updateArticleTech2(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    processTechnologyState(state) {\r\n      if (state == 0) return \"无关\";\r\n      if (state == 1) return \"有关\";\r\n      if (state == 2) return \"其他\";\r\n      return \"未定义\";\r\n    },\r\n    processSourceType(state) {\r\n      if (state == 1) return \"国内\";\r\n      if (state == 2) return \"国外\";\r\n      if (state == 3) return \"境内科技\";\r\n      if (state == 4) return \"境外科技\";\r\n      return \"未定义\";\r\n    },\r\n    processSourceArea(state) {\r\n      if (state == 1) return \"网络数据工控安全\";\r\n      if (state == 2) return \"数字化转型数字经济\";\r\n      if (state == 3) return \"软件\";\r\n      if (state == 4) return \"产业链供应链\";\r\n      if (state == 5) return \"集成电路\";\r\n      if (state == 6) return \"人工智能\";\r\n      return \"未定义\";\r\n    },\r\n    processReviewedState(state) {\r\n      if (state == 0) return \"未审核\";\r\n      if (state == 1) return \"审核通过\";\r\n      if (state == 2) return \"审核不通过\";\r\n      if (state == 3) return \"待修改\";\r\n      if (state == 4) return \"待撤回\";\r\n      return \"未定义\";\r\n    },\r\n    processPulledState(state) {\r\n      if (state == 0) return \"未推送\";\r\n      if (state == 1) return \"已推送\";\r\n      return \"未定义\";\r\n    },\r\n    handlePass(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm(\"是否确认审核通过编号为\" + articleIds + \"的数据？\")\r\n        .then(function () {\r\n          return articlePass(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"审核通过\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleNoPass(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm(\"是否确认审核不通过编号为\" + articleIds + \"的数据？\")\r\n        .then(function () {\r\n          return articleNoPass(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"审核不通过\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleCancelPass(row) {\r\n      const articleIds = row.id || this.ids;\r\n      this.$modal\r\n        .confirm(\"是否确认撤回审核通过编号为\" + articleIds + \"的数据？\")\r\n        .then(function () {\r\n          return cancelArticlePass(articleIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"撤回审核通过\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    onCreateTask() {\r\n      this.$modal\r\n        .confirm(\"是否确认要领取任务？\")\r\n        .then(function () {\r\n          return getTask();\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"领取任务成功!\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleRadioChange(e, id) {\r\n      if (e == \"1\") {\r\n        console.log(\"通过\");\r\n        return articlePass(id);\r\n      } else if (e == \"2\") {\r\n        console.log(\"不通过\");\r\n        return articleNoPass(id);\r\n      } else if (e == \"4\") {\r\n        console.log(\"待撤回\");\r\n        return cancelArticlePass(id);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n::v-deep .queryForm {\r\n  .el-form-item {\r\n    width: 100%;\r\n    margin-right: 0;\r\n  }\r\n  .el-form-item__content {\r\n    width: calc(100% - 80px);\r\n  }\r\n}\r\n\r\n::v-deep .radio-group {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  .el-radio {\r\n    margin-right: 0;\r\n    margin-bottom: 2px;\r\n  }\r\n}\r\n</style>"]}]}