{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue?vue&type=style&index=0&id=692f5a28&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue", "mtime": 1754010127826}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5pbnB1dF9GaXhlZCB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQoudHJlZUJveCB7DQogIC8vIG1hcmdpbi10b3A6NzBweDsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogY2FsYygxMDB2aCAtIDE3OHB4KTsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCn0NCg0KLnRyZWUtcGFnaW5hdGlvbiB7DQogIHBhZGRpbmc6IDEwcHg7DQogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZWJlZWY1Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQoNCiAgOjp2LWRlZXAgLmVsLXBhZ2luYXRpb24gew0KICAgIC5lbC1wYWdpbmF0aW9uX19zaXplcyB7DQogICAgICBtYXJnaW4tdG9wOiAtMnB4Ow0KICAgIH0NCiAgfQ0KfQ0KDQoucmlnaHRNYWluIHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNjBweCk7DQogIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgLlRvcEJ0bkdyb3VwIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIHBhZGRpbmc6IDAgMjBweDsNCiAgICAvLyBiYWNrZ3JvdW5kOiAjZGJkYmQ4Ow0KICAgIC8vIG1hcmdpbi1ib3R0b206IDIwcHg7DQogICAgaGVpZ2h0OiA2MHB4Ow0KICAgIGJveC1zaGFkb3c6IDAgMHB4IDEwcHggMHB4ICNjZWNkY2Q7DQogICAgYm9yZGVyLWJvdHRvbTogc29saWQgMXB4ICNlMmUyZTI7DQoNCiAgICAuVG9wQnRuR3JvdXBfbGVmdCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICB9DQoNCiAgICAudG9vbFRpdGxlIHsNCiAgICAgIG1hcmdpbjogMCAxMHB4Ow0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQoNCiAgICAgIC5kZWVwc2Vlay10ZXh0IHsNCiAgICAgICAgY29sb3I6ICM1NTg5ZjU7IC8vIOS9v+eUqOS4juWbvuagh+ebuOWQjOeahOminOiJsg0KICAgICAgICBtYXJnaW4tbGVmdDogNHB4Ow0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICAgICAgfQ0KDQogICAgICAmOm50aC1vZi10eXBlKDEpIHsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDMwcHg7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLkFydGljbE1haW4gew0KICAgIHBhZGRpbmc6IDAgMCAwIDMwcHg7DQogICAgY29sb3I6ICMzZjNmM2Y7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICB9DQoNCiAgLkFydGljbE1haW4gPiBzcGFuOmhvdmVyIHsNCiAgICBjb2xvcjogIzE4ODlmMzsNCiAgICBib3JkZXItYm90dG9tOiBzb2xpZCAxcHggIzA3OThmODsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogIH0NCn0NCg0KOjp2LWRlZXAgLmRyYXdlcl9UaXRsZSB7DQogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KfQ0KDQo6OnYtZGVlcCAuZHJhd2VyX1N0eWxlIHsNCiAgei1pbmRleDogMjsNCiAgbWFyZ2luOiAwIDE1cHggMCAxNXB4Ow0KICB3aWR0aDogNjYxcHg7DQogIGhlaWdodDogODB2aDsNCg0KICAudGl0bGUgew0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBmb250LXdlaWdodDogNTAwcHg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICB9DQoNCiAgLnNvdXJjZSB7DQogICAgY29sb3I6ICMwNzk4Zjg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgfQ0KDQogIC50aW1lIHsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgIGNvbG9yOiAjOWI5YjliOw0KICB9DQp9DQoNCjo6di1kZWVwIC5lbC1pY29uLWRvY3VtZW50OmJlZm9yZSB7DQogIGNvbG9yOiAjNTU4OWY1Ow0KfQ0KDQo6OnYtZGVlcCAuZWwtaWNvbi1kb2N1bWVudC1hZGQ6YmVmb3JlIHsNCiAgY29sb3I6ICM1NTg5ZjU7DQp9DQoNCjo6di1kZWVwIC5lbC1pY29uLWNoYXQtZG90LXJvdW5kOmJlZm9yZSB7DQogIGNvbG9yOiAjNTU4OWY1Ow0KfQ0KDQo6OnYtZGVlcCAuZWwtaWNvbi1waWUtY2hhcnQ6YmVmb3JlIHsNCiAgY29sb3I6ICM1NTg5ZjU7DQp9DQoNCjo6di1kZWVwIC5lbC10YWJsZSB0ZC5lbC10YWJsZV9fY2VsbCBkaXYgew0KICBwYWRkaW5nLWxlZnQ6IDEwcHg7DQp9DQoNCjo6di1kZWVwIC5lbC10YWJsZS1jb2x1bW4tLXNlbGVjdGlvbiAuY2VsbCB7DQogIHBhZGRpbmctcmlnaHQ6IDBweDsNCiAgcGFkZGluZy1sZWZ0OiAxNHB4Ow0KICBtYXJnaW4tbGVmdDogNXB4Ow0KfQ0KDQo6OnYtZGVlcCAuZWwtZm9ybS1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLnRyZWVNYWluIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KDQoudHJlZVF1ZXJ5IHsNCiAgOjp2LWRlZXAgLmVsLWlucHV0LS1taW5pIC5lbC1pbnB1dF9faW5uZXIgew0KICAgIGhlaWdodDogMjRweDsNCiAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICBwYWRkaW5nOiAwIDRweDsNCiAgfQ0KDQogIDo6di1kZWVwIC5lbC1pbnB1dF9fc3VmZml4IHsNCiAgICAvLyBoZWlnaHQ6IDIwcHg7DQogICAgcmlnaHQ6IC0ycHg7DQogICAgLy8gdG9wOiA1cHg7DQogIH0NCn0NCg0KLmtleXdvcmQgew0KICB3aWR0aDogMTAwJTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KDQogIC5oaXN0b3J5IHsNCiAgICB3aWR0aDogNDMwcHg7DQogICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgei1pbmRleDogOTk5OTsNCiAgICBsZWZ0OiAwOw0KICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYigyMjEsIDIxOSwgMjE5KTsNCg0KICAgIC5oaXN0b3J5SXRlbSB7DQogICAgICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQoNCiAgICAgIC5oaXN0b3J5VGV4dCB7DQogICAgICAgIHdpZHRoOiA0NTBweDsNCiAgICAgICAgaGVpZ2h0OiAzNHB4Ow0KICAgICAgICBsaW5lLWhlaWdodDogMzRweDsNCiAgICAgIH0NCg0KICAgICAgJjpudGgtbGFzdC1vZi10eXBlKDEpIHsNCiAgICAgICAgcGFkZGluZy1sZWZ0OiAwOw0KDQogICAgICAgIDo6di1kZWVwIC5lbC1idXR0b24tLXRleHQgew0KICAgICAgICAgIHBhZGRpbmc6IDEwcHggMjBweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQoua2V5d29yZC10aXAgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjOTk5Ow0KICBtYXJnaW4tbGVmdDogOTBweDsNCiAgbGluZS1oZWlnaHQ6IDE7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5oaXN0b3J5IHsNCiAgd2lkdGg6IDUzMHB4Ow0KDQogIC5oaXN0b3J5SXRlbSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgcGFkZGluZzogMCAxMHB4Ow0KICAgIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgICAuaGlzdG9yeVRleHQgew0KICAgICAgd2lkdGg6IDM1MHB4Ow0KICAgICAgaGVpZ2h0OiAzNHB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDM0cHg7DQogICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIH0NCiAgfQ0KfQ0KDQo6OnYtZGVlcCAuZWwtdGFibGUtLW1lZGl1bSAuZWwtdGFibGVfX2NlbGwgew0KICBwYWRkaW5nOiAxMHB4IDA7DQp9DQoNCi5hcnRpY2xlX3RpdGxlIHsNCiAgbWFyZ2luLWxlZnQ6IDEwcHg7DQogIGZvbnQtc2l6ZTogMTVweDsNCn0NCg0KLmFydGljbGVfdGl0bGU6aG92ZXIgew0KICBjb2xvcjogIzE4ODlmMzsNCiAgYm9yZGVyLWJvdHRvbTogc29saWQgMXB4ICMwNzk4Zjg7DQogIGN1cnNvcjogcG9pbnRlcjsNCn0NCg0KLy8gYWnnm7jlhbMNCi5haS1jaGF0LWNvbnRhaW5lciB7DQogIGhlaWdodDogNTUwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQoNCiAgLmNoYXQtbWVzc2FnZXMgew0KICAgIGZsZXg6IDE7DQogICAgb3ZlcmZsb3cteTogYXV0bzsNCiAgICBwYWRkaW5nOiAyNHB4Ow0KDQogICAgLm1lc3NhZ2Ugew0KICAgICAgbWFyZ2luLWJvdHRvbTogMjhweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCg0KICAgICAgLmF2YXRhciB7DQogICAgICAgIHdpZHRoOiA0MnB4Ow0KICAgICAgICBoZWlnaHQ6IDQycHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgICAgZmxleC1zaHJpbms6IDA7DQogICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNmZmY7DQogICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KDQogICAgICAgIGltZyB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgIG9iamVjdC1maXQ6IGNvbnRhaW47DQogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAubWVzc2FnZS13cmFwcGVyIHsNCiAgICAgICAgbWFyZ2luOiAwIDE2cHg7DQogICAgICAgIG1heC13aWR0aDogY2FsYygxMDAlIC0gMTAwcHgpOw0KICAgICAgfQ0KDQogICAgICAubWVzc2FnZS1jb250ZW50IHsNCiAgICAgICAgcGFkZGluZzogMTJweCAxNnB4Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAxOw0KICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA2KTsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgICAgICY6OmJlZm9yZSB7DQogICAgICAgICAgY29udGVudDogIiI7DQogICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICAgIHRvcDogMTRweDsNCiAgICAgICAgICB3aWR0aDogMDsNCiAgICAgICAgICBoZWlnaHQ6IDA7DQogICAgICAgICAgYm9yZGVyOiA2cHggc29saWQgdHJhbnNwYXJlbnQ7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAudXNlci1tZXNzYWdlIHsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3ctcmV2ZXJzZTsNCg0KICAgICAgLm1lc3NhZ2Utd3JhcHBlciB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LWVuZDsNCiAgICAgIH0NCg0KICAgICAgLm1lc3NhZ2UtY29udGVudCB7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlNmYzZmY7DQogICAgICAgIGNvbG9yOiAjMmQyZDJkOw0KICAgICAgICBsaW5lLWhlaWdodDogMS44ZW07DQogICAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZyBTQywgc3lzdGVtLXVpLCAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsDQogICAgICAgICAgSGVsdmV0aWNhIE5ldWUsIEhpcmFnaW5vIFNhbnMgR0IsIE1pY3Jvc29mdCBZYUhlaSBVSSwgTWljcm9zb2Z0IFlhSGVpLA0KICAgICAgICAgIEFyaWFsLCBzYW5zLXNlcmlmOw0KDQogICAgICAgICY6OmJlZm9yZSB7DQogICAgICAgICAgcmlnaHQ6IC0xMnB4Ow0KICAgICAgICAgIGJvcmRlci1sZWZ0LWNvbG9yOiAjZTZmM2ZmOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLmFpLW1lc3NhZ2Ugew0KICAgICAgLm1lc3NhZ2Utd3JhcHBlciB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICAgICAgfQ0KDQogICAgICAubWVzc2FnZS1jb250ZW50IHsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgICAgICAgY29sb3I6ICMyZDJkMmQ7DQoNCiAgICAgICAgJjo6YmVmb3JlIHsNCiAgICAgICAgICBsZWZ0OiAtMTJweDsNCiAgICAgICAgICBib3JkZXItcmlnaHQtY29sb3I6ICNmZmY7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAudGhpbmtpbmctYW5pbWF0aW9uIHsNCiAgICBkaXNwbGF5OiBpbmxpbmUtZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHBhZGRpbmc6IDEycHggMTZweDsNCiAgICBtaW4taGVpZ2h0OiA0NXB4Ow0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA2KTsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQoNCiAgICAmOjpiZWZvcmUgew0KICAgICAgY29udGVudDogIiI7DQogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICB0b3A6IDE0cHg7DQogICAgICBsZWZ0OiAtMTJweDsNCiAgICAgIHdpZHRoOiAwOw0KICAgICAgaGVpZ2h0OiAwOw0KICAgICAgYm9yZGVyOiA2cHggc29saWQgdHJhbnNwYXJlbnQ7DQogICAgICBib3JkZXItcmlnaHQtY29sb3I6ICNmZmY7DQogICAgfQ0KDQogICAgc3BhbiB7DQogICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICB3aWR0aDogNnB4Ow0KICAgICAgaGVpZ2h0OiA2cHg7DQogICAgICBtYXJnaW46IDAgM3B4Ow0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQwOWVmZjsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICAgIG9wYWNpdHk6IDAuNzsNCiAgICAgIGFuaW1hdGlvbjogdGhpbmtpbmcgMS40cyBpbmZpbml0ZSBlYXNlLWluLW91dCBib3RoOw0KDQogICAgICAmOm50aC1jaGlsZCgxKSB7DQogICAgICAgIGFuaW1hdGlvbi1kZWxheTogLTAuMzJzOw0KICAgICAgfQ0KDQogICAgICAmOm50aC1jaGlsZCgyKSB7DQogICAgICAgIGFuaW1hdGlvbi1kZWxheTogLTAuMTZzOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5tZXNzYWdlLWNvbnRlbnQgew0KICAgIG1pbi1oZWlnaHQ6IDQ1cHg7DQogICAgd2hpdGUtc3BhY2U6IHByZS13cmFwOw0KICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZyBTQywgc3lzdGVtLXVpLCAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsDQogICAgICBIZWx2ZXRpY2EgTmV1ZSwgSGlyYWdpbm8gU2FucyBHQiwgTWljcm9zb2Z0IFlhSGVpIFVJLCBNaWNyb3NvZnQgWWFIZWksDQogICAgICBBcmlhbCwgc2Fucy1zZXJpZjsNCg0KICAgIDo6di1kZWVwIHsNCiAgICAgIGgxLA0KICAgICAgaDIsDQogICAgICBoMywNCiAgICAgIGg0LA0KICAgICAgaDUsDQogICAgICBoNiB7DQogICAgICAgIG1hcmdpbjogMC4wNWVtIDAgMC4wMmVtIDA7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAxLjhlbTsNCiAgICAgICAgY29sb3I6ICMyZDM3NDg7DQogICAgICB9DQoNCiAgICAgIGgxIHsNCiAgICAgICAgZm9udC1zaXplOiAxLjZlbTsNCiAgICAgICAgbWFyZ2luLXRvcDogMDsNCiAgICAgICAgcGFkZGluZy1ib3R0b206IDAuMDVlbTsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMC4wMmVtOw0KICAgICAgfQ0KDQogICAgICBoMiB7DQogICAgICAgIGZvbnQtc2l6ZTogMS40ZW07DQogICAgICAgIHBhZGRpbmctYm90dG9tOiAwLjA1ZW07DQogICAgICAgIG1hcmdpbi1ib3R0b206IDAuMDJlbTsNCiAgICAgIH0NCg0KICAgICAgaDMgew0KICAgICAgICBmb250LXNpemU6IDEuMmVtOw0KICAgICAgfQ0KDQogICAgICBwIHsNCiAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICBsaW5lLWhlaWdodDogMS44ZW07DQogICAgICAgIGNvbG9yOiAjMmQzNzQ4Ow0KICAgICAgfQ0KDQogICAgICBzdHJvbmcgew0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICBjb2xvcjogIzFhMWExYTsNCiAgICAgIH0NCg0KICAgICAgZW0gew0KICAgICAgICBmb250LXN0eWxlOiBpdGFsaWM7DQogICAgICAgIGNvbG9yOiAjMmM1MjgyOw0KICAgICAgfQ0KDQogICAgICB1bCwNCiAgICAgIG9sIHsNCiAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICBwYWRkaW5nLWxlZnQ6IDFlbTsNCiAgICAgICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50Ow0KICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uICFpbXBvcnRhbnQ7DQogICAgICAgIC8vIHJvdy1nYXA6IDIwcHggIWltcG9ydGFudDsNCg0KICAgICAgICBsaSB7DQogICAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjhlbTsNCiAgICAgICAgICBjb2xvcjogIzJkMzc0ODsNCg0KICAgICAgICAgIC8vIOWmguaenGxp5Lit5YyF5ZCrcOagh+etvu+8jOWImeiuvue9ruihjOmrmOS4ujENCiAgICAgICAgICAmOmhhcyhwKSB7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlpoLmnpxsaeS4reayoeaciXDmoIfnrb7vvIzkv53mjIHpu5jorqTooYzpq5gxLjhlbe+8iOW3suWcqOS4iumdouiuvue9ru+8iQ0KDQogICAgICAgICAgcCB7DQogICAgICAgICAgICBtYXJnaW46IDA7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMS44ZW07DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGJsb2NrcXVvdGUgew0KICAgICAgICBtYXJnaW46IDAuMDVlbSAwOw0KICAgICAgICBwYWRkaW5nOiAwLjA1ZW0gMC40ZW07DQogICAgICAgIGNvbG9yOiAjMmM1MjgyOw0KICAgICAgICBiYWNrZ3JvdW5kOiAjZWJmOGZmOw0KICAgICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICM0Mjk5ZTE7DQoNCiAgICAgICAgcCB7DQogICAgICAgICAgbWFyZ2luOiAwLjAyZW0gMDsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMS44ZW07DQogICAgICAgIH0NCg0KICAgICAgICA+IDpmaXJzdC1jaGlsZCB7DQogICAgICAgICAgbWFyZ2luLXRvcDogMDsNCiAgICAgICAgfQ0KDQogICAgICAgID4gOmxhc3QtY2hpbGQgew0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY29kZSB7DQogICAgICAgIHBhZGRpbmc6IDAuMDVlbSAwLjFlbTsNCiAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICBmb250LXNpemU6IDAuOWVtOw0KICAgICAgICBiYWNrZ3JvdW5kOiAjZWRmMmY3Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7DQogICAgICAgIGNvbG9yOiAjMmQzNzQ4Ow0KICAgICAgfQ0KDQogICAgICBociB7DQogICAgICAgIGhlaWdodDogMXB4Ow0KICAgICAgICBtYXJnaW46IDAuMWVtIDA7DQogICAgICAgIGJvcmRlcjogbm9uZTsNCiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UyZThmMDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLmNoYXQtbWVzc2FnZXMgew0KICAmOjotd2Via2l0LXNjcm9sbGJhciB7DQogICAgd2lkdGg6IDZweDsNCiAgfQ0KDQogICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDE5MiwgMTk2LCAyMDQsIDAuNSk7DQogICAgYm9yZGVyLXJhZGl1czogM3B4Ow0KDQogICAgJjpob3ZlciB7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDE5MiwgMTk2LCAyMDQsIDAuOCk7DQogICAgfQ0KICB9DQp9DQoNCkBrZXlmcmFtZXMgdGhpbmtpbmcgew0KICAwJSwNCiAgODAlLA0KICAxMDAlIHsNCiAgICB0cmFuc2Zvcm06IHNjYWxlKDApOw0KICB9DQogIDQwJSB7DQogICAgdHJhbnNmb3JtOiBzY2FsZSgxKTsNCiAgfQ0KfQ0KDQovLyDkv67mlLnlvLnnqpfmoLflvI8NCjo6di1kZWVwIC5haS1kaWFsb2cgew0KICAuZWwtZGlhbG9nX19ib2R5IHsNCiAgICBwYWRkaW5nOiAwOw0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQogIH0NCg0KICAuZWwtZGlhbG9nX19mb290ZXIgew0KICAgIHBhZGRpbmc6IDE1cHggMjBweDsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTRlN2VkOw0KDQogICAgLmVsLWJ1dHRvbiB7DQogICAgICBwYWRkaW5nOiA5cHggMjBweDsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICB9DQogIH0NCn0NCg0KLmNoYXJ0LWNvbnRhaW5lciB7DQogIG1pbi1oZWlnaHQ6IDYwMHB4Ow0KICB3aWR0aDogMTAwJTsNCiAgb3ZlcmZsb3c6IGF1dG87DQogIHBhZGRpbmc6IDA7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAuY2hhcnQtY29udGVudCB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiA2MDBweDsNCiAgICBvdmVyZmxvdzogYXV0bzsNCiAgICBkaXNwbGF5OiBibG9jazsNCiAgfQ0KfQ0KDQo6OnYtZGVlcCAuY2hhcnQtZGlhbG9nIHsNCiAgLmVsLWRpYWxvZ19fYm9keSB7DQogICAgcGFkZGluZzogMDsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICB9DQoNCiAgLmVsLWRpYWxvZ19faGVhZGVyIHsNCiAgICAvLyBwYWRkaW5nOiAxNXB4Ow0KICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOw0KICB9DQoNCiAgLmVsLWRpYWxvZ19fZm9vdGVyIHsNCiAgICBwYWRkaW5nOiAxMHB4IDE1cHg7DQogICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlNGU3ZWQ7DQogIH0NCn0NCg0KLnBhZ2luYXRpb24tY29udGFpbmVyIHsNCiAgcGFkZGluZzogMCAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["Wechat.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqs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file": "Wechat.vue", "sourceRoot": "src/views/InfoEscalation", "sourcesContent": ["<template>\r\n  <div v-loading=\"globalLoading\" element-loading-text=\"数据加载中\">\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"26\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n        />\r\n      </pane>\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"74\">\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"Form\"\r\n            label-width=\"90px\"\r\n            @submit.native.prevent\r\n          >\r\n            <el-form-item label=\"发布日期:\" prop=\"dateType\">\r\n              <el-radio-group v-model=\"queryParams.dateType\" size=\"small\">\r\n                <el-radio-button :label=\"1\">今天</el-radio-button>\r\n                <el-radio-button :label=\"2\">近2天</el-radio-button>\r\n                <el-radio-button :label=\"4\">近7天</el-radio-button>\r\n                <el-radio-button :label=\"5\">近30天</el-radio-button>\r\n                <el-radio-button :label=\"10\">全部</el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <div style=\"display: flex\">\r\n              <el-form-item\r\n                label=\"是否与科技有关:\"\r\n                prop=\"isTechnology\"\r\n                style=\"margin-right: 20px\"\r\n              >\r\n                <el-radio-group v-model=\"queryParams.isTechnology\" size=\"small\">\r\n                  <el-radio-button\r\n                    v-for=\"dict in dict.type.is_technology\"\r\n                    :label=\"dict.value\"\r\n                    :key=\"'is_technology' + dict.value\"\r\n                    >{{ dict.label }}</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n              <el-form-item label=\"小信精选:\" prop=\"emotion\">\r\n                <el-radio-group v-model=\"queryParams.emotion\" size=\"small\">\r\n                  <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                    >选中</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item label=\"检索词库:\" prop=\"tags\">\r\n              <el-radio-group v-model=\"queryParams.tags\" size=\"small\">\r\n                <el-radio :label=\"''\">全部</el-radio>\r\n                <el-radio\r\n                  v-for=\"item in tagsList1\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.id\"\r\n                  >{{ item.name }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item\r\n              style=\"width: 100%; overflow: auto\"\r\n              label=\"\"\r\n              prop=\"tagsSubset\"\r\n              v-if=\"queryParams.tags != ''\"\r\n            >\r\n              <el-checkbox\r\n                style=\"float: left; margin-right: 30px\"\r\n                :indeterminate=\"isIndeterminate\"\r\n                v-model=\"checkAll\"\r\n                @change=\"handleCheckAllTagsSubset\"\r\n                >全选</el-checkbox\r\n              >\r\n              <el-checkbox-group v-model=\"queryParams.tagsSubset\">\r\n                <el-checkbox\r\n                  v-for=\"item in tagsList\"\r\n                  :key=\"item.name\"\r\n                  :label=\"item.name\"\r\n                ></el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n            <el-form-item class=\"keyword\" label=\"关键词:\" prop=\"keywords\">\r\n              <el-input\r\n                ref=\"keywordRef\"\r\n                placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                style=\"width: 430px\"\r\n                v-model=\"queryParams.keywords\"\r\n                @focus=\"showHistoryList()\"\r\n                @blur=\"hideHistoryList()\"\r\n                @keyup.enter.native=\"handleSearch()\"\r\n              >\r\n              </el-input>\r\n              <div class=\"history\" v-show=\"showHistory\">\r\n                <div\r\n                  class=\"historyItem\"\r\n                  v-for=\"(history, index) in historyList\"\r\n                  :key=\"index\"\r\n                  v-loading=\"historyLoading\"\r\n                >\r\n                  <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                    {{ history.keyword }}\r\n                  </div>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"removeHistory(history, 1)\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >删除</el-button\r\n                  >\r\n                </div>\r\n                <div class=\"historyItem\">\r\n                  <el-button type=\"text\" @click=\"moreHistory()\">更多</el-button>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"clearHistory()\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >清空</el-button\r\n                  >\r\n                </div>\r\n              </div>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                style=\"margin-left: 10px; height: 36px\"\r\n                @click=\"handleSearch\"\r\n                >搜索</el-button\r\n              >\r\n            </el-form-item>\r\n            <div class=\"keyword-tip\">\r\n              *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n            </div>\r\n          </el-form>\r\n          <div class=\"TopBtnGroup\">\r\n            <div class=\"TopBtnGroup_left\">\r\n              <el-checkbox v-model=\"checked\" @change=\"handleCheckAllChange\"\r\n                >全选</el-checkbox\r\n              >\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  title=\"批量删除文章\"\r\n                  class=\"icon-shanchu\"\r\n                  @click=\"batchDelete\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"icon-shuaxin-copy\"\r\n                  title=\"刷新\"\r\n                  @click=\"handleSearch('refresh')\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p class=\"toolTitle\">\r\n              <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"openReport\" v-hasPermi=\"['result:report:add']\"></i>\r\n            </p> -->\r\n              <!-- <p class=\"toolTitle\">\r\n              <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\"\r\n                v-hasPermi=\"['article:collection:snapshot']\" @click=\"resultEvent()\"></i>\r\n            </p> -->\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"添加到工作台账\"\r\n                  @click=\"openTaizhang\"\r\n                  v-hasPermi=\"['article:work:add']\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document-add\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"发布到每日最新热点\"\r\n                  @click=\"publishHot\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-chat-dot-round\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"Deepseek深度解读\"\r\n                  @click=\"articleAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n                  >Deepseek深度解读</span\r\n                >\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-pie-chart\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"生成Deepseek图表看板\"\r\n                  @click=\"chartAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"chartAiChat\"\r\n                  >生成Deepseek图表看板</span\r\n                >\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <el-checkbox\r\n                v-model=\"showSummary\"\r\n                @change=\"(e) => (showSummary = e)\"\r\n                style=\"margin-right: 10px\"\r\n                >是否显示摘要</el-checkbox\r\n              >\r\n              <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n              <el-select v-model=\"queryParams.sortMode\" size=\"mini\">\r\n                <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n                <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n                <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n                <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n                <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n          <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate0\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技无关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate1\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技有关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate2\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为其他</el-button\r\n              >\r\n            </el-col>\r\n          </el-row> -->\r\n          <el-table\r\n            :data=\"ArticleList\"\r\n            style=\"width: 100%; user-select: text\"\r\n            :show-header=\"false\"\r\n            ref=\"table\"\r\n            :height=\"\r\n              'calc(100vh - ' +\r\n              (374 + (queryParams.tags != '' ? 51 : 0)) +\r\n              'px)'\r\n            \"\r\n            @selection-change=\"handleTableSelectionChange\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"35\" align=\"center\" />\r\n            <el-table-column width=\"50\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #080808; font-size: 15px\">\r\n                  {{\r\n                    (queryParams.pageNum - 1) * queryParams.pageSize +\r\n                    scope.$index +\r\n                    1\r\n                  }}\r\n                </span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"编号\"\r\n              align=\"center\"\r\n              key=\"id\"\r\n              prop=\"id\"\r\n              width=\"100\"\r\n              v-if=\"false\"\r\n            />\r\n            <el-table-column prop=\"title\" label=\"日期\" min-width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <span class=\"article_title\" @click=\"openNewView(scope.row)\">\r\n                  <span\r\n                    style=\"color: #080808\"\r\n                    v-html=\"scope.row.title || scope.row.cnTitle\"\r\n                  ></span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ \"(\" + scope.row.publishTime + \")\" }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ scope.row.sourceName }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    大模型筛选:{{ scope.row.isTechnology == 1 ? \"是\" : \"否\" }}\r\n                  </span>\r\n                </span>\r\n                <div\r\n                  class=\"ArticlMain\"\r\n                  style=\"\r\n                    display: -webkit-box;\r\n                    -webkit-box-orient: vertical;\r\n                    -webkit-line-clamp: 2;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                    word-break: break-all;\r\n                  \"\r\n                  v-if=\"\r\n                    showSummary &&\r\n                    hasActualContent(scope.row.cnSummary || scope.row.summary)\r\n                  \"\r\n                >\r\n                  <span style=\"color: #9b9b9b\">摘要：</span>\r\n                  <span\r\n                    style=\"color: #4b4b4b\"\r\n                    v-html=\"\r\n                      changeColor(\r\n                        scope.row.cnSummary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        ) ||\r\n                          scope.row.summary.replace(\r\n                            /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                            'span'\r\n                          )\r\n                      )\r\n                    \"\r\n                    @click=\"openNewView(scope.row)\"\r\n                  ></span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column prop=\"publishTime\" label=\"发布时间\" width=\"180\">\r\n            </el-table-column> -->\r\n            <!-- <el-table-column label=\"操作\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"></i>\r\n              </template>\r\n            </el-table-column> -->\r\n          </el-table>\r\n          <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"handlePagination\"\r\n            :autoScroll=\"true\"\r\n          />\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"500px\"\r\n      :before-close=\"closeReport\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeReport\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"handleHistoryPagination\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n        :autoScroll=\"true\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek图表看板\"\r\n      :visible.sync=\"chartDialogVisible\"\r\n      width=\"1200px\"\r\n      :before-close=\"closeChartDialog\"\r\n      custom-class=\"chart-dialog\"\r\n      destroy-on-close\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div\r\n        v-if=\"chartDialogVisible\"\r\n        class=\"chart-container\"\r\n        v-loading=\"chartLoading\"\r\n        element-loading-text=\"正在生成图表看板...\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        element-loading-background=\"rgba(255, 255, 255, 0.8)\"\r\n      >\r\n        <div class=\"chart-content\" ref=\"chartContent\"></div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeChartDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listWork,\r\n  getWork,\r\n  delWork,\r\n  addWork,\r\n  updateWork,\r\n} from \"@/api/article/work\";\r\nimport { listKeywords } from \"@/api/article/keywords\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n  getListByIds,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport { mapGetters } from \"vuex\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\n\r\nexport default {\r\n  components: { Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tableLoading: false, // 表格loading状态\r\n      queryParams: {\r\n        id: 100,\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        dateType: 4,\r\n        tags: \"\",\r\n        tagsSubset: [],\r\n        keywords: \"\",\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      },\r\n      total: 0,\r\n      treeDataTransfer: [], // 原始树形数据\r\n      filterText: \"\", // 左侧树搜索栏\r\n      checkList: [], // 左侧勾选数据\r\n      ArticleList: [], // 列表数据\r\n      checked: false, // 全选\r\n      ids: [], // 选中的数据\r\n      // 非多个禁用\r\n      multiple: true,\r\n      dialogVisible: false, // 添加到报告弹框\r\n      reportOptions: [], // 报告列表\r\n      reportId: \"\", // 已选择的报告\r\n      tagsList: [], // 检索词库二级列表\r\n      tagsList1: [], // 检索词库一级列表\r\n      checkAll: false, // 检索词库全选\r\n      isIndeterminate: true, // 检索词库选了值\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      showSummary: true,\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n      /* 初始化完成标记 */\r\n      initializationCompleted: false,\r\n      /* 搜索防抖 */\r\n      searchDebounceTimer: null,\r\n      /* 查询防抖 */\r\n      queryDebounceTimer: null,\r\n      /* 防止重复查询 */\r\n      isQuerying: false,\r\n      /* 标记右侧筛选条件是否发生变化 */\r\n      isRightFilter: false,\r\n      /* 标记左侧树是否重置 */\r\n      isLeftReset: false,\r\n      /* 选中的数据源分类 */\r\n      selectedClassify: \"5\",\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n      nodeCheckList: [],\r\n      chartDialogVisible: false,\r\n      chartHtml: \"\",\r\n      chartLoading: true,\r\n      currentChartIframe: null, // 添加变量跟踪当前iframe\r\n      difyApikey: {\r\n        article: \"\",\r\n        chart: \"\",\r\n      },\r\n      chartPrompt: \"\",\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"queryParams.dateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.tags\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n\r\n        // this.queryParams.tagsSubset = [];\r\n        this.checkAll = true;\r\n        this.isIndeterminate = false;\r\n\r\n        if (newVal != \"\") {\r\n          // 不在这里设置tableLoading，让后续的queryArticleList来处理\r\n          listKeywords({ parentId: newVal, pageNum: 1, pageSize: 10 })\r\n            .then((res) => {\r\n              this.tagsList = res.data;\r\n              this.handleCheckAllTagsSubset(true);\r\n              // this.handleRightFilterChange();\r\n            })\r\n            .catch((error) => {\r\n              console.error(\"获取检索词库失败:\", error);\r\n              this.$message.error(\"获取检索词库失败\");\r\n            });\r\n        } else {\r\n          this.handleRightFilterChange();\r\n        }\r\n      },\r\n    },\r\n    \"queryParams.tagsSubset\": {\r\n      handler(newVal, oldVal) {\r\n        if (\r\n          !this.initializationCompleted ||\r\n          JSON.stringify(newVal) === JSON.stringify(oldVal)\r\n        )\r\n          return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      },\r\n    },\r\n    dialogVisible(val) {\r\n      if (val) {\r\n        api.getNewBuilt({ sourceType: \"1\" }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n            this.closeReport();\r\n          }\r\n        });\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"roles\"]),\r\n  },\r\n  async created() {\r\n    getConfigKey(\"sys.ai.platform\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.aiPlatform = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.articleAiPrompt = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.chartPrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.chartPrompt = res.msg;\r\n      }\r\n    });\r\n    // 获取用户头像\r\n    this.userAvatar = this.$store.getters.avatar;\r\n    try {\r\n      // 先加载基础数据\r\n      Promise.all([\r\n        this.getArticleHistory(),\r\n        listKeywords({ parentId: 0, pageNum: 1, pageSize: 10 }).then((res) => {\r\n          this.tagsList1 = res.data.filter((item) => item.parentId == 0);\r\n        }),\r\n      ]);\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      if (this.roles.includes(\"information\")) {\r\n        this.showSummary = false;\r\n      }\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {},\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        this.globalLoading = true;\r\n        // 先加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n\r\n        // 默认全选第一页数据源\r\n        if (this.treeDataTransfer && this.treeDataTransfer.length > 0) {\r\n          // 全选第一页的所有数据源\r\n          const firstPageData = [...this.treeDataTransfer];\r\n          this.checkList = firstPageData;\r\n          this.savedCheckboxData = firstPageData;\r\n\r\n          // 通知 TreeTable 组件设置选中状态\r\n          this.$nextTick(() => {\r\n            if (this.$refs.treeTable) {\r\n              this.$refs.treeTable.restoreSelectionSilently(firstPageData);\r\n            }\r\n          });\r\n\r\n          // 延迟一下再查询文章列表，确保选中状态已设置\r\n          setTimeout(() => {\r\n            this.queryArticleList();\r\n          }, 100);\r\n        } else {\r\n          // 如果没有数据源，直接查询文章列表\r\n          this.queryArticleList();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n        this.globalLoading = false;\r\n      }\r\n    },\r\n\r\n    // 处理右侧筛选条件变化\r\n    handleRightFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handlePagination() {\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 历史记录分页处理\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n      this.$nextTick(() => {\r\n        const dialogContent = document.querySelector(\".el-dialog__body\");\r\n        if (dialogContent) {\r\n          dialogContent.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          platformType: 0,\r\n          id: this.queryParams.id,\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          m: 1,\r\n          dateType:\r\n            this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n          tags: this.queryParams.tags,\r\n          tagsSubset: this.queryParams.tagsSubset,\r\n          keywords: this.queryParams.keywords,\r\n          isTechnology: this.queryParams.isTechnology,\r\n          emotion: this.queryParams.emotion,\r\n          label: this.queryParams.tagsSubset.join(\",\"),\r\n          // 添加关键字过滤参数\r\n          filterwords: this.filterText || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          hasCache: this.queryParams.hasCache,\r\n        };\r\n\r\n        if (!this.queryParams.tags) {\r\n          params.tagsSubset = [];\r\n          params.label = \"\";\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.queryParams.pageNum,\r\n            pageSize: this.queryParams.pageSize,\r\n            id: this.queryParams.id,\r\n            isSort: this.queryParams.sortMode,\r\n            dateType:\r\n              this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n            tags: this.queryParams.tags,\r\n            tagsSubset: this.queryParams.tagsSubset,\r\n            keywords: this.queryParams.keywords,\r\n            isTechnology: this.queryParams.isTechnology,\r\n            emotion: this.queryParams.emotion,\r\n            label: this.queryParams.tagsSubset.join(\",\"),\r\n            platformType: 0,\r\n          };\r\n\r\n          if (!this.queryParams.tags) {\r\n            params.tagsSubset = [];\r\n            params.label = \"\";\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 1 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          const res = await api.esRetrieval(params);\r\n\r\n          if (res.code == 200) {\r\n            let articleList = res.data.list\r\n              ? res.data.list.map((item) => {\r\n                  item.cnTitle = item.cnTitle\r\n                    ? this.changeColor(item.cnTitle)\r\n                    : null;\r\n                  item.title = this.changeColor(item.title);\r\n                  return item;\r\n                })\r\n              : [];\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.queryParams.keywords ||\r\n              this.queryParams.keywords.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n            this.total = res.data.total || 0;\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.queryParams.pageSize * (this.queryParams.pageNum - 1) >=\r\n                this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.queryParams.pageNum = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.queryParams.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.queryParams.pageNum = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤文本，避免触发 handleFilterSearch\r\n      this.filterText = \"\";\r\n      this.selectedClassify = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 重置树选择（保留原方法名以兼容）\r\n    treeClear() {\r\n      this.handleReset();\r\n    },\r\n\r\n    // 处理树分页\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库全选处理\r\n    handleCheckAllTagsSubset(val) {\r\n      this.queryParams.tagsSubset = val\r\n        ? this.tagsList.map((item) => item.name)\r\n        : [];\r\n      this.isIndeterminate = false;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 更新过滤文本\r\n      this.filterText = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库多选处理\r\n    handleCheckedChange(value) {\r\n      let checkedCount = value.length;\r\n      this.checkAll = checkedCount === this.tagsList.length;\r\n      this.isIndeterminate =\r\n        checkedCount > 0 && checkedCount < this.tagsList.length;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n      this.handleRightFilterChange();\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch(flag) {\r\n      this.scrollToTopImmediately();\r\n      if (!flag) {\r\n        this.queryParams.pageNum = 1;\r\n      }\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 关键词历史选择\r\n    keywordsChange(item) {\r\n      this.queryParams.keywords = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.queryParams.pageNum = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 右侧表格多选框选中数据\r\n    handleTableSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      if (selection.length == this.ArticleList.length) {\r\n        this.checked = true;\r\n      } else {\r\n        this.checked = false;\r\n      }\r\n      this.multiple = !selection.length;\r\n    },\r\n    // 全选\r\n    handleCheckAllChange(val) {\r\n      if (val) {\r\n        this.$refs[\"table\"].toggleAllSelection();\r\n      } else {\r\n        this.$refs[\"table\"].clearSelection();\r\n      }\r\n    },\r\n    // 打开添加到报告\r\n    openReport() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确定添加到报告\r\n    async reportSubmit() {\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      let keyWordList = this.ids.map((item) => {\r\n        return { reportId: this.reportId, listId: item };\r\n      });\r\n      let res = await api.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.queryArticleList();\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.$refs[\"table\"].clearSelection();\r\n      this.checked = false;\r\n      this.closeReport();\r\n    },\r\n    // 关闭添加到报告\r\n    closeReport() {\r\n      this.reportId = \"\";\r\n      this.dialogVisible = false;\r\n    },\r\n    // 批量删除\r\n    batchDelete() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要删除的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.ids.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 添加到台账\r\n    openTaizhang() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认添加已勾选的数据项到台账统计?\")\r\n        .then(() => {\r\n          addWork(this.ids).then(() => {\r\n            this.$message({ type: \"success\", message: \"添加成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 发布到每日最新热点\r\n    publishHot() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.ids.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 历史记录相关方法\r\n    async removeHistory(item, type) {\r\n      clearTimeout(this.historyTimeout);\r\n      await delArticleHistory([item.id]);\r\n      if (type == 1) {\r\n        this.$refs[\"keywordRef\"].focus();\r\n        this.getArticleHistory();\r\n      } else {\r\n        this.getArticleHistory();\r\n        this.getArticleHistory1();\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      this.showHistory = true;\r\n    },\r\n\r\n    hideHistoryList() {\r\n      this.historyTimeout = setTimeout(() => {\r\n        this.showHistory = false;\r\n      }, 500);\r\n    },\r\n\r\n    getArticleHistory() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ pageNum: 1, pageSize: 5, type: 1 }).then(\r\n        (response) => {\r\n          this.historyList = response.rows;\r\n          this.historyLoading = false;\r\n        }\r\n      );\r\n    },\r\n\r\n    async clearHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.$refs[\"keywordRef\"].focus();\r\n      await cleanArticleHistory(1);\r\n      this.getArticleHistory();\r\n    },\r\n\r\n    moreHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.showHistory = false;\r\n      this.historyLoading = true;\r\n      this.getArticleHistory1();\r\n      this.dialogVisible1 = true;\r\n    },\r\n\r\n    getArticleHistory1() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ ...this.queryParams1, type: 1 }).then((response) => {\r\n        this.historyList1 = response.rows;\r\n        this.total1 = response.total;\r\n        this.historyLoading = false;\r\n      });\r\n    },\r\n\r\n    // 文章详情\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 检查文本是否有实际内容\r\n    hasActualContent(text) {\r\n      if (!text) return false;\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      if (this.$refs.rightMain) {\r\n        this.$refs.rightMain.scrollTop = 0;\r\n      }\r\n\r\n      if (this.$refs.table) {\r\n        const bodyWrapper = this.$refs.table.$el.querySelector(\r\n          \".el-table__body-wrapper\"\r\n        );\r\n        if (bodyWrapper) {\r\n          bodyWrapper.scrollTop = 0;\r\n        }\r\n      }\r\n    },\r\n\r\n    // 关键字高亮\r\n    changeColor(str) {\r\n      const regex = /<img\\b[^>]*>/gi;\r\n      let Str = str && str.replace(regex, \"\");\r\n      if (\r\n        Str &&\r\n        ((this.queryParams.tags &&\r\n          this.queryParams.tagsSubset &&\r\n          this.queryParams.tagsSubset.length) ||\r\n          this.queryParams.keywords)\r\n      ) {\r\n        let keywords = [\r\n          ...this.queryParams.tagsSubset,\r\n          ...(this.queryParams.keywords\r\n            ? this.queryParams.keywords.split(\",\")\r\n            : []),\r\n        ];\r\n        keywords.forEach((keyitem) => {\r\n          if (keyitem && keyitem.length > 0) {\r\n            let replaceReg = new RegExp(keyitem, \"g\");\r\n            let replaceString =\r\n              '<span class=\"highlight\" style=\"color: red;\">' +\r\n              keyitem +\r\n              \"</span>\";\r\n            Str = Str.replace(replaceReg, replaceString);\r\n          }\r\n        });\r\n      }\r\n      return Str;\r\n    },\r\n\r\n    // 快照生成\r\n    resultEvent() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message.warning(\"请先选择文章\");\r\n      }\r\n      let ids = this.ids;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (ids.length == 1) {\r\n        let row = this.ArticleList.filter((item) => item.id == ids[0]);\r\n        if (row && row.snapshotUrl) zhuangtai = \"查看\";\r\n        url = row.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        this.$msgbox({\r\n          title: \"提示\",\r\n          message: \"快照正在生成中，请稍后查看\",\r\n          showCancelButton: false,\r\n          confirmButtonText: \"关闭\",\r\n          beforeClose: (_, __, done) => {\r\n            done();\r\n          },\r\n        });\r\n        API.downLoadExportKe(ids)\r\n          .then((response) => {\r\n            if (response.code != 200) {\r\n              this.$message({\r\n                message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                type: \"error\",\r\n              });\r\n            }\r\n          })\r\n          .catch(() => {});\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.ids.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n    chartAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyChartAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekChartAiChat();\r\n      }\r\n    },\r\n    // dify图表看板\r\n    async difyChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await difyAiQa(\r\n          articleResult.data.content,\r\n          \"blocking\",\r\n          \"dify.chart.apikey\"\r\n        );\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.answer) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = \"\";\r\n\r\n        try {\r\n          // 尝试解析JSON格式（有些返回可能是JSON字符串）\r\n          const parsedData = JSON.parse(aiData.answer);\r\n          content2 =\r\n            parsedData.answer ||\r\n            parsedData.html ||\r\n            parsedData.content ||\r\n            aiData.answer;\r\n        } catch (e) {\r\n          // 如果不是JSON格式，直接使用原始内容\r\n          content2 = aiData.answer;\r\n        }\r\n\r\n        // 处理思考标记\r\n        const thinkStartIndex = content2.indexOf(\"<think>\");\r\n        const thinkEndIndex = content2.indexOf(\"</think>\");\r\n\r\n        // 提取有效内容\r\n        if (thinkStartIndex !== -1 && thinkEndIndex !== -1) {\r\n          // 如果存在思考标记，只取</think>后面的内容\r\n          content2 = content2.substring(thinkEndIndex + 8).trim();\r\n        }\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // deepseek图表看板\r\n    async deepseekChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        const prompt = this.chartPrompt + `\\n\\n${articleResult.data.content}`;\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await deepseekAiQa(prompt, false);\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.choices) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = aiData.choices[0].message.content;\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // 关闭图表对话框\r\n    closeChartDialog() {\r\n      this.isAborted = true;\r\n      this.chartDialogVisible = false;\r\n      this.chartHtml = \"\";\r\n      this.chartLoading = false;\r\n      this.isRequesting = false;\r\n\r\n      // 清理Chart实例\r\n      if (this.currentChartIframe && this.currentChartIframe.contentWindow) {\r\n        try {\r\n          // 尝试销毁所有Chart实例\r\n          if (this.currentChartIframe.contentWindow.Chart) {\r\n            const instances =\r\n              this.currentChartIframe.contentWindow.Chart.instances;\r\n            if (instances) {\r\n              Object.values(instances).forEach((instance) => {\r\n                if (instance && typeof instance.destroy === \"function\") {\r\n                  instance.destroy();\r\n                }\r\n              });\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error(\"清理Chart实例失败:\", e);\r\n        }\r\n      }\r\n\r\n      // 清空图表容器内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      // 清理iframe引用\r\n      if (this.currentChartIframe) {\r\n        try {\r\n          this.currentChartIframe.onload = null;\r\n          this.currentChartIframe.onerror = null;\r\n          this.currentChartIframe = null;\r\n        } catch (e) {\r\n          console.error(\"清理iframe失败:\", e);\r\n        }\r\n      }\r\n    },\r\n    // 执行iframe内的所有内联脚本\r\n    executeIframeScripts(iframe) {\r\n      // 简化后的方法，不再尝试手动执行脚本\r\n      console.log(\"图表iframe已加载，等待自然渲染...\");\r\n\r\n      // 确保所有图表都有机会渲染后再隐藏loading\r\n      setTimeout(() => {\r\n        this.chartLoading = false;\r\n      }, 800);\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.input_Fixed {\r\n  width: 100%;\r\n}\r\n\r\n.treeBox {\r\n  // margin-top:70px;\r\n  width: 100%;\r\n  height: calc(100vh - 178px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.rightMain {\r\n  height: calc(100vh - 60px);\r\n  overflow: hidden;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    // background: #dbdbd8;\r\n    // margin-bottom: 20px;\r\n    height: 60px;\r\n    box-shadow: 0 0px 10px 0px #cecdcd;\r\n    border-bottom: solid 1px #e2e2e2;\r\n\r\n    .TopBtnGroup_left {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    .toolTitle {\r\n      margin: 0 10px;\r\n      display: flex;\r\n      align-items: center;\r\n      cursor: pointer;\r\n\r\n      .deepseek-text {\r\n        color: #5589f5; // 使用与图标相同的颜色\r\n        margin-left: 4px;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      &:nth-of-type(1) {\r\n        margin-left: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ArticlMain {\r\n    padding: 0 0 0 30px;\r\n    color: #3f3f3f;\r\n    font-size: 14px;\r\n    line-height: 24px;\r\n  }\r\n\r\n  .ArticlMain > span:hover {\r\n    color: #1889f3;\r\n    border-bottom: solid 1px #0798f8;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n::v-deep .drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n::v-deep .drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-document:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-pie-chart:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-table td.el-table__cell div {\r\n  padding-left: 10px;\r\n}\r\n\r\n::v-deep .el-table-column--selection .cell {\r\n  padding-right: 0px;\r\n  padding-left: 14px;\r\n  margin-left: 5px;\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 0;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 90px;\r\n  line-height: 1;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-table--medium .el-table__cell {\r\n  padding: 10px 0;\r\n}\r\n\r\n.article_title {\r\n  margin-left: 10px;\r\n  font-size: 15px;\r\n}\r\n\r\n.article_title:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n  cursor: pointer;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n.chart-container {\r\n  min-height: 600px;\r\n  width: 100%;\r\n  overflow: auto;\r\n  padding: 0;\r\n  position: relative;\r\n\r\n  .chart-content {\r\n    width: 100%;\r\n    height: 600px;\r\n    overflow: auto;\r\n    display: block;\r\n  }\r\n}\r\n\r\n::v-deep .chart-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .el-dialog__header {\r\n    // padding: 15px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 10px 15px;\r\n    border-top: 1px solid #e4e7ed;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0 !important;\r\n}\r\n</style>\r\n"]}]}