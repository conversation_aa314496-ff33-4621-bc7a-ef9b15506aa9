{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\artificialIntelligence.vue?vue&type=style&index=0&id=7e518ed4&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\domainClassification\\artificialIntelligence.vue", "mtime": 1754010096859}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnRyZWVCb3ggew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTgwcHgpOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0KDQoudHJlZS1wYWdpbmF0aW9uIHsNCiAgcGFkZGluZzogMTBweDsNCiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlYmVlZjU7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCg0KICA6OnYtZGVlcCAuZWwtcGFnaW5hdGlvbiB7DQogICAgLmVsLXBhZ2luYXRpb25fX3NpemVzIHsNCiAgICAgIG1hcmdpbi10b3A6IC0ycHg7DQogICAgfQ0KICB9DQp9DQoNCi50cmVlTWFpbiB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCn0NCg0KLnRyZWVRdWVyeSB7DQogIDo6di1kZWVwIC5lbC1pbnB1dC0tbWluaSAuZWwtaW5wdXRfX2lubmVyIHsNCiAgICBoZWlnaHQ6IDI0cHg7DQogICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgcGFkZGluZzogMCA0cHg7DQogIH0NCg0KICA6OnYtZGVlcCAuZWwtaW5wdXRfX3N1ZmZpeCB7DQogICAgLy8gaGVpZ2h0OiAyMHB4Ow0KICAgIHJpZ2h0OiAtMnB4Ow0KICAgIC8vIHRvcDogNXB4Ow0KICB9DQp9DQoNCi50b29sQm94IHsNCiAgbWluLWhlaWdodDogMTMwcHg7DQogIGhlaWdodDogYXV0bzsNCiAgcGFkZGluZy1ib3R0b206IDE1cHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTUsIDI1NSwgMjU1KTsNCiAgLy8gYm94LXNoYWRvdzogLTFweCAycHggMTVweCAjY2VjZGNkOw0KICBib3JkZXItbGVmdDogc29saWQgMXB4IHJnYigyMjEsIDIxOSwgMjE5KTsNCg0KICAudGl0bGUgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIGhlaWdodDogNzBweDsNCiAgICBwYWRkaW5nOiAwIDMwcHg7DQogICAgZm9udC1zaXplOiAxOXB4Ow0KICB9DQoNCiAgLm1haW5Ub29sIHsNCiAgICBwYWRkaW5nOiAwIDI4cHg7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGNvbG9yOiByZ2IoNTgsIDU4LCA1OCk7DQogIH0NCg0KICAubWFpblRvb2xPbmUgew0KICAgIG1hcmdpbi10b3A6IDE1cHg7DQogICAgaGVpZ2h0OiBhdXRvOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7DQogICAgLy8gYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgfQ0KDQogIC5tYWluVG9vbFR3byB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGhlaWdodDogNDBweDsNCg0KICAgIHAgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBnYXA6IDEwcHg7DQogICAgfQ0KICB9DQoNCiAgLmJ0biB7DQogICAgbWFyZ2luOiAxNXB4IDAgMCAyNXB4Ow0KICB9DQp9DQoNCi5rZXl3b3JkIHsNCiAgd2lkdGg6IDEwMCU7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAuaGlzdG9yeSB7DQogICAgd2lkdGg6IDQzMHB4Ow0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIHotaW5kZXg6IDk5OTk7DQogICAgbGVmdDogNjVweDsNCiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2IoMjIxLCAyMTksIDIxOSk7DQoNCiAgICAuaGlzdG9yeUl0ZW0gew0KICAgICAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KDQogICAgICAuaGlzdG9yeVRleHQgew0KICAgICAgICB3aWR0aDogNDUwcHg7DQogICAgICAgIGhlaWdodDogMzRweDsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDM0cHg7DQogICAgICB9DQoNCiAgICAgICY6bnRoLWxhc3Qtb2YtdHlwZSgxKSB7DQogICAgICAgIHBhZGRpbmctbGVmdDogMDsNCg0KICAgICAgICA6OnYtZGVlcCAuZWwtYnV0dG9uLS10ZXh0IHsNCiAgICAgICAgICBwYWRkaW5nOiAxMHB4IDIwcHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLmhpc3Rvcnkgew0KICB3aWR0aDogNTMwcHg7DQoNCiAgLmhpc3RvcnlJdGVtIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBwYWRkaW5nOiAwIDEwcHg7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCg0KICAgIC5oaXN0b3J5VGV4dCB7DQogICAgICB3aWR0aDogMzUwcHg7DQogICAgICBoZWlnaHQ6IDM0cHg7DQogICAgICBsaW5lLWhlaWdodDogMzRweDsNCiAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["artificialIntelligence.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+tCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "artificialIntelligence.vue", "sourceRoot": "src/views/domainClassification", "sourcesContent": ["<template>\r\n  <div v-if=\"funEsSeach\">\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"26\"\r\n      >\r\n        <div class=\"treeMain\" style=\"width: 100%; margin: 0\">\r\n          <!-- 搜索框 - 直接从Wechat.vue复制 -->\r\n          <div\r\n            style=\"\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              gap: 10px;\r\n            \"\r\n          >\r\n            <el-input\r\n              placeholder=\"输入关键字进行过滤\"\r\n              v-model=\"filterText\"\r\n              clearable\r\n              style=\"margin-bottom: 10px\"\r\n              class=\"input_Fixed\"\r\n            >\r\n              <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\r\n            </el-input>\r\n          </div>\r\n          <!-- 控制区域 -->\r\n          <div\r\n            style=\"\r\n              margin-top: -10px;\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              padding: 0 10px;\r\n            \"\r\n          >\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              content=\"重置\"\r\n              placement=\"top\"\r\n            >\r\n              <el-button\r\n                icon=\"el-icon-refresh\"\r\n                @click=\"treeClear\"\r\n                type=\"text\"\r\n                style=\"color: #666\"\r\n                >重置</el-button\r\n              >\r\n            </el-tooltip>\r\n          </div>\r\n          <div class=\"treeBox\">\r\n            <el-tree\r\n              :data=\"treeData\"\r\n              ref=\"tree\"\r\n              show-checkbox\r\n              node-key=\"id\"\r\n              :default-expanded-keys=\"[1000]\"\r\n              @check=\"handleTreeCheck\"\r\n              @node-click=\"treeNodeClick\"\r\n              :expand-on-click-node=\"false\"\r\n              :filter-node-method=\"filterNode\"\r\n            >\r\n              <template slot-scope=\"scoped\">\r\n                <div style=\"display: flex; align-items: center\">\r\n                  <div>{{ scoped.data.label }}</div>\r\n                  <div\r\n                    v-if=\"scoped.data.country && scoped.data.country !== '0'\"\r\n                    style=\"display: flex; align-items: center\"\r\n                  >\r\n                    <div>[</div>\r\n                    <dict-tag\r\n                      :options=\"dict.type.country\"\r\n                      :value=\"scoped.data.country\"\r\n                    />\r\n                    <div>]</div>\r\n                  </div>\r\n                  <div style=\"font-weight: 600\">\r\n                    {{ `${scoped.data.count ? `(${scoped.data.count})` : \"\"}` }}\r\n                  </div>\r\n                  <div v-if=\"scoped.data.label !== '数据源' && scoped.data.url\">\r\n                    <el-tooltip\r\n                      content=\"打开数据源链接\"\r\n                      placement=\"top-start\"\r\n                      effect=\"light\"\r\n                    >\r\n                      <i\r\n                        class=\"el-icon-connection\"\r\n                        style=\"\r\n                          font-size: 20px;\r\n                          color: #409eff;\r\n                          margin-left: 5px;\r\n                          margin-top: 4px;\r\n                        \"\r\n                        @click=\"openUrl(scoped.data.url)\"\r\n                      ></i>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n              </template>\r\n            </el-tree>\r\n          </div>\r\n          <!-- 分页组件 -->\r\n          <div class=\"tree-pagination\">\r\n            <el-pagination\r\n              @current-change=\"handleTreeCurrentChange\"\r\n              @size-change=\"handleTreePageSizeChange\"\r\n              :current-page=\"treeCurrentPage\"\r\n              :pager-count=\"5\"\r\n              :page-size=\"treePageSize\"\r\n              :page-sizes=\"[50, 100, 150]\"\r\n              layout=\"total, sizes, prev, pager, next\"\r\n              :total=\"treeTotal\"\r\n              :small=\"true\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </pane>\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"74\">\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n        >\r\n          <div class=\"toolBox\">\r\n            <!-- <div class=\"title\" :style=\"{ height: ActiveData.title ? '' : '50px' }\">\r\n              <p v-if=\"ActiveData.title\">{{ ActiveData.title }}</p>\r\n              <p v-else></p>\r\n            </div> -->\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == '' ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = ''\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >是否与科技有关:</span\r\n                >\r\n                <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                  <el-radio-button\r\n                    v-for=\"dict in dict.type.is_technology\"\r\n                    :label=\"dict.value\"\r\n                    :key=\"'is_technology' + dict.value\"\r\n                    >{{ dict.label }}</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </p>\r\n              <div class=\"keyword\">\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >关键词:</span\r\n                >\r\n                <el-input\r\n                  ref=\"keywordRef\"\r\n                  placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                  style=\"width: 430px\"\r\n                  v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\"\r\n                  @blur=\"hideHistoryList()\"\r\n                >\r\n                </el-input>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"funEsSeach('filter')\"\r\n                  :loading=\"buttonDisabled\"\r\n                  style=\"margin-left: 10px; height: 36px\"\r\n                  >搜索</el-button\r\n                >\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div\r\n                    class=\"historyItem\"\r\n                    v-for=\"(history, index) in historyList\"\r\n                    :key=\"index\"\r\n                    v-loading=\"historyLoading\"\r\n                  >\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                      {{ history.keyword }}\r\n                    </div>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"removeHistory(history, 1)\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >删除</el-button\r\n                    >\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\"\r\n                      >更多</el-button\r\n                    >\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"clearHistory()\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >清空</el-button\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"btn\">\r\n              <el-button size=\"mini\" @click=\"resetting\">重置</el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"funEsSeach('filter')\"\r\n                :loading=\"buttonDisabled\"\r\n                >搜索</el-button\r\n              >\r\n            </div> -->\r\n          </div>\r\n          <MainArticle\r\n            v-loading=\"tableLoading\"\r\n            :flag=\"'artificialIntelligence'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach('filter')\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport topSeach from \"@/views/components/topSeach.vue\";\r\nimport MainArticle from \"../components/MainArticle.vue\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\n\r\nexport default {\r\n  components: { topSeach, MainArticle, Splitpanes, Pane },\r\n  dicts: [\"is_technology\", \"country\"],\r\n  data() {\r\n    return {\r\n      width: \"258\",\r\n      isReSize: false,\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      /* 左侧tree数据 */\r\n      filterText: \"\",\r\n      treeData: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n      /* 搜索防抖 */\r\n      searchDebounceTimer: null,\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: \"\" /* 匹配模式 */,\r\n        keyword: \"\" /* 关键词 */,\r\n        timeRange: \"4\" /* 时间范围 */,\r\n        customDay: [] /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: [] /* 自定义天 */,\r\n        isTechnology: \"1\",\r\n        sortMode: \"0\",\r\n      } /* 搜索条件 */,\r\n      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      ActiveData: {},\r\n      seniorSerchFlag: false /* 普通检索或高级检索 */,\r\n      areaList: [] /* 国内地区 */,\r\n      countryList: [] /* 国家或地区 */,\r\n      KeList: [],\r\n      funEsSeach: null,\r\n      treeQuery: {\r\n        filterwords: \"\", // 添加树搜索关键字\r\n      },\r\n      domainList: [],\r\n      industryList: [],\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      nodeCheckList: [],\r\n      isTreeSlotProcessing: false,\r\n      initializationCompleted: false, // 标记初始化是否完成\r\n      // 从Wechat.vue同步的属性\r\n      tableLoading: false, // 表格loading状态\r\n      treeReady: false, // 树组件是否已准备好\r\n      isQuerying: false, // 查询防抖\r\n      queryDebounceTimer: null, // 查询防抖定时器\r\n    };\r\n  },\r\n  async created() {\r\n    try {\r\n      // 初始化funEsSeach方法\r\n      this.funEsSeach = this.EsSeach;\r\n\r\n      // 先加载基础数据\r\n      await this.getArticleHistory();\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // 在mounted中再次检查树组件状态\r\n    this.$nextTick(() => {\r\n      this.checkTreeReadyStatus();\r\n    });\r\n  },\r\n  watch: {\r\n    // 监听过滤文本变化，调用后端接口搜索 - 直接从Wechat.vue复制\r\n    filterText(val) {\r\n      // 清除之前的防抖定时器\r\n      if (this.searchDebounceTimer) {\r\n        clearTimeout(this.searchDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，500ms后执行搜索\r\n      this.searchDebounceTimer = setTimeout(() => {\r\n        this.handleTreeSearch(val);\r\n      }, 500);\r\n    },\r\n\r\n    // 监听筛选条件变化\r\n    \"SeachData.timeRange\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.collectionDateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 先加载树数据\r\n        await this.queryTreeData();\r\n\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n\r\n        // 再加载文章列表（内部已经处理了 tableLoading）\r\n        await this.queryArticleList();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // 处理筛选条件变化 - 直接从Wechat.vue复制\r\n    handleFilterChange() {\r\n      // 清空左侧树选中状态\r\n      this.clearTreeSelections();\r\n\r\n      // 重置分页到第一页\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n      this.scrollTreeToTop(); // 右侧筛选条件变化时也要滚动左侧树到顶部\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 清空树选中状态\r\n    clearTreeSelections() {\r\n      if (this.$refs.tree) {\r\n        this.$refs.tree.setCheckedKeys([]);\r\n      }\r\n      this.checkList = [];\r\n    },\r\n\r\n    // 同时查询树和列表 - 直接从Wechat.vue复制\r\n    async queryTreeAndList() {\r\n      try {\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 检查树组件准备状态\r\n    checkTreeReadyStatus() {\r\n      let attempts = 0;\r\n      const maxAttempts = 50; // 最多检查2.5秒\r\n\r\n      const checkInterval = setInterval(() => {\r\n        attempts++;\r\n\r\n        if (\r\n          this.$refs.tree &&\r\n          this.treeData &&\r\n          this.treeData[0] &&\r\n          this.treeData[0].children &&\r\n          this.treeData[0].children.length > 0\r\n        ) {\r\n          this.treeReady = true;\r\n          clearInterval(checkInterval);\r\n        } else if (attempts >= maxAttempts) {\r\n          clearInterval(checkInterval);\r\n        }\r\n      }, 50);\r\n    },\r\n\r\n    // 确保树组件准备就绪\r\n    async ensureTreeReady() {\r\n      if (\r\n        this.treeReady &&\r\n        this.$refs.tree &&\r\n        this.treeData[0].children.length > 0\r\n      ) {\r\n        return Promise.resolve();\r\n      }\r\n\r\n      return new Promise((resolve) => {\r\n        let attempts = 0;\r\n        const maxAttempts = 100; // 最多等待5秒\r\n\r\n        const checkTree = () => {\r\n          attempts++;\r\n\r\n          if (\r\n            this.$refs.tree &&\r\n            this.treeData &&\r\n            this.treeData[0] &&\r\n            this.treeData[0].children &&\r\n            this.treeData[0].children.length > 0\r\n          ) {\r\n            this.treeReady = true;\r\n\r\n            resolve();\r\n          } else if (attempts >= maxAttempts) {\r\n            resolve(); // 超时也要resolve，避免卡死\r\n          } else {\r\n            setTimeout(checkTree, 50);\r\n          }\r\n        };\r\n        checkTree();\r\n      });\r\n    },\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      try {\r\n        const params = {\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          platformType: 1,\r\n          id: this.$route.query.id || 1,\r\n          m: 1,\r\n          dateType:\r\n            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n          startTime: this.SeachData.customDay\r\n            ? this.SeachData.customDay[0]\r\n            : \"\",\r\n          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : \"\",\r\n          collectionDateType:\r\n            this.SeachData.collectionDateType != 6\r\n              ? this.SeachData.collectionDateType\r\n              : \"\",\r\n          collectionStartTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[0]\r\n            : \"\",\r\n          collectionEndTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[1]\r\n            : \"\",\r\n          keywords: this.SeachData.keyword,\r\n          isTechnology: this.SeachData.isTechnology,\r\n          // 添加关键字过滤参数\r\n          filterwords: this.treeQuery.filterwords || \"\",\r\n          // 添加 artificialIntelligence 特有的参数\r\n          menuType: this.$route.query.menuType,\r\n        };\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: item.sourceSn || `node_${index}`, // 使用sourceSn作为ID，确保唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeData = [\r\n            {\r\n              id: 1000,\r\n              label: \"数据源\",\r\n              children: mapData(dataList),\r\n            },\r\n          ];\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n\r\n          // 等待DOM更新后标记树组件为准备就绪\r\n          this.$nextTick(() => {\r\n            // 简单延迟，让mounted中的检查来处理准备状态\r\n            setTimeout(() => {\r\n              // 不在这里设置treeReady，让mounted中的检查来处理\r\n            }, 100);\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      }\r\n    },\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList() {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      // 立即显示loading状态\r\n      this.tableLoading = true;\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.$route.query.id || 1,\r\n            isSort: this.SeachData.sortMode,\r\n            dateType:\r\n              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n            startTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[0]\r\n              : \"\",\r\n            endTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[1]\r\n              : \"\",\r\n            collectionDateType:\r\n              this.SeachData.collectionDateType != 6\r\n                ? this.SeachData.collectionDateType\r\n                : \"\",\r\n            collectionStartTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[0]\r\n              : \"\",\r\n            collectionEndTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[1]\r\n              : \"\",\r\n            keywords: this.SeachData.keyword,\r\n            isTechnology: this.SeachData.isTechnology,\r\n            platformType: 1,\r\n            // 添加 artificialIntelligence 特有的参数\r\n            menuType: this.$route.query.menuType,\r\n          };\r\n\r\n          // 如果有选中的数据源，添加数据源参数 - 直接从Wechat.vue复制\r\n          if (this.checkList && this.checkList.length > 0) {\r\n            const data = this.checkList.map((item) => item.label);\r\n            const sourceSn = data\r\n              .map((item) => {\r\n                const foundItem = this.treeDataTransfer.find(\r\n                  (row) => row.label === item\r\n                );\r\n                return foundItem ? foundItem.sourceSn : null;\r\n              })\r\n              .filter((sn) => sn !== null);\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 2 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          const res = await api.KeIntegration({ ...params, ...this.treeQuery });\r\n\r\n          if (res.code == 200) {\r\n            this.ArticleList = res.data.list || [];\r\n            this.total = res.data.total || 0;\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.pageSize * (this.currentPage - 1) >= this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.currentPage = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n          this.buttonDisabled = false;\r\n        }\r\n      }, 300);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      this.$nextTick(() => {\r\n        // 尝试多种滚动方式确保滚动成功\r\n        const scrollBoxElement = document.querySelector(\".scollBox\");\r\n        if (scrollBoxElement) {\r\n          scrollBoxElement.scrollTop = 0;\r\n        }\r\n\r\n        // 如果MainArticle组件有scroll引用，也尝试滚动它\r\n        if (\r\n          this.$refs.mainArticle &&\r\n          this.$refs.mainArticle.$refs &&\r\n          this.$refs.mainArticle.$refs.scroll\r\n        ) {\r\n          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;\r\n        }\r\n\r\n        // 滚动整个右侧区域\r\n        const rightMain = document.querySelector(\".rightMain\");\r\n        if (rightMain) {\r\n          rightMain.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 滚动左侧树到顶部\r\n    scrollTreeToTop() {\r\n      this.$nextTick(() => {\r\n        const treeBox = document.querySelector(\".treeBox\");\r\n        if (treeBox) {\r\n          treeBox.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    // 树节点勾选处理（使用check事件）- 直接从Wechat.vue复制\r\n    handleTreeCheck(data, checkedInfo) {\r\n      // 如果树组件还没准备好，直接返回\r\n      if (!this.treeReady || !this.$refs.tree) {\r\n        return;\r\n      }\r\n\r\n      // check事件的参数：\r\n      // data: 当前点击的节点数据\r\n      // checkedInfo: { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }\r\n\r\n      // 获取所有选中的节点（排除根节点）\r\n      const allCheckedNodes = checkedInfo.checkedNodes || [];\r\n      const selectedNodes = allCheckedNodes.filter(\r\n        (node) => node.id !== 1000 && node.label !== \"数据源\"\r\n      );\r\n\r\n      // 更新选中列表\r\n      this.checkList = selectedNodes.map((node) => ({ ...node }));\r\n\r\n      // 重置页码并查询内容 - 修改为KeMonitor的分页参数\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // checkChange方法 - 直接从Wechat.vue复制\r\n    async checkChange(item, isCheck) {\r\n      // 如果树组件还没准备好，直接返回，不处理勾选\r\n      if (!this.treeReady || !this.$refs.tree) {\r\n        return;\r\n      }\r\n\r\n      // 延迟执行，确保Element UI树组件内部状态已经更新\r\n      setTimeout(() => {\r\n        // 如果勾选的是数据源根节点，处理全选/取消全选\r\n        if (item.id === 1000 || item.label === \"数据源\") {\r\n          if (isCheck) {\r\n            // 全选：选择所有可见的子节点\r\n            this.checkList = [...this.treeData[0].children];\r\n            const allIds = this.treeData[0].children.map((node) => node.id);\r\n\r\n            this.$refs.tree.setCheckedKeys(allIds);\r\n          } else {\r\n            // 取消全选：清空所有选择\r\n            this.checkList = [];\r\n            this.$refs.tree.setCheckedKeys([]);\r\n          }\r\n        } else {\r\n          // 处理单个节点的勾选/取消勾选\r\n\r\n          if (isCheck) {\r\n            // 添加到选中列表（多选）\r\n            const existingIndex = this.checkList.findIndex(\r\n              (row) => row.label === item.label\r\n            );\r\n            if (existingIndex === -1) {\r\n              this.checkList.push({ ...item });\r\n            }\r\n          } else {\r\n            // 从选中列表中移除\r\n            this.checkList = this.checkList.filter(\r\n              (row) => row.label !== item.label\r\n            );\r\n          }\r\n        }\r\n\r\n        // 重置页码并查询内容 - 修改为KeMonitor的分页参数\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      }, 50); // 延迟50ms执行\r\n    },\r\n\r\n    // 树节点点击处理（混合选择模式）- 直接从Wechat.vue复制\r\n    async treeNodeClick(item) {\r\n      // 如果点击的是数据源根节点，不进行操作\r\n      if (item.id === 1000 || item.label === \"数据源\") {\r\n        return;\r\n      }\r\n\r\n      // 如果树组件还没准备好，直接返回，不处理点击\r\n      if (!this.treeReady || !this.$refs.tree) {\r\n        return;\r\n      }\r\n\r\n      // 先滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 延迟执行，确保Element UI树组件内部状态已经更新\r\n      setTimeout(() => {\r\n        // 检查是否已选中该节点\r\n        const existingIndex = this.checkList.findIndex(\r\n          (row) => row.label === item.label\r\n        );\r\n\r\n        if (existingIndex !== -1) {\r\n          // 如果当前项已被选中，则只取消该项的选择（保持其他选择）\r\n          this.checkList = this.checkList.filter(\r\n            (row) => row.label !== item.label\r\n          );\r\n\r\n          // 更新树组件的选中状态\r\n          const remainingIds = this.checkList.map((node) => node.id);\r\n          this.$refs.tree.setCheckedKeys(remainingIds);\r\n        } else {\r\n          // 如果当前项未被选中，清空所有选择，设置为唯一选中项（单选）\r\n          this.checkList = [{ ...item }];\r\n\r\n          // 更新树组件的选中状态\r\n          this.$refs.tree.setCheckedKeys([item.id]);\r\n        }\r\n\r\n        // 重置页码并查询内容 - 修改为KeMonitor的分页参数\r\n        this.currentPage = 1;\r\n        this.queryArticleList();\r\n      }, 50); // 延迟50ms执行\r\n    },\r\n    // 重置树选择 - 直接从Wechat.vue复制\r\n    treeClear() {\r\n      // 清空选中状态\r\n      this.clearTreeSelections();\r\n\r\n      // 重置页码并查询列表数据 - 修改为KeMonitor的分页参数\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.scrollTreeToTop();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 处理树搜索 - 直接从Wechat.vue复制并适配KeMonitor\r\n    async handleTreeSearch(keyword) {\r\n      try {\r\n        // 树搜索不应该影响右侧列表的loading状态\r\n        // this.tableLoading = true; // 移除这行\r\n\r\n        // 更新搜索关键字\r\n        this.treeQuery.filterwords = keyword || \"\";\r\n\r\n        if (keyword) {\r\n          // 使用filterwords参数调用树接口进行搜索\r\n          this.scrollTreeToTop(); // 关键字过滤时滚动到顶部\r\n          await this.queryTreeData();\r\n        } else {\r\n          // 如果搜索关键字为空，重新获取原始数据\r\n          this.treeCurrentPage = 1;\r\n          this.scrollTreeToTop(); // 清空关键字时滚动到顶部\r\n          await this.queryTreeData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"搜索树节点失败:\", error);\r\n        this.$message.error(\"搜索失败，请重试\");\r\n      }\r\n      // 不需要finally块来关闭tableLoading\r\n    },\r\n\r\n    // 保留原有的必要方法\r\n    SwitchInfo(data) {\r\n      this.isInfo = data;\r\n    },\r\n\r\n    seniorSerch() {\r\n      this.seniorSerchFlag = !this.seniorSerchFlag;\r\n    },\r\n\r\n    async getArea() {\r\n      try {\r\n        const Response = await api.getAreaList();\r\n        if (Response && Response.code == 200 && Response.data) {\r\n          this.areaList = Response.data[0] || [];\r\n          this.countryList = Response.data[1] || [];\r\n        } else {\r\n          console.warn(\"获取地区数据失败或数据为空\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取区域数据失败:\", err);\r\n        this.$message.error(\"地区数据获取失败\");\r\n      }\r\n    },\r\n\r\n    async getArticleHistory() {\r\n      try {\r\n        const res = await listArticleHistory({\r\n          pageNum: 1,\r\n          pageSize: 5,\r\n          type: 2,\r\n        });\r\n        if (res && res.code === 200) {\r\n          this.historyList = res.rows || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取历史记录失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 树节点过滤方法\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n\r\n    // 添加EsSeach方法以兼容原有调用\r\n    EsSeach(flag) {\r\n      if (flag === \"filter\") {\r\n        // 筛选变化时同时查询树和列表\r\n        this.queryTreeAndList();\r\n      } else {\r\n        // 其他情况只查询列表\r\n        this.queryArticleList();\r\n      }\r\n    },\r\n\r\n    // 重置搜索条件 - 简化版本\r\n    resetting() {\r\n      try {\r\n        this.SeachData = {\r\n          metaMode: \"\" /* 匹配模式 */,\r\n          keyword: \"\" /* 关键词 */,\r\n          timeRange: \"\" /* 时间范围 */,\r\n          customDay: [] /* 自定义天 */,\r\n          collectionDateType: null /* 时间范围 */,\r\n          collectionTime: [] /* 自定义天 */,\r\n          isTechnology: \"1\",\r\n          sortMode: \"0\",\r\n        };\r\n\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryTreeAndList();\r\n      } catch (error) {\r\n        console.error(\"重置搜索条件时出错:\", error);\r\n        this.$message.error(\"重置搜索条件失败\");\r\n      }\r\n    },\r\n    async removeHistory(item, type) {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (item && item.id) {\r\n          await delArticleHistory([item.id]);\r\n\r\n          if (type == 1) {\r\n            if (this.$refs[\"keywordRef\"]) {\r\n              this.$refs[\"keywordRef\"].focus();\r\n            }\r\n            await this.getArticleHistory();\r\n          } else {\r\n            await this.getArticleHistory();\r\n            await this.getArticleHistory1();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"删除历史记录时出错:\", error);\r\n        this.$message.error(\"删除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      try {\r\n        this.showHistory = true;\r\n      } catch (error) {\r\n        console.error(\"显示历史列表时出错:\", error);\r\n      }\r\n    },\r\n\r\n    hideHistoryList() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        this.historyTimeout = setTimeout(() => {\r\n          this.showHistory = false;\r\n        }, 500);\r\n      } catch (error) {\r\n        console.error(\"隐藏历史列表时出错:\", error);\r\n        this.showHistory = false; // 确保在出错时也能隐藏列表\r\n      }\r\n    },\r\n\r\n    // 关键词历史选择 - 直接从Wechat.vue复制\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.currentPage = 1;\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    async clearHistory() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (this.$refs[\"keywordRef\"]) {\r\n          this.$refs[\"keywordRef\"].focus();\r\n        }\r\n\r\n        await cleanArticleHistory(2);\r\n        await this.getArticleHistory();\r\n      } catch (error) {\r\n        console.error(\"清除历史记录时出错:\", error);\r\n        this.$message.error(\"清除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    moreHistory() {\r\n      try {\r\n        this.historyLoading = true;\r\n        this.getArticleHistory1();\r\n        this.dialogVisible1 = true;\r\n      } catch (error) {\r\n        console.error(\"加载更多历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n      }\r\n    },\r\n\r\n    async getArticleHistory1() {\r\n      try {\r\n        this.historyLoading = true;\r\n        const response = await listArticleHistory({\r\n          ...this.queryParams1,\r\n          type: 2,\r\n        });\r\n\r\n        if (response) {\r\n          this.historyList1 = response.rows || [];\r\n          this.total1 = response.total || 0;\r\n        }\r\n\r\n        this.historyLoading = false;\r\n      } catch (error) {\r\n        console.error(\"获取文章历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n        this.$message.error(\"获取搜索历史失败\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 处理树形分页页码变化\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.clearTreeSelections();\r\n      this.scrollTreeToTop(); // 左侧树翻页时滚动到顶部\r\n      this.queryTreeData();\r\n    },\r\n\r\n    // 处理树形分页每页大小变化\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.clearTreeSelections();\r\n      this.scrollTreeToTop(); // 左侧树页面大小变化时滚动到顶部\r\n      this.queryTreeData();\r\n    },\r\n\r\n    // 添加缺失的openNewView方法\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 添加缺失的handleHistoryPagination方法\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 180px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  // box-shadow: -1px 2px 15px #cecdcd;\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}