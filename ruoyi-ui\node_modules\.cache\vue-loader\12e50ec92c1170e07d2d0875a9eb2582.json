{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue?vue&type=template&id=00dd63fe&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue", "mtime": 1754108735728}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9Im1haW4iIHYtbG9hZGluZz0ibG9hZGluZyI+CiAgPGRpdiBjbGFzcz0iTWFpbkFydGljbGUiIHYtaWY9ImZsYWcgPT0gJ01vbml0b3InIj4KICAgIDxkaXYgY2xhc3M9IlRvcEJ0bkdyb3VwIj4KICAgICAgPGRpdiBjbGFzcz0ibGVmdEJ0bkdyb3VwIj4KICAgICAgICA8cD4KICAgICAgICAgIDxlbC1jaGVja2JveAogICAgICAgICAgICB2LW1vZGVsPSJjaGVja2VkIgogICAgICAgICAgICA6aXNJbmRldGVybWluYXRlPSJpc0luZGV0ZXJtaW5hdGUiCiAgICAgICAgICAgIEBjaGFuZ2U9ImhhbmRsZUNoZWNrQWxsQ2hhbmdlIgogICAgICAgICAgICA+5YWo6YCJPC9lbC1jaGVja2JveAogICAgICAgICAgPgogICAgICAgIDwvcD4KICAgICAgICA8IS0tIDxwIGNsYXNzPSJ0b29sVGl0bGUiPjxpIHRpdGxlPSLkuIvovb0iIGNsYXNzPSJpY29uLXhpYXphaTEiIHN0eWxlPSJjb2xvcjpncmVlbiIgQGNsaWNrPSJkb3duTG9hZCI+PC9pPjwvcD4gIDIwMjMtOS00IOayiOiAgeW4iCAg5pqC5pe25rOo6YeKIC0tPgogICAgICAgIDxwIGNsYXNzPSJ0b29sVGl0bGUiPgogICAgICAgICAgPGkgY2xhc3M9Imljb24tc2h1YXhpbi1jb3B5IiB0aXRsZT0i5Yi35pawIiBAY2xpY2s9IlJlZnJlc2giPjwvaT4KICAgICAgICA8L3A+CiAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aQogICAgICAgICAgICBjbGFzcz0iaWNvbi0tX3RpYW5qaWFkYW9rdSIKICAgICAgICAgICAgdGl0bGU9Iua3u+WKoOWIsOaKpeWRiiIKICAgICAgICAgICAgQGNsaWNrPSJkaWFsb2dWaXNpYmxlID0gdHJ1ZSIKICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydyZXN1bHQ6cmVwb3J0OmFkZCddIgogICAgICAgICAgPjwvaT4KICAgICAgICA8L3A+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2PgogICAgICAgIDxlbC1jaGVja2JveAogICAgICAgICAgdi1tb2RlbD0ic2hvd1N1bW1hcnkiCiAgICAgICAgICBAY2hhbmdlPSIoZSkgPT4gKHNob3dTdW1tYXJ5ID0gZSkiCiAgICAgICAgICBzdHlsZT0ibWFyZ2luLXJpZ2h0OiAxMHB4IgogICAgICAgICAgPuaYr+WQpuaYvuekuuaRmOimgTwvZWwtY2hlY2tib3gKICAgICAgICA+CiAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtc2l6ZTogMTRweCI+5o6S5bqP5pa55byPOjwvc3Bhbj4mbmJzcDsKICAgICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9IlNlYWNoRGF0YS5zb3J0TW9kZSIgc2l6ZT0ibWluaSI+CiAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInlj5HluIPml7bpl7TlgJLluo/mjpLluo8iIDp2YWx1ZT0iZmFsc2UiPjwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5oyJ5Y+R5biD5pe26Ze05q2j5bqP5o6S5bqPIiA6dmFsdWU9InRydWUiPjwvZWwtb3B0aW9uPgogICAgICAgIDwvZWwtc2VsZWN0PgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogICAgPGVsLWNoZWNrYm94LWdyb3VwCiAgICAgIHYtbW9kZWw9ImNoZWNrZWRDaXRpZXMiCiAgICAgIEBjaGFuZ2U9ImhhbmRsZUNoZWNrZWRDaXRpZXNDaGFuZ2UiCiAgICA+CiAgICAgIDxkaXYKICAgICAgICBjbGFzcz0ic2NvbGxCb3giCiAgICAgICAgQG1vdXNld2hlZWw9InNjcm9sbENoYW5nZSIKICAgICAgICByZWY9InNjcm9sbCIKICAgICAgICBzdHlsZT0iaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDA1cHgpIgogICAgICA+CiAgICAgICAgPGRpdiBjbGFzcz0iQXJ0aWNsIiB2LWZvcj0iKGl0ZW0sIGtleSkgaW4gQXJ0aWNsZUxpc3QiIDprZXk9ImtleSI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xfbGVmdCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9IkFydGljbFRvcCI+CiAgICAgICAgICAgICAgPHAgc3R5bGU9Im1hcmdpbjogMjBweCAwIDVweCAwOyBsaW5lLWhlaWdodDogMjBweCI+CiAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3ggOmxhYmVsPSJpdGVtLmlkIiA6dmFsdWU9Iml0ZW0uaWQiPgogICAgICAgICAgICAgICAgICB7eyBudWxsIH19IDwvZWwtY2hlY2tib3gKICAgICAgICAgICAgICAgID4mbmJzcDsmbmJzcDsKICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiCiAgICAgICAgICAgICAgICAgID48c3BhbiBzdHlsZT0icGFkZGluZzogMCAxMHB4IDAgMCIKICAgICAgICAgICAgICAgICAgICA+e3sgKGN1cnJlbnRQYWdlIC0gMSkgKiBwYWdlU2l6ZSArIGtleSArIDEgfX0mbmJzcDs8L3NwYW4KICAgICAgICAgICAgICAgICAgPjxzcGFuCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9InRpdGxlX0FydGljbGUiCiAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtKSIKICAgICAgICAgICAgICAgICAgICB2LWh0bWw9ImNoYW5nZUNvbG9yKGl0ZW0uY25UaXRsZSB8fCBpdGVtLnRpdGxlKSIKICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICA8L3NwYW4KICAgICAgICAgICAgICAgICAgPiZuYnNwOyZuYnNwOyZuYnNwOyZuYnNwOzwvYQogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImluZm9fZmxleCI+CiAgICAgICAgICAgICAgPGRpdgogICAgICAgICAgICAgICAgY2xhc3M9IkFydGljbEJvdHRvbSIKICAgICAgICAgICAgICAgIHYtaWY9Iml0ZW0uc291cmNlVHlwZSAhPT0gJzEnICYmIGl0ZW0uc291cmNlVHlwZSAhPT0gJzMnIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgICAgIOWOn+aWh+agh+mimDoKICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iPgogICAgICAgICAgICAgICAgICAgIHt7IGl0ZW0udGl0bGUgfX0KICAgICAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iQXJ0aWNsQm90dG9tIj4KICAgICAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgICAgIOexu+WeizoKICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iPgogICAgICAgICAgICAgICAgICAgIHt7IHR5cGVIYW5kbGUoaXRlbS5zb3VyY2VUeXBlKSB9fQogICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgICAgIOWqkuS9k+adpea6kDoKICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iPnt7IGl0ZW0uc291cmNlTmFtZSB9fTwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiB2LWlmPSJpdGVtLmF1dGhvciI+CiAgICAgICAgICAgICAgICAgIOS9nOiAhToKICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iIHYtaHRtbD0iaXRlbS5hdXRob3IiPjwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdj4KICAgICAgICAgICAgICAgICAg5Y+R5biD5pe26Ze0OgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+e3sKICAgICAgICAgICAgICAgICAgICBmb3JtYXRQdWJsaXNoVGltZSgKICAgICAgICAgICAgICAgICAgICAgIGl0ZW0ucHVibGlzaFRpbWUsCiAgICAgICAgICAgICAgICAgICAgICBpdGVtLndlYnN0ZVB1Ymxpc2hUaW1lCiAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICB9fTwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPCEtLSA8c3BhbiBzdHlsZT0iY29sb3I6IHJnYigzMiwgMTI2LCAyNTApOyI+55u45Ly85paH56ugPC9zcGFuPiAtLT4KICAgICAgICAgICAgICAgIDwhLS0gPHA+5qCH562+QTwvcD4gLS0+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iQXJ0aWNsQm90dG9tIj4KICAgICAgICAgICAgICAgIDxkaXYgdi1pZj0iaXRlbS5pbmR1c3RyeU5hbWUiPgogICAgICAgICAgICAgICAgICDmiYDlsZ7ooYzkuJo6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57eyBpbmR1c3RyeUhhbmRsZShpdGVtKSB9fTwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiB2LWlmPSJpdGVtLmRvbWFpbiI+CiAgICAgICAgICAgICAgICAgIOaJgOWxnumihuWfnzoKICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iPnt7IGRvbWFpbkhhbmRsZShpdGVtKSB9fTwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdj4KICAgICAgICAgICAgICAgICAg5Y6f5paH6ZO+5o6lOgogICAgICAgICAgICAgICAgICA8c3BhbgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJsaW5rU3R5bGUiCiAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtLCAnbGluaycpIgogICAgICAgICAgICAgICAgICAgIHN0eWxlPSJjb2xvcjogIzE4ODlmMyIKICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICAgIHt7CiAgICAgICAgICAgICAgICAgICAgICB0eXBlSGFuZGxlKGl0ZW0uc291cmNlVHlwZSkgPT0gIuW+ruS/oeWFrOS8l+WPtyIKICAgICAgICAgICAgICAgICAgICAgICAgPyBpdGVtLnNob3J0VXJsCiAgICAgICAgICAgICAgICAgICAgICAgIDogaXRlbS5vcmlnaW5hbFVybAogICAgICAgICAgICAgICAgICAgIH19CiAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdj4KICAgICAgICAgICAgICAgICAg5aSn5qih5Z6L562b6YCJOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+CiAgICAgICAgICAgICAgICAgICAge3sgZ2V0VGVjaG5vbG9neUxhYmVsKGl0ZW0uaXNUZWNobm9sb2d5KSB9fQogICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICBjbGFzcz0iQXJ0aWNsTWFpbiIKICAgICAgICAgICAgICBzdHlsZT0iCiAgICAgICAgICAgICAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsKICAgICAgICAgICAgICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7CiAgICAgICAgICAgICAgICAtd2Via2l0LWxpbmUtY2xhbXA6IDI7CiAgICAgICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgICAgICAgICAgICAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7CiAgICAgICAgICAgICAgIgogICAgICAgICAgICAgIHYtaWY9IgogICAgICAgICAgICAgICAgc2hvd1N1bW1hcnkgJiYKICAgICAgICAgICAgICAgIGhhc0FjdHVhbENvbnRlbnQoaXRlbS5jblN1bW1hcnkgfHwgaXRlbS5zdW1tYXJ5KQogICAgICAgICAgICAgICIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogIzliOWI5YiI+5pGY6KaB77yaPC9zcGFuPgogICAgICAgICAgICAgIDxzcGFuCiAgICAgICAgICAgICAgICB2LWh0bWw9ImNoYW5nZUNvbG9yKGdldFNhZmVTdW1tYXJ5KGl0ZW0pKSIKICAgICAgICAgICAgICAgIEBjbGljaz0ib3Blbk5ld1ZpZXcoaXRlbSkiCiAgICAgICAgICAgICAgPjwvc3Bhbj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwhLS0gPGRpdiBjbGFzcz0iaW1nQm94IiB2LWlmPSJpdGVtLmltZ2FnZSI+CiAgICAgICAgICAgICAgPGltZyBzcmM9Ii4uLy4uL2Fzc2V0cy9pbWFnZXMvcHJpdmlldy5wbmciIGFsdD0i5Yqg6L295aSx6LSl5LqGIiAvPgogICAgICAgICAgICAgIDxpbWcgc3JjPSIuLi8uLi9hc3NldHMvaW1hZ2VzL3ByaXZpZXcucG5nIiBhbHQ9IuWKoOi9veWksei0peS6hiIgLz4KICAgICAgICAgICAgICA8aW1nIHNyYz0iLi4vLi4vYXNzZXRzL2ltYWdlcy9wcml2aWV3LnBuZyIgYWx0PSLliqDovb3lpLHotKXkuoYiIC8+CiAgICAgICAgICAgICAgPGltZyBzcmM9Ii4uLy4uL2Fzc2V0cy9pbWFnZXMvcHJpdmlldy5wbmciIGFsdD0i5Yqg6L295aSx6LSl5LqGIiAvPgogICAgICAgICAgICAgIDxpbWcgc3JjPSIuLi8uLi9hc3NldHMvaW1hZ2VzL3ByaXZpZXcucG5nIiBhbHQ9IuWKoOi9veWksei0peS6hiIgLz4KICAgICAgICAgICAgICA8aW1nIHNyYz0iLi4vLi4vYXNzZXRzL2ltYWdlcy9wcml2aWV3LnBuZyIgYWx0PSLliqDovb3lpLHotKXkuoYiIC8+CiAgICAgICAgICAgIDwvZGl2PiAtLT4KICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0ibGVmdEJ0bkdyb3VwMiI+CiAgICAgICAgICAgIDxwPgogICAgICAgICAgICAgIDxpCiAgICAgICAgICAgICAgICA6Y2xhc3M9InsKICAgICAgICAgICAgICAgICAgJ2ljb24taG90Mi1jb3B5JzoKICAgICAgICAgICAgICAgICAgICBpdGVtLmlzV2hldGhlciA9PSBudWxsIHx8IGl0ZW0uaXNXaGV0aGVyID09IDAsCiAgICAgICAgICAgICAgICAgICdpY29uLWhvdDInOiBpdGVtLmlzV2hldGhlciA9PSAxLAogICAgICAgICAgICAgICAgfSIKICAgICAgICAgICAgICAgIHRpdGxlPSLng63ngrkiCiAgICAgICAgICAgICAgICBAY2xpY2s9ImhvdEluY3JlYXNlKGl0ZW0pIgogICAgICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydhcnRpY2xlOmxhYmVsOmFkZCddIgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPHA+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIDpjbGFzcz0iewogICAgICAgICAgICAgICAgICAnaWNvbi1qaWF4aW5nc2hvdWNhbmd0aWFuY2hvbmctY29weSc6ICFpdGVtLmZhdm9yaXRlcywKICAgICAgICAgICAgICAgICAgJ2ljb24tamlheGluZ3Nob3VjYW5ndGlhbmNob25nJzogaXRlbS5mYXZvcml0ZXMsCiAgICAgICAgICAgICAgICB9IgogICAgICAgICAgICAgICAgdGl0bGU9IuaUtuiXjyIKICAgICAgICAgICAgICAgIEBjbGljay5wYXNzaXZlPSJjb2xsZWN0KGl0ZW0pIgogICAgICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydhcnRpY2xlOmNvbGxlY3Rpb246YWRkJ10iCiAgICAgICAgICAgICAgPjwvaT4KICAgICAgICAgICAgPC9wPgogICAgICAgICAgICA8cD4KICAgICAgICAgICAgICA8aQogICAgICAgICAgICAgICAgY2xhc3M9Imljb24tLV90aWFuamlhZGFva3UiCiAgICAgICAgICAgICAgICB0aXRsZT0i5re75Yqg5Yiw5oql5ZGKIgogICAgICAgICAgICAgICAgQGNsaWNrPSJzZXBhcmF0ZUFkZChpdGVtKSIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsncmVzdWx0OnJlcG9ydDphZGQnXSIKICAgICAgICAgICAgICA+PC9pPgogICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDxwPgogICAgICAgICAgICAgIDxpCiAgICAgICAgICAgICAgICBjbGFzcz0iaWNvbi1iaWFvcWlhbiIKICAgICAgICAgICAgICAgIHRpdGxlPSLmoIfnrb4iCiAgICAgICAgICAgICAgICBAY2xpY2s9InRhZ0hhbmRsZXIoaXRlbSkiCiAgICAgICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ2FydGljbGU6bGFiZWw6YWRkJ10iCiAgICAgICAgICAgICAgPjwvaT4KICAgICAgICAgICAgPC9wPgogICAgICAgICAgICA8IS0tIDxwPjxpIGNsYXNzPSJpY29uLWZ1emhpIiB0aXRsZT0i5aSN5Yi2IiBAY2xpY2s9ImNvcHlUZXh0KGl0ZW0pIj48L2k+PC9wPiAgIDIwMjMtOS00IOayiOiAgeW4iCDmmoLml7bms6jph4ogLS0+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZWwtZW1wdHkKICAgICAgICAgIDppbWFnZS1zaXplPSIyMDAiCiAgICAgICAgICBkZXNjcmlwdGlvbj0i5b2T5YmN562b6YCJ5p2h5Lu25LiL5pqC5peg5pWw5o2uIgogICAgICAgICAgdi1pZj0iQXJ0aWNsZUxpc3QgPT0gbnVsbCB8fCBBcnRpY2xlTGlzdC5sZW5ndGggPT0gMCIKICAgICAgICA+PC9lbC1lbXB0eT4KICAgICAgPC9kaXY+CiAgICA8L2VsLWNoZWNrYm94LWdyb3VwPgogIDwvZGl2PgogIDxkaXYKICAgIGNsYXNzPSJNYWluQXJ0aWNsZSIKICAgIHYtaWY9ImZsYWcgPT0gJ01vbml0b3JVc2UnIHx8IGZsYWcgPT0gJ3NwZWNpYWxTdWJqZWN0VXNlJyIKICA+CiAgICA8ZGl2IGNsYXNzPSJUb3BCdG5Hcm91cCI+CiAgICAgIDxkaXYgY2xhc3M9ImxlZnRCdG5Hcm91cCI+CiAgICAgICAgPHA+CiAgICAgICAgICA8ZWwtY2hlY2tib3gKICAgICAgICAgICAgdi1tb2RlbD0iY2hlY2tlZCIKICAgICAgICAgICAgOmlzSW5kZXRlcm1pbmF0ZT0iaXNJbmRldGVybWluYXRlIgogICAgICAgICAgICBAY2hhbmdlPSJoYW5kbGVDaGVja0FsbENoYW5nZSIKICAgICAgICAgICAgPuWFqOmAiTwvZWwtY2hlY2tib3gKICAgICAgICAgID4KICAgICAgICA8L3A+CiAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aQogICAgICAgICAgICB0aXRsZT0i5om56YeP5LiL6L295paH56ugIgogICAgICAgICAgICBjbGFzcz0iaWNvbi14aWF6YWkxIgogICAgICAgICAgICBzdHlsZT0iY29sb3I6IGdyZWVuIgogICAgICAgICAgICBAY2xpY2s9ImRvd25Mb2FkRXhjZWwiCiAgICAgICAgICA+PC9pPgogICAgICAgIDwvcD4KICAgICAgICA8cCBjbGFzcz0idG9vbFRpdGxlIj4KICAgICAgICAgIDxpCiAgICAgICAgICAgIHRpdGxlPSLmibnph4/liKDpmaTmlofnq6AiCiAgICAgICAgICAgIGNsYXNzPSJpY29uLXNoYW5jaHUiCiAgICAgICAgICAgIEBjbGljaz0iYmF0Y2hEZWxldGUiCiAgICAgICAgICA+PC9pPgogICAgICAgIDwvcD4KICAgICAgICA8IS0tIDxwIGNsYXNzPSJ0b29sVGl0bGUiPgogICAgICAgICAgPGkgdGl0bGU9IuaJuemHj+eUn+aIkOW/q+eFpyIgY2xhc3M9Imljb24tcGluZ211a3VhaXpoYW8iIHN0eWxlPSJjb2xvcjpncmVlbiIgQGNsaWNrPSJyZXN1bHRFdmVudCgnQmF0Y2hHZW5lcmF0aW9uJykiPjwvaT4KICAgICAgICA8L3A+IC0tPgogICAgICAgIDxwIGNsYXNzPSJ0b29sVGl0bGUiPgogICAgICAgICAgPGkgY2xhc3M9Imljb24tc2h1YXhpbi1jb3B5IiB0aXRsZT0i5Yi35pawIiBAY2xpY2s9IlJlZnJlc2giPjwvaT4KICAgICAgICA8L3A+CiAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aQogICAgICAgICAgICBjbGFzcz0iaWNvbi0tX3RpYW5qaWFkYW9rdSIKICAgICAgICAgICAgdGl0bGU9Iua3u+WKoOWIsOaKpeWRiiIKICAgICAgICAgICAgQGNsaWNrPSJkaWFsb2dWaXNpYmxlID0gdHJ1ZSIKICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydyZXN1bHQ6cmVwb3J0OmFkZCddIgogICAgICAgICAgPjwvaT4KICAgICAgICA8L3A+CiAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aQogICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1kb2N1bWVudC1hZGQiCiAgICAgICAgICAgIHN0eWxlPSJmb250LXNpemU6IDI0cHgiCiAgICAgICAgICAgIHRpdGxlPSLlj5HluIPliLDmr4/ml6XmnIDmlrDng63ngrkiCiAgICAgICAgICAgIEBjbGljaz0icHVibGlzaEhvdCIKICAgICAgICAgID48L2k+CiAgICAgICAgPC9wPgogICAgICAgIDxwIGNsYXNzPSJ0b29sVGl0bGUiIHYtaWY9IiRyb3V0ZS5xdWVyeS5kb21haW4iPgogICAgICAgICAgPGkKICAgICAgICAgICAgY2xhc3M9ImVsLWljb24tY2hhdC1kb3Qtcm91bmQiCiAgICAgICAgICAgIHN0eWxlPSJmb250LXNpemU6IDI0cHgiCiAgICAgICAgICAgIHRpdGxlPSJEZWVwc2Vla+a3seW6puino+ivuyIKICAgICAgICAgICAgQGNsaWNrPSJhcnRpY2xlQWlDaGF0IgogICAgICAgICAgPjwvaT4KICAgICAgICAgIDxzcGFuIGNsYXNzPSJkZWVwc2Vlay10ZXh0IiBAY2xpY2s9ImFydGljbGVBaUNoYXQiCiAgICAgICAgICAgID5EZWVwc2Vla+a3seW6puino+ivuzwvc3BhbgogICAgICAgICAgPgogICAgICAgIDwvcD4KICAgICAgPC9kaXY+CiAgICAgIDxkaXY+CiAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICB2LW1vZGVsPSJzaG93U3VtbWFyeSIKICAgICAgICAgIEBjaGFuZ2U9IihlKSA9PiAoc2hvd1N1bW1hcnkgPSBlKSIKICAgICAgICAgIHN0eWxlPSJtYXJnaW4tcmlnaHQ6IDEwcHgiCiAgICAgICAgICA+5piv5ZCm5pi+56S65pGY6KaBPC9lbC1jaGVja2JveAogICAgICAgID4KICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC1zaXplOiAxNHB4Ij7mjpLluo/mlrnlvI86PC9zcGFuPiZuYnNwOwogICAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0iU2VhY2hEYXRhLnNvcnRNb2RlIiBzaXplPSJtaW5pIj4KICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaMieWPkeW4g+aXtumXtOWAkuW6j+aOkuW6jyIgOnZhbHVlPSInMCciPjwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5oyJ5Y+R5biD5pe26Ze05q2j5bqP5o6S5bqPIiA6dmFsdWU9IicxJyI+PC9lbC1vcHRpb24+CiAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInph4fpm4bml7bpl7TlgJLluo/mjpLluo8iIDp2YWx1ZT0iJzInIj48L2VsLW9wdGlvbj4KICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaMiemHh+mbhuaXtumXtOato+W6j+aOkuW6jyIgOnZhbHVlPSInMyciPjwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5oyJ57O757uf5o6o6I2Q5o6S5bqPIiA6dmFsdWU9Iic0JyI+PC9lbC1vcHRpb24+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8ZWwtY2hlY2tib3gtZ3JvdXAKICAgICAgdi1tb2RlbD0iY2hlY2tlZENpdGllcyIKICAgICAgQGNoYW5nZT0iaGFuZGxlQ2hlY2tlZENpdGllc0NoYW5nZSIKICAgID4KICAgICAgPGRpdgogICAgICAgIGNsYXNzPSJzY29sbEJveCIKICAgICAgICBAbW91c2V3aGVlbD0ic2Nyb2xsQ2hhbmdlIgogICAgICAgIHJlZj0ic2Nyb2xsIgogICAgICAgIDpzdHlsZT0iCiAgICAgICAgICAkcm91dGUucXVlcnkuZG9tYWluCiAgICAgICAgICAgID8gJ2hlaWdodDogY2FsYygxMDB2aCAtIDE3MHB4KScKICAgICAgICAgICAgOiAnaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMzg5cHgpJwogICAgICAgICIKICAgICAgPgogICAgICAgIDxkaXYgY2xhc3M9IkFydGljbCIgdi1mb3I9IihpdGVtLCBrZXkpIGluIEFydGljbGVMaXN0IiA6a2V5PSJrZXkiPgogICAgICAgICAgPGRpdiBjbGFzcz0iQXJ0aWNsX2xlZnQiPgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xUb3AiPgogICAgICAgICAgICAgIDxwIHN0eWxlPSJtYXJnaW46IDIwcHggMCA1cHggMDsgbGluZS1oZWlnaHQ6IDIwcHgiPgogICAgICAgICAgICAgICAgPGVsLWNoZWNrYm94IDpsYWJlbD0iaXRlbS5pZCIgOnZhbHVlPSJpdGVtLmlkIj4KICAgICAgICAgICAgICAgICAge3sgbnVsbCB9fSA8L2VsLWNoZWNrYm94CiAgICAgICAgICAgICAgICA+Jm5ic3A7Jm5ic3A7CiAgICAgICAgICAgICAgICA8YSBocmVmPSIjIgogICAgICAgICAgICAgICAgICA+PHNwYW4gc3R5bGU9InBhZGRpbmc6IDAgMTBweCAwIDAiPnt7CiAgICAgICAgICAgICAgICAgICAgKGN1cnJlbnRQYWdlIC0gMSkgKiBwYWdlU2l6ZSArIGtleSArIDEKICAgICAgICAgICAgICAgICAgfX08L3NwYW4KICAgICAgICAgICAgICAgICAgPjxzcGFuCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9InRpdGxlX0FydGljbGUiCiAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtKSIKICAgICAgICAgICAgICAgICAgICB2LWh0bWw9ImNoYW5nZUNvbG9yKGl0ZW0udGl0bGUpIgogICAgICAgICAgICAgICAgICA+PC9zcGFuCiAgICAgICAgICAgICAgICA+PC9hPgogICAgICAgICAgICAgICAgJm5ic3A7Jm5ic3A7Jm5ic3A7Jm5ic3A7CiAgICAgICAgICAgICAgICA8IS0tIHYtaWY9Iml0ZW0uaXNUcmFuc2xhdGVkID09IDAiIC0tPgogICAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgICAgY2xhc3M9Imljb24tdGFpemhhbmd0cmFuc2xhdGUiCiAgICAgICAgICAgICAgICAgIEBjbGljaz0idHJhbnNsYXRlVGl0bGUoaXRlbSkiCiAgICAgICAgICAgICAgICAgIHYtaWY9Iml0ZW0uc291cmNlVHlwZSAhPT0gJzEnICYmIGl0ZW0uc291cmNlVHlwZSAhPT0gJzMnIgogICAgICAgICAgICAgICAgPjwvaT4KICAgICAgICAgICAgICAgIDxzcGFuCiAgICAgICAgICAgICAgICAgIGNsYXNzPSJsaW5rU3R5bGUiCiAgICAgICAgICAgICAgICAgIEBjbGljaz0ib3Blbk5ld1ZpZXcoaXRlbSwgJ2xpbmsnKSIKICAgICAgICAgICAgICAgICAgc3R5bGU9ImNvbG9yOiAjMTg4OWYzOyBjdXJzb3I6IHBvaW50ZXIiCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgIOWOn+aWh+mTvuaOpQogICAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgICA8IS0tIHYtaWY9Iml0ZW0uZmlsZVVybCIgLS0+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgY2xhc3M9IkFydGljbE1haW4iCiAgICAgICAgICAgICAgaWQKICAgICAgICAgICAgICB2LWlmPSIKICAgICAgICAgICAgICAgIGl0ZW0uc291cmNlVHlwZSAhPT0gJzEnICYmCiAgICAgICAgICAgICAgICBpdGVtLnNvdXJjZVR5cGUgIT09ICczJyAmJgogICAgICAgICAgICAgICAgaXRlbS5jblRpdGxlCiAgICAgICAgICAgICAgIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPGEgaHJlZj0iIyI+CiAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6ICM5YjliOWIiPuS4reaWh+agh+mimO+8mjwvc3Bhbj4KICAgICAgICAgICAgICAgIDxzcGFuCiAgICAgICAgICAgICAgICAgIHYtaHRtbD0iY2hhbmdlQ29sb3IoaXRlbS5jblRpdGxlKSIKICAgICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtKSIKICAgICAgICAgICAgICAgID48L3NwYW4+CiAgICAgICAgICAgICAgPC9hPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW5mb19mbGV4Ij4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xCb3R0b20iPgogICAgICAgICAgICAgICAgPGRpdj4KICAgICAgICAgICAgICAgICAg5aqS5L2T5p2l5rqQOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+e3sgaXRlbS5zb3VyY2VOYW1lIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDlj5HluIPml7bpl7Q6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57ewogICAgICAgICAgICAgICAgICAgIGZvcm1hdFB1Ymxpc2hUaW1lKAogICAgICAgICAgICAgICAgICAgICAgaXRlbS5wdWJsaXNoVGltZSwKICAgICAgICAgICAgICAgICAgICAgIGl0ZW0ud2Vic3RlUHVibGlzaFRpbWUKICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDph4fpm4bml7bpl7Q6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57ewogICAgICAgICAgICAgICAgICAgIHBhcnNlVGltZShpdGVtLmNyZWF0ZVRpbWUsICJ7eX0te219LXtkfSB7aH06e2l9OntzfSIpCiAgICAgICAgICAgICAgICAgIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDlpKfmqKHlnovnrZvpgIk6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj4KICAgICAgICAgICAgICAgICAgICB7ewogICAgICAgICAgICAgICAgICAgICAgJHJvdXRlLnF1ZXJ5LmRvbWFpbgogICAgICAgICAgICAgICAgICAgICAgICA/ICLmmK8iCiAgICAgICAgICAgICAgICAgICAgICAgIDogZ2V0VGVjaG5vbG9neUxhYmVsKGl0ZW0uaXNUZWNobm9sb2d5KQogICAgICAgICAgICAgICAgICAgIH19CiAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdgogICAgICAgICAgICAgIGNsYXNzPSJBcnRpY2xNYWluIgogICAgICAgICAgICAgIHN0eWxlPSIKICAgICAgICAgICAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94OwogICAgICAgICAgICAgICAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsKICAgICAgICAgICAgICAgIC13ZWJraXQtbGluZS1jbGFtcDogMjsKICAgICAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICAgICAgICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICAgICAgICAgICAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgdi1pZj0iCiAgICAgICAgICAgICAgICBzaG93U3VtbWFyeSAmJgogICAgICAgICAgICAgICAgaGFzQWN0dWFsQ29udGVudChpdGVtLmNuU3VtbWFyeSB8fCBpdGVtLnN1bW1hcnkpCiAgICAgICAgICAgICAgIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjOWI5YjliIj7mkZjopoHvvJo8L3NwYW4+CiAgICAgICAgICAgICAgPHNwYW4KICAgICAgICAgICAgICAgIHYtaHRtbD0iY2hhbmdlQ29sb3IoZ2V0U2FmZVN1bW1hcnkoaXRlbSkpIgogICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtKSIKICAgICAgICAgICAgICA+PC9zcGFuPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0iYnRuQm94Ij4KICAgICAgICAgICAgPCEtLSA8cD4KICAgICAgICAgICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgcGxhaW4gc2l6ZT0ibWluaSIgQGNsaWNrPSJyZXN1bHRFdmVudChpdGVtKSI+CiAgICAgICAgICAgICAgICAgIHt7IGl0ZW0uc25hcHNob3RVcmwKICAgICAgICAgICAgICAgICAgICA/ICfkuIvovb3lv6vnhacnIDogJ+eUn+aIkOW/q+eFpycgfX0KICAgICAgICAgICAgICAgIDwvZWwtYnV0dG9uPgogICAgICAgICAgICAgIDwvcD4gLS0+CiAgICAgICAgICAgIDxwPgogICAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICAgIHYtaWY9Iml0ZW0uZmlsZVVybCIKICAgICAgICAgICAgICAgIDp0eXBlPSJpdGVtLmZpbGVVcmwgPyAncHJpbWFyeScgOiAnaW5mbyciCiAgICAgICAgICAgICAgICA6ZGlzYWJsZWQ9IiFpdGVtLmZpbGVVcmwiCiAgICAgICAgICAgICAgICBwbGFpbgogICAgICAgICAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICAgICAgICAgIEBjbGljaz0iZG9jdW1lbnREb3dubG9hZChpdGVtKSIKICAgICAgICAgICAgICAgID57eyAi6ZmE5Lu25LiL6L29IiB9fTwvZWwtYnV0dG9uCiAgICAgICAgICAgICAgPgogICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDxwPgogICAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogODBweCIKICAgICAgICAgICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgICAgICAgICAgcGxhaW4KICAgICAgICAgICAgICAgIEBjbGljaz0iaGFuZGxlVXBkYXRlKGl0ZW0pIgogICAgICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydhcnRpY2xlOmFydGljbGVMaXN0OmVkaXQnXSIKICAgICAgICAgICAgICAgID57eyBgIOS/ruaUuSBgIH19PC9lbC1idXR0b24KICAgICAgICAgICAgICA+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPHA+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiA4MHB4IgogICAgICAgICAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICAgICAgICAgIHR5cGU9ImRhbmdlciIKICAgICAgICAgICAgICAgIHBsYWluCiAgICAgICAgICAgICAgICBAY2xpY2s9ImhhbmRsZURlbGV0ZShpdGVtKSIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnYXJ0aWNsZTpsaXN0OnJlbW92ZSddIgogICAgICAgICAgICAgICAgPnt7IGDliKDpmaRgIH19PC9lbC1idXR0b24KICAgICAgICAgICAgICA+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICAgIDxlbC1lbXB0eQogICAgICAgICAgOmltYWdlLXNpemU9IjIwMCIKICAgICAgICAgIGRlc2NyaXB0aW9uPSLlvZPliY3nrZvpgInmnaHku7bkuIvmmoLml6DmlbDmja4iCiAgICAgICAgICB2LWlmPSJBcnRpY2xlTGlzdCA9PSBudWxsIHx8IEFydGljbGVMaXN0Lmxlbmd0aCA9PSAwIgogICAgICAgID48L2VsLWVtcHR5PgogICAgICA8L2Rpdj4KICAgIDwvZWwtY2hlY2tib3gtZ3JvdXA+CiAgPC9kaXY+CiAgPGRpdiBjbGFzcz0iTWFpbkFydGljbGUiIHYtaWY9ImZsYWcgPT0gJ1NwZWNpYWwnIj4KICAgIDxkaXYgY2xhc3M9IlRvcEJ0bkdyb3VwIj4KICAgICAgPGRpdiBjbGFzcz0ibGVmdEJ0bkdyb3VwIj4KICAgICAgICA8cD4KICAgICAgICAgIDxlbC1jaGVja2JveAogICAgICAgICAgICB2LW1vZGVsPSJjaGVja2VkIgogICAgICAgICAgICA6aXNJbmRldGVybWluYXRlPSJpc0luZGV0ZXJtaW5hdGUiCiAgICAgICAgICAgIEBjaGFuZ2U9ImhhbmRsZUNoZWNrQWxsQ2hhbmdlIgogICAgICAgICAgICA+5YWo6YCJPC9lbC1jaGVja2JveAogICAgICAgICAgPgogICAgICAgIDwvcD4KICAgICAgICA8IS0tIDxwIGNsYXNzPSJ0b29sVGl0bGUiPjxpIHRpdGxlPSLkuIvovb0iIGNsYXNzPSJpY29uLXhpYXphaTEiIHN0eWxlPSJjb2xvcjpncmVlbiIgQGNsaWNrPSJkb3duTG9hZCI+PC9pPjwvcD4gIDIwMjMtOS00IOayiOiAgeW4iCAg5pqC5pe25rOo6YeKIC0tPgogICAgICAgIDxwIGNsYXNzPSJ0b29sVGl0bGUiPgogICAgICAgICAgPGkgY2xhc3M9Imljb24tc2h1YXhpbi1jb3B5IiB0aXRsZT0i5Yi35pawIiBAY2xpY2s9IlJlZnJlc2giPjwvaT4KICAgICAgICA8L3A+CiAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aQogICAgICAgICAgICBjbGFzcz0iaWNvbi0tX3RpYW5qaWFkYW9rdSIKICAgICAgICAgICAgdGl0bGU9Iua3u+WKoOWIsOaKpeWRiiIKICAgICAgICAgICAgQGNsaWNrPSJkaWFsb2dWaXNpYmxlID0gdHJ1ZSIKICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydyZXN1bHQ6cmVwb3J0OmFkZCddIgogICAgICAgICAgPjwvaT4KICAgICAgICA8L3A+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2PgogICAgICAgIDxlbC1jaGVja2JveAogICAgICAgICAgdi1tb2RlbD0ic2hvd1N1bW1hcnkiCiAgICAgICAgICBAY2hhbmdlPSIoZSkgPT4gKHNob3dTdW1tYXJ5ID0gZSkiCiAgICAgICAgICBzdHlsZT0ibWFyZ2luLXJpZ2h0OiAxMHB4IgogICAgICAgICAgPuaYr+WQpuaYvuekuuaRmOimgTwvZWwtY2hlY2tib3gKICAgICAgICA+CiAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtc2l6ZTogMTRweCI+5o6S5bqP5pa55byPOjwvc3Bhbj4mbmJzcDsKICAgICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9IlNlYWNoRGF0YS5zb3J0TW9kZSIgc2l6ZT0ibWluaSI+CiAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInlj5HluIPml7bpl7TlgJLluo/mjpLluo8iIDp2YWx1ZT0iZmFsc2UiPjwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5oyJ5Y+R5biD5pe26Ze05q2j5bqP5o6S5bqPIiA6dmFsdWU9InRydWUiPjwvZWwtb3B0aW9uPgogICAgICAgIDwvZWwtc2VsZWN0PgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogICAgPGVsLWNoZWNrYm94LWdyb3VwCiAgICAgIHYtbW9kZWw9ImNoZWNrZWRDaXRpZXMiCiAgICAgIEBjaGFuZ2U9ImhhbmRsZUNoZWNrZWRDaXRpZXNDaGFuZ2UiCiAgICA+CiAgICAgIDxkaXYKICAgICAgICBjbGFzcz0ic2NvbGxCb3giCiAgICAgICAgQG1vdXNld2hlZWw9InNjcm9sbENoYW5nZSIKICAgICAgICByZWY9InNjcm9sbCIKICAgICAgICBzdHlsZT0iaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDA1cHgpIgogICAgICA+CiAgICAgICAgPGRpdiBjbGFzcz0iQXJ0aWNsIiB2LWZvcj0iKGl0ZW0sIGtleSkgaW4gQXJ0aWNsZUxpc3QiIDprZXk9ImtleSI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xfbGVmdCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9IkFydGljbFRvcCI+CiAgICAgICAgICAgICAgPHAgc3R5bGU9Im1hcmdpbjogMjBweCAwIDVweCAwOyBsaW5lLWhlaWdodDogMjBweCI+CiAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3ggOmxhYmVsPSJpdGVtLmlkIiA6dmFsdWU9Iml0ZW0uaWQiPgogICAgICAgICAgICAgICAgICB7eyBudWxsIH19IDwvZWwtY2hlY2tib3gKICAgICAgICAgICAgICAgID4mbmJzcDsmbmJzcDsKICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiCiAgICAgICAgICAgICAgICAgID48c3BhbiBzdHlsZT0icGFkZGluZzogMCAxMHB4IDAgMCIKICAgICAgICAgICAgICAgICAgICA+e3sgKGN1cnJlbnRQYWdlIC0gMSkgKiBwYWdlU2l6ZSArIGtleSArIDEgfX0mbmJzcDs8L3NwYW4KICAgICAgICAgICAgICAgICAgPjxzcGFuCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9InRpdGxlX0FydGljbGUiCiAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtKSIKICAgICAgICAgICAgICAgICAgICB2LWh0bWw9ImNoYW5nZUNvbG9yKGl0ZW0uY25UaXRsZSB8fCBpdGVtLnRpdGxlKSIKICAgICAgICAgICAgICAgICAgPjwvc3BhbgogICAgICAgICAgICAgICAgICA+Jm5ic3A7Jm5ic3A7Jm5ic3A7Jm5ic3A7PC9hCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9wPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW5mb19mbGV4Ij4KICAgICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgICBjbGFzcz0iQXJ0aWNsQm90dG9tIgogICAgICAgICAgICAgICAgdi1pZj0iaXRlbS5zb3VyY2VUeXBlICE9PSAnMScgJiYgaXRlbS5zb3VyY2VUeXBlICE9PSAnMyciCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGRpdj4KICAgICAgICAgICAgICAgICAg5Y6f5paH5qCH6aKYOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+CiAgICAgICAgICAgICAgICAgICAge3sgaXRlbS50aXRsZSB9fQogICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xCb3R0b20iPgogICAgICAgICAgICAgICAgPGRpdj4KICAgICAgICAgICAgICAgICAg57G75Z6LOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+CiAgICAgICAgICAgICAgICAgICAge3sgdHlwZUhhbmRsZShpdGVtLnNvdXJjZVR5cGUpIH19CiAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdj4KICAgICAgICAgICAgICAgICAg5aqS5L2T5p2l5rqQOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+e3sgaXRlbS5zb3VyY2VOYW1lIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9Iml0ZW0uYXV0aG9yIj4KICAgICAgICAgICAgICAgICAg5L2c6ICFOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiIgdi1odG1sPSJpdGVtLmF1dGhvciI+PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDlj5HluIPml7bpl7Q6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57ewogICAgICAgICAgICAgICAgICAgIGZvcm1hdFB1Ymxpc2hUaW1lKAogICAgICAgICAgICAgICAgICAgICAgaXRlbS5wdWJsaXNoVGltZSwKICAgICAgICAgICAgICAgICAgICAgIGl0ZW0ud2Vic3RlUHVibGlzaFRpbWUKICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8IS0tIDxzcGFuIHN0eWxlPSJjb2xvcjogcmdiKDMyLCAxMjYsIDI1MCk7Ij7nm7jkvLzmlofnq6A8L3NwYW4+IC0tPgogICAgICAgICAgICAgICAgPCEtLSA8cD7moIfnrb5BPC9wPiAtLT4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xCb3R0b20iPgogICAgICAgICAgICAgICAgPGRpdiB2LWlmPSJpdGVtLmluZHVzdHJ5TmFtZSI+CiAgICAgICAgICAgICAgICAgIOaJgOWxnuihjOS4mjoKICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iPnt7IGluZHVzdHJ5SGFuZGxlKGl0ZW0pIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9Iml0ZW0uZG9tYWluIj4KICAgICAgICAgICAgICAgICAg5omA5bGe6aKG5Z+fOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+e3sgZG9tYWluSGFuZGxlKGl0ZW0pIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDljp/mlofpk77mjqU6CiAgICAgICAgICAgICAgICAgIDxzcGFuCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9ImxpbmtTdHlsZSIKICAgICAgICAgICAgICAgICAgICBAY2xpY2s9Im9wZW5OZXdWaWV3KGl0ZW0sICdsaW5rJykiCiAgICAgICAgICAgICAgICAgICAgc3R5bGU9ImNvbG9yOiAjMTg4OWYzIgogICAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgICAge3sKICAgICAgICAgICAgICAgICAgICAgIHR5cGVIYW5kbGUoaXRlbS5zb3VyY2VUeXBlKSA9PSAi5b6u5L+h5YWs5LyX5Y+3IgogICAgICAgICAgICAgICAgICAgICAgICA/IGl0ZW0uc2hvcnRVcmwKICAgICAgICAgICAgICAgICAgICAgICAgOiBpdGVtLm9yaWdpbmFsVXJsCiAgICAgICAgICAgICAgICAgICAgfX0KICAgICAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDlpKfmqKHlnovnrZvpgIk6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj4KICAgICAgICAgICAgICAgICAgICB7eyBnZXRUZWNobm9sb2d5TGFiZWwoaXRlbS5pc1RlY2hub2xvZ3kpIH19CiAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdgogICAgICAgICAgICAgIGNsYXNzPSJBcnRpY2xNYWluIgogICAgICAgICAgICAgIHN0eWxlPSIKICAgICAgICAgICAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94OwogICAgICAgICAgICAgICAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsKICAgICAgICAgICAgICAgIC13ZWJraXQtbGluZS1jbGFtcDogMjsKICAgICAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICAgICAgICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICAgICAgICAgICAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgdi1pZj0iCiAgICAgICAgICAgICAgICBzaG93U3VtbWFyeSAmJgogICAgICAgICAgICAgICAgaGFzQWN0dWFsQ29udGVudChpdGVtLmNuU3VtbWFyeSB8fCBpdGVtLnN1bW1hcnkpCiAgICAgICAgICAgICAgIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjOWI5YjliIj7mkZjopoHvvJo8L3NwYW4+CiAgICAgICAgICAgICAgPHNwYW4KICAgICAgICAgICAgICAgIHYtaHRtbD0iY2hhbmdlQ29sb3IoZ2V0U2FmZVN1bW1hcnkoaXRlbSkpIgogICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtKSIKICAgICAgICAgICAgICA+PC9zcGFuPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPCEtLSA8ZGl2IGNsYXNzPSJpbWdCb3giIHYtaWY9Iml0ZW0uaW1nYWdlIj4KICAgICAgICAgICAgICA8aW1nIHNyYz0iLi4vLi4vYXNzZXRzL2ltYWdlcy9wcml2aWV3LnBuZyIgYWx0PSLliqDovb3lpLHotKXkuoYiIC8+CiAgICAgICAgICAgICAgPGltZyBzcmM9Ii4uLy4uL2Fzc2V0cy9pbWFnZXMvcHJpdmlldy5wbmciIGFsdD0i5Yqg6L295aSx6LSl5LqGIiAvPgogICAgICAgICAgICAgIDxpbWcgc3JjPSIuLi8uLi9hc3NldHMvaW1hZ2VzL3ByaXZpZXcucG5nIiBhbHQ9IuWKoOi9veWksei0peS6hiIgLz4KICAgICAgICAgICAgICA8aW1nIHNyYz0iLi4vLi4vYXNzZXRzL2ltYWdlcy9wcml2aWV3LnBuZyIgYWx0PSLliqDovb3lpLHotKXkuoYiIC8+CiAgICAgICAgICAgICAgPGltZyBzcmM9Ii4uLy4uL2Fzc2V0cy9pbWFnZXMvcHJpdmlldy5wbmciIGFsdD0i5Yqg6L295aSx6LSl5LqGIiAvPgogICAgICAgICAgICAgIDxpbWcgc3JjPSIuLi8uLi9hc3NldHMvaW1hZ2VzL3ByaXZpZXcucG5nIiBhbHQ9IuWKoOi9veWksei0peS6hiIgLz4KICAgICAgICAgICAgPC9kaXY+IC0tPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJsZWZ0QnRuR3JvdXAyIj4KICAgICAgICAgICAgPHA+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIDpjbGFzcz0iewogICAgICAgICAgICAgICAgICAnaWNvbi1ob3QyLWNvcHknOgogICAgICAgICAgICAgICAgICAgIGl0ZW0uaXNXaGV0aGVyID09IG51bGwgfHwgaXRlbS5pc1doZXRoZXIgPT0gMCwKICAgICAgICAgICAgICAgICAgJ2ljb24taG90Mic6IGl0ZW0uaXNXaGV0aGVyID09IDEsCiAgICAgICAgICAgICAgICB9IgogICAgICAgICAgICAgICAgdGl0bGU9IueDreeCuSIKICAgICAgICAgICAgICAgIEBjbGljaz0iaG90SW5jcmVhc2UoaXRlbSkiCiAgICAgICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ2FydGljbGU6bGFiZWw6YWRkJ10iCiAgICAgICAgICAgICAgPjwvaT4KICAgICAgICAgICAgPC9wPgogICAgICAgICAgICA8cD4KICAgICAgICAgICAgICA8aQogICAgICAgICAgICAgICAgOmNsYXNzPSJ7CiAgICAgICAgICAgICAgICAgICdpY29uLWppYXhpbmdzaG91Y2FuZ3RpYW5jaG9uZy1jb3B5JzogIWl0ZW0uZmF2b3JpdGVzLAogICAgICAgICAgICAgICAgICAnaWNvbi1qaWF4aW5nc2hvdWNhbmd0aWFuY2hvbmcnOiBpdGVtLmZhdm9yaXRlcywKICAgICAgICAgICAgICAgIH0iCiAgICAgICAgICAgICAgICB0aXRsZT0i5pS26JePIgogICAgICAgICAgICAgICAgQGNsaWNrLnBhc3NpdmU9ImNvbGxlY3QoaXRlbSkiCiAgICAgICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ2FydGljbGU6Y29sbGVjdGlvbjphZGQnXSIKICAgICAgICAgICAgICA+PC9pPgogICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDxwPgogICAgICAgICAgICAgIDxpCiAgICAgICAgICAgICAgICBjbGFzcz0iaWNvbi0tX3RpYW5qaWFkYW9rdSIKICAgICAgICAgICAgICAgIHRpdGxlPSLmt7vliqDliLDmiqXlkYoiCiAgICAgICAgICAgICAgICBAY2xpY2s9InNlcGFyYXRlQWRkKGl0ZW0pIgogICAgICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydyZXN1bHQ6cmVwb3J0OmFkZCddIgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPHA+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIGNsYXNzPSJpY29uLWJpYW9xaWFuIgogICAgICAgICAgICAgICAgdGl0bGU9Iuagh+etviIKICAgICAgICAgICAgICAgIEBjbGljaz0idGFnSGFuZGxlcihpdGVtKSIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnYXJ0aWNsZTpsYWJlbDphZGQnXSIKICAgICAgICAgICAgICA+PC9pPgogICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDwhLS0gPHA+PGkgY2xhc3M9Imljb24tZnV6aGkiIHRpdGxlPSLlpI3liLYiIEBjbGljaz0iY29weVRleHQoaXRlbSkiPjwvaT48L3A+ICAgMjAyMy05LTQg5rKI6ICB5biIIOaaguaXtuazqOmHiiAtLT4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICAgIDxlbC1lbXB0eQogICAgICAgICAgOmltYWdlLXNpemU9IjIwMCIKICAgICAgICAgIGRlc2NyaXB0aW9uPSLlvZPliY3nrZvpgInmnaHku7bkuIvmmoLml6DmlbDmja4iCiAgICAgICAgICB2LWlmPSJBcnRpY2xlTGlzdCA9PSBudWxsIHx8IEFydGljbGVMaXN0Lmxlbmd0aCA9PSAwIgogICAgICAgID48L2VsLWVtcHR5PgogICAgICA8L2Rpdj4KICAgIDwvZWwtY2hlY2tib3gtZ3JvdXA+CiAgPC9kaXY+CiAgPGRpdiBjbGFzcz0iTWFpbkFydGljbGUiIHYtaWY9ImZsYWcgPT0gJ2luZm9JbnRlciciPgogICAgPGRpdiBjbGFzcz0iVG9wQnRuR3JvdXAiPgogICAgICA8ZGl2IGNsYXNzPSJsZWZ0QnRuR3JvdXAiPgogICAgICAgIDxwPgogICAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICAgIHYtbW9kZWw9ImNoZWNrZWQiCiAgICAgICAgICAgIDppc0luZGV0ZXJtaW5hdGU9ImlzSW5kZXRlcm1pbmF0ZSIKICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlQ2hlY2tBbGxDaGFuZ2UiCiAgICAgICAgICAgID7lhajpgIk8L2VsLWNoZWNrYm94CiAgICAgICAgICA+CiAgICAgICAgPC9wPgogICAgICAgIDwhLS0gPHAgY2xhc3M9InRvb2xUaXRsZSI+PGkgdGl0bGU9IuS4i+i9vSIgY2xhc3M9Imljb24teGlhemFpMSIgc3R5bGU9ImNvbG9yOmdyZWVuIiBAY2xpY2s9ImRvd25Mb2FkIj48L2k+PC9wPiAgMjAyMy05LTQg5rKI6ICB5biIICDmmoLml7bms6jph4ogLS0+CiAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aSBjbGFzcz0iaWNvbi1zaHVheGluLWNvcHkiIHRpdGxlPSLliLfmlrAiIEBjbGljaz0iUmVmcmVzaCI+PC9pPgogICAgICAgIDwvcD4KICAgICAgICA8cCBjbGFzcz0idG9vbFRpdGxlIj4KICAgICAgICAgIDxpCiAgICAgICAgICAgIGNsYXNzPSJpY29uLS1fdGlhbmppYWRhb2t1IgogICAgICAgICAgICB0aXRsZT0i5re75Yqg5Yiw5oql5ZGKIgogICAgICAgICAgICBAY2xpY2s9ImRpYWxvZ1Zpc2libGUgPSB0cnVlIgogICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ3Jlc3VsdDpyZXBvcnQ6YWRkJ10iCiAgICAgICAgICA+PC9pPgogICAgICAgIDwvcD4KICAgICAgPC9kaXY+CiAgICAgIDxkaXY+CiAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICB2LW1vZGVsPSJzaG93U3VtbWFyeSIKICAgICAgICAgIEBjaGFuZ2U9IihlKSA9PiAoc2hvd1N1bW1hcnkgPSBlKSIKICAgICAgICAgIHN0eWxlPSJtYXJnaW4tcmlnaHQ6IDEwcHgiCiAgICAgICAgICA+5piv5ZCm5pi+56S65pGY6KaBPC9lbC1jaGVja2JveAogICAgICAgID4KICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC1zaXplOiAxNHB4Ij7mjpLluo/mlrnlvI86PC9zcGFuPiZuYnNwOwogICAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0iU2VhY2hEYXRhLnNvcnRNb2RlIiBzaXplPSJtaW5pIj4KICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaMieWPkeW4g+aXtumXtOWAkuW6j+aOkuW6jyIgOnZhbHVlPSJmYWxzZSI+PC9lbC1vcHRpb24+CiAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInlj5HluIPml7bpl7TmraPluo/mjpLluo8iIDp2YWx1ZT0idHJ1ZSI+PC9lbC1vcHRpb24+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8ZWwtY2hlY2tib3gtZ3JvdXAKICAgICAgdi1tb2RlbD0iY2hlY2tlZENpdGllcyIKICAgICAgQGNoYW5nZT0iaGFuZGxlQ2hlY2tlZENpdGllc0NoYW5nZSIKICAgID4KICAgICAgPGRpdiBjbGFzcz0ic2NvbGxCb3giIEBtb3VzZXdoZWVsPSJzY3JvbGxDaGFuZ2UiIHJlZj0ic2Nyb2xsIj4KICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2wiIHYtZm9yPSIoaXRlbSwga2V5KSBpbiBBcnRpY2xlTGlzdCIgOmtleT0ia2V5Ij4KICAgICAgICAgIDxkaXYgY2xhc3M9IkFydGljbF9sZWZ0Ij4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iQXJ0aWNsVG9wIj4KICAgICAgICAgICAgICA8cCBzdHlsZT0ibWFyZ2luOiAyMHB4IDAgNXB4IDA7IGxpbmUtaGVpZ2h0OiAyMHB4Ij4KICAgICAgICAgICAgICAgIDxlbC1jaGVja2JveCA6bGFiZWw9Iml0ZW0uaWQiIDp2YWx1ZT0iaXRlbS5pZCI+CiAgICAgICAgICAgICAgICAgIHt7IG51bGwgfX0gPC9lbC1jaGVja2JveAogICAgICAgICAgICAgICAgPiZuYnNwOyZuYnNwOwogICAgICAgICAgICAgICAgPGEgaHJlZj0iIyIKICAgICAgICAgICAgICAgICAgPjxzcGFuIHN0eWxlPSJwYWRkaW5nOiAwIDEwcHggMCAwIgogICAgICAgICAgICAgICAgICAgID57eyAoY3VycmVudFBhZ2UgLSAxKSAqIHBhZ2VTaXplICsga2V5ICsgMSB9fSZuYnNwOzwvc3BhbgogICAgICAgICAgICAgICAgICA+PHNwYW4KICAgICAgICAgICAgICAgICAgICBjbGFzcz0idGl0bGVfQXJ0aWNsZSIKICAgICAgICAgICAgICAgICAgICBAY2xpY2s9Im9wZW5OZXdWaWV3KGl0ZW0pIgogICAgICAgICAgICAgICAgICAgIHYtaHRtbD0iY2hhbmdlQ29sb3IoaXRlbS5jblRpdGxlIHx8IGl0ZW0udGl0bGUpIgogICAgICAgICAgICAgICAgICA+PC9zcGFuCiAgICAgICAgICAgICAgICAgID4mbmJzcDsmbmJzcDsmbmJzcDsmbmJzcDs8L2EKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbmZvX2ZsZXgiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9IkFydGljbEJvdHRvbSI+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDnsbvlnos6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj4KICAgICAgICAgICAgICAgICAgICB7eyB0eXBlSGFuZGxlKGl0ZW0uc291cmNlVHlwZSkgfX0KICAgICAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDlqpLkvZPmnaXmupA6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57eyBpdGVtLnNvdXJjZU5hbWUgfX08L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgdi1pZj0iaXRlbS5hdXRob3IiPgogICAgICAgICAgICAgICAgICDkvZzogIU6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIiB2LWh0bWw9Iml0ZW0uYXV0aG9yIj48L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgICAgIOWPkeW4g+aXtumXtDoKICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iPnt7CiAgICAgICAgICAgICAgICAgICAgZm9ybWF0UHVibGlzaFRpbWUoCiAgICAgICAgICAgICAgICAgICAgICBpdGVtLnB1Ymxpc2hUaW1lLAogICAgICAgICAgICAgICAgICAgICAgaXRlbS53ZWJzdGVQdWJsaXNoVGltZQogICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgfX08L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwhLS0gPHNwYW4gc3R5bGU9ImNvbG9yOiByZ2IoMzIsIDEyNiwgMjUwKTsiPuebuOS8vOaWh+eroDwvc3Bhbj4gLS0+CiAgICAgICAgICAgICAgICA8IS0tIDxwPuagh+etvkE8L3A+IC0tPgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9IkFydGljbEJvdHRvbSI+CiAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9Iml0ZW0uaW5kdXN0cnlOYW1lIj4KICAgICAgICAgICAgICAgICAg5omA5bGe6KGM5LiaOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+e3sgaW5kdXN0cnlIYW5kbGUoaXRlbSkgfX08L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgdi1pZj0iaXRlbS5kb21haW4iPgogICAgICAgICAgICAgICAgICDmiYDlsZ7poobln586CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57eyBkb21haW5IYW5kbGUoaXRlbSkgfX08L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgICAgIOWOn+aWh+mTvuaOpToKICAgICAgICAgICAgICAgICAgPHNwYW4KICAgICAgICAgICAgICAgICAgICBjbGFzcz0ibGlua1N0eWxlIgogICAgICAgICAgICAgICAgICAgIEBjbGljaz0ib3Blbk5ld1ZpZXcoaXRlbSwgJ2xpbmsnKSIKICAgICAgICAgICAgICAgICAgICBzdHlsZT0iY29sb3I6ICMxODg5ZjMiCiAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgICB7ewogICAgICAgICAgICAgICAgICAgICAgdHlwZUhhbmRsZShpdGVtLnNvdXJjZVR5cGUpID09ICLlvq7kv6HlhazkvJflj7ciCiAgICAgICAgICAgICAgICAgICAgICAgID8gaXRlbS5zaG9ydFVybAogICAgICAgICAgICAgICAgICAgICAgICA6IGl0ZW0ub3JpZ2luYWxVcmwKICAgICAgICAgICAgICAgICAgICB9fQogICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICBjbGFzcz0iQXJ0aWNsTWFpbiIKICAgICAgICAgICAgICBzdHlsZT0iCiAgICAgICAgICAgICAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsKICAgICAgICAgICAgICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7CiAgICAgICAgICAgICAgICAtd2Via2l0LWxpbmUtY2xhbXA6IDI7CiAgICAgICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgICAgICAgICAgICAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7CiAgICAgICAgICAgICAgIgogICAgICAgICAgICAgIHYtaWY9IgogICAgICAgICAgICAgICAgc2hvd1N1bW1hcnkgJiYKICAgICAgICAgICAgICAgIGhhc0FjdHVhbENvbnRlbnQoaXRlbS5jblN1bW1hcnkgfHwgaXRlbS5zdW1tYXJ5KQogICAgICAgICAgICAgICIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogIzliOWI5YiI+5pGY6KaB77yaPC9zcGFuPgogICAgICAgICAgICAgIDxzcGFuCiAgICAgICAgICAgICAgICB2LWh0bWw9ImNoYW5nZUNvbG9yKGdldFNhZmVTdW1tYXJ5KGl0ZW0pKSIKICAgICAgICAgICAgICAgIEBjbGljaz0ib3Blbk5ld1ZpZXcoaXRlbSkiCiAgICAgICAgICAgICAgPjwvc3Bhbj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwhLS0gPGRpdiBjbGFzcz0iaW1nQm94IiB2LWlmPSJpdGVtLmltZ2FnZSI+CiAgICAgICAgICAgIDxpbWcgc3JjPSIuLi8uLi9hc3NldHMvaW1hZ2VzL3ByaXZpZXcucG5nIiBhbHQ9IuWKoOi9veWksei0peS6hiIgLz4KICAgICAgICAgICAgPGltZyBzcmM9Ii4uLy4uL2Fzc2V0cy9pbWFnZXMvcHJpdmlldy5wbmciIGFsdD0i5Yqg6L295aSx6LSl5LqGIiAvPgogICAgICAgICAgICA8aW1nIHNyYz0iLi4vLi4vYXNzZXRzL2ltYWdlcy9wcml2aWV3LnBuZyIgYWx0PSLliqDovb3lpLHotKXkuoYiIC8+CiAgICAgICAgICAgIDxpbWcgc3JjPSIuLi8uLi9hc3NldHMvaW1hZ2VzL3ByaXZpZXcucG5nIiBhbHQ9IuWKoOi9veWksei0peS6hiIgLz4KICAgICAgICAgICAgPGltZyBzcmM9Ii4uLy4uL2Fzc2V0cy9pbWFnZXMvcHJpdmlldy5wbmciIGFsdD0i5Yqg6L295aSx6LSl5LqGIiAvPgogICAgICAgICAgICA8aW1nIHNyYz0iLi4vLi4vYXNzZXRzL2ltYWdlcy9wcml2aWV3LnBuZyIgYWx0PSLliqDovb3lpLHotKXkuoYiIC8+CiAgICAgICAgICA8L2Rpdj4gLS0+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDxkaXYgY2xhc3M9ImxlZnRCdG5Hcm91cDIiPgogICAgICAgICAgICA8cD4KICAgICAgICAgICAgICA8aQogICAgICAgICAgICAgICAgOmNsYXNzPSJ7CiAgICAgICAgICAgICAgICAgICdpY29uLWhvdDItY29weSc6CiAgICAgICAgICAgICAgICAgICAgaXRlbS5pc1doZXRoZXIgPT0gbnVsbCB8fCBpdGVtLmlzV2hldGhlciA9PSAwLAogICAgICAgICAgICAgICAgICAnaWNvbi1ob3QyJzogaXRlbS5pc1doZXRoZXIgPT0gMSwKICAgICAgICAgICAgICAgIH0iCiAgICAgICAgICAgICAgICB0aXRsZT0i54Ot54K5IgogICAgICAgICAgICAgICAgQGNsaWNrPSJob3RJbmNyZWFzZShpdGVtKSIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnYXJ0aWNsZTpsYWJlbDphZGQnXSIKICAgICAgICAgICAgICA+PC9pPgogICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDxwPgogICAgICAgICAgICAgIDxpCiAgICAgICAgICAgICAgICA6Y2xhc3M9InsKICAgICAgICAgICAgICAgICAgJ2ljb24tamlheGluZ3Nob3VjYW5ndGlhbmNob25nLWNvcHknOiAhaXRlbS5mYXZvcml0ZXMsCiAgICAgICAgICAgICAgICAgICdpY29uLWppYXhpbmdzaG91Y2FuZ3RpYW5jaG9uZyc6IGl0ZW0uZmF2b3JpdGVzLAogICAgICAgICAgICAgICAgfSIKICAgICAgICAgICAgICAgIHRpdGxlPSLmlLbol48iCiAgICAgICAgICAgICAgICBAY2xpY2sucGFzc2l2ZT0iY29sbGVjdChpdGVtKSIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnYXJ0aWNsZTpjb2xsZWN0aW9uOmFkZCddIgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPHA+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIGNsYXNzPSJpY29uLS1fdGlhbmppYWRhb2t1IgogICAgICAgICAgICAgICAgdGl0bGU9Iua3u+WKoOWIsOaKpeWRiiIKICAgICAgICAgICAgICAgIEBjbGljaz0ic2VwYXJhdGVBZGQoaXRlbSkiCiAgICAgICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ3Jlc3VsdDpyZXBvcnQ6YWRkJ10iCiAgICAgICAgICAgICAgPjwvaT4KICAgICAgICAgICAgPC9wPgogICAgICAgICAgICA8cD4KICAgICAgICAgICAgICA8aQogICAgICAgICAgICAgICAgY2xhc3M9Imljb24tYmlhb3FpYW4iCiAgICAgICAgICAgICAgICB0aXRsZT0i5qCH562+IgogICAgICAgICAgICAgICAgQGNsaWNrPSJ0YWdIYW5kbGVyKGl0ZW0pIgogICAgICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydhcnRpY2xlOmxhYmVsOmFkZCddIgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPCEtLSA8cD48aSBjbGFzcz0iaWNvbi1mdXpoaSIgdGl0bGU9IuWkjeWItiIgQGNsaWNrPSJjb3B5VGV4dChpdGVtKSI+PC9pPjwvcD4gICAyMDIzLTktNCDmsojogIHluIgg5pqC5pe25rOo6YeKIC0tPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGVsLWVtcHR5CiAgICAgICAgICA6aW1hZ2Utc2l6ZT0iMjAwIgogICAgICAgICAgZGVzY3JpcHRpb249IuW9k+WJjeetm+mAieadoeS7tuS4i+aaguaXoOaVsOaNriIKICAgICAgICAgIHYtaWY9IkFydGljbGVMaXN0ID09IG51bGwgfHwgQXJ0aWNsZUxpc3QubGVuZ3RoID09IDAiCiAgICAgICAgPjwvZWwtZW1wdHk+CiAgICAgIDwvZGl2PgogICAgPC9lbC1jaGVja2JveC1ncm91cD4KICA8L2Rpdj4KICA8ZGl2IGNsYXNzPSJNYWluQXJ0aWNsZSIgdi1pZj0iZmxhZyA9PSAnV2VjaGF0JyI+CiAgICA8ZGl2IGNsYXNzPSJUb3BCdG5Hcm91cCI+CiAgICAgIDxkaXYgY2xhc3M9ImxlZnRCdG5Hcm91cCI+CiAgICAgICAgPHA+CiAgICAgICAgICA8ZWwtY2hlY2tib3gKICAgICAgICAgICAgdi1tb2RlbD0iY2hlY2tlZCIKICAgICAgICAgICAgOmlzSW5kZXRlcm1pbmF0ZT0iaXNJbmRldGVybWluYXRlIgogICAgICAgICAgICBAY2hhbmdlPSJoYW5kbGVDaGVja0FsbENoYW5nZSIKICAgICAgICAgICAgPuWFqOmAiTwvZWwtY2hlY2tib3gKICAgICAgICAgID4KICAgICAgICA8L3A+CiAgICAgICAgPCEtLSA8cCBjbGFzcz0idG9vbFRpdGxlIj48aSB0aXRsZT0i5LiL6L29IiBjbGFzcz0iaWNvbi14aWF6YWkxIiBzdHlsZT0iY29sb3I6Z3JlZW4iIEBjbGljaz0iZG93bkxvYWQiPjwvaT48L3A+ICAyMDIzLTktNCDmsojogIHluIggIOaaguaXtuazqOmHiiAtLT4KICAgICAgICA8cCBjbGFzcz0idG9vbFRpdGxlIj4KICAgICAgICAgIDxpIGNsYXNzPSJpY29uLXNodWF4aW4tY29weSIgdGl0bGU9IuWIt+aWsCIgQGNsaWNrPSJSZWZyZXNoIj48L2k+CiAgICAgICAgPC9wPgogICAgICAgIDwhLS0gPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aQogICAgICAgICAgICBjbGFzcz0iaWNvbi0tX3RpYW5qaWFkYW9rdSIKICAgICAgICAgICAgdGl0bGU9Iua3u+WKoOWIsOaKpeWRiiIKICAgICAgICAgICAgQGNsaWNrPSJkaWFsb2dWaXNpYmxlID0gdHJ1ZSIKICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydyZXN1bHQ6cmVwb3J0OmFkZCddIgogICAgICAgICAgPjwvaT4KICAgICAgICA8L3A+IC0tPgogICAgICA8L2Rpdj4KICAgICAgPGRpdj4KICAgICAgICA8ZWwtY2hlY2tib3gKICAgICAgICAgIHYtbW9kZWw9InNob3dTdW1tYXJ5IgogICAgICAgICAgQGNoYW5nZT0iKGUpID0+IChzaG93U3VtbWFyeSA9IGUpIgogICAgICAgICAgc3R5bGU9Im1hcmdpbi1yaWdodDogMTBweCIKICAgICAgICAgID7mmK/lkKbmmL7npLrmkZjopoE8L2VsLWNoZWNrYm94CiAgICAgICAgPgogICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXNpemU6IDE0cHgiPuaOkuW6j+aWueW8jzo8L3NwYW4+Jm5ic3A7CiAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJTZWFjaERhdGEuc29ydE1vZGUiIHNpemU9Im1pbmkiPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5oyJ5Y+R5biD5pe26Ze05YCS5bqP5o6S5bqPIiA6dmFsdWU9ImZhbHNlIj48L2VsLW9wdGlvbj4KICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaMieWPkeW4g+aXtumXtOato+W6j+aOkuW6jyIgOnZhbHVlPSJ0cnVlIj48L2VsLW9wdGlvbj4KICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDxlbC1jaGVja2JveC1ncm91cAogICAgICB2LW1vZGVsPSJjaGVja2VkQ2l0aWVzIgogICAgICBAY2hhbmdlPSJoYW5kbGVDaGVja2VkQ2l0aWVzQ2hhbmdlIgogICAgPgogICAgICA8ZGl2CiAgICAgICAgY2xhc3M9InNjb2xsQm94IgogICAgICAgIEBtb3VzZXdoZWVsPSJzY3JvbGxDaGFuZ2UiCiAgICAgICAgcmVmPSJzY3JvbGwiCiAgICAgICAgc3R5bGU9ImhlaWdodDogY2FsYyg1MHZoIC0gMjAwcHgpIgogICAgICA+CiAgICAgICAgPGRpdiBjbGFzcz0iQXJ0aWNsIiB2LWZvcj0iKGl0ZW0sIGtleSkgaW4gQXJ0aWNsZUxpc3QiIDprZXk9ImtleSI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xfa3FjYiI+CiAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iQXJ0aWNsVG9wIiBzdHlsZT0id2lkdGg6IDU1JSI+CiAgICAgICAgICAgICAgICA8cCBzdHlsZT0ibGluZS1oZWlnaHQ6IDIwcHgiPgogICAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3ggOmxhYmVsPSJpdGVtLmlkIiA6dmFsdWU9Iml0ZW0uaWQiPgogICAgICAgICAgICAgICAgICAgIHt7IG51bGwgfX0gPC9lbC1jaGVja2JveAogICAgICAgICAgICAgICAgICA+Jm5ic3A7Jm5ic3A7CiAgICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiCiAgICAgICAgICAgICAgICAgICAgPjxzcGFuIHN0eWxlPSJwYWRkaW5nOiAwIDEwcHggMCAwIgogICAgICAgICAgICAgICAgICAgICAgPnt7CiAgICAgICAgICAgICAgICAgICAgICAgIChjdXJyZW50UGFnZSAtIDEpICogcGFnZVNpemUgKyBrZXkgKyAxCiAgICAgICAgICAgICAgICAgICAgICB9fSZuYnNwOzwvc3BhbgogICAgICAgICAgICAgICAgICAgID48c3BhbgogICAgICAgICAgICAgICAgICAgICAgY2xhc3M9InRpdGxlX0FydGljbGUiCiAgICAgICAgICAgICAgICAgICAgICBAY2xpY2s9Im9wZW5OZXdWaWV3KGl0ZW0pIgogICAgICAgICAgICAgICAgICAgICAgdi1odG1sPSJjaGFuZ2VDb2xvcihpdGVtLmNuVGl0bGUgfHwgaXRlbS50aXRsZSkiCiAgICAgICAgICAgICAgICAgICAgPjwvc3BhbgogICAgICAgICAgICAgICAgICAgID4mbmJzcDsmbmJzcDsmbmJzcDsmbmJzcDs8L2EKICAgICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICA8c3BhbgogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJsaW5rU3R5bGUiCiAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtLCAnbGluaycpIgogICAgICAgICAgICAgICAgICAgIHN0eWxlPSJjb2xvcjogIzE4ODlmMzsgY3Vyc29yOiBwb2ludGVyIgogICAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgICAg5Y6f5paH6ZO+5o6lCiAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbmZvX2ZsZXgiIHN0eWxlPSJ3aWR0aDogYXV0byI+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xCb3R0b20iPgogICAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICAgIOexu+WeizoKICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+CiAgICAgICAgICAgICAgICAgICAgICB7eyB0eXBlSGFuZGxlKGl0ZW0uc291cmNlVHlwZSkgfX0KICAgICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICAgIOWqkuS9k+adpea6kDoKICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+e3sgaXRlbS5zb3VyY2VOYW1lIH19PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiB2LWlmPSJpdGVtLmF1dGhvciI+CiAgICAgICAgICAgICAgICAgICAg5L2c6ICFOgogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIiB2LWh0bWw9Iml0ZW0uYXV0aG9yIj48L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICAgIOWPkeW4g+aXtumXtDoKICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+e3sKICAgICAgICAgICAgICAgICAgICAgIGZvcm1hdFB1Ymxpc2hUaW1lKAogICAgICAgICAgICAgICAgICAgICAgICBpdGVtLnB1Ymxpc2hUaW1lLAogICAgICAgICAgICAgICAgICAgICAgICBpdGVtLndlYnN0ZVB1Ymxpc2hUaW1lCiAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgfX08L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgICA8IS0tIDxzcGFuIHN0eWxlPSJjb2xvcjogcmdiKDMyLCAxMjYsIDI1MCk7Ij7nm7jkvLzmlofnq6A8L3NwYW4+IC0tPgogICAgICAgICAgICAgICAgICA8IS0tIDxwPuagh+etvkE8L3A+IC0tPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xCb3R0b20iPgogICAgICAgICAgICAgICAgICA8ZGl2IHYtaWY9Iml0ZW0uaW5kdXN0cnlOYW1lIj4KICAgICAgICAgICAgICAgICAgICDmiYDlsZ7ooYzkuJo6CiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iPnt7IGluZHVzdHJ5SGFuZGxlKGl0ZW0pIH19PC9zcGFuPgogICAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgICAgPGRpdiB2LWlmPSJpdGVtLmRvbWFpbiI+CiAgICAgICAgICAgICAgICAgICAg5omA5bGe6aKG5Z+fOgogICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57eyBkb21haW5IYW5kbGUoaXRlbSkgfX08L3NwYW4+CiAgICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgY2xhc3M9IkFydGljbE1haW4iCiAgICAgICAgICAgICAgc3R5bGU9IgogICAgICAgICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1ib3g7CiAgICAgICAgICAgICAgICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOwogICAgICAgICAgICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAyOwogICAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgICAgICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogICAgICAgICAgICAgICAgd29yZC1icmVhazogYnJlYWstYWxsOwogICAgICAgICAgICAgICIKICAgICAgICAgICAgICB2LWlmPSIKICAgICAgICAgICAgICAgIHNob3dTdW1tYXJ5ICYmCiAgICAgICAgICAgICAgICBoYXNBY3R1YWxDb250ZW50KGl0ZW0uY25TdW1tYXJ5IHx8IGl0ZW0uc3VtbWFyeSkKICAgICAgICAgICAgICAiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6ICM5YjliOWIiPuaRmOimge+8mjwvc3Bhbj4KICAgICAgICAgICAgICA8c3BhbgogICAgICAgICAgICAgICAgdi1odG1sPSJjaGFuZ2VDb2xvcihnZXRTYWZlU3VtbWFyeShpdGVtKSkiCiAgICAgICAgICAgICAgICBAY2xpY2s9Im9wZW5OZXdWaWV3KGl0ZW0pIgogICAgICAgICAgICAgID48L3NwYW4+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8IS0tIDxkaXYgY2xhc3M9ImxlZnRCdG5Hcm91cDIiPgogICAgICAgICAgICA8cD4KICAgICAgICAgICAgICA8aQogICAgICAgICAgICAgICAgOmNsYXNzPSJ7CiAgICAgICAgICAgICAgICAgICdpY29uLWhvdDItY29weSc6CiAgICAgICAgICAgICAgICAgICAgaXRlbS5pc1doZXRoZXIgPT0gbnVsbCB8fCBpdGVtLmlzV2hldGhlciA9PSAwLAogICAgICAgICAgICAgICAgICAnaWNvbi1ob3QyJzogaXRlbS5pc1doZXRoZXIgPT0gMSwKICAgICAgICAgICAgICAgIH0iCiAgICAgICAgICAgICAgICB0aXRsZT0i54Ot54K5IgogICAgICAgICAgICAgICAgQGNsaWNrPSJob3RJbmNyZWFzZShpdGVtKSIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnYXJ0aWNsZTpsYWJlbDphZGQnXSIKICAgICAgICAgICAgICA+PC9pPgogICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDxwPgogICAgICAgICAgICAgIDxpCiAgICAgICAgICAgICAgICA6Y2xhc3M9InsKICAgICAgICAgICAgICAgICAgJ2ljb24tamlheGluZ3Nob3VjYW5ndGlhbmNob25nLWNvcHknOiAhaXRlbS5mYXZvcml0ZXMsCiAgICAgICAgICAgICAgICAgICdpY29uLWppYXhpbmdzaG91Y2FuZ3RpYW5jaG9uZyc6IGl0ZW0uZmF2b3JpdGVzLAogICAgICAgICAgICAgICAgfSIKICAgICAgICAgICAgICAgIHRpdGxlPSLmlLbol48iCiAgICAgICAgICAgICAgICBAY2xpY2sucGFzc2l2ZT0iY29sbGVjdChpdGVtKSIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnYXJ0aWNsZTpjb2xsZWN0aW9uOmFkZCddIgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPHA+CiAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgIGNsYXNzPSJpY29uLS1fdGlhbmppYWRhb2t1IgogICAgICAgICAgICAgICAgdGl0bGU9Iua3u+WKoOWIsOaKpeWRiiIKICAgICAgICAgICAgICAgIEBjbGljaz0ic2VwYXJhdGVBZGQoaXRlbSkiCiAgICAgICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ3Jlc3VsdDpyZXBvcnQ6YWRkJ10iCiAgICAgICAgICAgICAgPjwvaT4KICAgICAgICAgICAgPC9wPgogICAgICAgICAgICA8cD4KICAgICAgICAgICAgICA8aQogICAgICAgICAgICAgICAgY2xhc3M9Imljb24tYmlhb3FpYW4iCiAgICAgICAgICAgICAgICB0aXRsZT0i5qCH562+IgogICAgICAgICAgICAgICAgQGNsaWNrPSJ0YWdIYW5kbGVyKGl0ZW0pIgogICAgICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydhcnRpY2xlOmxhYmVsOmFkZCddIgogICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDwvcD4gLS0+CiAgICAgICAgICA8IS0tIDxwPjxpIGNsYXNzPSJpY29uLWZ1emhpIiB0aXRsZT0i5aSN5Yi2IiBAY2xpY2s9ImNvcHlUZXh0KGl0ZW0pIj48L2k+PC9wPiAgIDIwMjMtOS00IOayiOiAgeW4iCDmmoLml7bms6jph4ogLS0+CiAgICAgICAgICA8IS0tIDwvZGl2PiAtLT4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZWwtZW1wdHkKICAgICAgICAgIDppbWFnZS1zaXplPSIyMDAiCiAgICAgICAgICBkZXNjcmlwdGlvbj0i5b2T5YmN562b6YCJ5p2h5Lu25LiL5pqC5peg5pWw5o2uIgogICAgICAgICAgdi1pZj0iQXJ0aWNsZUxpc3QgPT0gbnVsbCB8fCBBcnRpY2xlTGlzdC5sZW5ndGggPT0gMCIKICAgICAgICA+PC9lbC1lbXB0eT4KICAgICAgPC9kaXY+CiAgICA8L2VsLWNoZWNrYm94LWdyb3VwPgogIDwvZGl2PgogIDxkaXYKICAgIGNsYXNzPSJNYWluQXJ0aWNsZSIKICAgIHYtaWY9ImZsYWcgPT0gJ2FydGlmaWNpYWxJbnRlbGxpZ2VuY2UnIHx8IGZsYWcgPT0gJ25ldHdvcmtTZWN1cml0eSciCiAgPgogICAgPGRpdiBjbGFzcz0iVG9wQnRuR3JvdXAiPgogICAgICA8ZGl2IGNsYXNzPSJsZWZ0QnRuR3JvdXAiPgogICAgICAgIDxwPgogICAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICAgIHYtbW9kZWw9ImNoZWNrZWQiCiAgICAgICAgICAgIDppc0luZGV0ZXJtaW5hdGU9ImlzSW5kZXRlcm1pbmF0ZSIKICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlQ2hlY2tBbGxDaGFuZ2UiCiAgICAgICAgICAgID7lhajpgIk8L2VsLWNoZWNrYm94CiAgICAgICAgICA+CiAgICAgICAgPC9wPgogICAgICAgIDxwIGNsYXNzPSJ0b29sVGl0bGUiPgogICAgICAgICAgPGkKICAgICAgICAgICAgdGl0bGU9IuaJuemHj+S4i+i9veaWh+eroCIKICAgICAgICAgICAgY2xhc3M9Imljb24teGlhemFpMSIKICAgICAgICAgICAgc3R5bGU9ImNvbG9yOiBncmVlbiIKICAgICAgICAgICAgQGNsaWNrPSJkb3duTG9hZEV4Y2VsIgogICAgICAgICAgPjwvaT4KICAgICAgICA8L3A+CiAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aQogICAgICAgICAgICB0aXRsZT0i5om56YeP5Yig6Zmk5paH56ugIgogICAgICAgICAgICBjbGFzcz0iaWNvbi1zaGFuY2h1IgogICAgICAgICAgICBAY2xpY2s9ImJhdGNoRGVsZXRlIgogICAgICAgICAgPjwvaT4KICAgICAgICA8L3A+CiAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aSBjbGFzcz0iaWNvbi1zaHVheGluLWNvcHkiIHRpdGxlPSLliLfmlrAiIEBjbGljaz0iUmVmcmVzaCI+PC9pPgogICAgICAgIDwvcD4KICAgICAgICA8cCBjbGFzcz0idG9vbFRpdGxlIj4KICAgICAgICAgIDxpCiAgICAgICAgICAgIGNsYXNzPSJpY29uLS1fdGlhbmppYWRhb2t1IgogICAgICAgICAgICB0aXRsZT0i5re75Yqg5Yiw5oql5ZGKIgogICAgICAgICAgICBAY2xpY2s9ImRpYWxvZ1Zpc2libGUgPSB0cnVlIgogICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ3Jlc3VsdDpyZXBvcnQ6YWRkJ10iCiAgICAgICAgICA+PC9pPgogICAgICAgIDwvcD4KICAgICAgPC9kaXY+CiAgICAgIDxkaXY+CiAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICB2LW1vZGVsPSJzaG93U3VtbWFyeSIKICAgICAgICAgIEBjaGFuZ2U9IihlKSA9PiAoc2hvd1N1bW1hcnkgPSBlKSIKICAgICAgICAgIHN0eWxlPSJtYXJnaW4tcmlnaHQ6IDEwcHgiCiAgICAgICAgICA+5piv5ZCm5pi+56S65pGY6KaBPC9lbC1jaGVja2JveAogICAgICAgID4KICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC1zaXplOiAxNHB4Ij7mjpLluo/mlrnlvI86PC9zcGFuPiZuYnNwOwogICAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0iU2VhY2hEYXRhLnNvcnRNb2RlIiBzaXplPSJtaW5pIj4KICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaMieWPkeW4g+aXtumXtOWAkuW6j+aOkuW6jyIgOnZhbHVlPSInMCciPjwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5oyJ5Y+R5biD5pe26Ze05q2j5bqP5o6S5bqPIiA6dmFsdWU9IicxJyI+PC9lbC1vcHRpb24+CiAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInph4fpm4bml7bpl7TlgJLluo/mjpLluo8iIDp2YWx1ZT0iJzInIj48L2VsLW9wdGlvbj4KICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaMiemHh+mbhuaXtumXtOato+W6j+aOkuW6jyIgOnZhbHVlPSInMyciPjwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5oyJ57O757uf5o6o6I2Q5o6S5bqPIiA6dmFsdWU9Iic0JyI+PC9lbC1vcHRpb24+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8ZWwtY2hlY2tib3gtZ3JvdXAKICAgICAgdi1tb2RlbD0iY2hlY2tlZENpdGllcyIKICAgICAgQGNoYW5nZT0iaGFuZGxlQ2hlY2tlZENpdGllc0NoYW5nZSIKICAgID4KICAgICAgPGRpdiBjbGFzcz0ic2NvbGxCb3giIEBtb3VzZXdoZWVsPSJzY3JvbGxDaGFuZ2UiIHJlZj0ic2Nyb2xsIj4KICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2wiIHYtZm9yPSIoaXRlbSwga2V5KSBpbiBBcnRpY2xlTGlzdCIgOmtleT0ia2V5Ij4KICAgICAgICAgIDxkaXYKICAgICAgICAgICAgY2xhc3M9IkFydGljbF9sZWZ0IgogICAgICAgICAgICA6c3R5bGU9Iml0ZW0uZmlsZVVybCA/ICd3aWR0aDogODUlJyA6ICd3aWR0aDogMTAwJSciCiAgICAgICAgICA+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9IkFydGljbFRvcCI+CiAgICAgICAgICAgICAgPHAgc3R5bGU9Im1hcmdpbjogMjBweCAwIDVweCAwOyBsaW5lLWhlaWdodDogMjBweCI+CiAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3ggOmxhYmVsPSJpdGVtLmlkIiA6dmFsdWU9Iml0ZW0uaWQiPgogICAgICAgICAgICAgICAgICB7eyBudWxsIH19IDwvZWwtY2hlY2tib3gKICAgICAgICAgICAgICAgID4mbmJzcDsmbmJzcDsKICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiCiAgICAgICAgICAgICAgICAgID48c3BhbiBzdHlsZT0icGFkZGluZzogMCAxMHB4IDAgMCIKICAgICAgICAgICAgICAgICAgICA+e3sgKGN1cnJlbnRQYWdlIC0gMSkgKiBwYWdlU2l6ZSArIGtleSArIDEgfX0mbmJzcDs8L3NwYW4KICAgICAgICAgICAgICAgICAgPjxzcGFuCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9InRpdGxlX0FydGljbGUiCiAgICAgICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtKSIKICAgICAgICAgICAgICAgICAgICB2LWh0bWw9ImNoYW5nZUNvbG9yKGl0ZW0udGl0bGUpIgogICAgICAgICAgICAgICAgICA+PC9zcGFuCiAgICAgICAgICAgICAgICA+PC9hPgogICAgICAgICAgICAgICAgJm5ic3A7Jm5ic3A7Jm5ic3A7Jm5ic3A7CiAgICAgICAgICAgICAgICA8IS0tIHYtaWY9Iml0ZW0uaXNUcmFuc2xhdGVkID09IDAiIC0tPgogICAgICAgICAgICAgICAgPGkKICAgICAgICAgICAgICAgICAgY2xhc3M9Imljb24tdGFpemhhbmd0cmFuc2xhdGUiCiAgICAgICAgICAgICAgICAgIEBjbGljaz0idHJhbnNsYXRlVGl0bGUoaXRlbSkiCiAgICAgICAgICAgICAgICAgIHYtaWY9Iml0ZW0uc291cmNlVHlwZSAhPT0gJzEnICYmIGl0ZW0uc291cmNlVHlwZSAhPT0gJzMnIgogICAgICAgICAgICAgICAgPjwvaT4KICAgICAgICAgICAgICAgIDxzcGFuCiAgICAgICAgICAgICAgICAgIGNsYXNzPSJsaW5rU3R5bGUiCiAgICAgICAgICAgICAgICAgIEBjbGljaz0ib3Blbk5ld1ZpZXcoaXRlbSwgJ2xpbmsnKSIKICAgICAgICAgICAgICAgICAgc3R5bGU9ImNvbG9yOiAjMTg4OWYzOyBjdXJzb3I6IHBvaW50ZXIiCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgIOWOn+aWh+mTvuaOpQogICAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICBjbGFzcz0iQXJ0aWNsTWFpbiIKICAgICAgICAgICAgICBpZAogICAgICAgICAgICAgIHYtaWY9IgogICAgICAgICAgICAgICAgaXRlbS5zb3VyY2VUeXBlICE9PSAnMScgJiYKICAgICAgICAgICAgICAgIGl0ZW0uc291cmNlVHlwZSAhPT0gJzMnICYmCiAgICAgICAgICAgICAgICBpdGVtLmNuVGl0bGUKICAgICAgICAgICAgICAiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8YSBocmVmPSIjIj4KICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogIzliOWI5YiI+5Lit5paH5qCH6aKYOjwvc3Bhbj4KICAgICAgICAgICAgICAgIDxzcGFuCiAgICAgICAgICAgICAgICAgIHYtaHRtbD0iY2hhbmdlQ29sb3IoaXRlbS5jblRpdGxlKSIKICAgICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtKSIKICAgICAgICAgICAgICAgID48L3NwYW4+CiAgICAgICAgICAgICAgPC9hPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW5mb19mbGV4Ij4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xCb3R0b20iPgogICAgICAgICAgICAgICAgPGRpdj4KICAgICAgICAgICAgICAgICAg5aqS5L2T5p2l5rqQOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+e3sgaXRlbS5zb3VyY2VOYW1lIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDlj5HluIPml7bpl7Q6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57ewogICAgICAgICAgICAgICAgICAgIGZvcm1hdFB1Ymxpc2hUaW1lKAogICAgICAgICAgICAgICAgICAgICAgaXRlbS5wdWJsaXNoVGltZSwKICAgICAgICAgICAgICAgICAgICAgIGl0ZW0ud2Vic3RlUHVibGlzaFRpbWUKICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDph4fpm4bml7bpl7Q6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57ewogICAgICAgICAgICAgICAgICAgIHBhcnNlVGltZShpdGVtLmNyZWF0ZVRpbWUsICJ7eX0te219LXtkfSB7aH06e2l9OntzfSIpCiAgICAgICAgICAgICAgICAgIH19PC9zcGFuPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDlpKfmqKHlnovnrZvpgIk6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj4KICAgICAgICAgICAgICAgICAgICB7eyBnZXRUZWNobm9sb2d5TGFiZWwoaXRlbS5pc1RlY2hub2xvZ3kpIH19CiAgICAgICAgICAgICAgICAgIDwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgPGRpdgogICAgICAgICAgICAgIGNsYXNzPSJBcnRpY2xNYWluIgogICAgICAgICAgICAgIHN0eWxlPSIKICAgICAgICAgICAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94OwogICAgICAgICAgICAgICAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsKICAgICAgICAgICAgICAgIC13ZWJraXQtbGluZS1jbGFtcDogMjsKICAgICAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICAgICAgICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICAgICAgICAgICAgICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgdi1pZj0iCiAgICAgICAgICAgICAgICBzaG93U3VtbWFyeSAmJgogICAgICAgICAgICAgICAgaGFzQWN0dWFsQ29udGVudChpdGVtLmNuU3VtbWFyeSB8fCBpdGVtLnN1bW1hcnkpCiAgICAgICAgICAgICAgIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjOWI5YjliIj7mkZjopoHvvJo8L3NwYW4+CiAgICAgICAgICAgICAgPHNwYW4KICAgICAgICAgICAgICAgIHYtaHRtbD0iY2hhbmdlQ29sb3IoZ2V0U2FmZVN1bW1hcnkoaXRlbSkpIgogICAgICAgICAgICAgICAgQGNsaWNrPSJvcGVuTmV3VmlldyhpdGVtKSIKICAgICAgICAgICAgICA+PC9zcGFuPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0iYnRuQm94IiB2LWlmPSJpdGVtLmZpbGVVcmwiPgogICAgICAgICAgICA8cD4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgICAgICB2LWlmPSJpdGVtLmZpbGVVcmwiCiAgICAgICAgICAgICAgICA6dHlwZT0iaXRlbS5maWxlVXJsID8gJ3ByaW1hcnknIDogJ2luZm8nIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSIhaXRlbS5maWxlVXJsIgogICAgICAgICAgICAgICAgcGxhaW4KICAgICAgICAgICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgICAgICAgICBAY2xpY2s9ImRvY3VtZW50RG93bmxvYWQoaXRlbSkiCiAgICAgICAgICAgICAgICA+e3sgIumZhOS7tuS4i+i9vSIgfX08L2VsLWJ1dHRvbgogICAgICAgICAgICAgID4KICAgICAgICAgICAgPC9wPgogICAgICAgICAgICA8IS0tIDxwIHN0eWxlPSJtYXJnaW4tdG9wOiAtNXB4OyI+CiAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHN0eWxlPSJ3aWR0aDogODBweDsiIHNpemU9Im1pbmkiIHR5cGU9InByaW1hcnkiIHBsYWluIEBjbGljaz0iaGFuZGxlVXBkYXRlKGl0ZW0pIgogICAgICAgICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ2FydGljbGU6YXJ0aWNsZUxpc3Q6ZWRpdCddIj57eyBgIOS/ruaUuSBgIH19PC9lbC1idXR0b24+CiAgICAgICAgICAgICAgPC9wPgogICAgICAgICAgICAgIDxwIHN0eWxlPSJtYXJnaW4tdG9wOiAtNXB4OyI+CiAgICAgICAgICAgICAgICA8ZWwtYnV0dG9uIHN0eWxlPSJ3aWR0aDogODBweDsiIHNpemU9Im1pbmkiIHR5cGU9ImRhbmdlciIgcGxhaW4gQGNsaWNrPSJoYW5kbGVEZWxldGUoaXRlbSkiCiAgICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnYXJ0aWNsZTpsaXN0OnJlbW92ZSddIj57eyBg5Yig6ZmkYCB9fTwvZWwtYnV0dG9uPgogICAgICAgICAgICAgIDwvcD4gLS0+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZWwtZW1wdHkKICAgICAgICAgIDppbWFnZS1zaXplPSIyMDAiCiAgICAgICAgICBkZXNjcmlwdGlvbj0i5b2T5YmN562b6YCJ5p2h5Lu25LiL5pqC5peg5pWw5o2uIgogICAgICAgICAgdi1pZj0iQXJ0aWNsZUxpc3QgPT0gbnVsbCB8fCBBcnRpY2xlTGlzdC5sZW5ndGggPT0gMCIKICAgICAgICA+PC9lbC1lbXB0eT4KICAgICAgPC9kaXY+CiAgICA8L2VsLWNoZWNrYm94LWdyb3VwPgogIDwvZGl2PgogIDxkaXYgY2xhc3M9Ik1haW5BcnRpY2xlIiB2LWlmPSJmbGFnID09ICd6aGlrdWJhb2dhbyciPgogICAgPGRpdiBjbGFzcz0iVG9wQnRuR3JvdXAiPgogICAgICA8ZGl2IGNsYXNzPSJsZWZ0QnRuR3JvdXAiPgogICAgICAgIDxwPgogICAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICAgIHYtbW9kZWw9ImNoZWNrZWQiCiAgICAgICAgICAgIDppc0luZGV0ZXJtaW5hdGU9ImlzSW5kZXRlcm1pbmF0ZSIKICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlQ2hlY2tBbGxDaGFuZ2UiCiAgICAgICAgICAgID7lhajpgIk8L2VsLWNoZWNrYm94CiAgICAgICAgICA+CiAgICAgICAgPC9wPgogICAgICAgIDxwIGNsYXNzPSJ0b29sVGl0bGUiPgogICAgICAgICAgPGkKICAgICAgICAgICAgY2xhc3M9ImVsLWljb24tdXBsb2FkMiIKICAgICAgICAgICAgc3R5bGU9ImZvbnQtc2l6ZTogMjJweDsgY29sb3I6ICMxMjk2ZGIiCiAgICAgICAgICAgIHRpdGxlPSLmibnph4/lr7zlhaXmiqXlkYoiCiAgICAgICAgICAgIEBjbGljaz0ib3BlbkJhdGNoSW1wb3J0RGlhbG9nIgogICAgICAgICAgPjwvaT4KICAgICAgICA8L3A+CiAgICAgICAgPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aQogICAgICAgICAgICB0aXRsZT0i5om56YeP5LiL6L295oql5ZGKIgogICAgICAgICAgICBjbGFzcz0iaWNvbi14aWF6YWkxIgogICAgICAgICAgICBzdHlsZT0iY29sb3I6IGdyZWVuIgogICAgICAgICAgICBAY2xpY2s9ImRvd25Mb2FkRXhjZWwiCiAgICAgICAgICA+PC9pPgogICAgICAgIDwvcD4KICAgICAgICA8cCBjbGFzcz0idG9vbFRpdGxlIj4KICAgICAgICAgIDxpCiAgICAgICAgICAgIHRpdGxlPSLmibnph4/liKDpmaTmiqXlkYoiCiAgICAgICAgICAgIGNsYXNzPSJpY29uLXNoYW5jaHUiCiAgICAgICAgICAgIEBjbGljaz0iYmF0Y2hEZWxldGUiCiAgICAgICAgICA+PC9pPgogICAgICAgIDwvcD4KICAgICAgICA8cCBjbGFzcz0idG9vbFRpdGxlIj4KICAgICAgICAgIDxpIGNsYXNzPSJpY29uLXNodWF4aW4tY29weSIgdGl0bGU9IuWIt+aWsCIgQGNsaWNrPSJSZWZyZXNoIj48L2k+CiAgICAgICAgPC9wPgogICAgICAgIDwhLS0gPHAgY2xhc3M9InRvb2xUaXRsZSI+CiAgICAgICAgICA8aQogICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1jaGF0LWRvdC1yb3VuZCIKICAgICAgICAgICAgc3R5bGU9ImZvbnQtc2l6ZTogMjRweCIKICAgICAgICAgICAgdGl0bGU9IkRlZXBzZWVr5oql5ZGK6Kej6K+7IgogICAgICAgICAgICBAY2xpY2s9InJlcG9ydEFpQ2hhdCIKICAgICAgICAgID48L2k+CiAgICAgICAgICA8c3BhbiBjbGFzcz0iZGVlcHNlZWstdGV4dCIgQGNsaWNrPSJyZXBvcnRBaUNoYXQiCiAgICAgICAgICAgID5EZWVwc2Vla+aKpeWRiuino+ivuzwvc3BhbgogICAgICAgICAgPgogICAgICAgIDwvcD4gLS0+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2PgogICAgICAgIDxlbC1jaGVja2JveAogICAgICAgICAgdi1tb2RlbD0ic2hvd1N1bW1hcnkiCiAgICAgICAgICBAY2hhbmdlPSIoZSkgPT4gKHNob3dTdW1tYXJ5ID0gZSkiCiAgICAgICAgICBzdHlsZT0ibWFyZ2luLXJpZ2h0OiAxMHB4IgogICAgICAgICAgPuaYr+WQpuaYvuekuuaRmOimgTwvZWwtY2hlY2tib3gKICAgICAgICA+CiAgICAgICAgPHNwYW4gc3R5bGU9ImZvbnQtc2l6ZTogMTRweCI+5o6S5bqP5pa55byPOjwvc3Bhbj4mbmJzcDsKICAgICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9IlNlYWNoRGF0YS5zb3J0TW9kZSIgc2l6ZT0ibWluaSI+CiAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInlj5HluIPml7bpl7TlgJLluo/mjpLluo8iIDp2YWx1ZT0iJzAnIj48L2VsLW9wdGlvbj4KICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaMieWPkeW4g+aXtumXtOato+W6j+aOkuW6jyIgOnZhbHVlPSInMSciPjwvZWwtb3B0aW9uPgogICAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5oyJ6YeH6ZuG5pe26Ze05YCS5bqP5o6S5bqPIiA6dmFsdWU9IicyJyI+PC9lbC1vcHRpb24+CiAgICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmjInph4fpm4bml7bpl7TmraPluo/mjpLluo8iIDp2YWx1ZT0iJzMnIj48L2VsLW9wdGlvbj4KICAgICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaMieezu+e7n+aOqOiNkOaOkuW6jyIgOnZhbHVlPSInNCciPjwvZWwtb3B0aW9uPgogICAgICAgIDwvZWwtc2VsZWN0PgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgogICAgPGVsLWNoZWNrYm94LWdyb3VwCiAgICAgIHYtbW9kZWw9ImNoZWNrZWRDaXRpZXMiCiAgICAgIEBjaGFuZ2U9ImhhbmRsZUNoZWNrZWRDaXRpZXNDaGFuZ2UiCiAgICA+CiAgICAgIDxkaXYKICAgICAgICBjbGFzcz0ic2NvbGxCb3giCiAgICAgICAgc3R5bGU9ImhlaWdodDogY2FsYygxMDB2aCAtIDMxMHB4KSIKICAgICAgICBAbW91c2V3aGVlbD0ic2Nyb2xsQ2hhbmdlIgogICAgICAgIHJlZj0ic2Nyb2xsIgogICAgICA+CiAgICAgICAgPGRpdiBjbGFzcz0iQXJ0aWNsIiB2LWZvcj0iKGl0ZW0sIGtleSkgaW4gQXJ0aWNsZUxpc3QiIDprZXk9ImtleSI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJBcnRpY2xfbGVmdCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9IkFydGljbFRvcCI+CiAgICAgICAgICAgICAgPHAgc3R5bGU9Im1hcmdpbjogMjBweCAwIDVweCAwOyBsaW5lLWhlaWdodDogMjBweCI+CiAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3ggOmxhYmVsPSJpdGVtLmlkIiA6dmFsdWU9Iml0ZW0uaWQiPgogICAgICAgICAgICAgICAgICB7eyBudWxsIH19IDwvZWwtY2hlY2tib3gKICAgICAgICAgICAgICAgID4mbmJzcDsmbmJzcDsKICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiCiAgICAgICAgICAgICAgICAgID48c3BhbiBzdHlsZT0icGFkZGluZzogMCAxMHB4IDAgMCI+e3sKICAgICAgICAgICAgICAgICAgICAoY3VycmVudFBhZ2UgLSAxKSAqIHBhZ2VTaXplICsga2V5ICsgMQogICAgICAgICAgICAgICAgICB9fTwvc3BhbgogICAgICAgICAgICAgICAgICA+PHNwYW4KICAgICAgICAgICAgICAgICAgICBjbGFzcz0idGl0bGVfQXJ0aWNsZSIKICAgICAgICAgICAgICAgICAgICBAY2xpY2s9Im9wZW5OZXdWaWV3KGl0ZW0pIgogICAgICAgICAgICAgICAgICAgIHYtaHRtbD0iY2hhbmdlQ29sb3IoaXRlbS50aXRsZSkiCiAgICAgICAgICAgICAgICAgID48L3NwYW4KICAgICAgICAgICAgICAgID48L2E+CiAgICAgICAgICAgICAgICAmbmJzcDsmbmJzcDsmbmJzcDsmbmJzcDsKICAgICAgICAgICAgICAgIDwhLS0gdi1pZj0iaXRlbS5pc1RyYW5zbGF0ZWQgPT0gMCIgLS0+CiAgICAgICAgICAgICAgICA8IS0tIDxpCiAgICAgICAgICAgICAgICAgIGNsYXNzPSJpY29uLXRhaXpoYW5ndHJhbnNsYXRlIgogICAgICAgICAgICAgICAgICBAY2xpY2s9InRyYW5zbGF0ZVRpdGxlKGl0ZW0pIgogICAgICAgICAgICAgICAgICB2LWlmPSJpdGVtLnNvdXJjZVR5cGUgIT09ICcxJyAmJiBpdGVtLnNvdXJjZVR5cGUgIT09ICczJyIKICAgICAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgICAgICA8c3BhbgogICAgICAgICAgICAgICAgICBjbGFzcz0ibGlua1N0eWxlIgogICAgICAgICAgICAgICAgICBAY2xpY2s9Im9wZW5OZXdWaWV3KGl0ZW0sICdsaW5rJykiCiAgICAgICAgICAgICAgICAgIHN0eWxlPSJjb2xvcjogIzE4ODlmMzsgY3Vyc29yOiBwb2ludGVyIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICDljp/mlofpk77mjqUKICAgICAgICAgICAgICAgIDwvc3Bhbj4gLS0+CiAgICAgICAgICAgICAgPC9wPgogICAgICAgICAgICAgIDwhLS0gdi1pZj0iaXRlbS5maWxlVXJsIiAtLT4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICBjbGFzcz0iQXJ0aWNsTWFpbiIKICAgICAgICAgICAgICBpZAogICAgICAgICAgICAgIHYtaWY9IgogICAgICAgICAgICAgICAgaXRlbS5zb3VyY2VUeXBlICE9PSAnMScgJiYKICAgICAgICAgICAgICAgIGl0ZW0uc291cmNlVHlwZSAhPT0gJzMnICYmCiAgICAgICAgICAgICAgICBpdGVtLmNuVGl0bGUKICAgICAgICAgICAgICAiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8YSBocmVmPSIjIj4KICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogIzliOWI5YiI+5Lit5paH5qCH6aKY77yaPC9zcGFuPgogICAgICAgICAgICAgICAgPHNwYW4KICAgICAgICAgICAgICAgICAgdi1odG1sPSJjaGFuZ2VDb2xvcihpdGVtLmNuVGl0bGUpIgogICAgICAgICAgICAgICAgICBAY2xpY2s9Im9wZW5OZXdWaWV3KGl0ZW0pIgogICAgICAgICAgICAgICAgPjwvc3Bhbj4KICAgICAgICAgICAgICA8L2E+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbmZvX2ZsZXgiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9IkFydGljbEJvdHRvbSI+CiAgICAgICAgICAgICAgICA8ZGl2PgogICAgICAgICAgICAgICAgICDlqpLkvZPmnaXmupA6CiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJpbmZvbWF0aW9uIj57eyBpdGVtLnNvdXJjZU5hbWUgfX08L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgICAgIOWPkeW4g+aXtumXtDoKICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iPnt7CiAgICAgICAgICAgICAgICAgICAgZm9ybWF0UHVibGlzaFRpbWUoCiAgICAgICAgICAgICAgICAgICAgICBpdGVtLnB1Ymxpc2hUaW1lLAogICAgICAgICAgICAgICAgICAgICAgaXRlbS53ZWJzdGVQdWJsaXNoVGltZQogICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgfX08L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXY+CiAgICAgICAgICAgICAgICAgIOmHh+mbhuaXtumXtDoKICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9ImluZm9tYXRpb24iPnt7CiAgICAgICAgICAgICAgICAgICAgcGFyc2VUaW1lKGl0ZW0uY3JlYXRlVGltZSwgInt5fS17bX0te2R9IHtofTp7aX06e3N9IikKICAgICAgICAgICAgICAgICAgfX08L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICAgIDwhLS0gPGRpdj4KICAgICAgICAgICAgICAgICAg5aSn5qih5Z6L562b6YCJOgogICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iaW5mb21hdGlvbiI+CiAgICAgICAgICAgICAgICAgICAge3sgZ2V0VGVjaG5vbG9neUxhYmVsKGl0ZW0uaXNUZWNobm9sb2d5KSB9fQogICAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgICA8L2Rpdj4gLS0+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgY2xhc3M9IkFydGljbE1haW4iCiAgICAgICAgICAgICAgc3R5bGU9IgogICAgICAgICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1ib3g7CiAgICAgICAgICAgICAgICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOwogICAgICAgICAgICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAyOwogICAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgICAgICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogICAgICAgICAgICAgICAgd29yZC1icmVhazogYnJlYWstYWxsOwogICAgICAgICAgICAgICIKICAgICAgICAgICAgICB2LWlmPSIKICAgICAgICAgICAgICAgIHNob3dTdW1tYXJ5ICYmCiAgICAgICAgICAgICAgICBoYXNBY3R1YWxDb250ZW50KGl0ZW0uY25TdW1tYXJ5IHx8IGl0ZW0uc3VtbWFyeSkKICAgICAgICAgICAgICAiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6ICM5YjliOWIiPuaRmOimge+8mjwvc3Bhbj4KICAgICAgICAgICAgICA8c3BhbgogICAgICAgICAgICAgICAgdi1odG1sPSJjaGFuZ2VDb2xvcihnZXRTYWZlU3VtbWFyeShpdGVtKSkiCiAgICAgICAgICAgICAgICBAY2xpY2s9Im9wZW5OZXdWaWV3KGl0ZW0pIgogICAgICAgICAgICAgID48L3NwYW4+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJidG5Cb3giPgogICAgICAgICAgICA8IS0tIDxwPgogICAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBwbGFpbiBzaXplPSJtaW5pIiBAY2xpY2s9InJlc3VsdEV2ZW50KGl0ZW0pIj4KICAgICAgICAgICAgICAgICAge3sgaXRlbS5zbmFwc2hvdFVybAogICAgICAgICAgICAgICAgICAgID8gJ+S4i+i9veW/q+eFpycgOiAn55Sf5oiQ5b+r54WnJyB9fQogICAgICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgICAgPC9wPiAtLT4KICAgICAgICAgICAgPHA+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgICAgdi1pZj0iaXRlbS5maWxlVXJsIgogICAgICAgICAgICAgICAgOnR5cGU9Iml0ZW0uZmlsZVVybCA/ICdwcmltYXJ5JyA6ICdpbmZvJyIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0iIWl0ZW0uZmlsZVVybCIKICAgICAgICAgICAgICAgIHBsYWluCiAgICAgICAgICAgICAgICBzaXplPSJtaW5pIgogICAgICAgICAgICAgICAgQGNsaWNrPSJkb2N1bWVudERvd25sb2FkKGl0ZW0pIgogICAgICAgICAgICAgICAgPnt7ICLpmYTku7bkuIvovb0iIH19PC9lbC1idXR0b24KICAgICAgICAgICAgICA+CiAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgPHA+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiA4MHB4IgogICAgICAgICAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgICAgICAgICAgIHR5cGU9ImRhbmdlciIKICAgICAgICAgICAgICAgIHBsYWluCiAgICAgICAgICAgICAgICBAY2xpY2s9ImhhbmRsZURlbGV0ZShpdGVtKSIKICAgICAgICAgICAgICAgID7liKDpmaQ8L2VsLWJ1dHRvbgogICAgICAgICAgICAgID4KICAgICAgICAgICAgPC9wPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGVsLWVtcHR5CiAgICAgICAgICA6aW1hZ2Utc2l6ZT0iMjAwIgogICAgICAgICAgZGVzY3JpcHRpb249IuW9k+WJjeetm+mAieadoeS7tuS4i+aaguaXoOaVsOaNriIKICAgICAgICAgIHYtaWY9IkFydGljbGVMaXN0ID09IG51bGwgfHwgQXJ0aWNsZUxpc3QubGVuZ3RoID09IDAiCiAgICAgICAgPjwvZWwtZW1wdHk+CiAgICAgIDwvZGl2PgogICAgPC9lbC1jaGVja2JveC1ncm91cD4KICA8L2Rpdj4KICA8ZGl2IGNsYXNzPSJwYWdpbmF0aW9uIiByZWY9InBhZ2luYXRpb24iPgogICAgPGVsLXBhZ2luYXRpb24KICAgICAgOnNtYWxsPSJmYWxzZSIKICAgICAgQHNpemUtY2hhbmdlPSJoYW5kbGVTaXplQ2hhbmdlIgogICAgICBAY3VycmVudC1jaGFuZ2U9ImhhbmRsZUN1cnJlbnRDaGFuZ2UiCiAgICAgIDpjdXJyZW50LXBhZ2U9ImN1cnJlbnRQYWdlIgogICAgICA6cGFnZS1zaXplcz0iWzEwLCAyMCwgMzAsIDQwLCA1MF0iCiAgICAgIDpwYWdlLXNpemU9InBhZ2VTaXplIgogICAgICBsYXlvdXQ9Ii0+LCB0b3RhbCwgc2l6ZXMsIHByZXYsIHBhZ2VyLCBuZXh0LCBqdW1wZXIiCiAgICAgIDp0b3RhbD0idG90YWwiCiAgICA+PC9lbC1wYWdpbmF0aW9uPgogIDwvZGl2PgogIDxlbC1kaWFsb2cKICAgIHRpdGxlPSLmt7vliqDliLDmiqXlkYoiCiAgICA6dmlzaWJsZS5zeW5jPSJkaWFsb2dWaXNpYmxlIgogICAgd2lkdGg9IjMwJSIKICAgIDpjbG9zZS1vbi1jbGljay1tb2RhbD0iZmFsc2UiCiAgPgogICAgPGVsLXJvdyBzdHlsZT0ibGluZS1oZWlnaHQ6IDUwcHgiPgogICAgICA8ZWwtY29sIDpzcGFuPSIxOCI+CiAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgdi1tb2RlbD0icmVwb3J0SWQiCiAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup5oql5ZGKIgogICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgPgogICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICB2LWZvcj0iKGl0ZW0sIGtleSkgaW4gcmVwb3J0T3B0aW9ucyIKICAgICAgICAgICAgOmtleT0ia2V5IgogICAgICAgICAgICA6bGFiZWw9Iml0ZW0udGl0bGUiCiAgICAgICAgICAgIDp2YWx1ZT0iaXRlbS5pZCIKICAgICAgICAgID48L2VsLW9wdGlvbj4KICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgPC9lbC1jb2w+CiAgICA8L2VsLXJvdz4KICAgIDxzcGFuIHNsb3Q9ImZvb3RlciIgY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iZGlhbG9nVmlzaWJsZSA9IGZhbHNlIj7lj5Yg5raIPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJyZXBvcnRTdWJtaXQiPuehriDlrpo8L2VsLWJ1dHRvbj4KICAgIDwvc3Bhbj4KICA8L2VsLWRpYWxvZz4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZQogICAgOnZpc2libGUuc3luYz0idGFnRGlhbG9nIgogICAgd2lkdGg9IjIwJSIKICAgIDpiZWZvcmUtY2xvc2U9ImNsb3NlVGFnIgogICAgOmNsb3NlLW9uLWNsaWNrLW1vZGFsPSJmYWxzZSIKICA+CiAgICA8ZWwtZm9ybQogICAgICBsYWJlbC1wb3NpdGlvbj0ibGVmdCIKICAgICAgbGFiZWwtd2lkdGg9IjQwcHgiCiAgICAgIDptb2RlbD0iZm9ybUxhYmVsQWxpZ24iCiAgICAgIHJlZj0icnVsZUZvcm0iCiAgICA+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuihjOS4miIgcHJvcD0iaW5kdXN0cnkiPgogICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgIHYtbW9kZWw9ImZvcm1MYWJlbEFsaWduLmluZHVzdHJ5IgogICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICBtdWx0aXBsZQogICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeihjOS4miIKICAgICAgICAgIDpmaWx0ZXJhYmxlPSJ0cnVlIgogICAgICAgICAgZGVmYXVsdC1maXJzdC1vcHRpb24KICAgICAgICA+CiAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIG9wdGlvbnMxIgogICAgICAgICAgICA6a2V5PSJpdGVtLmlkIgogICAgICAgICAgICA6bGFiZWw9Iml0ZW0uaW5kdXN0cnlOYW1lIgogICAgICAgICAgICA6dmFsdWU9Iml0ZW0uaWQiCiAgICAgICAgICA+PC9lbC1vcHRpb24+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpoobln58iIHByb3A9ImRvbWFpbiI+CiAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgdi1tb2RlbD0iZm9ybUxhYmVsQWxpZ24uZG9tYWluIgogICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgbXVsdGlwbGUKICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqemihuWfnyIKICAgICAgICAgIGRlZmF1bHQtZmlyc3Qtb3B0aW9uCiAgICAgICAgPgogICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiBvcHRpb25zIgogICAgICAgICAgICA6a2V5PSJpdGVtLmlkIgogICAgICAgICAgICA6bGFiZWw9Iml0ZW0uZmllbGROYW1lIgogICAgICAgICAgICA6dmFsdWU9Iml0ZW0uaWQiCiAgICAgICAgICA+PC9lbC1vcHRpb24+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmoIfnrb4iIHByb3A9InRhZyI+CiAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgdi1tb2RlbD0iZm9ybUxhYmVsQWxpZ24udGFnIgogICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgbXVsdGlwbGUKICAgICAgICAgIGZpbHRlcmFibGUKICAgICAgICAgIGFsbG93LWNyZWF0ZQogICAgICAgICAgZGVmYXVsdC1maXJzdC1vcHRpb24KICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fmt7vliqDmlofnq6DmoIfnrb4iCiAgICAgICAgPgogICAgICAgICAgPCEtLSA8ZWwtb3B0aW9uIHYtZm9yPSJpdGVtIGluIG9wdGlvbnMiIDprZXk9Iml0ZW0udmFsdWUiIDpsYWJlbD0iaXRlbS5sYWJlbCIgOnZhbHVlPSJpdGVtLnZhbHVlIj4KICAgICAgICAgIDwvZWwtb3B0aW9uPi0tPgogICAgICAgIDwvZWwtc2VsZWN0PgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgIDwvZWwtZm9ybT4KICAgIDxkaXYgc2xvdD0iZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9ImNsb3NlVGFnIiBzaXplPSJtaW5pIj7lj5Yg5raIPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJTdWJtaXRUYWciIHR5cGU9InByaW1hcnkiIHNpemU9Im1pbmkiCiAgICAgICAgPuehriDlrpo8L2VsLWJ1dHRvbgogICAgICA+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KICA8ZWwtZHJhd2VyCiAgICBAb3Blbj0ib3BlbkRyYXdlciIKICAgIDp0aXRsZT0iZHJhd2VySW5mby5jblRpdGxlIHx8IGRyYXdlckluZm8udGl0bGUiCiAgICA6dmlzaWJsZS5zeW5jPSJkcmF3ZXIiCiAgICBjdXN0b20tY2xhc3M9ImRyYXdlcl9ib3giCiAgICBkaXJlY3Rpb249InJ0bCIKICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICA+CiAgICA8ZGl2CiAgICAgIHYtaWY9ImZsYWcgPT0gJ01vbml0b3JVc2UnIHx8IGZsYWcgPT0gJ3NwZWNpYWxTdWJqZWN0VXNlJyIKICAgICAgc3R5bGU9IndpZHRoOiAxMDAlOyB0ZXh0LWFsaWduOiByaWdodDsgcGFkZGluZy1yaWdodDogMTBweCIKICAgID4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIEBjbGljaz0iZG9jdW1lbnREb3dubG9hZCgnZHJhd2VyJykiCiAgICAgICAgOnR5cGU9ImRyYXdlckluZm8uZmlsZVVybCA/ICdwcmltYXJ5JyA6ICdpbmZvJyIKICAgICAgICBzaXplPSJtaW5pIgogICAgICAgIHBsYWluCiAgICAgICAgOmRpc2FibGVkPSIhZHJhd2VySW5mby5maWxlVXJsIgogICAgICAgID57eyAi6ZmE5Lu25LiL6L29IiB9fTwvZWwtYnV0dG9uCiAgICAgID4KICAgICAgPCEtLSA8ZWwtYnV0dG9uIEBjbGljaz0icmVzdWx0RXZlbnQoJ2RyYXdlcicpIiB0eXBlPSJwcmltYXJ5IiBzaXplPSJtaW5pIiBwbGFpbj4KICAgICAgICB7eyBkcmF3ZXJJbmZvLnNuYXBzaG90VXJsCiAgICAgICAgICA/ICfkuIvovb3lv6vnhacnIDogJ+eUn+aIkOW/q+eFpycgfX0KICAgICAgPC9lbC1idXR0b24+IC0tPgogICAgICA8IS0tIHYtaWY9InRyYW5zbGF0aW9uQnRuU2hvdyIgLS0+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgcGxhaW4KICAgICAgICBAY2xpY2s9InRyYW5zbGF0ZUV2ZW50KGRyYXdlckluZm8pIgogICAgICAgID7nv7vor5HlhoXlrrk8L2VsLWJ1dHRvbgogICAgICA+CiAgICA8L2Rpdj4KICAgIDx0ZW1wbGF0ZSBzbG90PSJ0aXRsZSI+CiAgICAgIDxzcGFuIGNsYXNzPSJkcmF3ZXJfVGl0bGUiPnt7CiAgICAgICAgZHJhd2VySW5mby5jblRpdGxlIHx8IGRyYXdlckluZm8udGl0bGUKICAgICAgfX08L3NwYW4+CiAgICA8L3RlbXBsYXRlPgogICAgPGRpdiBjbGFzcz0iZHJhd2VyX1N0eWxlIj4KICAgICAgPHAgY2xhc3M9InRpdGxlIj4KICAgICAgICB7eyBkcmF3ZXJJbmZvLmNuVGl0bGUgfHwgZHJhd2VySW5mby50aXRsZSB9fQogICAgICAgIDwhLS0gdi1pZj0iIWRyYXdlckluZm8uY25UaXRsZSIgLS0+CiAgICAgICAgPGkKICAgICAgICAgIGNsYXNzPSJpY29uLXRhaXpoYW5ndHJhbnNsYXRlIgogICAgICAgICAgQGNsaWNrPSJ0cmFuc2xhdGVUaXRsZShkcmF3ZXJJbmZvKSIKICAgICAgICA+PC9pPgogICAgICA8L3A+CiAgICAgIDxwIHN0eWxlPSJ0ZXh0LWFsaWduOiBjZW50ZXIiPgogICAgICAgIDxzcGFuIGNsYXNzPSJzb3VyY2UiPnt7IGRyYXdlckluZm8uc291cmNlTmFtZSB9fTwvc3Bhbj4KICAgICAgICA8c3BhbiBjbGFzcz0idGltZSI+e3sgZHJhd2VySW5mby5wdWJsaXNoVGltZSB9fTwvc3Bhbj4KICAgICAgPC9wPgogICAgICA8ZGl2CiAgICAgICAgc3R5bGU9InVzZXItc2VsZWN0OiB0ZXh0ICFpbXBvcnRhbnQ7IGxpbmUtaGVpZ2h0OiAzMHB4IgogICAgICAgIHYtaHRtbD0iCiAgICAgICAgICBkcmF3ZXJJbmZvLmNuQ29udGVudCAmJgogICAgICAgICAgZHJhd2VySW5mby5jbkNvbnRlbnQucmVwbGFjZSgvPGltZ1xiW14+XSo+L2dpLCAnJykKICAgICAgICAiCiAgICAgID48L2Rpdj4KICAgICAgPGVsLWVtcHR5CiAgICAgICAgZGVzY3JpcHRpb249IuW9k+WJjeaWh+eroOaaguaXoOaVsOaNriIKICAgICAgICB2LWlmPSIhZHJhd2VySW5mby5jbkNvbnRlbnQiCiAgICAgID48L2VsLWVtcHR5PgogICAgPC9kaXY+CiAgPC9lbC1kcmF3ZXI+CiAgPGVsLWRpYWxvZwogICAgOnRpdGxlPSIn5L+u5pS55paH56ugJyIKICAgIDp2aXNpYmxlLnN5bmM9Im9wZW4iCiAgICB3aWR0aD0iMTAwMHB4IgogICAgYXBwZW5kLXRvLWJvZHkKICAgIDpjbG9zZS1vbi1jbGljay1tb2RhbD0iZmFsc2UiCiAgPgogICAgPGRpdiBjbGFzcz0iZGlhbG9nX0JveCI+CiAgICAgIDxlbC1mb3JtCiAgICAgICAgOm1vZGVsPSJmb3JtIgogICAgICAgIGNsYXNzPSJmb3JtX1N0eWxlIgogICAgICAgIGxhYmVsLXBvc2l0aW9uPSJ0b3AiCiAgICAgICAgcmVmPSJmb3JtIgogICAgICAgIDpydWxlcz0icnVsZXMiCiAgICAgICAgc2l6ZT0ibWluaSIKICAgICAgPgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMjAiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMjQiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmlofnq6DmoIfpopgiIHByb3A9InRpdGxlIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS50aXRsZSI+PC9lbC1pbnB1dD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Lit5paH5qCH6aKYIiBwcm9wPSJjblRpdGxlIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZm9ybS5jblRpdGxlIj48L2VsLWlucHV0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMjAiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlubPlj7DnsbvlnosiIHByb3A9InNvdXJjZVR5cGUiPgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uc291cmNlVHlwZSIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgdi1mb3I9IihpdGVtLCBpbmRleCkgaW4gc291cmNlVHlwZUxpc3QiCiAgICAgICAgICAgICAgICAgIDprZXk9ImluZGV4IgogICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubmFtZSIKICAgICAgICAgICAgICAgICAgOnZhbHVlPSJpdGVtLmlkIgogICAgICAgICAgICAgICAgPjwvZWwtb3B0aW9uPgogICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlqpLkvZPmnaXmupAiIHByb3A9InNvdXJjZU5hbWUiPgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uc291cmNlTmFtZSIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgdi1mb3I9IihpdGVtLCBpbmRleCkgaW4gc291cmNlVHlwZUxpc3RzIgogICAgICAgICAgICAgICAgICA6a2V5PSJpbmRleCIKICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLm5hbWUiCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iaXRlbS5uYW1lIgogICAgICAgICAgICAgICAgPjwvZWwtb3B0aW9uPgogICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMjAiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlj5HluIPml7bpl7QiIHByb3A9InB1Ymxpc2hUaW1lIj4KICAgICAgICAgICAgICA8ZWwtZGF0ZS1waWNrZXIKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0ucHVibGlzaFRpbWUiCiAgICAgICAgICAgICAgICB2YWx1ZS1mb3JtYXQ9Inl5eXktTU0tZGQgSEg6bW06c3MiCiAgICAgICAgICAgICAgICB0eXBlPSJkYXRldGltZSIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9IumAieaLqeaXpeacnyIKICAgICAgICAgICAgICA+PC9lbC1kYXRlLXBpY2tlcj4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5paH56ug5L2c6ICFIiBwcm9wPSJhdXRob3IiPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5hdXRob3IiCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICA+PC9lbC1pbnB1dD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8L2VsLXJvdz4KICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLljp/mlofpk77mjqUiIHByb3A9Im9yaWdpbmFsVXJsIj4KICAgICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtLm9yaWdpbmFsVXJsIj48L2VsLWlucHV0PgogICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaRmOimgSIgcHJvcD0ic3VtbWFyeSI+CiAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5zdW1tYXJ5IgogICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgICAgOmF1dG9zaXplPSJ7IG1pblJvd3M6IDMsIG1heFJvd3M6IDYgfSIKICAgICAgICAgID48L2VsLWlucHV0PgogICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuS4reaWh+aRmOimgSIgcHJvcD0iY25TdW1tYXJ5Ij4KICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmNuU3VtbWFyeSIKICAgICAgICAgICAgdHlwZT0idGV4dGFyZWEiCiAgICAgICAgICAgIDphdXRvc2l6ZT0ieyBtaW5Sb3dzOiAzLCBtYXhSb3dzOiA2IH0iCiAgICAgICAgICA+PC9lbC1pbnB1dD4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLkuK3mloflhoXlrrkiIHByb3A9ImNuQ29udGVudCI+CiAgICAgICAgICA8ZWRpdG9yIHYtbW9kZWw9ImZvcm0uY25Db250ZW50IiA6bWluSGVpZ2h0PSIxNTAiPjwvZWRpdG9yPgogICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaWh+eroOWGheWuuSIgcHJvcD0iY29udGVudCI+CiAgICAgICAgICA8ZWRpdG9yIHYtbW9kZWw9ImZvcm0uY29udGVudCIgOm1pbkhlaWdodD0iMTUwIj48L2VkaXRvcj4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8IS0tIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWwgemdouWbvueJhyIgcHJvcD0iY292ZXIiPgogICAgICAgICAgPGVsLXVwbG9hZCBhY3Rpb249IiMiIHJlZj0idXBsb2FkIiA6bGltaXQ9IjMiIDpvbi1leGNlZWQ9ImV4Y2VlZCIgbGlzdC10eXBlPSJwaWN0dXJlLWNhcmQiCiAgICAgICAgICAgIDphdXRvLXVwbG9hZD0iZmFsc2UiIDpoZWFkZXJzPSJ2ZXJ0aWZ5VXBsb2FkLmhlYWRlcnMiIDpmaWxlLWxpc3Q9ImZpbGVMaXN0IiA6b24tY2hhbmdlPSJoYW5kbGVDaGFuZ2UiCiAgICAgICAgICAgIDpodHRwLXJlcXVlc3Q9InJlcXVlc3RMb2FkIj4KICAgICAgICAgICAgPGkgc2xvdD0iZGVmYXVsdCIgY2xhc3M9ImVsLWljb24tcGx1cyI+PC9pPgogICAgICAgICAgICA8ZGl2IHNsb3Q9ImZpbGUiIHNsb3Qtc2NvcGU9InsgZmlsZSB9Ij4KICAgICAgICAgICAgICA8aW1nIGNsYXNzPSJlbC11cGxvYWQtbGlzdF9faXRlbS10aHVtYm5haWwiIDpzcmM9ImZpbGUudXJsIiBhbHQ9IuaWh+S7tue8qeeVpeWbvuWKoOi9veWksei0pSIgLz4KICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0iZWwtdXBsb2FkLWxpc3RfX2l0ZW0tYWN0aW9ucyI+CiAgICAgICAgICAgICAgICA8c3BhbiB2LWlmPSIhZGlzYWJsZWQiIGNsYXNzPSJlbC11cGxvYWQtbGlzdF9faXRlbS1kZWxldGUiIEBjbGljaz0iaGFuZGxlUmVtb3ZlKGZpbGUpIj4KICAgICAgICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tZGVsZXRlIj48L2k+CiAgICAgICAgICAgICAgICA8L3NwYW4+CiAgICAgICAgICAgICAgPC9zcGFuPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZWwtdXBsb2FkPgogICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuS4iuS8oOmZhOS7tiIgcHJvcD0iZmlsZVVybCI+CiAgICAgICAgICA8ZWwtdXBsb2FkIGNsYXNzPSJ1cGxvYWQtZGVtbyIgOmFjdGlvbj0iZmlsZVVybHVybCIgOmJlZm9yZS11cGxvYWQ9ImJlZm9yZVVwbG9hZFVybCIgbXVsdGlwbGUgOmxpbWl0PSIxIgogICAgICAgICAgICA6aHR0cC1yZXF1ZXN0PSJ1cGxvYWRVcmxSZXF1ZXN0IiA6b24tc3VjY2Vzcz0idXBsb2FkVXJsU3VjY2VzcyIgOmZpbGUtbGlzdD0iZmlsZVVybExpc3QiCiAgICAgICAgICAgIDpvbi1leGNlZWQ9InVwbG9hZFVybEV4Y2VlZCIgOm9uLXJlbW92ZT0idXBsb2FkVXJsUmVtb3ZlIj4KICAgICAgICAgICAgPGVsLWJ1dHRvbiBzaXplPSJzbWFsbCIgdHlwZT0icHJpbWFyeSI+54K55Ye75LiK5LygPC9lbC1idXR0b24+CiAgICAgICAgICAgIDxkaXYgc2xvdD0idGlwIiBjbGFzcz0iZWwtdXBsb2FkX190aXAiPuWPquiDveS4iuS8oGpwZy9wbmfmlofku7bvvIzkuJTkuI3otoXov4c1MDBrYjwvZGl2PgogICAgICAgICAgPC9lbC11cGxvYWQ+CiAgICAgICAgPC9lbC1mb3JtLWl0ZW0+IC0tPgogICAgICA8L2VsLWZvcm0+CiAgICA8L2Rpdj4KICAgIDxkaXYgc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJzdWJtaXRGb3JtIj7noa4g5a6aPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjYW5jZWwiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgoKICA8IS0tIOaJuemHj+WvvOWFpeW8ueahhiAtLT4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i5om56YeP5a+85YWl5oql5ZGKIgogICAgOnZpc2libGUuc3luYz0iYmF0Y2hJbXBvcnRWaXNpYmxlIgogICAgd2lkdGg9IjgwMHB4IgogICAgOmNsb3NlLW9uLWNsaWNrLW1vZGFsPSJmYWxzZSIKICA+CiAgICA8ZGl2IGNsYXNzPSJiYXRjaC1pbXBvcnQtY29udGFpbmVyIj4KICAgICAgPCEtLSDmlofku7bpgInmi6nljLrln58gLS0+CiAgICAgIDxkaXYgY2xhc3M9ImZpbGUtc2VsZWN0LWFyZWEiPgogICAgICAgIDxlbC11cGxvYWQKICAgICAgICAgIHJlZj0iYmF0Y2hVcGxvYWQiCiAgICAgICAgICBhY3Rpb249IiMiCiAgICAgICAgICA6YXV0by11cGxvYWQ9ImZhbHNlIgogICAgICAgICAgOnNob3ctZmlsZS1saXN0PSJmYWxzZSIKICAgICAgICAgIG11bHRpcGxlCiAgICAgICAgICA6b24tY2hhbmdlPSJoYW5kbGVGaWxlU2VsZWN0IgogICAgICAgICAgYWNjZXB0PSIucGRmLC5kb2MsLmRvY3gsLnR4dCIKICAgICAgICA+CiAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIGljb249ImVsLWljb24tdXBsb2FkIj7pgInmi6nmlofku7Y8L2VsLWJ1dHRvbj4KICAgICAgICAgIDxkaXYgc2xvdD0idGlwIiBjbGFzcz0iZWwtdXBsb2FkX190aXAiPgogICAgICAgICAgICDmlK/mjIHpgInmi6nlpJrkuKrmlofku7bvvIzmoLzlvI/vvJpQREbjgIFET0PjgIFET0NY44CBVFhUCiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2VsLXVwbG9hZD4KICAgICAgPC9kaXY+CgogICAgICA8IS0tIOaWh+S7tuWIl+ihqOihqOagvCAtLT4KICAgICAgPGRpdiBjbGFzcz0iZmlsZS10YWJsZS1hcmVhIiB2LWlmPSJiYXRjaEltcG9ydEZpbGVzLmxlbmd0aCA+IDAiPgogICAgICAgIDxlbC10YWJsZQogICAgICAgICAgOmRhdGE9ImJhdGNoSW1wb3J0RmlsZXMiCiAgICAgICAgICBib3JkZXIKICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJTsgbWFyZ2luLXRvcDogMjBweCIKICAgICAgICAgIG1heC1oZWlnaHQ9IjMwMCIKICAgICAgICA+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9ImZpbGVOYW1lIiBsYWJlbD0i5paH5Lu25ZCN56ewIiB3aWR0aD0iMzAwIj4KICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICB7eyBzY29wZS5yb3cuZmlsZU5hbWUgfX0KICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJzb3VyY2VOYW1lIiBsYWJlbD0i5pWw5o2u5rqQ5ZCN56ewIj4KICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgIHYtbW9kZWw9InNjb3BlLnJvdy5zb3VyY2VOYW1lIgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaVsOaNrua6kOWQjeensCIKICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgID48L2VsLWlucHV0PgogICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmk43kvZwiIHdpZHRoPSI4MCI+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgQGNsaWNrPSJyZW1vdmVGaWxlKHNjb3BlLiRpbmRleCkiCiAgICAgICAgICAgICAgICBzdHlsZT0iY29sb3I6ICNmNTZjNmMiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAg5Yig6ZmkCiAgICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICA8L2VsLXRhYmxlPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgoKICAgIDxkaXYgc2xvdD0iZm9vdGVyIiBjbGFzcz0iZGlhbG9nLWZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjYW5jZWxCYXRjaEltcG9ydCI+5Y+WIOa2iDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICBAY2xpY2s9ImNvbmZpcm1CYXRjaEltcG9ydCIKICAgICAgICA6ZGlzYWJsZWQ9ImJhdGNoSW1wb3J0RmlsZXMubGVuZ3RoID09PSAwIgogICAgICA+CiAgICAgICAg56GuIOiupAogICAgICA8L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgoKICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0iRGVlcHNlZWvmt7Hluqbop6Por7siCiAgICA6dmlzaWJsZS5zeW5jPSJhaURpYWxvZ1Zpc2libGUiCiAgICB3aWR0aD0iMTAwMHB4IgogICAgOmJlZm9yZS1jbG9zZT0iY2xvc2VBaURpYWxvZyIKICAgIGN1c3RvbS1jbGFzcz0iYWktZGlhbG9nIgogICAgOmNsb3NlLW9uLWNsaWNrLW1vZGFsPSJmYWxzZSIKICA+CiAgICA8ZGl2IGNsYXNzPSJhaS1jaGF0LWNvbnRhaW5lciI+CiAgICAgIDxkaXYgY2xhc3M9ImNoYXQtbWVzc2FnZXMiIHJlZj0iY2hhdE1lc3NhZ2VzIj4KICAgICAgICA8ZGl2CiAgICAgICAgICBjbGFzcz0ibWVzc2FnZSIKICAgICAgICAgIHYtZm9yPSIobWVzc2FnZSwgaW5kZXgpIGluIGNoYXRNZXNzYWdlcyIKICAgICAgICAgIDprZXk9ImluZGV4IgogICAgICAgICAgOmNsYXNzPSJbCiAgICAgICAgICAgICdtZXNzYWdlJywKICAgICAgICAgICAgbWVzc2FnZS5yb2xlID09PSAndXNlcicgPyAndXNlci1tZXNzYWdlJyA6ICdhaS1tZXNzYWdlJywKICAgICAgICAgIF0iCiAgICAgICAgPgogICAgICAgICAgPGRpdiBjbGFzcz0iYXZhdGFyIj4KICAgICAgICAgICAgPGltZwogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMzBweDsgaGVpZ2h0OiAzMHB4IgogICAgICAgICAgICAgIHYtaWY9Im1lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInIgogICAgICAgICAgICAgIDpzcmM9InVzZXJBdmF0YXIgfHwgcmVxdWlyZSgnQC9hc3NldHMvaW1hZ2VzL2hvbWUvdXNlci5wbmcnKSIKICAgICAgICAgICAgICBhbHQ9IueUqOaIt+WktOWDjyIKICAgICAgICAgICAgLz4KICAgICAgICAgICAgPGltZyB2LWVsc2Ugc3JjPSJAL2Fzc2V0cy9pbWFnZXMvbG9nbzIucG5nIiBhbHQ9IkFJ5aS05YOPIiAvPgogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXNzYWdlLXdyYXBwZXIiPgogICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgdi1pZj0iCiAgICAgICAgICAgICAgICBtZXNzYWdlLnJvbGUgPT09ICdhc3Npc3RhbnQnICYmIGlzVGhpbmtpbmcgJiYgIW1lc3NhZ2UuY29udGVudAogICAgICAgICAgICAgICIKICAgICAgICAgICAgICBjbGFzcz0idGhpbmtpbmctYW5pbWF0aW9uIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHNwYW4+PC9zcGFuPgogICAgICAgICAgICAgIDxzcGFuPjwvc3Bhbj4KICAgICAgICAgICAgICA8c3Bhbj48L3NwYW4+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgdi1lbHNlCiAgICAgICAgICAgICAgY2xhc3M9Im1lc3NhZ2UtY29udGVudCIKICAgICAgICAgICAgICB2LWh0bWw9IgogICAgICAgICAgICAgICAgbWVzc2FnZS5yb2xlID09PSAnYXNzaXN0YW50JwogICAgICAgICAgICAgICAgICA/IG1lc3NhZ2UuY29udGVudAogICAgICAgICAgICAgICAgICA6IG1lc3NhZ2UuY29udGVudAogICAgICAgICAgICAgICIKICAgICAgICAgICAgPjwvZGl2PgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8dGVtcGxhdGUgc2xvdD0iZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9ImNsb3NlQWlEaWFsb2ciPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgIDwvdGVtcGxhdGU+CiAgPC9lbC1kaWFsb2c+CgogIDwhLS0gRGVlcHNlZWvmiqXlkYrop6Por7vlvLnnqpcgLS0+CiAgPGRlZXBzZWVrLXJlcG9ydC1kaWFsb2cKICAgIDp2aXNpYmxlLnN5bmM9InNob3dEZWVwc2Vla0RpYWxvZyIKICAgIDphcnRpY2xlLWRhdGE9ImN1cnJlbnRBcnRpY2xlIgogIC8+CjwvZGl2Pgo="}, null]}